{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { FormsModule } from \"@angular/forms\"; // Pour ngModel dans les filtres\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatSelectModule } from \"@angular/material/select\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\";\nimport { CourseListComponent } from \"./course-list/course-list.component\";\nimport { CourseDetailComponent } from \"./course-detail/course-detail.component\";\nimport { CourseCreateEditComponent } from \"./course-create-edit/course-create-edit.component\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\nexport let CoursesModule = class CoursesModule {};\nCoursesModule = __decorate([NgModule({\n  declarations: [CourseListComponent, CourseDetailComponent, CourseCreateEditComponent],\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatChipsModule, MatProgressBarModule, RouterModule.forChild([{\n    path: \"\",\n    component: CourseListComponent\n  }, {\n    path: \"create\",\n    component: CourseCreateEditComponent\n  }, {\n    path: \"edit/:id\",\n    component: CourseCreateEditComponent\n  }, {\n    path: \":id\",\n    component: CourseDetailComponent\n  }])]\n})], CoursesModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatChipsModule", "MatProgressBarModule", "CourseListComponent", "CourseDetailComponent", "CourseCreateEditComponent", "ReactiveFormsModule", "CoursesModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component"], "sources": ["C:\\e-learning\\src\\app\\features\\courses\\courses.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // Pour ngModel dans les filtres\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\n\nimport { CourseListComponent } from \"./course-list/course-list.component\"\nimport { CourseDetailComponent } from \"./course-detail/course-detail.component\"\nimport { CourseCreateEditComponent } from \"./course-create-edit/course-create-edit.component\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\n\n@NgModule({\n  declarations: [CourseListComponent, CourseDetailComponent, CourseCreateEditComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    RouterModule.forChild([\n      { path: \"\", component: CourseListComponent },\n      { path: \"create\", component: CourseCreateEditComponent },\n      { path: \"edit/:id\", component: CourseCreateEditComponent },\n      { path: \":id\", component: CourseDetailComponent },\n    ]),\n  ],\n})\nexport class CoursesModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB,EAAC;AAE7C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AAErE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,mBAAmB,QAAQ,gBAAgB;AAwB7C,WAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAG;AAAhBA,aAAa,GAAAC,UAAA,EAtBzBjB,QAAQ,CAAC;EACRkB,YAAY,EAAE,CAACN,mBAAmB,EAAEC,qBAAqB,EAAEC,yBAAyB,CAAC;EACrFK,OAAO,EAAE,CACPlB,YAAY,EACZE,WAAW,EACXY,mBAAmB,EACnBX,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,oBAAoB,EACpBT,YAAY,CAACkB,QAAQ,CAAC,CACpB;IAAEC,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEV;EAAmB,CAAE,EAC5C;IAAES,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAER;EAAyB,CAAE,EACxD;IAAEO,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAER;EAAyB,CAAE,EAC1D;IAAEO,IAAI,EAAE,KAAK;IAAEC,SAAS,EAAET;EAAqB,CAAE,CAClD,CAAC;CAEL,CAAC,C,EACWG,aAAa,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}