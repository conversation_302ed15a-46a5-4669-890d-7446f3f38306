{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatCheckboxModule } from \"@angular/material/checkbox\";\nimport { MatSelectModule } from \"@angular/material/select\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { LoginComponent } from \"./login/login.component\";\nimport { RegisterComponent } from \"./register/register.component\";\nexport let AuthModule = class AuthModule {};\nAuthModule = __decorate([NgModule({\n  declarations: [LoginComponent, RegisterComponent],\n  imports: [CommonModule, ReactiveFormsModule, MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatCheckboxModule, MatSelectModule, MatIconModule, MatProgressSpinnerModule, RouterModule.forChild([{\n    path: \"login\",\n    component: LoginComponent\n  }, {\n    path: \"register\",\n    component: RegisterComponent\n  }, {\n    path: \"\",\n    redirectTo: \"login\",\n    pathMatch: \"full\"\n  }])]\n})], AuthModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatCheckboxModule", "MatSelectModule", "MatIconModule", "MatProgressSpinnerModule", "LoginComponent", "RegisterComponent", "AuthModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "redirectTo", "pathMatch"], "sources": ["C:\\e-learning\\src\\app\\features\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCheckboxModule } from \"@angular/material/checkbox\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { LoginComponent } from \"./login/login.component\"\nimport { RegisterComponent } from \"./register/register.component\"\n\n@NgModule({\n  declarations: [LoginComponent, RegisterComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatCheckboxModule,\n    MatSelectModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([\n      { path: \"login\", component: LoginComponent },\n      { path: \"register\", component: RegisterComponent },\n      { path: \"\", redirectTo: \"login\", pathMatch: \"full\" },\n    ]),\n  ],\n})\nexport class AuthModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;AAsB1D,WAAMC,UAAU,GAAhB,MAAMA,UAAU,GAAG;AAAbA,UAAU,GAAAC,UAAA,EApBtBf,QAAQ,CAAC;EACRgB,YAAY,EAAE,CAACJ,cAAc,EAAEC,iBAAiB,CAAC;EACjDI,OAAO,EAAE,CACPhB,YAAY,EACZE,mBAAmB,EACnBC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,wBAAwB,EACxBT,YAAY,CAACgB,QAAQ,CAAC,CACpB;IAAEC,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAER;EAAc,CAAE,EAC5C;IAAEO,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEP;EAAiB,CAAE,EAClD;IAAEM,IAAI,EAAE,EAAE;IAAEE,UAAU,EAAE,OAAO;IAAEC,SAAS,EAAE;EAAM,CAAE,CACrD,CAAC;CAEL,CAAC,C,EACWR,UAAU,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}