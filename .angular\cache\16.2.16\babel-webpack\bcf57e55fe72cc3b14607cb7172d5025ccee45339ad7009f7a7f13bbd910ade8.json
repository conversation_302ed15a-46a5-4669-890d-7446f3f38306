{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ClientService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlApi}client`;\n  }\n  // GET: Tous les clients (correspond à GET /api/client)\n  getAllClients() {\n    return this.http.get(this.apiUrl);\n  }\n  // GET: Un client par ID (correspond à GET /api/client/{id})\n  getClient(id) {\n    return this.http.get(`${this.apiUrl}/${id}`);\n  }\n  // POST: Créer un client (correspond à POST /api/client)\n  createClient(client) {\n    // ✅ Envoyer les données telles quelles pour voir ce que le serveur attend\n    console.log('Données envoyées au serveur:', client); // ✅ Debug\n    return this.http.post(this.apiUrl, client);\n  }\n  // PUT: Modifier un client (correspond à PUT /api/client/{id})\n  updateClient(id, client) {\n    return this.http.put(`${this.apiUrl}/${id}`, client);\n  }\n  // DELETE: Supprimer un client (correspond à DELETE /api/client/{id})\n  deleteClient(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n  // POST: S'inscrire à un cours gratuit (correspond à POST /api/client/{clientId}/inscrire/{coursId})\n  sInscrire(clientId, coursId) {\n    return this.http.post(`${this.apiUrl}/${clientId}/inscrire/${coursId}`, {});\n  }\n  // POST: Acheter un cours payant (correspond à POST /api/client/{clientId}/acheter/{coursId})\n  acheterContenu(clientId, coursId) {\n    return this.http.post(`${this.apiUrl}/${clientId}/acheter/${coursId}`, {});\n  }\n  static {\n    this.ɵfac = function ClientService_Factory(t) {\n      return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ClientService,\n      factory: ClientService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "ClientService", "constructor", "http", "apiUrl", "urlApi", "getAllClients", "get", "getClient", "id", "createClient", "client", "console", "log", "post", "updateClient", "put", "deleteClient", "delete", "sInscrire", "clientId", "coursId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Client } from \"../models/user.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class ClientService {\n  private apiUrl = `${environment.urlApi}client`\n\n  constructor(private http: HttpClient) {}\n\n  // GET: Tous les clients (correspond à GET /api/client)\n  getAllClients(): Observable<Client[]> {\n    return this.http.get<Client[]>(this.apiUrl)\n  }\n\n  // GET: Un client par ID (correspond à GET /api/client/{id})\n  getClient(id: number): Observable<Client> {\n    return this.http.get<Client>(`${this.apiUrl}/${id}`)\n  }\n\n  // POST: Créer un client (correspond à POST /api/client)\n  createClient(client: any): Observable<Client> {\n    // ✅ Envoyer les données telles quelles pour voir ce que le serveur attend\n    console.log('Données envoyées au serveur:', client); // ✅ Debug\n    return this.http.post<Client>(this.apiUrl, client)\n  }\n\n  // PUT: Modifier un client (correspond à PUT /api/client/{id})\n  updateClient(id: number, client: Client): Observable<any> {\n    return this.http.put(`${this.apiUrl}/${id}`, client)\n  }\n\n  // DELETE: Supprimer un client (correspond à DELETE /api/client/{id})\n  deleteClient(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/${id}`)\n  }\n\n  // POST: S'inscrire à un cours gratuit (correspond à POST /api/client/{clientId}/inscrire/{coursId})\n  sInscrire(clientId: number, coursId: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/${clientId}/inscrire/${coursId}`, {})\n  }\n\n  // POST: Acheter un cours payant (correspond à POST /api/client/{clientId}/acheter/{coursId})\n  acheterContenu(clientId: number, coursId: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/${clientId}/acheter/${coursId}`, {})\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,MAAM,QAAQ;EAEP;EAEvC;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAW,IAAI,CAACH,MAAM,CAAC;EAC7C;EAEA;EACAI,SAASA,CAACC,EAAU;IAClB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,IAAIK,EAAE,EAAE,CAAC;EACtD;EAEA;EACAC,YAAYA,CAACC,MAAW;IACtB;IACAC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,MAAM,CAAC,CAAC,CAAC;IACrD,OAAO,IAAI,CAACR,IAAI,CAACW,IAAI,CAAS,IAAI,CAACV,MAAM,EAAEO,MAAM,CAAC;EACpD;EAEA;EACAI,YAAYA,CAACN,EAAU,EAAEE,MAAc;IACrC,OAAO,IAAI,CAACR,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACZ,MAAM,IAAIK,EAAE,EAAE,EAAEE,MAAM,CAAC;EACtD;EAEA;EACAM,YAAYA,CAACR,EAAU;IACrB,OAAO,IAAI,CAACN,IAAI,CAACe,MAAM,CAAC,GAAG,IAAI,CAACd,MAAM,IAAIK,EAAE,EAAE,CAAC;EACjD;EAEA;EACAU,SAASA,CAACC,QAAgB,EAAEC,OAAe;IACzC,OAAO,IAAI,CAAClB,IAAI,CAACW,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,IAAIgB,QAAQ,aAAaC,OAAO,EAAE,EAAE,EAAE,CAAC;EAC7E;EAEA;EACAC,cAAcA,CAACF,QAAgB,EAAEC,OAAe;IAC9C,OAAO,IAAI,CAAClB,IAAI,CAACW,IAAI,CAAC,GAAG,IAAI,CAACV,MAAM,IAAIgB,QAAQ,YAAYC,OAAO,EAAE,EAAE,EAAE,CAAC;EAC5E;;;uBAxCWpB,aAAa,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAbzB,aAAa;MAAA0B,OAAA,EAAb1B,aAAa,CAAA2B,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}