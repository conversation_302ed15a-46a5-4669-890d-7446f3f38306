{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from \"@angular/core\";\nimport { Validators } from \"@angular/forms\";\nexport let LoginComponent = class LoginComponent {\n  constructor(fb, authService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.loginForm = this.fb.group({\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", Validators.required],\n      rememberMe: [false]\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const {\n        email,\n        password\n      } = this.loginForm.value;\n      this.authService.login({\n        email,\n        password\n      }).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open(\"Connexion réussie !\", \"Fermer\", {\n            duration: 3000\n          });\n          this.router.navigate([\"/dashboard\"]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(\"Erreur de connexion. Vérifiez vos identifiants.\", \"Fermer\", {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n};\nLoginComponent = __decorate([Component({\n  selector: \"app-login\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <!-- Header -->\n        <div class=\"auth-header\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <h1>Training Platform</h1>\n          </div>\n          <p>Plateforme de formation en ligne</p>\n        </div>\n\n        <!-- Login Form -->\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Connexion</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"rememberMe\">Se souvenir de moi</mat-checkbox>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"loginForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">Se connecter</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Pas encore de compte ? \n                <a routerLink=\"/auth/register\" class=\"link\">S'inscrire</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .auth-header {\n      text-align: center;\n      margin-bottom: 2rem;\n      color: white;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .logo mat-icon {\n      font-size: 2.5rem;\n      margin-right: 0.5rem;\n    }\n\n    .logo h1 {\n      font-size: 2rem;\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `]\n})], LoginComponent);", "map": {"version": 3, "names": ["Component", "Validators", "LoginComponent", "constructor", "fb", "authService", "router", "snackBar", "hidePassword", "isLoading", "ngOnInit", "loginForm", "group", "email", "required", "password", "rememberMe", "onSubmit", "valid", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "__decorate", "selector", "template", "styles"], "sources": ["C:\\e-learning\\src\\app\\features\\auth\\login\\login.component.ts"], "sourcesContent": ["import { Component, type OnInit } from \"@angular/core\"\nimport { type FormBuilder, type FormGroup, Validators } from \"@angular/forms\"\nimport type { Router } from \"@angular/router\"\nimport type { MatSnackBar } from \"@angular/material/snack-bar\"\nimport type { AuthService } from \"../../../core/services/auth.service\"\n\n@Component({\n  selector: \"app-login\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <!-- Header -->\n        <div class=\"auth-header\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <h1>Training Platform</h1>\n          </div>\n          <p>Plateforme de formation en ligne</p>\n        </div>\n\n        <!-- Login Form -->\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Connexion</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"rememberMe\">Se souvenir de moi</mat-checkbox>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"loginForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">Se connecter</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Pas encore de compte ? \n                <a routerLink=\"/auth/register\" class=\"link\">S'inscrire</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .auth-header {\n      text-align: center;\n      margin-bottom: 2rem;\n      color: white;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .logo mat-icon {\n      font-size: 2.5rem;\n      margin-right: 0.5rem;\n    }\n\n    .logo h1 {\n      font-size: 2rem;\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `,\n  ],\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup\n  hidePassword = true\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.loginForm = this.fb.group({\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", Validators.required],\n      rememberMe: [false],\n    })\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true\n      const { email, password } = this.loginForm.value\n\n      this.authService.login({ email, password }).subscribe({\n        next: (response) => {\n          this.isLoading = false\n          this.snackBar.open(\"Connexion réussie !\", \"Fermer\", { duration: 3000 })\n          this.router.navigate([\"/dashboard\"])\n        },\n        error: (error) => {\n          this.isLoading = false\n          this.snackBar.open(\"Erreur de connexion. Vérifiez vos identifiants.\", \"Fermer\", { duration: 5000 })\n        },\n      })\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAqB,eAAe;AACtD,SAA2CC,UAAU,QAAQ,gBAAgB;AAkJtE,WAAMC,cAAc,GAApB,MAAMA,cAAc;EAKzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;EAOd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACY,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAEd,UAAU,CAACa,QAAQ,CAAC;MACnCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,SAAS,CAACO,KAAK,EAAE;MACxB,IAAI,CAACT,SAAS,GAAG,IAAI;MACrB,MAAM;QAAEI,KAAK;QAAEE;MAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACQ,KAAK;MAEhD,IAAI,CAACd,WAAW,CAACe,KAAK,CAAC;QAAEP,KAAK;QAAEE;MAAQ,CAAE,CAAC,CAACM,SAAS,CAAC;QACpDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACd,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACiB,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACvE,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACiB,IAAI,CAAC,iDAAiD,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACrG;OACD,CAAC;;EAEN;CACD;AAtCYvB,cAAc,GAAA0B,UAAA,EA7I1B5B,SAAS,CAAC;EACT6B,QAAQ,EAAE,WAAW;EACrBC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+DT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuED;CAEF,CAAC,C,EACW7B,cAAc,CAsC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}