import { Component, OnInit } from "@angular/core"
import { CourseService } from "../../../core/services/course.service"
import { Course } from "../../../core/models/course.model"
import { MatSnackBar } from "@angular/material/snack-bar"
import { Router } from "@angular/router"

@Component({
  selector: "app-course-list",
  template: `
    <div class="course-list-container">
      <div class="header-section">
        <h1>Catalogue des Cours</h1>

        <!-- Filtres -->
        <div class="filters-row">
          <mat-form-field appearance="outline" class="search-input">
            <mat-label>Rechercher un cours...</mat-label>
            <input matInput [(ngModel)]="searchTerm" (input)="applyFilters()">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="filter-select">
            <mat-label>Niveau</mat-label>
            <mat-select [(ngModel)]="filterLevel" (selectionChange)="applyFilters()">
              <mat-option value="all">Tous les niveaux</mat-option>
              <mat-option value="Débutant">Débutant</mat-option>
              <mat-option value="Intermédiaire">Intermédiaire</mat-option>
              <mat-option value="Avancé">Avancé</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="filter-select">
            <mat-label>Prix</mat-label>
            <mat-select [(ngModel)]="filterPrice" (selectionChange)="applyFilters()">
              <mat-option value="all">Tous les prix</mat-option>
              <mat-option value="free">Gratuit</mat-option>
              <mat-option value="paid">Payant</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <!-- Liste des cours -->
      <div class="courses-grid" *ngIf="filteredCourses.length > 0; else noCourses">
        <mat-card *ngFor="let course of filteredCourses" class="course-card">
          <mat-card-header>
            <div class="card-header-top">
              <mat-chip-listbox>
                <mat-chip [class]="getLevelColor(course.niveau)">{{ course.niveau }}</mat-chip>
              </mat-chip-listbox>
              <span class="price" *ngIf="!course.estGratuit">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>
              <mat-chip *ngIf="course.estGratuit" class="free-chip">Gratuit</mat-chip>
            </div>
            <mat-card-title>{{ course.titre }}</mat-card-title>
            <mat-card-subtitle class="description">{{ course.description }}</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <div class="course-info">
              <div class="info-item">
                <mat-icon>person</mat-icon>
                <span>{{ course.formateur.prenom }} {{ course.formateur.nom }}</span>
              </div>
              <div class="info-item">
                <mat-icon>schedule</mat-icon>
                <span>{{ course.duree }} min</span>
              </div>
              <div class="info-item">
                <mat-icon>group</mat-icon>
                <span>{{ course.nombreEtudiants }} étudiants</span>
              </div>
              <div class="info-item">
                <mat-icon class="star-icon">star</mat-icon>
                <span>{{ course.note }}</span>
              </div>
            </div>

            <div class="content-preview">
              <h4>Contenu du cours:</h4>
              <div *ngFor="let content of course.contenus | slice:0:3" class="content-item">
                <mat-icon>{{ getContentIcon(content.typeContenu) }}</mat-icon>
                <span>{{ content.titre }}</span>
                <span *ngIf="content.duree" class="content-duration">({{ content.duree }} min)</span>
              </div>
              <p *ngIf="course.contenus.length > 3" class="more-content">+{{ course.contenus.length - 3 }} autres contenus</p>
            </div>

            <div class="card-actions">
              <button mat-stroked-button color="primary" [routerLink]="['/courses', course.id]">
                Voir détails
              </button>
              <button mat-raised-button color="accent" *ngIf="course.estGratuit">Commencer</button>
              <button mat-raised-button color="primary" *ngIf="!course.estGratuit" [routerLink]="['/payment', course.id]">
                <mat-icon>euro_symbol</mat-icon>
                Acheter
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <ng-template #noCourses>
        <div class="no-courses">
          <mat-icon>book_off</mat-icon>
          <h3>Aucun cours trouvé</h3>
          <p>Essayez de modifier vos critères de recherche.</p>
        </div>
      </ng-template>
    </div>
  `,
  styles: [
    `
    .course-list-container {
      padding: 2rem;
      background-color: #f5f5f5;
      min-height: 100vh;
    }

    .header-section {
      margin-bottom: 2rem;
    }

    .header-section h1 {
      font-size: 2.5rem;
      font-weight: bold;
      color: #333;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .filters-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 2rem;
    }

    .search-input {
      flex-grow: 1;
      max-width: 400px;
    }

    .filter-select {
      width: 200px;
    }

    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .course-card {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .course-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .card-header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .mat-chip {
      font-size: 0.8rem;
      padding: 0.3rem 0.7rem;
      height: auto;
    }

    .mat-chip.bg-green-100 { background-color: #d4edda; color: #155724; }
    .mat-chip.bg-yellow-100 { background-color: #fff3cd; color: #856404; }
    .mat-chip.bg-red-100 { background-color: #f8d7da; color: #721c24; }
    .free-chip { background-color: #e6ffed; color: #28a745; }

    .price {
      font-size: 1.5rem;
      font-weight: bold;
      color: #673ab7; /* Purple */
    }

    .course-card mat-card-title {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .course-card .description {
      font-size: 0.9rem;
      color: #666;
      line-height: 1.4;
      height: 3em; /* Limit to 2 lines */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .course-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-top: 1rem;
      font-size: 0.9rem;
      color: #555;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 0.3rem;
    }

    .info-item mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
      color: #888;
    }

    .info-item .star-icon {
      color: #ffc107; /* Yellow */
    }

    .content-preview {
      margin-top: 1.5rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .content-preview h4 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 0.8rem;
      color: #444;
    }

    .content-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 0.4rem;
    }

    .content-item mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: #777;
    }

    .content-duration {
      margin-left: auto;
      font-size: 0.8rem;
      color: #888;
    }

    .more-content {
      font-size: 0.8rem;
      color: #888;
      margin-top: 0.5rem;
    }

    .card-actions {
      display: flex;
      gap: 0.8rem;
      margin-top: 1.5rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .card-actions button {
      flex: 1;
      font-size: 0.9rem;
      padding: 0.6rem 1rem;
    }

    .card-actions button mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      margin-right: 0.3rem;
    }

    .no-courses {
      text-align: center;
      padding: 4rem 0;
      color: #777;
    }

    .no-courses mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #aaa;
    }

    .no-courses h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
      .filters-row {
        flex-direction: column;
        align-items: stretch;
      }
      .search-input, .filter-select {
        max-width: 100%;
        width: 100%;
      }
      .courses-grid {
        grid-template-columns: 1fr;
      }
    }
  `,
  ],
})
export class CourseListComponent implements OnInit {
  courses: Course[] = []
  filteredCourses: Course[] = []
  searchTerm = ""
  filterLevel = "all"
  filterPrice = "all"

  constructor(
    private courseService: CourseService,
    private snackBar: MatSnackBar,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.loadCourses()
  }

  loadCourses(): void {
    // Mock data for demonstration
    this.courses = [
      {
        id: 1,
        titre: "React Fundamentals",
        description: "Apprenez les bases de React avec des exemples pratiques et des projets concrets.",
        prix: 99.99,
        duree: 120,
        niveau: "Débutant",
        formateurId: 1,
        formateur: { id: 1, nom: "Dupont", prenom: "Jean" },
        contenus: [
          {
            id: 1,
            titre: "Introduction à React",
            typeContenu: "Video",
            coursId: 1,
            estComplete: false,
            estDebloque: true,
            ordre: 1,
          },
          {
            id: 2,
            titre: "Components et Props",
            typeContenu: "Video",
            coursId: 1,
            estComplete: false,
            estDebloque: true,
            ordre: 2,
          },
          {
            id: 3,
            titre: "Quiz - Bases de React",
            typeContenu: "Quiz",
            coursId: 1,
            estComplete: false,
            estDebloque: true,
            ordre: 3,
          },
          {
            id: 4,
            titre: "Résumé du chapitre",
            typeContenu: "Resume",
            coursId: 1,
            estComplete: false,
            estDebloque: true,
            ordre: 4,
          },
        ],
        nombreEtudiants: 245,
        note: 4.8,
        estGratuit: false,
      },
      {
        id: 2,
        titre: "JavaScript Avancé",
        description: "Maîtrisez les concepts avancés de JavaScript : closures, prototypes, async/await.",
        prix: 149.99,
        duree: 180,
        niveau: "Avancé",
        formateurId: 2,
        formateur: { id: 2, nom: "Martin", prenom: "Sophie" },
        contenus: [
          {
            id: 5,
            titre: "Closures et Scope",
            typeContenu: "Video",
            coursId: 2,
            estComplete: false,
            estDebloque: true,
            ordre: 1,
          },
          {
            id: 6,
            titre: "Prototypes et Héritage",
            typeContenu: "Video",
            coursId: 2,
            estComplete: false,
            estDebloque: true,
            ordre: 2,
          },
          {
            id: 7,
            titre: "Quiz - Concepts avancés",
            typeContenu: "Quiz",
            coursId: 2,
            estComplete: false,
            estDebloque: true,
            ordre: 3,
          },
        ],
        nombreEtudiants: 156,
        note: 4.9,
        estGratuit: false,
      },
      {
        id: 3,
        titre: "Introduction au Web",
        description: "Cours gratuit pour débuter dans le développement web avec HTML, CSS et JavaScript.",
        prix: 0,
        duree: 60,
        niveau: "Débutant",
        formateurId: 3,
        formateur: { id: 3, nom: "Bernard", prenom: "Pierre" },
        contenus: [
          {
            id: 8,
            titre: "HTML Basics",
            typeContenu: "Video",
            coursId: 3,
            estComplete: false,
            estDebloque: true,
            ordre: 1,
          },
          {
            id: 9,
            titre: "CSS Styling",
            typeContenu: "Video",
            coursId: 3,
            estComplete: false,
            estDebloque: true,
            ordre: 2,
          },
          {
            id: 10,
            titre: "Quiz final",
            typeContenu: "Quiz",
            coursId: 3,
            estComplete: false,
            estDebloque: true,
            ordre: 3,
          },
        ],
        nombreEtudiants: 892,
        note: 4.6,
        estGratuit: true,
      },
    ]
    this.applyFilters()

    // Uncomment to fetch from API
    /*
    this.courseService.getAllCours().subscribe({
      next: (data) => {
        this.courses = data;
        this.applyFilters();
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement des cours.', 'Fermer', { duration: 3000 });
        console.error(err);
      }
    });
    */
  }

  applyFilters(): void {
    this.filteredCourses = this.courses.filter((course) => {
      const matchesSearch =
        course.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(this.searchTerm.toLowerCase())
      const matchesLevel = this.filterLevel === "all" || course.niveau === this.filterLevel
      const matchesPrice =
        this.filterPrice === "all" ||
        (this.filterPrice === "free" && course.estGratuit) ||
        (this.filterPrice === "paid" && !course.estGratuit)

      return matchesSearch && matchesLevel && matchesPrice
    })
  }

  getContentIcon(type: string): string {
    switch (type) {
      case "Video":
        return "play_circle"
      case "Quiz":
        return "quiz"
      case "Resume":
        return "description"
      default:
        return "book"
    }
  }

  getLevelColor(niveau: string): string {
    switch (niveau) {
      case "Débutant":
        return "bg-green-100"
      case "Intermédiaire":
        return "bg-yellow-100"
      case "Avancé":
        return "bg-red-100"
      default:
        return "bg-gray-100"
    }
  }
}
