"use strict";
(self["webpackChunkelearning_platform"] = self["webpackChunkelearning_platform"] || []).push([["src_app_features_messages_messages_module_ts"],{

/***/ 6375:
/*!**************************************************!*\
  !*** ./src/app/core/services/message.service.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessageService: () => (/* binding */ MessageService)
/* harmony export */ });
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common/http */ 6443);



class MessageService {
  constructor(http) {
    this.http = http;
    this.apiUrl = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlApi}messages`;
  }
  // POST: Envoyer un message (correspond à POST /api/messages)
  envoyerMessage(message) {
    return this.http.post(this.apiUrl, message);
  }
  // GET: Messages entre deux utilisateurs (correspond à GET /api/messages/entre/{id1}/{id2})
  getMessages(id1, id2) {
    return this.http.get(`${this.apiUrl}/entre/${id1}/${id2}`);
  }
  static {
    this.ɵfac = function MessageService_Factory(t) {
      return new (t || MessageService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: MessageService,
      factory: MessageService.ɵfac,
      providedIn: "root"
    });
  }
}

/***/ }),

/***/ 3188:
/*!*********************************************************!*\
  !*** ./src/app/features/messages/messages.component.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesComponent: () => (/* binding */ MessagesComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _core_services_message_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../core/services/message.service */ 6375);
/* harmony import */ var _core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core/services/auth.service */ 8010);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/snack-bar */ 3347);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_list__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/list */ 943);
/* harmony import */ var _angular_material_divider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/divider */ 4102);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/chips */ 2772);














function MessagesComponent_mat_list_item_19_mat_chip_listbox_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-chip-listbox", 22)(1, "mat-chip", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const conv_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](conv_r5.unreadCount);
  }
}
const _c0 = function (a0) {
  return {
    "selected-conversation": a0
  };
};
function MessagesComponent_mat_list_item_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-list-item", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function MessagesComponent_mat_list_item_19_Template_mat_list_item_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r9);
      const conv_r5 = restoredCtx.$implicit;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r8.selectConversation(conv_r5.participantId));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "div", 16)(2, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 17)(5, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "mat-chip-listbox")(8, "mat-chip");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "div", 18)(11, "span", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "span", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](15, MessagesComponent_mat_list_item_19_mat_chip_listbox_15_Template, 3, 1, "mat-chip-listbox", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](16, "mat-divider");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const conv_r5 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction1"](9, _c0, ctx_r0.selectedConversationId === conv_r5.participantId));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.getInitials(conv_r5.participantName));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](conv_r5.participantName);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](ctx_r0.getRoleChipClass(conv_r5.participantRole));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](conv_r5.participantRole);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](conv_r5.lastMessageContent);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.formatDate(conv_r5.lastMessageDate));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", conv_r5.unreadCount > 0);
  }
}
function MessagesComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 24)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "chat_bubble_outline");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "Aucune conversation trouv\u00E9e.");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
const _c1 = function (a0, a1) {
  return {
    "my-message": a0,
    "other-message": a1
  };
};
function MessagesComponent_ng_container_22_div_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 33)(1, "div", 34)(2, "p", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "span", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const msg_r11 = ctx.$implicit;
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction2"](3, _c1, msg_r11.expediteurId === (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.id), msg_r11.expediteurId !== (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.id)));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](msg_r11.contenu);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r10.formatDate(msg_r11.dateEnvoi));
  }
}
function MessagesComponent_ng_container_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "mat-card-header", 25)(2, "div", 26)(3, "div", 16)(4, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div")(7, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "mat-chip-listbox")(10, "mat-chip");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "mat-card-content", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](13, MessagesComponent_ng_container_22_div_13_Template, 6, 6, "div", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "mat-card-actions", 29)(15, "mat-form-field", 30)(16, "textarea", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("ngModelChange", function MessagesComponent_ng_container_22_Template_textarea_ngModelChange_16_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r12.newMessageContent = $event);
    })("keydown.enter", function MessagesComponent_ng_container_22_Template_textarea_keydown_enter_16_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r13);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r14.sendMessage($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "button", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function MessagesComponent_ng_container_22_Template_button_click_17_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r13);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r15.sendMessage());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, "send");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r2.getInitials((ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantName) || ""));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantName);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](ctx_r2.getRoleChipClass((ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantRole) || "Client"));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantRole, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r2.messages);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngModel", ctx_r2.newMessageContent);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", !ctx_r2.newMessageContent.trim());
  }
}
function MessagesComponent_ng_template_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 36)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "chat");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "S\u00E9lectionnez une conversation pour commencer");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
class MessagesComponent {
  constructor(messageService, authService, snackBar) {
    this.messageService = messageService;
    this.authService = authService;
    this.snackBar = snackBar;
    this.conversations = [];
    this.filteredConversations = [];
    this.selectedConversationId = null;
    this.messages = [];
    this.newMessageContent = "";
    this.searchTerm = "";
    this.currentUser = null;
    this.selectedParticipant = null;
  }
  ngOnInit() {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      if (this.currentUser) {
        this.loadConversations();
      }
    });
  }
  loadConversations() {
    // Mock data for demonstration
    this.conversations = [{
      participantId: 2,
      participantName: "Jean Dupont",
      participantRole: "Formateur",
      lastMessageContent: "Bonjour, avez-vous des questions sur le cours React ?",
      lastMessageDate: new Date("2024-01-15T10:30:00Z"),
      unreadCount: 2
    }, {
      participantId: 3,
      participantName: "Sophie Bernard",
      participantRole: "Client",
      lastMessageContent: "Merci pour votre aide !",
      lastMessageDate: new Date("2024-01-14T15:45:00Z"),
      unreadCount: 0
    }, {
      participantId: 4,
      participantName: "Admin System",
      participantRole: "Admin",
      lastMessageContent: "Votre demande a été traitée.",
      lastMessageDate: new Date("2024-01-13T11:00:00Z"),
      unreadCount: 0
    }];
    this.applySearch();
    // Uncomment to fetch from API
    /*
    // This would require a backend endpoint to get conversation previews for a user
    // For example: this.messageService.getConversations(this.currentUser.id).subscribe(...)
    */
  }

  applySearch() {
    this.filteredConversations = this.conversations.filter(conv => conv.participantName.toLowerCase().includes(this.searchTerm.toLowerCase()));
  }
  selectConversation(participantId) {
    this.selectedConversationId = participantId;
    this.selectedParticipant = this.conversations.find(c => c.participantId === participantId) || null;
    this.loadMessages(participantId);
  }
  loadMessages(participantId) {
    if (!this.currentUser) return;
    // Mock messages for the selected conversation
    this.messages = [{
      id: 1,
      expediteurId: participantId,
      destinataireId: this.currentUser.id,
      contenu: "Bonjour, j'espère que vous appréciez le cours React !",
      dateEnvoi: new Date("2024-01-15T09:00:00Z"),
      expediteur: {
        id: participantId,
        nom: "Dupont",
        prenom: "Jean",
        email: "",
        role: "Formateur"
      },
      destinataire: this.currentUser
    }, {
      id: 2,
      expediteurId: this.currentUser.id,
      destinataireId: participantId,
      contenu: "Bonjour Jean, oui c'est très intéressant ! J'ai une question sur les hooks.",
      dateEnvoi: new Date("2024-01-15T09:15:00Z"),
      expediteur: this.currentUser,
      destinataire: {
        id: participantId,
        nom: "Dupont",
        prenom: "Jean",
        email: "",
        role: "Formateur"
      }
    }, {
      id: 3,
      expediteurId: participantId,
      destinataireId: this.currentUser.id,
      contenu: "Parfait ! N'hésitez pas à me poser toutes vos questions. Les hooks peuvent sembler complexes au début.",
      dateEnvoi: new Date("2024-01-15T09:30:00Z"),
      expediteur: {
        id: participantId,
        nom: "Dupont",
        prenom: "Jean",
        email: "",
        role: "Formateur"
      },
      destinataire: this.currentUser
    }, {
      id: 4,
      expediteurId: participantId,
      destinataireId: this.currentUser.id,
      contenu: "Bonjour, avez-vous des questions sur le cours React ?",
      dateEnvoi: new Date("2024-01-15T10:30:00Z"),
      expediteur: {
        id: participantId,
        nom: "Dupont",
        prenom: "Jean",
        email: "",
        role: "Formateur"
      },
      destinataire: this.currentUser
    }];
    // Uncomment to fetch from API
    /*
    this.messageService.getMessages(this.currentUser.id, participantId).subscribe({
      next: (data) => {
        this.messages = data;
        // Mark messages as read
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement des messages.', 'Fermer', { duration: 3000 });
        console.error(err);
      }
    });
    */
  }

  sendMessage(event) {
    const keyboardEvent = event;
    if (keyboardEvent && keyboardEvent.key === "Enter" && !keyboardEvent.shiftKey) {
      keyboardEvent.preventDefault();
    } else if (keyboardEvent && keyboardEvent.key !== "Enter") {
      return; // Only proceed on Enter key press
    }

    if (!this.newMessageContent.trim() || !this.selectedConversationId || !this.currentUser) return;
    const message = {
      expediteurId: this.currentUser.id,
      destinataireId: this.selectedConversationId,
      contenu: this.newMessageContent,
      dateEnvoi: new Date()
    };
    // Mock message sending
    this.messages.push(message);
    this.newMessageContent = "";
    this.snackBar.open("Message envoyé (simulé) !", "Fermer", {
      duration: 1000
    });
    // Uncomment to send via API
    /*
    this.messageService.envoyerMessage(message).subscribe({
      next: (res) => {
        this.messages.push(message); // Add to local list after successful send
        this.newMessageContent = '';
        this.snackBar.open('Message envoyé !', 'Fermer', { duration: 1000 });
      },
      error: (err) => {
        this.snackBar.open('Erreur lors de l\'envoi du message.', 'Fermer', { duration: 3000 });
        console.error(err);
      }
    });
    */
  }

  getInitials(name) {
    const parts = name.split(" ");
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
    } else if (parts.length === 1 && parts[0].length > 0) {
      return parts[0][0].toUpperCase();
    }
    return "";
  }
  formatDate(date) {
    const d = new Date(date);
    const now = new Date();
    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60);
    if (diffInHours < 24 && d.getDate() === now.getDate()) {
      return d.toLocaleTimeString("fr-FR", {
        hour: "2-digit",
        minute: "2-digit"
      });
    } else if (diffInHours < 48 && d.getDate() === now.getDate() - 1) {
      return "Hier";
    } else {
      return d.toLocaleDateString("fr-FR", {
        day: "2-digit",
        month: "2-digit"
      });
    }
  }
  getRoleChipClass(role) {
    switch (role) {
      case "Client":
        return "role-chip-Client";
      case "Formateur":
        return "role-chip-Formateur";
      case "Admin":
        return "role-chip-Admin";
      default:
        return "";
    }
  }
  static {
    this.ɵfac = function MessagesComponent_Factory(t) {
      return new (t || MessagesComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_message_service__WEBPACK_IMPORTED_MODULE_0__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_3__.MatSnackBar));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: MessagesComponent,
      selectors: [["app-messages"]],
      decls: 25,
      vars: 5,
      consts: [[1, "messages-container"], [1, "messages-wrapper"], [1, "conversation-list-card"], [1, "card-title-with-icon"], ["mat-icon-button", ""], [1, "conversation-search"], ["appearance", "outline", 1, "full-width"], ["matInput", "", 3, "ngModel", "ngModelChange", "input"], ["matSuffix", ""], [1, "conversations-scroll-area"], [3, "ngClass", "click", 4, "ngFor", "ngForOf"], ["class", "no-conversations", 4, "ngIf"], [1, "chat-area-card"], [4, "ngIf", "ngIfElse"], ["noConversationSelected", ""], [3, "ngClass", "click"], ["matListItemAvatar", "", 1, "avatar-placeholder"], ["matListItemTitle", "", 1, "conversation-title"], ["matListItemLine", "", 1, "conversation-last-message"], [1, "message-content"], [1, "message-date"], ["matListItemMeta", "", 4, "ngIf"], ["matListItemMeta", ""], ["color", "warn", "selected", ""], [1, "no-conversations"], [1, "chat-header"], [1, "chat-header-info"], [1, "messages-display-area"], ["class", "message-bubble-wrapper", 3, "ngClass", 4, "ngFor", "ngForOf"], [1, "message-input-area"], ["appearance", "outline", 1, "full-width", "message-input-field"], ["matInput", "", "placeholder", "Tapez votre message...", "rows", "1", 3, "ngModel", "ngModelChange", "keydown.enter"], ["mat-mini-fab", "", "color", "primary", 3, "disabled", "click"], [1, "message-bubble-wrapper", 3, "ngClass"], [1, "message-bubble"], [1, "message-timestamp"], [1, "no-conversation-selected"]],
      template: function MessagesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "mat-card", 2)(3, "mat-card-header")(4, "mat-card-title", 3)(5, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6, "message");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, " Messages ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "button", 4)(9, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, "add");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "mat-card-content", 5)(12, "mat-form-field", 6)(13, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "Rechercher une conversation...");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "input", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("ngModelChange", function MessagesComponent_Template_input_ngModelChange_15_listener($event) {
            return ctx.searchTerm = $event;
          })("input", function MessagesComponent_Template_input_input_15_listener() {
            return ctx.applySearch();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](16, "mat-icon", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](17, "search");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "mat-nav-list", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](19, MessagesComponent_mat_list_item_19_Template, 17, 11, "mat-list-item", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](20, MessagesComponent_div_20_Template, 5, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "mat-card", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](22, MessagesComponent_ng_container_22_Template, 20, 8, "ng-container", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](23, MessagesComponent_ng_template_23_Template, 5, 0, "ng-template", null, 14, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](24);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](15);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngModel", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.filteredConversations);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.filteredConversations.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.selectedConversationId)("ngIfElse", _r3);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgModel, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardActions, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardHeader, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardTitle, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatIconButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatMiniFabButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__.MatIcon, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatSuffix, _angular_material_list__WEBPACK_IMPORTED_MODULE_11__.MatNavList, _angular_material_list__WEBPACK_IMPORTED_MODULE_11__.MatListItem, _angular_material_list__WEBPACK_IMPORTED_MODULE_11__.MatListItemAvatar, _angular_material_divider__WEBPACK_IMPORTED_MODULE_12__.MatDivider, _angular_material_list__WEBPACK_IMPORTED_MODULE_11__.MatListItemLine, _angular_material_list__WEBPACK_IMPORTED_MODULE_11__.MatListItemTitle, _angular_material_list__WEBPACK_IMPORTED_MODULE_11__.MatListItemMeta, _angular_material_chips__WEBPACK_IMPORTED_MODULE_13__.MatChip, _angular_material_chips__WEBPACK_IMPORTED_MODULE_13__.MatChipListbox],
      styles: [".messages-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding: 2rem;\n}\n\n.messages-wrapper[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 1.5rem;\n  max-width: 1400px;\n  margin: 0 auto;\n  height: calc(100vh - 4rem); \n\n}\n\n@media (max-width: 960px) {\n  .messages-wrapper[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    height: auto;\n  }\n  .conversation-list-card[_ngcontent-%COMP%] {\n    height: auto;\n    min-height: 300px;\n  }\n  .chat-area-card[_ngcontent-%COMP%] {\n    height: 600px; \n\n  }\n}\n.conversation-list-card[_ngcontent-%COMP%], .chat-area-card[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n}\n\n.conversation-list-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 0;\n}\n\n.card-title-with-icon[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  font-size: 1.4rem;\n  font-weight: 600;\n}\n\n.card-title-with-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  margin-right: 0.5rem;\n  font-size: 1.5rem;\n  width: 1.5rem;\n  height: 1.5rem;\n}\n\n.conversation-search[_ngcontent-%COMP%] {\n  padding: 1rem 1.5rem 0.5rem;\n}\n\n.conversation-search[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.conversations-scroll-area[_ngcontent-%COMP%] {\n  flex-grow: 1;\n  overflow-y: auto;\n  padding: 0;\n}\n\n.mat-list-item[_ngcontent-%COMP%] {\n  height: auto !important;\n  padding: 1rem 1.5rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease-in-out;\n}\n\n.mat-list-item[_ngcontent-%COMP%]:hover {\n  background-color: #f5f5f5;\n}\n\n.mat-list-item.selected-conversation[_ngcontent-%COMP%] {\n  background-color: #ede7f6; \n\n  border-left: 4px solid #673ab7; \n\n}\n\n.avatar-placeholder[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background-color: #e1bee7; \n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.1rem;\n  font-weight: bold;\n  color: #8e24aa; \n\n  flex-shrink: 0;\n}\n\n.conversation-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-weight: 500;\n  font-size: 1rem;\n}\n\n.conversation-last-message[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.85rem;\n  color: #777;\n  margin-top: 0.2rem;\n}\n\n.message-content[_ngcontent-%COMP%] {\n  flex-grow: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-right: 0.5rem;\n}\n\n.message-date[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n}\n\n.mat-chip[_ngcontent-%COMP%] {\n  font-size: 0.7rem;\n  padding: 0.2rem 0.5rem;\n  height: auto;\n}\n\n.role-chip-Client[_ngcontent-%COMP%] {\n  background-color: #e0e0e0;\n  color: #424242;\n}\n\n.role-chip-Formateur[_ngcontent-%COMP%] {\n  background-color: #bbdefb;\n  color: #1976d2;\n}\n\n.role-chip-Admin[_ngcontent-%COMP%] {\n  background-color: #e1bee7;\n  color: #8e24aa;\n}\n\n.no-conversations[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  color: #999;\n}\n\n.no-conversations[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n  margin-bottom: 1rem;\n}\n\n\n\n.chat-area-card[_ngcontent-%COMP%] {\n  background-color: #fff;\n}\n\n.chat-header[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #eee;\n  padding: 1rem 1.5rem;\n}\n\n.chat-header-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.chat-header-info[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 0.2rem;\n}\n\n.messages-display-area[_ngcontent-%COMP%] {\n  flex-grow: 1;\n  overflow-y: auto;\n  padding: 1.5rem;\n  background-color: #fcfcfc;\n}\n\n.message-bubble-wrapper[_ngcontent-%COMP%] {\n  display: flex;\n  margin-bottom: 1rem;\n}\n\n.message-bubble-wrapper.my-message[_ngcontent-%COMP%] {\n  justify-content: flex-end;\n}\n\n.message-bubble-wrapper.other-message[_ngcontent-%COMP%] {\n  justify-content: flex-start;\n}\n\n.message-bubble[_ngcontent-%COMP%] {\n  max-width: 70%;\n  padding: 0.8rem 1.2rem;\n  border-radius: 18px;\n  position: relative;\n  word-wrap: break-word;\n}\n\n.my-message[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\n  background-color: #673ab7; \n\n  color: white;\n  border-bottom-right-radius: 4px;\n}\n\n.other-message[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\n  background-color: #e0e0e0; \n\n  color: #333;\n  border-bottom-left-radius: 4px;\n}\n\n.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 0.95rem;\n  line-height: 1.4;\n}\n\n.message-timestamp[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  margin-top: 0.5rem;\n  display: block;\n  text-align: right;\n  color: rgba(255, 255, 255, 0.7); \n\n}\n\n.other-message[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\n  color: #777;\n}\n\n.message-input-area[_ngcontent-%COMP%] {\n  border-top: 1px solid #eee;\n  padding: 1rem 1.5rem;\n  display: flex;\n  align-items: flex-end;\n  gap: 0.8rem;\n}\n\n.message-input-field[_ngcontent-%COMP%] {\n  flex-grow: 1;\n}\n\n.message-input-field[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\n  min-height: 40px;\n  max-height: 120px;\n  overflow-y: auto;\n}\n\n.message-input-area[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n}\n\n.no-conversation-selected[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: #999;\n  text-align: center;\n}\n\n.no-conversation-selected[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 80:
/*!******************************************************!*\
  !*** ./src/app/features/messages/messages.module.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesModule: () => (/* binding */ MessagesModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_list__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/list */ 943);
/* harmony import */ var _angular_material_badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/badge */ 6256);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/chips */ 2772);
/* harmony import */ var _angular_material_divider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/divider */ 4102);
/* harmony import */ var _messages_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messages.component */ 3188);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


 // For ngModel in textarea
// Angular Material












class MessagesModule {
  static {
    this.ɵfac = function MessagesModule_Factory(t) {
      return new (t || MessagesModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: MessagesModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormsModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_4__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_5__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_7__.MatInputModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_8__.MatFormFieldModule, _angular_material_list__WEBPACK_IMPORTED_MODULE_9__.MatListModule, _angular_material_badge__WEBPACK_IMPORTED_MODULE_10__.MatBadgeModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_11__.MatChipsModule, _angular_material_divider__WEBPACK_IMPORTED_MODULE_12__.MatDividerModule, _angular_router__WEBPACK_IMPORTED_MODULE_13__.RouterModule.forChild([{
        path: "",
        component: _messages_component__WEBPACK_IMPORTED_MODULE_0__.MessagesComponent
      }])]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](MessagesModule, {
    declarations: [_messages_component__WEBPACK_IMPORTED_MODULE_0__.MessagesComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormsModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_4__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_5__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_7__.MatInputModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_8__.MatFormFieldModule, _angular_material_list__WEBPACK_IMPORTED_MODULE_9__.MatListModule, _angular_material_badge__WEBPACK_IMPORTED_MODULE_10__.MatBadgeModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_11__.MatChipsModule, _angular_material_divider__WEBPACK_IMPORTED_MODULE_12__.MatDividerModule, _angular_router__WEBPACK_IMPORTED_MODULE_13__.RouterModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_features_messages_messages_module_ts.js.map