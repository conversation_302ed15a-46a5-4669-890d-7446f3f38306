{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport class AuthInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(req, next) {\n    const token = this.authService.getToken();\n    if (token) {\n      const authReq = req.clone({\n        headers: req.headers.set(\"Authorization\", `Bearer ${token}`)\n      });\n      return next.handle(authReq);\n    }\n    return next.handle(req);\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthInterceptor", "constructor", "authService", "intercept", "req", "next", "token", "getToken", "authReq", "clone", "headers", "set", "handle", "i0", "ɵɵinject", "i1", "AuthService", "factory", "ɵfac"], "sources": ["C:\\e-learning\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpInterceptor, HttpRequest, HttpHandler } from \"@angular/common/http\"\nimport { AuthService } from \"../services/auth.service\"\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  constructor(private authService: AuthService) {}\n\n  intercept(req: HttpRequest<any>, next: HttpHandler) {\n    const token = this.authService.getToken()\n\n    if (token) {\n      const authReq = req.clone({\n        headers: req.headers.set(\"Authorization\", `Bearer ${token}`),\n      })\n      return next.handle(authReq)\n    }\n\n    return next.handle(req)\n  }\n}\n"], "mappings": ";;AAKA,OAAM,MAAOA,eAAe;EAC1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,MAAMC,KAAK,GAAG,IAAI,CAACJ,WAAW,CAACK,QAAQ,EAAE;IAEzC,IAAID,KAAK,EAAE;MACT,MAAME,OAAO,GAAGJ,GAAG,CAACK,KAAK,CAAC;QACxBC,OAAO,EAAEN,GAAG,CAACM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUL,KAAK,EAAE;OAC5D,CAAC;MACF,OAAOD,IAAI,CAACO,MAAM,CAACJ,OAAO,CAAC;;IAG7B,OAAOH,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;EACzB;;;uBAdWJ,eAAe,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAfhB,eAAe;MAAAiB,OAAA,EAAfjB,eAAe,CAAAkB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}