{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { FormsModule } from \"@angular/forms\"; // For ngModel in textarea\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatListModule } from \"@angular/material/list\";\nimport { MatBadgeModule } from \"@angular/material/badge\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatDividerModule } from \"@angular/material/divider\";\nimport { MessagesComponent } from \"./messages.component\";\nexport let MessagesModule = class MessagesModule {};\nMessagesModule = __decorate([NgModule({\n  declarations: [MessagesComponent],\n  imports: [CommonModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatListModule, MatBadgeModule, MatChipsModule, MatDividerModule, RouterModule.forChild([{\n    path: \"\",\n    component: MessagesComponent\n  }])]\n})], MessagesModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatListModule", "MatBadgeModule", "MatChipsModule", "MatDividerModule", "MessagesComponent", "MessagesModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component"], "sources": ["C:\\e-learning\\src\\app\\features\\messages\\messages.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // For ngModel in textarea\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatListModule } from \"@angular/material/list\"\nimport { MatBadgeModule } from \"@angular/material/badge\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatDividerModule } from \"@angular/material/divider\"\n\nimport { MessagesComponent } from \"./messages.component\"\n\n@NgModule({\n  declarations: [MessagesComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatForm<PERSON>ieldModule,\n    MatListModule,\n    MatBadgeModule,\n    MatChipsModule,\n    MatDividerModule,\n    RouterModule.forChild([{ path: \"\", component: MessagesComponent }]),\n  ],\n})\nexport class MessagesModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB,EAAC;AAE7C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,iBAAiB,QAAQ,sBAAsB;AAmBjD,WAAMC,cAAc,GAApB,MAAMA,cAAc,GAAG;AAAjBA,cAAc,GAAAC,UAAA,EAjB1Bf,QAAQ,CAAC;EACRgB,YAAY,EAAE,CAACH,iBAAiB,CAAC;EACjCI,OAAO,EAAE,CACPhB,YAAY,EACZE,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBV,YAAY,CAACgB,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEP;EAAiB,CAAE,CAAC,CAAC;CAEtE,CAAC,C,EACWC,cAAc,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}