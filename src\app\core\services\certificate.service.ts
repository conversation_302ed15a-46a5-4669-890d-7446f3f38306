import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"

@Injectable({
  providedIn: "root",
})
export class CertificateService {
  constructor(private http: HttpClient) {}

  // GET: Télécharger un certificat PDF
  telechargerCertificat(nomClient: string, prenomClient: string, titreCours: string, score: number): Observable<Blob> {
    const params = {
      nomClient,
      prenomClient,
      titreCours,
      score: score.toString(),
    }

    return this.http.get(`${environment.urlApi}certificats/telecharger`, {
      params,
      responseType: "blob",
    })
  }
}
