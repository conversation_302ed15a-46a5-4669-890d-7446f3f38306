import { Injectable } from "@angular/core"
import { HttpClient, HttpParams } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"

@Injectable({
  providedIn: "root",
})
export class CertificateService {
  private apiUrl = `${environment.urlApi}certificats`

  constructor(private http: HttpClient) {}

  // GET: Télécharger un certificat PDF (correspond à GET /api/certificats/telecharger)
  telechargerCertificat(nomClient: string, prenomClient: string, titreCours: string, score: number): Observable<Blob> {
    const params = new HttpParams()
      .set('nomClient', nomClient)
      .set('prenomClient', prenomClient)
      .set('titreCours', titreCours)
      .set('score', score.toString())

    return this.http.get(`${this.apiUrl}/telecharger`, {
      params,
      responseType: "blob",
    })
  }

  // Méthode utilitaire pour télécharger et sauvegarder le PDF
  downloadAndSaveCertificate(
    nomClient: string,
    prenomClient: string,
    titreCours: string,
    score: number,
    filename: string = 'certificat.pdf'
  ): void {
    this.telechargerCertificat(nomClient, prenomClient, titreCours, score).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        link.click()
        window.URL.revokeObjectURL(url)
      },
      error: (error) => {
        console.error('Erreur lors du téléchargement du certificat:', error)
      }
    })
  }
}
