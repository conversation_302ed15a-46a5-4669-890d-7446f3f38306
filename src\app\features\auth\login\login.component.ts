import { Component, OnInit } from "@angular/core"
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms"
import { Router } from "@angular/router"
import { MatSnackBar } from "@angular/material/snack-bar"
import { AuthService } from "../../../core/services/auth.service"

@Component({
  selector: "app-login",
  template: `
    <div class="auth-container">
      <div class="auth-wrapper">
        <!-- Header -->
        <div class="auth-header">
          <div class="logo">
            <mat-icon>school</mat-icon>
            <h1>Training Platform</h1>
          </div>
          <p>Plateforme de formation en ligne</p>
        </div>

        <!-- Login Form -->
        <mat-card class="auth-card">
          <mat-card-header>
            <mat-card-title>Connexion</mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>E-mail</mat-label>
                <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                  L'email est requis
                </mat-error>
                <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                  Format d'email invalide
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Mot de passe</mat-label>
                <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password">
                <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                </button>
                <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                  Le mot de passe est requis
                </mat-error>
              </mat-form-field>

              <div class="checkbox-container">
                <mat-checkbox formControlName="rememberMe">Se souvenir de moi</mat-checkbox>
              </div>

              <button mat-raised-button color="primary" type="submit" 
                      [disabled]="loginForm.invalid || isLoading" class="full-width submit-btn">
                <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
                <span *ngIf="!isLoading">Se connecter</span>
              </button>
            </form>

            <div class="auth-links">
              <p>
                Pas encore de compte ? 
                <a routerLink="/auth/register" class="link">S'inscrire</a>
              </p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [
    `
    .auth-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
    }

    .auth-wrapper {
      width: 100%;
      max-width: 400px;
    }

    .auth-header {
      text-align: center;
      margin-bottom: 2rem;
      color: white;
    }

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.5rem;
    }

    .logo mat-icon {
      font-size: 2.5rem;
      margin-right: 0.5rem;
    }

    .logo h1 {
      font-size: 2rem;
      margin: 0;
      font-weight: bold;
    }

    .auth-card {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .full-width {
      width: 100%;
      margin-bottom: 1rem;
    }

    .checkbox-container {
      margin-bottom: 1.5rem;
    }

    .submit-btn {
      height: 48px;
      font-size: 1.1rem;
    }

    .auth-links {
      text-align: center;
      margin-top: 1.5rem;
    }

    .link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }

    .link:hover {
      text-decoration: underline;
    }
  `,
  ],
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup
  hidePassword = true
  isLoading = false

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      email: ["", [Validators.required, Validators.email]],
      password: ["", Validators.required],
      rememberMe: [false],
    })
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true
      const { email, password } = this.loginForm.value

      this.authService.login({ Email: email, Password: password }).subscribe({
        next: (response) => {
          this.isLoading = false
          this.snackBar.open("Connexion réussie !", "Fermer", { duration: 3000 })
          this.router.navigate(["/dashboard"])
        },
        error: (error) => {
          this.isLoading = false
          this.snackBar.open("Erreur de connexion. Vérifiez vos identifiants.", "Fermer", { duration: 5000 })
        },
      })
    }
  }
}
