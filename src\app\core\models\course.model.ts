import { Formateur } from "./user.model"

// Correspond à la classe Cours en .NET
export interface Course {
  id: number
  titre: string
  description?: string
  prix: number
  estPayant?: boolean       // Correspond à EstPayant en .NET
  formateurId: number       // Correspond à FormateurId en .NET
  formateur?: Formateur     // Navigation property
  contenus?: Contenu[]      // Navigation property
  // Propriétés additionnelles pour le frontend
  duree?: number
  niveau?: "Débutant" | "Intermédiaire" | "Avancé"
  nombreEtudiants?: number
  note?: number
  estAchete?: boolean
  progression?: number
  estGratuit?: boolean      // Propriété calculée: !estPayant
}

// Correspond à la classe de base Contenu en .NET avec héritage
export interface Contenu {
  id: number
  titre: string
  description?: string
  typeContenu: "Quiz" | "Video" | "Resume"  // Discriminator pour l'héritage
  coursId: number                           // Correspond à CoursId en .NET
  // Propriétés communes pour tous les types de contenu
  estComplete?: boolean
  estDebloque?: boolean
  ordre?: number
  // Propriétés spécifiques selon le type (optionnelles dans la classe de base)
  duree?: number        // Pour Video
  seuilReussite?: number // Pour Quiz
  contenuTexte?: string  // Pour Resume
  url?: string          // Pour Video
  questions?: Question[] // Pour Quiz
}

export interface Quiz extends Contenu {
  description?: string
  questions: Question[]
  seuilReussite: number
  dureeEstimee?: number
}

export interface Video extends Contenu {
  url: string
  duree: number
}

export interface Resume extends Contenu {
  contenuTexte: string
}

export interface Question {
  id: number
  texte: string
  options: string[]
  bonneReponse: number
}
