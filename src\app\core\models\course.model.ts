import type { Formateur } from "./user.model"

export interface Course {
  id: number
  titre: string
  description: string
  prix: number
  duree: number
  niveau: "Débutant" | "Intermédiaire" | "Avancé"
  formateurId: number
  formateur: Formateur
  contenus: Contenu[]
  nombreEtudiants: number
  note: number
  estGratuit: boolean
  estAchete?: boolean
  progression?: number
}

export interface Contenu {
  id: number
  titre: string
  typeContenu: "Quiz" | "Video" | "Resume"
  coursId: number
  // Common properties for all content types
  estComplete?: boolean
  estDebloque?: boolean
  ordre?: number
  // Specific properties for each type (optional for base Contenu)
  duree?: number // For Video
  seuilReussite?: number // For Quiz
  contenuTexte?: string // For Resume
}

export interface Quiz extends Contenu {
  questions: Question[]
  seuilReussite: number
  dureeEstimee?: number
}

export interface Video extends Contenu {
  url: string
  duree: number
}

export interface Resume extends Contenu {
  contenuTexte: string
}

export interface Question {
  id: number
  texte: string
  options: string[]
  bonneReponse: number
}
