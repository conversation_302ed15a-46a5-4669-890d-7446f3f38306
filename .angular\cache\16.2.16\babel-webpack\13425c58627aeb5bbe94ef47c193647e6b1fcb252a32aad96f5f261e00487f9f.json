{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferCount(bufferSize, startBufferEvery = null) {\n  startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n  return operate((source, subscriber) => {\n    let buffers = [];\n    let count = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      let toEmit = null;\n      if (count++ % startBufferEvery === 0) {\n        buffers.push([]);\n      }\n      for (const buffer of buffers) {\n        buffer.push(value);\n        if (bufferSize <= buffer.length) {\n          toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n          toEmit.push(buffer);\n        }\n      }\n      if (toEmit) {\n        for (const buffer of toEmit) {\n          arrRemove(buffers, buffer);\n          subscriber.next(buffer);\n        }\n      }\n    }, () => {\n      for (const buffer of buffers) {\n        subscriber.next(buffer);\n      }\n      subscriber.complete();\n    }, undefined, () => {\n      buffers = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "bufferCount", "bufferSize", "startBufferEvery", "source", "subscriber", "buffers", "count", "subscribe", "value", "toEmit", "push", "buffer", "length", "next", "complete", "undefined"], "sources": ["C:/e-learning/node_modules/rxjs/dist/esm/internal/operators/bufferCount.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferCount(bufferSize, startBufferEvery = null) {\n    startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n    return operate((source, subscriber) => {\n        let buffers = [];\n        let count = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            let toEmit = null;\n            if (count++ % startBufferEvery === 0) {\n                buffers.push([]);\n            }\n            for (const buffer of buffers) {\n                buffer.push(value);\n                if (bufferSize <= buffer.length) {\n                    toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n                    toEmit.push(buffer);\n                }\n            }\n            if (toEmit) {\n                for (const buffer of toEmit) {\n                    arrRemove(buffers, buffer);\n                    subscriber.next(buffer);\n                }\n            }\n        }, () => {\n            for (const buffer of buffers) {\n                subscriber.next(buffer);\n            }\n            subscriber.complete();\n        }, undefined, () => {\n            buffers = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,OAAO,SAASC,WAAWA,CAACC,UAAU,EAAEC,gBAAgB,GAAG,IAAI,EAAE;EAC7DA,gBAAgB,GAAGA,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGD,UAAU;EAC3G,OAAOJ,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,KAAK,GAAG,CAAC;IACbH,MAAM,CAACI,SAAS,CAACT,wBAAwB,CAACM,UAAU,EAAGI,KAAK,IAAK;MAC7D,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIH,KAAK,EAAE,GAAGJ,gBAAgB,KAAK,CAAC,EAAE;QAClCG,OAAO,CAACK,IAAI,CAAC,EAAE,CAAC;MACpB;MACA,KAAK,MAAMC,MAAM,IAAIN,OAAO,EAAE;QAC1BM,MAAM,CAACD,IAAI,CAACF,KAAK,CAAC;QAClB,IAAIP,UAAU,IAAIU,MAAM,CAACC,MAAM,EAAE;UAC7BH,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,EAAE;UAC3DA,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC;QACvB;MACJ;MACA,IAAIF,MAAM,EAAE;QACR,KAAK,MAAME,MAAM,IAAIF,MAAM,EAAE;UACzBV,SAAS,CAACM,OAAO,EAAEM,MAAM,CAAC;UAC1BP,UAAU,CAACS,IAAI,CAACF,MAAM,CAAC;QAC3B;MACJ;IACJ,CAAC,EAAE,MAAM;MACL,KAAK,MAAMA,MAAM,IAAIN,OAAO,EAAE;QAC1BD,UAAU,CAACS,IAAI,CAACF,MAAM,CAAC;MAC3B;MACAP,UAAU,CAACU,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEC,SAAS,EAAE,MAAM;MAChBV,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}