{"version": 3, "file": "src_app_features_certificates_certificates_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AAC6D;AAEE;;;AAKzD,MAAOE,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,kEAAW,CAACK,MAAM,aAAa;EAEZ;EAEvC;EACAC,qBAAqBA,CAACC,SAAiB,EAAEC,YAAoB,EAAEC,UAAkB,EAAEC,KAAa;IAC9F,MAAMC,MAAM,GAAG,IAAIZ,4DAAU,EAAE,CAC5Ba,GAAG,CAAC,WAAW,EAAEL,SAAS,CAAC,CAC3BK,GAAG,CAAC,cAAc,EAAEJ,YAAY,CAAC,CACjCI,GAAG,CAAC,YAAY,EAAEH,UAAU,CAAC,CAC7BG,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC;IAEjC,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACV,MAAM,cAAc,EAAE;MACjDO,MAAM;MACNI,YAAY,EAAE;KACf,CAAC;EACJ;EAEA;EACAC,0BAA0BA,CACxBT,SAAiB,EACjBC,YAAoB,EACpBC,UAAkB,EAClBC,KAAa,EACbO,QAAA,GAAmB,gBAAgB;IAEnC,IAAI,CAACX,qBAAqB,CAACC,SAAS,EAAEC,YAAY,EAAEC,UAAU,EAAEC,KAAK,CAAC,CAACQ,SAAS,CAAC;MAC/EC,IAAI,EAAGC,IAAI,IAAI;QACb,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QACfI,IAAI,CAACI,QAAQ,GAAGZ,QAAQ;QACxBQ,IAAI,CAACK,KAAK,EAAE;QACZR,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;MACjC,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACtE;KACD,CAAC;EACJ;;;uBAxCW/B,kBAAkB,EAAAiC,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAlBnC,kBAAkB;MAAAqC,OAAA,EAAlBrC,kBAAkB,CAAAsC,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICUdN,4DAAA,aAA2F;IAG3EA,oDAAA,mBAAY;IAAAA,0DAAA,EAAW;IACjCA,4DAAA,SAAI;IAAAA,oDAAA,kCAA2B;IAAAA,0DAAA,EAAK;IACpCA,4DAAA,QAAG;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAI;;;;IAApCA,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAY,MAAA,CAAAC,wBAAA,GAAgC;;;;;;IAOrCb,4DAAA,kBAAqE;IAGrDA,oDAAA,mBAAY;IAAAA,0DAAA,EAAW;IAEnCA,4DAAA,UAAK;IACaA,oDAAA,GAAuB;IAAAA,0DAAA,EAAiB;IACxDA,4DAAA,wBAAmB;IACjBA,oDAAA,GACF;IAAAA,0DAAA,EAAoB;IAIxBA,4DAAA,4BAA8C;IAEhCA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,YAAM;IAAAA,oDAAA,uBAAU;IAAAA,4DAAA,cAAQ;IAAAA,oDAAA,IAAgD;IAAAA,0DAAA,EAAS;IAEnFA,4DAAA,eAAyB;IACbA,oDAAA,sBAAc;IAAAA,0DAAA,EAAW;IACnCA,4DAAA,YAAM;IAAAA,oDAAA,IAAqF;IAAAA,0DAAA,EAAO;IAEpGA,4DAAA,eAAyB;IACbA,oDAAA,YAAI;IAAAA,0DAAA,EAAW;IACzBA,4DAAA,YAAM;IAAAA,oDAAA,eACJ;IAAAA,4DAAA,kBAAqF;IACnFA,oDAAA,IACF;IAAAA,0DAAA,EAAS;IACTA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAETA,4DAAA,eAAyB;IACjBA,oDAAA,+BACJ;IAAAA,4DAAA,wBAAkB;IACqBA,oDAAA,IAAsB;IAAAA,0DAAA,EAAW;IAK5EA,4DAAA,eAA0B;IACmBA,wDAAA,mBAAAe,iFAAA;MAAA,MAAAC,WAAA,GAAAhB,2DAAA,CAAAkB,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAArB,2DAAA;MAAA,OAASA,yDAAA,CAAAqB,MAAA,CAAAG,kBAAA,CAAAL,OAAA,CAAAM,EAAA,CAA4B;IAAA,EAAC;IAC/EzB,4DAAA,gBAAU;IAAAA,oDAAA,kBAAU;IAAAA,0DAAA,EAAW;IAACA,oDAAA,qBAClC;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAA6E;IAApCA,wDAAA,mBAAA0B,iFAAA;MAAA,MAAAV,WAAA,GAAAhB,2DAAA,CAAAkB,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAO,MAAA,GAAA3B,2DAAA;MAAA,OAASA,yDAAA,CAAA2B,MAAA,CAAAC,mBAAA,CAAAT,OAAA,CAAyB;IAAA,EAAC;IAC1EnB,4DAAA,gBAAU;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAW;IAACA,oDAAA,aAChC;IAAAA,0DAAA,EAAS;IAGXA,4DAAA,eAA8B;IAGdA,oDAAA,oBAAY;IAAAA,0DAAA,EAAW;IAACA,oDAAA,gCACpC;IAAAA,0DAAA,EAAW;;;;;IA9CGA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAmB,OAAA,CAAAU,KAAA,kBAAAV,OAAA,CAAAU,KAAA,CAAAC,KAAA,CAAuB;IAErC9B,uDAAA,GACF;IADEA,gEAAA,UAAAmB,OAAA,CAAAU,KAAA,kBAAAV,OAAA,CAAAU,KAAA,CAAAG,SAAA,kBAAAb,OAAA,CAAAU,KAAA,CAAAG,SAAA,CAAAC,MAAA,OAAAd,OAAA,CAAAU,KAAA,kBAAAV,OAAA,CAAAU,KAAA,CAAAG,SAAA,kBAAAb,OAAA,CAAAU,KAAA,CAAAG,SAAA,CAAAE,GAAA,MACF;IAOwBlC,uDAAA,GAAgD;IAAhDA,gEAAA,KAAAmB,OAAA,CAAAgB,MAAA,kBAAAhB,OAAA,CAAAgB,MAAA,CAAAF,MAAA,OAAAd,OAAA,CAAAgB,MAAA,kBAAAhB,OAAA,CAAAgB,MAAA,CAAAD,GAAA,KAAgD;IAIlElC,uDAAA,GAAqF;IAArFA,gEAAA,uBAAAmB,OAAA,CAAAkB,cAAA,GAAAC,MAAA,CAAAC,UAAA,CAAApB,OAAA,CAAAkB,cAAA,cAAqF;IAKjFrC,uDAAA,GAA4E;IAA5EA,wDAAA,YAAAsC,MAAA,CAAAG,kBAAA,CAAAtB,OAAA,CAAAuB,SAAA,OAAAvB,OAAA,CAAAwB,aAAA,OAA4E;IAClF3C,uDAAA,GACF;IADEA,gEAAA,MAAAmB,OAAA,CAAAuB,SAAA,OACF;IACA1C,uDAAA,GACF;IADEA,gEAAA,cAAAmB,OAAA,CAAAwB,aAAA,QACF;IAKyC3C,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAmB,OAAA,CAAAyB,WAAA,CAAsB;;;;;IA0BvE5C,4DAAA,mBAAwG;IAEpFA,oDAAA,mCAA4B;IAAAA,0DAAA,EAAiB;IAE/DA,4DAAA,2BAAqC;IAEJA,oDAAA,GAAyB;IAAAA,0DAAA,EAAM;IAC5DA,4DAAA,cAAwB;IAAAA,oDAAA,sBAAU;IAAAA,0DAAA,EAAM;IAE1CA,4DAAA,eAAuB;IACSA,oDAAA,IAAqB;IAAAA,0DAAA,EAAM;IACzDA,4DAAA,eAAwB;IAAAA,oDAAA,mBAAW;IAAAA,0DAAA,EAAM;IAE3CA,4DAAA,eAAuB;IACUA,oDAAA,IAA6B;IAAAA,0DAAA,EAAM;IAClEA,4DAAA,eAAwB;IAAAA,oDAAA,iCAAoB;IAAAA,0DAAA,EAAM;IAEpDA,4DAAA,eAAuB;IACUA,oDAAA,IAA2B;IAAAA,0DAAA,EAAM;IAChEA,4DAAA,eAAwB;IAAAA,oDAAA,qCAAmB;IAAAA,0DAAA,EAAM;;;;IAbpBA,uDAAA,GAAyB;IAAzBA,+DAAA,CAAA6C,MAAA,CAAAC,YAAA,CAAAC,MAAA,CAAyB;IAIxB/C,uDAAA,GAAqB;IAArBA,gEAAA,KAAA6C,MAAA,CAAAG,YAAA,QAAqB;IAIpBhD,uDAAA,GAA6B;IAA7BA,+DAAA,CAAA6C,MAAA,CAAAI,qBAAA,GAA6B;IAI7BjD,uDAAA,GAA2B;IAA3BA,+DAAA,CAAA6C,MAAA,CAAAK,mBAAA,GAA2B;;;;;IA/EhElD,4DAAA,aAA+B;IAC7BA,wDAAA,IAAAoD,uDAAA,wBAwDW;IACbpD,0DAAA,EAAM;IAGNA,wDAAA,IAAAqD,uDAAA,uBAsBW;;;;IAlFkBrD,uDAAA,GAAe;IAAfA,wDAAA,YAAAsD,MAAA,CAAAR,YAAA,CAAe;IA4DjC9C,uDAAA,GAAwE;IAAxEA,wDAAA,UAAAsD,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,IAAA,sBAAAF,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,IAAA,cAAwE;;;AAoQrF,MAAOC,qBAAqB;EAIhCzF,YACU0F,kBAAsC,EACtCC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IANlB,KAAAd,YAAY,GAAiB,EAAE;IAC/B,KAAAS,WAAW,GAAgB,IAAI;EAM5B;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACF,WAAW,CAACG,YAAY,CAAC9E,SAAS,CAAE+E,IAAI,IAAI;MAC/C,IAAI,CAACR,WAAW,GAAGQ,IAAI;MACvB,IAAI,IAAI,CAACR,WAAW,EAAE;QACpB,IAAI,CAACS,gBAAgB,EAAE;;IAE3B,CAAC,CAAC;EACJ;EAEAA,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAClB,YAAY,GAAG,CAClB;MACErB,EAAE,EAAE,CAAC;MACLpD,SAAS,EAAE,eAAe;MAC1B4F,aAAa,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;MAC/CC,OAAO,EAAE,CAAC;MACVtC,KAAK,EAAE;QACLJ,EAAE,EAAE,CAAC;QACLK,KAAK,EAAE,oBAAoB;QAC3BE,SAAS,EAAE;UAAEP,EAAE,EAAE,CAAC;UAAES,GAAG,EAAE,QAAQ;UAAED,MAAM,EAAE;QAAM;OAClD;MACDmC,QAAQ,EAAE,CAAC;MACXjC,MAAM,EAAE;QAAEV,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,QAAQ;QAAED,MAAM,EAAE;MAAQ,CAAE;MAClDoC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE;QAAE7C,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,OAAO;QAAED,MAAM,EAAE;MAAQ,CAAE;MAChDI,cAAc,EAAE,IAAI6B,IAAI,CAAC,sBAAsB,CAAC;MAChDxB,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;KACd,EACD;MACEnB,EAAE,EAAE,CAAC;MACLpD,SAAS,EAAE,eAAe;MAC1B4F,aAAa,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;MAC/CC,OAAO,EAAE,CAAC;MACVtC,KAAK,EAAE;QACLJ,EAAE,EAAE,CAAC;QACLK,KAAK,EAAE,mBAAmB;QAC1BE,SAAS,EAAE;UAAEP,EAAE,EAAE,CAAC;UAAES,GAAG,EAAE,SAAS;UAAED,MAAM,EAAE;QAAQ;OACrD;MACDmC,QAAQ,EAAE,CAAC;MACXjC,MAAM,EAAE;QAAEV,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,QAAQ;QAAED,MAAM,EAAE;MAAQ,CAAE;MAClDoC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE;QAAE7C,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,OAAO;QAAED,MAAM,EAAE;MAAQ,CAAE;MAChDI,cAAc,EAAE,IAAI6B,IAAI,CAAC,sBAAsB,CAAC;MAChDxB,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;KACd,CACF;IAED;IACA;;;EAGF;;EAEA2B,YAAYA,CAAA;IACV,QAAQ,IAAI,CAAChB,WAAW,EAAEC,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,iBAAiB;MAC1B,KAAK,WAAW;QACd,OAAO,kBAAkB;MAC3B,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC;QACE,OAAO,aAAa;;EAE1B;EAEAgB,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACjB,WAAW,EAAEC,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,uCAAuC;MAChD,KAAK,WAAW;QACd,OAAO,iCAAiC;MAC1C,KAAK,OAAO;QACV,OAAO,gDAAgD;MACzD;QACE,OAAO,4CAA4C;;EAEzD;EAEA3C,wBAAwBA,CAAA;IACtB,QAAQ,IAAI,CAAC0C,WAAW,EAAEC,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,sEAAsE;MAC/E,KAAK,WAAW;QACd,OAAO,wDAAwD;MACjE,KAAK,OAAO;QACV,OAAO,2DAA2D;MACpE;QACE,OAAO,mDAAmD;;EAEhE;EAEAjB,UAAUA,CAACkC,IAAU;IACnB,OAAO,IAAIP,IAAI,CAACO,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEApC,kBAAkBA,CAACjE,KAAa,EAAEsG,KAAa;IAC7C,IAAItG,KAAK,IAAIsG,KAAK,GAAG,EAAE,EAAE,OAAO,aAAa;IAC7C,IAAItG,KAAK,IAAIsG,KAAK,GAAG,EAAE,EAAE,OAAO,YAAY;IAC5C,IAAItG,KAAK,IAAIsG,KAAK,EAAE,OAAO,cAAc;IACzC,OAAO,WAAW;EACpB;EAEAlD,mBAAmBA,CAACmD,IAAgB;IAClC,IAAI,CAACA,IAAI,CAAClD,KAAK,IAAI,CAACkD,IAAI,CAAC5C,MAAM,EAAE;MAC/B,IAAI,CAACyB,QAAQ,CAACoB,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC3F;;IAGF,IAAI,CAACvB,kBAAkB,CACpBtF,qBAAqB,CAAC2G,IAAI,CAAC5C,MAAO,CAACD,GAAG,EAAE6C,IAAI,CAAC5C,MAAO,CAACF,MAAM,EAAE8C,IAAI,CAAClD,KAAM,CAACC,KAAK,EAAEiD,IAAI,CAACrC,SAAS,IAAI,CAAC,CAAC,CACpG1D,SAAS,CAAC;MACTC,IAAI,EAAGC,IAAI,IAAI;QACb,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMgG,CAAC,GAAG1F,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCyF,CAAC,CAACxF,IAAI,GAAGP,GAAG;QACZ+F,CAAC,CAACvF,QAAQ,GAAG,cAAcoF,IAAI,CAACnC,WAAW,MAAM;QACjDpD,QAAQ,CAAC2F,IAAI,CAACC,WAAW,CAACF,CAAC,CAAC;QAC5BA,CAAC,CAACtF,KAAK,EAAE;QACTJ,QAAQ,CAAC2F,IAAI,CAACE,WAAW,CAACH,CAAC,CAAC;QAC5B9F,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;QAC/B,IAAI,CAACyE,QAAQ,CAACoB,IAAI,CAAC,yBAAyB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC7E,CAAC;MACDnF,KAAK,EAAGwF,GAAG,IAAI;QACb,IAAI,CAAC1B,QAAQ,CAACoB,IAAI,CAAC,8CAA8C,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAChGlF,OAAO,CAACD,KAAK,CAACwF,GAAG,CAAC;MACpB;KACD,CAAC;EACN;EAEA9D,kBAAkBA,CAAC+D,aAAqB;IACtC,IAAI,CAAC3B,QAAQ,CAACoB,IAAI,CAAC,yDAAyD,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAC3G;IACA;EACF;;EAEAjC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACF,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC5C,MAAMyC,UAAU,GAAG,IAAI,CAAC1C,YAAY,CAAC2C,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,IAAIX,IAAI,CAACrC,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1F,OAAOiD,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAG,IAAI,CAAC1C,YAAY,CAACC,MAAM,CAAC;EAC1D;EAEAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACH,YAAY,CAAC+C,MAAM,CAAEd,IAAI,IAAK,CAACA,IAAI,CAACrC,SAAS,IAAI,CAAC,KAAK,CAACqC,IAAI,CAACpC,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,CAACI,MAAM;EAC3G;EAEAG,mBAAmBA,CAAA;IACjB,MAAM4C,eAAe,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACjD,YAAY,CAACkD,GAAG,CAAEjB,IAAI,IAAKA,IAAI,CAACX,QAAQ,CAAC,CAAC;IAC/E,OAAO0B,eAAe,CAACG,IAAI;EAC7B;;;uBAxKWxC,qBAAqB,EAAAzD,+DAAA,CAAAE,kFAAA,GAAAF,+DAAA,CAAAmG,oEAAA,GAAAnG,+DAAA,CAAAqG,oEAAA;IAAA;EAAA;;;YAArB5C,qBAAqB;MAAA8C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlV9B7G,4DAAA,aAAoC;UAE5BA,oDAAA,GAAoB;UAAAA,0DAAA,EAAK;UAC7BA,4DAAA,QAAG;UAAAA,oDAAA,GAAuB;UAAAA,0DAAA,EAAI;UAGhCA,wDAAA,IAAA+G,oCAAA,iBAQM;UAEN/G,wDAAA,IAAAgH,4CAAA,gCAAAhH,oEAAA,CAqFc;UAChBA,0DAAA,EAAM;;;;UApGEA,uDAAA,GAAoB;UAApBA,+DAAA,CAAA8G,GAAA,CAAAvC,YAAA,GAAoB;UACrBvE,uDAAA,GAAuB;UAAvBA,+DAAA,CAAA8G,GAAA,CAAAtC,eAAA,GAAuB;UAGtBxE,uDAAA,GAAiC;UAAjCA,wDAAA,SAAA8G,GAAA,CAAAhE,YAAA,CAAAC,MAAA,OAAiC,aAAAmE,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfC;AACA;AAE9C;AACsD;AACI;AACJ;AACE;AAEQ;;;AAa1D,MAAOO,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAR3BN,yDAAY,EACZE,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,mEAAc,EACdJ,yDAAY,CAACM,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEnE,0EAAqBA;MAAA,CAAE,CAAC,CAAC;IAAA;EAAA;;;sHAG9DgE,kBAAkB;IAAAI,YAAA,GAVdpE,0EAAqB;IAAAqE,OAAA,GAElCX,yDAAY,EACZE,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,mEAAc,EAAAtH,yDAAA;EAAA;AAAA,K", "sources": ["./src/app/core/services/certificate.service.ts", "./src/app/features/certificates/certificates.component.ts", "./src/app/features/certificates/certificates.module.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient, HttpParams } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class CertificateService {\n  private apiUrl = `${environment.urlApi}certificats`\n\n  constructor(private http: HttpClient) {}\n\n  // GET: Télécharger un certificat PDF (correspond à GET /api/certificats/telecharger)\n  telechargerCertificat(nomClient: string, prenomClient: string, titreCours: string, score: number): Observable<Blob> {\n    const params = new HttpParams()\n      .set('nomClient', nomClient)\n      .set('prenomClient', prenomClient)\n      .set('titreCours', titreCours)\n      .set('score', score.toString())\n\n    return this.http.get(`${this.apiUrl}/telecharger`, {\n      params,\n      responseType: \"blob\",\n    })\n  }\n\n  // Méthode utilitaire pour télécharger et sauvegarder le PDF\n  downloadAndSaveCertificate(\n    nomClient: string,\n    prenomClient: string,\n    titreCours: string,\n    score: number,\n    filename: string = 'certificat.pdf'\n  ): void {\n    this.telechargerCertificat(nomClient, prenomClient, titreCours, score).subscribe({\n      next: (blob) => {\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n        link.download = filename\n        link.click()\n        window.URL.revokeObjectURL(url)\n      },\n      error: (error) => {\n        console.error('Erreur lors du téléchargement du certificat:', error)\n      }\n    })\n  }\n}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { CertificateService } from \"../../core/services/certificate.service\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { Certificat } from \"../../core/models/certificate.model\"\nimport { User } from \"../../core/models/user.model\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\n\n@Component({\n  selector: \"app-certificates\",\n  template: `\n    <div class=\"certificates-container\">\n      <div class=\"header-section\">\n        <h1>{{ getPageTitle() }}</h1>\n        <p>{{ getPageSubtitle() }}</p>\n      </div>\n\n      <div *ngIf=\"certificates.length === 0; else certificatesList\" class=\"no-certificates-card\">\n        <mat-card>\n          <mat-card-content class=\"no-certificates-content\">\n            <mat-icon>emoji_events</mat-icon>\n            <h3>Aucun certificat disponible</h3>\n            <p>{{ getNoCertificatesMessage() }}</p>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #certificatesList>\n        <div class=\"certificates-grid\">\n          <mat-card *ngFor=\"let cert of certificates\" class=\"certificate-card\">\n            <mat-card-header class=\"certificate-header\">\n              <div class=\"icon-wrapper\">\n                <mat-icon>emoji_events</mat-icon>\n              </div>\n              <div>\n                <mat-card-title>{{ cert.cours?.titre }}</mat-card-title>\n                <mat-card-subtitle>\n                  Par {{ cert.cours?.formateur?.prenom }} {{ cert.cours?.formateur?.nom }}\n                </mat-card-subtitle>\n              </div>\n            </mat-card-header>\n\n            <mat-card-content class=\"certificate-details\">\n              <div class=\"detail-item\">\n                <mat-icon>person</mat-icon>\n                <span>Étudiant: <strong>{{ cert.client?.prenom }} {{ cert.client?.nom }}</strong></span>\n              </div>\n              <div class=\"detail-item\">\n                <mat-icon>calendar_today</mat-icon>\n                <span>Date d'obtention: {{ cert.dateGeneration ? formatDate(cert.dateGeneration) : 'N/A' }}</span>\n              </div>\n              <div class=\"detail-item\">\n                <mat-icon>book</mat-icon>\n                <span>Score: \n                  <strong [ngClass]=\"getScoreColorClass(cert.scoreQuiz || 0, cert.seuilReussite || 0)\">\n                    {{ cert.scoreQuiz }}%\n                  </strong>\n                  (seuil: {{ cert.seuilReussite }}%)\n                </span>\n              </div>\n              <div class=\"detail-item\">\n                <span>N° de série: \n                  <mat-chip-listbox>\n                    <mat-chip class=\"serial-number-chip\">{{ cert.numeroSerie }}</mat-chip>\n                  </mat-chip-listbox>\n                </span>\n              </div>\n\n              <div class=\"card-actions\">\n                <button mat-stroked-button color=\"primary\" (click)=\"previewCertificate(cert.id!)\">\n                  <mat-icon>visibility</mat-icon> Aperçu\n                </button>\n                <button mat-raised-button color=\"accent\" (click)=\"downloadCertificate(cert)\">\n                  <mat-icon>download</mat-icon> PDF\n                </button>\n              </div>\n\n              <div class=\"validation-badge\">\n                <mat-chip-listbox>\n                  <mat-chip class=\"validated-chip\">\n                    <mat-icon>check_circle</mat-icon> Certificat Validé\n                  </mat-chip>\n                </mat-chip-listbox>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Statistics for Formateurs and Admins -->\n        <mat-card *ngIf=\"currentUser?.role === 'Formateur' || currentUser?.role === 'Admin'\" class=\"stats-card\">\n          <mat-card-header>\n            <mat-card-title>Statistiques des Certificats</mat-card-title>\n          </mat-card-header>\n          <mat-card-content class=\"stats-grid\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value blue\">{{ certificates.length }}</div>\n              <div class=\"stat-label\">Total émis</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-value green\">{{ averageScore() }}%</div>\n              <div class=\"stat-label\">Score moyen</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-value yellow\">{{ excellentResultsCount() }}</div>\n              <div class=\"stat-label\">Excellents résultats</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-value purple\">{{ uniqueStudentsCount() }}</div>\n              <div class=\"stat-label\">Étudiants certifiés</div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </ng-template>\n    </div>\n  `,\n  styles: [\n    `\n    .certificates-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n    }\n\n    .header-section {\n      margin-bottom: 2rem;\n      text-align: center;\n    }\n\n    .header-section h1 {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 0.5rem;\n    }\n\n    .header-section p {\n      font-size: 1.1rem;\n      color: #666;\n    }\n\n    .no-certificates-card {\n      max-width: 600px;\n      margin: 0 auto;\n      text-align: center;\n      padding: 2rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .no-certificates-content mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #aaa;\n    }\n\n    .no-certificates-content h3 {\n      font-size: 1.5rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    .no-certificates-content p {\n      color: #777;\n    }\n\n    .certificates-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n      gap: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .certificate-card {\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\n    }\n\n    .certificate-card:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n    }\n\n    .certificate-header {\n      display: flex;\n      align-items: center;\n      padding-bottom: 0.5rem;\n    }\n\n    .icon-wrapper {\n      width: 56px;\n      height: 56px;\n      border-radius: 50%;\n      background-color: #fff3e0; /* Light orange */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 1rem;\n      flex-shrink: 0;\n    }\n\n    .icon-wrapper mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n      color: #ffb300; /* Orange */\n    }\n\n    .certificate-header mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .certificate-header mat-card-subtitle {\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    .certificate-details {\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .detail-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 0.8rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .detail-item mat-icon {\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n      margin-right: 0.8rem;\n      color: #999;\n    }\n\n    .serial-number-chip {\n      font-family: 'monospace';\n      font-size: 0.8rem;\n      padding: 0.2rem 0.6rem;\n      height: auto;\n      background-color: #f0f0f0;\n      color: #555;\n    }\n\n    .score-green { color: #4caf50; }\n    .score-blue { color: #2196f3; }\n    .score-yellow { color: #ffc107; }\n    .score-red { color: #f44336; }\n\n    .card-actions {\n      display: flex;\n      gap: 0.8rem;\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .card-actions button {\n      flex: 1;\n      font-size: 0.9rem;\n      padding: 0.6rem 1rem;\n    }\n\n    .card-actions button mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.3rem;\n    }\n\n    .validation-badge {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .validated-chip {\n      background-color: #e8f5e9; /* Light green */\n      color: #388e3c; /* Dark green */\n      font-size: 0.9rem;\n      padding: 0.4rem 0.8rem;\n      height: auto;\n    }\n\n    .validated-chip mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.3rem;\n    }\n\n    .stats-card {\n      margin-top: 2rem;\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .stats-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      gap: 1.5rem;\n      text-align: center;\n    }\n\n    .stat-item {\n      padding: 1rem;\n      background-color: #f9f9f9;\n      border-radius: 8px;\n      border: 1px solid #eee;\n    }\n\n    .stat-value {\n      font-size: 2rem;\n      font-weight: bold;\n      margin-bottom: 0.3rem;\n    }\n\n    .stat-value.blue { color: #2196f3; }\n    .stat-value.green { color: #4caf50; }\n    .stat-value.yellow { color: #ffc107; }\n    .stat-value.purple { color: #9c27b0; }\n\n    .stat-label {\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .certificates-grid {\n        grid-template-columns: 1fr;\n      }\n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `,\n  ],\n})\nexport class CertificatesComponent implements OnInit {\n  certificates: Certificat[] = []\n  currentUser: User | null = null\n\n  constructor(\n    private certificateService: CertificateService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (this.currentUser) {\n        this.loadCertificates()\n      }\n    })\n  }\n\n  loadCertificates(): void {\n    // Mock data for demonstration\n    this.certificates = [\n      {\n        id: 1,\n        nomClient: \"Pierre Martin\",\n        dateObtention: new Date(\"2024-01-15T14:30:00Z\"),\n        coursId: 1,\n        cours: {\n          id: 1,\n          titre: \"React Fundamentals\",\n          formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\" },\n        },\n        clientId: 1,\n        client: { id: 1, nom: \"Martin\", prenom: \"Pierre\" },\n        adminId: 1,\n        admin: { id: 1, nom: \"Admin\", prenom: \"System\" },\n        dateGeneration: new Date(\"2024-01-15T14:30:00Z\"),\n        scoreQuiz: 85,\n        seuilReussite: 70,\n        numeroSerie: \"CERT-2024-001\",\n      },\n      {\n        id: 2,\n        nomClient: \"Pierre Martin\",\n        dateObtention: new Date(\"2024-01-10T16:45:00Z\"),\n        coursId: 2,\n        cours: {\n          id: 2,\n          titre: \"JavaScript Avancé\",\n          formateur: { id: 2, nom: \"Bernard\", prenom: \"Sophie\" },\n        },\n        clientId: 1,\n        client: { id: 1, nom: \"Martin\", prenom: \"Pierre\" },\n        adminId: 1,\n        admin: { id: 1, nom: \"Admin\", prenom: \"System\" },\n        dateGeneration: new Date(\"2024-01-10T16:45:00Z\"),\n        scoreQuiz: 92,\n        seuilReussite: 75,\n        numeroSerie: \"CERT-2024-002\",\n      },\n    ]\n\n    // Uncomment to fetch from API if you have an endpoint for listing certificates\n    /*\n    // Example: this.certificateService.getCertificatesForUser(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  getPageTitle(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Mes Certificats\"\n      case \"Formateur\":\n        return \"Certificats Émis\"\n      case \"Admin\":\n        return \"Gestion des Certificats\"\n      default:\n        return \"Certificats\"\n    }\n  }\n\n  getPageSubtitle(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Vos certificats de réussite aux cours\"\n      case \"Formateur\":\n        return \"Certificats émis pour vos cours\"\n      case \"Admin\":\n        return \"Tous les certificats générés sur la plateforme\"\n      default:\n        return \"Consultez les certificats de la plateforme\"\n    }\n  }\n\n  getNoCertificatesMessage(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Terminez un cours avec succès pour obtenir votre premier certificat.\"\n      case \"Formateur\":\n        return \"Aucun certificat n'a encore été généré pour vos cours.\"\n      case \"Admin\":\n        return \"Aucun certificat n'a encore été généré sur la plateforme.\"\n      default:\n        return \"Aucun certificat n'est disponible pour le moment.\"\n    }\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString(\"fr-FR\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"2-digit\",\n    })\n  }\n\n  getScoreColorClass(score: number, seuil: number): string {\n    if (score >= seuil + 20) return \"score-green\"\n    if (score >= seuil + 10) return \"score-blue\"\n    if (score >= seuil) return \"score-yellow\"\n    return \"score-red\"\n  }\n\n  downloadCertificate(cert: Certificat): void {\n    if (!cert.cours || !cert.client) {\n      this.snackBar.open(\"Informations du certificat incomplètes.\", \"Fermer\", { duration: 3000 })\n      return\n    }\n\n    this.certificateService\n      .telechargerCertificat(cert.client!.nom, cert.client!.prenom, cert.cours!.titre, cert.scoreQuiz || 0)\n      .subscribe({\n        next: (blob) => {\n          const url = window.URL.createObjectURL(blob)\n          const a = document.createElement(\"a\")\n          a.href = url\n          a.download = `certificat-${cert.numeroSerie}.pdf`\n          document.body.appendChild(a)\n          a.click()\n          document.body.removeChild(a)\n          window.URL.revokeObjectURL(url)\n          this.snackBar.open(\"Certificat téléchargé !\", \"Fermer\", { duration: 3000 })\n        },\n        error: (err) => {\n          this.snackBar.open(\"Erreur lors du téléchargement du certificat.\", \"Fermer\", { duration: 3000 })\n          console.error(err)\n        },\n      })\n  }\n\n  previewCertificate(certificateId: number): void {\n    this.snackBar.open(\"Fonctionnalité d'aperçu non implémentée pour le moment.\", \"Fermer\", { duration: 3000 })\n    // You would typically open a new tab/window with a route that renders the PDF\n    // Example: window.open(`/certificates/${certificateId}/preview`, '_blank');\n  }\n\n  averageScore(): number {\n    if (this.certificates.length === 0) return 0\n    const totalScore = this.certificates.reduce((sum, cert) => sum + (cert.scoreQuiz || 0), 0)\n    return Math.round(totalScore / this.certificates.length)\n  }\n\n  excellentResultsCount(): number {\n    return this.certificates.filter((cert) => (cert.scoreQuiz || 0) >= (cert.seuilReussite || 0) + 20).length\n  }\n\n  uniqueStudentsCount(): number {\n    const uniqueClientIds = new Set(this.certificates.map((cert) => cert.clientId))\n    return uniqueClientIds.size\n  }\n}\n", "import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatChipsModule } from \"@angular/material/chips\"\n\nimport { CertificatesComponent } from \"./certificates.component\"\n\n@NgModule({\n  declarations: [CertificatesComponent],\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    RouterModule.forChild([{ path: \"\", component: CertificatesComponent }]),\n  ],\n})\nexport class CertificatesModule {}\n"], "names": ["HttpParams", "environment", "CertificateService", "constructor", "http", "apiUrl", "urlApi", "telechargerCertificat", "nomClient", "prenomClient", "titreCours", "score", "params", "set", "toString", "get", "responseType", "downloadAndSaveCertificate", "filename", "subscribe", "next", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "error", "console", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getNoCertificatesMessage", "ɵɵlistener", "CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_38_listener", "restoredCtx", "ɵɵrestoreView", "_r7", "cert_r5", "$implicit", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "previewCertificate", "id", "CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_42_listener", "ctx_r8", "downloadCertificate", "cours", "titre", "ɵɵtextInterpolate2", "formateur", "prenom", "nom", "client", "ɵɵtextInterpolate1", "dateGeneration", "ctx_r3", "formatDate", "ɵɵproperty", "getScoreColorClass", "scoreQuiz", "<PERSON>uil<PERSON><PERSON><PERSON>", "numeroSerie", "ctx_r4", "certificates", "length", "averageScore", "excellentResultsCount", "uniqueStudentsCount", "ɵɵtemplate", "CertificatesComponent_ng_template_7_mat_card_1_Template", "CertificatesComponent_ng_template_7_mat_card_2_Template", "ctx_r2", "currentUser", "role", "CertificatesComponent", "certificateService", "authService", "snackBar", "ngOnInit", "currentUser$", "user", "loadCertificates", "dateObtention", "Date", "coursId", "clientId", "adminId", "admin", "getPageTitle", "getPageSubtitle", "date", "toLocaleDateString", "year", "month", "day", "<PERSON>uil", "cert", "open", "duration", "a", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "err", "certificateId", "totalScore", "reduce", "sum", "Math", "round", "filter", "uniqueClientIds", "Set", "map", "size", "ɵɵdirectiveInject", "i2", "AuthService", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "CertificatesComponent_Template", "rf", "ctx", "CertificatesComponent_div_6_Template", "CertificatesComponent_ng_template_7_Template", "ɵɵtemplateRefExtractor", "_r1", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "CertificatesModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}