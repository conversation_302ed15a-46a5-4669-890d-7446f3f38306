{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatCheckboxModule } from \"@angular/material/checkbox\";\nimport { MatSelectModule } from \"@angular/material/select\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { LoginComponent } from \"./login/login.component\";\nimport { RegisterComponent } from \"./register/register.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthModule {\n  static {\n    this.ɵfac = function AuthModule_Factory(t) {\n      return new (t || AuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatCheckboxModule, MatSelectModule, MatIconModule, MatProgressSpinnerModule, RouterModule.forChild([{\n        path: \"login\",\n        component: LoginComponent\n      }, {\n        path: \"register\",\n        component: RegisterComponent\n      }, {\n        path: \"\",\n        redirectTo: \"login\",\n        pathMatch: \"full\"\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginComponent, RegisterComponent],\n    imports: [CommonModule, ReactiveFormsModule, MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatCheckboxModule, MatSelectModule, MatIconModule, MatProgressSpinnerModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatCheckboxModule", "MatSelectModule", "MatIconModule", "MatProgressSpinnerModule", "LoginComponent", "RegisterComponent", "AuthModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "redirectTo", "pathMatch", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCheckboxModule } from \"@angular/material/checkbox\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { LoginComponent } from \"./login/login.component\"\nimport { RegisterComponent } from \"./register/register.component\"\n\n@NgModule({\n  declarations: [LoginComponent, RegisterComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatCheckboxModule,\n    MatSelectModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([\n      { path: \"login\", component: LoginComponent },\n      { path: \"register\", component: RegisterComponent },\n      { path: \"\", redirectTo: \"login\", pathMatch: \"full\" },\n    ]),\n  ],\n})\nexport class AuthModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;;;AAsBjE,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAjBnBb,YAAY,EACZE,mBAAmB,EACnBC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,wBAAwB,EACxBT,YAAY,CAACa,QAAQ,CAAC,CACpB;QAAEC,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAEL;MAAc,CAAE,EAC5C;QAAEI,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEJ;MAAiB,CAAE,EAClD;QAAEG,IAAI,EAAE,EAAE;QAAEE,UAAU,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAM,CAAE,CACrD,CAAC;IAAA;EAAA;;;2EAGOL,UAAU;IAAAM,YAAA,GAnBNR,cAAc,EAAEC,iBAAiB;IAAAQ,OAAA,GAE9CpB,YAAY,EACZE,mBAAmB,EACnBC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,wBAAwB,EAAAW,EAAA,CAAApB,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}