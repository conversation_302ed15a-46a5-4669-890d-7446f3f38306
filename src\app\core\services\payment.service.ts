import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Paiement, PaymentResponse } from "../models/payment.model"

@Injectable({
  providedIn: "root",
})
export class PaymentService {
  constructor(private http: HttpClient) {}

  // POST: Effectuer un paiement
  effectuerPaiement(paiement: Paiement): Observable<PaymentResponse> {
    return this.http.post<PaymentResponse>(`${environment.urlApi}paiement/effectuer`, paiement)
  }
}
