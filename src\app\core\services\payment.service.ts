import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Paiement, PaymentResponse } from "../models/payment.model"

@Injectable({
  providedIn: "root",
})
export class PaymentService {
  private apiUrl = `${environment.urlApi}paiement`

  constructor(private http: HttpClient) {}

  // POST: Effectuer un paiement (correspond à POST /api/paiement/effectuer)
  effectuerPaiement(paiement: Paiement): Observable<PaymentResponse> {
    return this.http.post<PaymentResponse>(`${this.apiUrl}/effectuer`, paiement)
  }
}
