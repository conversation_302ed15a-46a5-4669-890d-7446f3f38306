import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Contenu } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class ContenuService {
  constructor(private http: HttpClient) {}

  // GET: Tous les contenus
  getContenus(): Observable<Contenu[]> {
    return this.http.get<Contenu[]>(`${environment.urlApi}contenu`)
  }

  // GET: Un contenu par ID
  getContenu(id: number): Observable<Contenu> {
    return this.http.get<Contenu>(`${environment.urlApi}contenu/${id}`)
  }

  // GET: Contenus par cours
  getContenusByCours(coursId: number): Observable<Contenu[]> {
    return this.http.get<Contenu[]>(`${environment.urlApi}contenu/by-cours/${coursId}`)
  }

  // POST: Créer un contenu
  createContenu(contenu: Contenu): Observable<Contenu> {
    return this.http.post<Contenu>(`${environment.urlApi}contenu`, contenu)
  }

  // PUT: Modifier un contenu
  updateContenu(id: number, contenu: Contenu): Observable<any> {
    return this.http.put(`${environment.urlApi}contenu/${id}`, contenu)
  }

  // DELETE: Supprimer un contenu
  deleteContenu(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}contenu/${id}`)
  }
}
