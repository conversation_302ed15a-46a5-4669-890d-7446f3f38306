import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Contenu } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class ContenuService {
  private apiUrl = `${environment.urlApi}contenu`

  constructor(private http: HttpClient) {}

  // GET: Tous les contenus (correspond à GET /api/contenu)
  getContenus(): Observable<Contenu[]> {
    return this.http.get<Contenu[]>(this.apiUrl)
  }

  // GET: Un contenu par ID (correspond à GET /api/contenu/{id})
  getContenu(id: number): Observable<Contenu> {
    return this.http.get<Contenu>(`${this.apiUrl}/${id}`)
  }

  // GET: Contenus par cours (correspond à GET /api/contenu/by-cours/{coursId})
  getContenusByCours(coursId: number): Observable<Contenu[]> {
    return this.http.get<Contenu[]>(`${this.apiUrl}/by-cours/${coursId}`)
  }

  // POST: Créer un contenu (correspond à POST /api/contenu)
  createContenu(contenu: Contenu): Observable<Contenu> {
    return this.http.post<Contenu>(this.apiUrl, contenu)
  }

  // PUT: Modifier un contenu (correspond à PUT /api/contenu/{id})
  updateContenu(id: number, contenu: Contenu): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, contenu)
  }

  // DELETE: Supprimer un contenu (correspond à DELETE /api/contenu/{id})
  deleteContenu(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`)
  }
}
