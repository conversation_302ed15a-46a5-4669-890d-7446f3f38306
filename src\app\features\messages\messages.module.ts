import { NgModule } from "@angular/core"
import { CommonModule } from "@angular/common"
import { RouterModule } from "@angular/router"
import { FormsModule } from "@angular/forms" // For ngModel in textarea

// Angular Material
import { MatCardModule } from "@angular/material/card"
import { MatButtonModule } from "@angular/material/button"
import { MatIconModule } from "@angular/material/icon"
import { MatInputModule } from "@angular/material/input"
import { MatFormFieldModule } from "@angular/material/form-field"
import { MatListModule } from "@angular/material/list"
import { MatBadgeModule } from "@angular/material/badge"
import { MatChipsModule } from "@angular/material/chips"
import { MatDividerModule } from "@angular/material/divider"

import { MessagesComponent } from "./messages.component"

@NgModule({
  declarations: [MessagesComponent],
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatForm<PERSON>ieldModule,
    MatListModule,
    MatBadgeModule,
    MatChipsModule,
    MatDividerModule,
    RouterModule.forChild([{ path: "", component: MessagesComponent }]),
  ],
})
export class MessagesModule {}
