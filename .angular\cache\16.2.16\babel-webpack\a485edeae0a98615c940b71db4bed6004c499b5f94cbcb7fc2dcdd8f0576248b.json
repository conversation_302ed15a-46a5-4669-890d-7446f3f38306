{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nexport let AuthInterceptor = class AuthInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(req, next) {\n    const token = this.authService.getToken();\n    if (token) {\n      const authReq = req.clone({\n        headers: req.headers.set(\"Authorization\", `Bearer ${token}`)\n      });\n      return next.handle(authReq);\n    }\n    return next.handle(req);\n  }\n};\nAuthInterceptor = __decorate([Injectable()], AuthInterceptor);", "map": {"version": 3, "names": ["Injectable", "AuthInterceptor", "constructor", "authService", "intercept", "req", "next", "token", "getToken", "authReq", "clone", "headers", "set", "handle", "__decorate"], "sources": ["C:\\e-learning\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport type { <PERSON>ttpInterceptor, HttpRequest, HttpHandler } from \"@angular/common/http\"\nimport type { AuthService } from \"../services/auth.service\"\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  constructor(private authService: AuthService) {}\n\n  intercept(req: HttpRequest<any>, next: <PERSON>ttpHandler) {\n    const token = this.authService.getToken()\n\n    if (token) {\n      const authReq = req.clone({\n        headers: req.headers.set(\"Authorization\", `Bearer ${token}`),\n      })\n      return next.handle(authReq)\n    }\n\n    return next.handle(req)\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAKnC,WAAMC,eAAe,GAArB,MAAMA,eAAe;EAC1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,MAAMC,KAAK,GAAG,IAAI,CAACJ,WAAW,CAACK,QAAQ,EAAE;IAEzC,IAAID,KAAK,EAAE;MACT,MAAME,OAAO,GAAGJ,GAAG,CAACK,KAAK,CAAC;QACxBC,OAAO,EAAEN,GAAG,CAACM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUL,KAAK,EAAE;OAC5D,CAAC;MACF,OAAOD,IAAI,CAACO,MAAM,CAACJ,OAAO,CAAC;;IAG7B,OAAOH,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;EACzB;CACD;AAfYJ,eAAe,GAAAa,UAAA,EAD3Bd,UAAU,EAAE,C,EACAC,eAAe,CAe3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}