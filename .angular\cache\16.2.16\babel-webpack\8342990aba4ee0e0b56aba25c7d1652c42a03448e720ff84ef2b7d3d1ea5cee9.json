{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nexport let AuthGuard = class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate() {\n    if (this.authService.isAuthenticated()) {\n      return true;\n    } else {\n      this.router.navigate([\"/auth/login\"]);\n      return false;\n    }\n  }\n};\nAuthGuard = __decorate([Injectable({\n  providedIn: \"root\"\n})], AuthGuard);", "map": {"version": 3, "names": ["Injectable", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "isAuthenticated", "navigate", "__decorate", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport type { CanActivate, Router } from \"@angular/router\"\nimport type { AuthService } from \"../services/auth.service\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AuthGuard implements CanActivate {\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n  ) {}\n\n  canActivate(): boolean {\n    if (this.authService.isAuthenticated()) {\n      return true\n    } else {\n      this.router.navigate([\"/auth/login\"])\n      return false\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAOnC,WAAMC,SAAS,GAAf,MAAMA,SAAS;EACpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACF,WAAW,CAACG,eAAe,EAAE,EAAE;MACtC,OAAO,IAAI;KACZ,MAAM;MACL,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC,OAAO,KAAK;;EAEhB;CACD;AAdYN,SAAS,GAAAO,UAAA,EAHrBR,UAAU,CAAC;EACVS,UAAU,EAAE;CACb,CAAC,C,EACWR,SAAS,CAcrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}