{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class QuizService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlApi}quiz`;\n  }\n  // GET: Tous les quiz\n  getQuiz() {\n    return this.http.get(`${environment.urlApi}quiz`);\n  }\n  // GET: Un quiz par ID\n  getQuizById(id) {\n    return this.http.get(`${environment.urlApi}quiz/${id}`);\n  }\n  // POST: Créer un quiz\n  createQuiz(quiz) {\n    return this.http.post(`${environment.urlApi}quiz`, quiz);\n  }\n  // PUT: Modifier un quiz\n  updateQuiz(id, quiz) {\n    return this.http.put(`${environment.urlApi}quiz/${id}`, quiz);\n  }\n  // DELETE: Supprimer un quiz\n  deleteQuiz(id) {\n    return this.http.delete(`${environment.urlApi}quiz/${id}`);\n  }\n  // POST: Soumettre résultat de quiz\n  soumettreResultat(quizId, resultat) {\n    return this.http.post(`${environment.urlApi}quiz/${quizId}/soumettre`, resultat);\n  }\n  static {\n    this.ɵfac = function QuizService_Factory(t) {\n      return new (t || QuizService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuizService,\n      factory: QuizService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "QuizService", "constructor", "http", "apiUrl", "urlApi", "getQuiz", "get", "getQuizById", "id", "createQuiz", "quiz", "post", "updateQuiz", "put", "deleteQuiz", "delete", "soumettreResultat", "quizId", "resultat", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\quiz.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Quiz } from \"../models/course.model\"\nimport { ResultatQuiz } from \"../models/resultat-quiz.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class QuizService {\n  private apiUrl = `${environment.urlApi}quiz`\n\n  constructor(private http: HttpClient) {}\n\n  // GET: Tous les quiz\n  getQuiz(): Observable<Quiz[]> {\n    return this.http.get<Quiz[]>(`${environment.urlApi}quiz`)\n  }\n\n  // GET: Un quiz par ID\n  getQuizById(id: number): Observable<Quiz> {\n    return this.http.get<Quiz>(`${environment.urlApi}quiz/${id}`)\n  }\n\n  // POST: Créer un quiz\n  createQuiz(quiz: Quiz): Observable<Quiz> {\n    return this.http.post<Quiz>(`${environment.urlApi}quiz`, quiz)\n  }\n\n  // PUT: Modifier un quiz\n  updateQuiz(id: number, quiz: Quiz): Observable<any> {\n    return this.http.put(`${environment.urlApi}quiz/${id}`, quiz)\n  }\n\n  // DELETE: Supprimer un quiz\n  deleteQuiz(id: number): Observable<any> {\n    return this.http.delete(`${environment.urlApi}quiz/${id}`)\n  }\n\n  // POST: Soumettre résultat de quiz\n  soumettreResultat(quizId: number, resultat: ResultatQuiz): Observable<any> {\n    return this.http.post(`${environment.urlApi}quiz/${quizId}/soumettre`, resultat)\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAO/D,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,MAAM,MAAM;EAEL;EAEvC;EACAC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAS,GAAGP,WAAW,CAACK,MAAM,MAAM,CAAC;EAC3D;EAEA;EACAG,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAO,GAAGP,WAAW,CAACK,MAAM,QAAQI,EAAE,EAAE,CAAC;EAC/D;EAEA;EACAC,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAO,GAAGZ,WAAW,CAACK,MAAM,MAAM,EAAEM,IAAI,CAAC;EAChE;EAEA;EACAE,UAAUA,CAACJ,EAAU,EAAEE,IAAU;IAC/B,OAAO,IAAI,CAACR,IAAI,CAACW,GAAG,CAAC,GAAGd,WAAW,CAACK,MAAM,QAAQI,EAAE,EAAE,EAAEE,IAAI,CAAC;EAC/D;EAEA;EACAI,UAAUA,CAACN,EAAU;IACnB,OAAO,IAAI,CAACN,IAAI,CAACa,MAAM,CAAC,GAAGhB,WAAW,CAACK,MAAM,QAAQI,EAAE,EAAE,CAAC;EAC5D;EAEA;EACAQ,iBAAiBA,CAACC,MAAc,EAAEC,QAAsB;IACtD,OAAO,IAAI,CAAChB,IAAI,CAACS,IAAI,CAAC,GAAGZ,WAAW,CAACK,MAAM,QAAQa,MAAM,YAAY,EAAEC,QAAQ,CAAC;EAClF;;;uBAjCWlB,WAAW,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXtB,WAAW;MAAAuB,OAAA,EAAXvB,WAAW,CAAAwB,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}