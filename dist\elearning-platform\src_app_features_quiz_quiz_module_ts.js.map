{"version": 3, "file": "src_app_features_quiz_quiz_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AAG+D;;;AAOzD,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,kEAAW,CAACK,MAAM,MAAM;EAEL;EAEvC;EACAC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAS,IAAI,CAACH,MAAM,CAAC;EAC3C;EAEA;EACAI,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAO,GAAG,IAAI,CAACH,MAAM,IAAIK,EAAE,EAAE,CAAC;EACpD;EAEA;EACAC,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAO,IAAI,CAACR,MAAM,EAAEO,IAAI,CAAC;EAChD;EAEA;EACAE,UAAUA,CAACJ,EAAU,EAAEE,IAAU;IAC/B,OAAO,IAAI,CAACR,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACV,MAAM,IAAIK,EAAE,EAAE,EAAEE,IAAI,CAAC;EACpD;EAEA;EACAI,UAAUA,CAACN,EAAU;IACnB,OAAO,IAAI,CAACN,IAAI,CAACa,MAAM,CAAC,GAAG,IAAI,CAACZ,MAAM,IAAIK,EAAE,EAAE,CAAC;EACjD;EAEA;EACAQ,iBAAiBA,CAACC,MAAc,EAAEC,QAAsB;IACtD,OAAO,IAAI,CAAChB,IAAI,CAACS,IAAI,CAAS,GAAG,IAAI,CAACR,MAAM,IAAIc,MAAM,YAAY,EAAEC,QAAQ,CAAC;EAC/E;;;uBAjCWlB,WAAW,EAAAmB,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAXrB,WAAW;MAAAuB,OAAA,EAAXvB,WAAW,CAAAwB,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDyB;;;;;;;;;;;;;;;;IAiC/BN,4DAAA,2BAGsB;IACpBA,oDAAA,GACF;IAAAA,0DAAA,EAAmB;;;;;IAHjBA,wDAAA,UAAAY,IAAA,CAAW;IAEXZ,uDAAA,GACF;IADEA,gEAAA,MAAAe,SAAA,MACF;;;;;;IAWJf,4DAAA,iBAGiE;IAFzDA,wDAAA,mBAAAiB,+DAAA;MAAAjB,2DAAA,CAAAmB,IAAA;MAAA,MAAAC,MAAA,GAAApB,2DAAA;MAAA,OAASA,yDAAA,CAAAoB,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAG9BvB,oDAAA,0BACF;IAAAA,0DAAA,EAAS;;;;IAHDA,wDAAA,aAAAwB,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAjC,IAAA,CAAAmC,SAAA,CAAAF,MAAA,CAAAG,oBAAA,EAAAtC,EAAA,MAAAuC,SAAA,CAAmF;;;;;IASzF5B,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,uBAAgB;IAAAA,0DAAA,EAAO;;;;;;IALlDA,4DAAA,iBAGmE;IAF3DA,wDAAA,mBAAA8B,+DAAA;MAAA9B,2DAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAAhC,2DAAA;MAAA,OAASA,yDAAA,CAAAgC,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAG5BjC,wDAAA,IAAAmC,oDAAA,0BAA2D;IAC3DnC,wDAAA,IAAAoC,6CAAA,mBAAgD;IAClDpC,0DAAA,EAAS;;;;IAJDA,wDAAA,aAAAqC,MAAA,CAAAZ,eAAA,CAAAY,MAAA,CAAA9C,IAAA,CAAAmC,SAAA,CAAAW,MAAA,CAAAV,oBAAA,EAAAtC,EAAA,MAAAuC,SAAA,IAAAS,MAAA,CAAAC,SAAA,CAAgG;IAE1EtC,uDAAA,GAAe;IAAfA,wDAAA,SAAAqC,MAAA,CAAAC,SAAA,CAAe;IACpCtC,uDAAA,GAAgB;IAAhBA,wDAAA,UAAAqC,MAAA,CAAAC,SAAA,CAAgB;;;;;;;;;;;;IASrBtC,4DAAA,iBAKkC;IAA1BA,wDAAA,mBAAAuC,+DAAA;MAAA,MAAAC,WAAA,GAAAxC,2DAAA,CAAAyC,IAAA;MAAA,MAAAC,KAAA,GAAAF,WAAA,CAAAG,KAAA;MAAA,MAAAC,OAAA,GAAA5C,2DAAA;MAAA,OAASA,yDAAA,CAAA4C,OAAA,CAAAC,YAAA,CAAAH,KAAA,CAAe;IAAA,EAAC;IAC/B1C,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;;;IANDA,wDAAA,YAAAA,6DAAA,IAAA+C,GAAA,EAAAL,KAAA,KAAAM,MAAA,CAAArB,oBAAA,EAAAqB,MAAA,CAAAvB,eAAA,CAAAwB,KAAA,CAAA5D,EAAA,MAAAuC,SAAA,IAAAc,KAAA,KAAAM,MAAA,CAAArB,oBAAA,EAGE;IAER3B,uDAAA,GACF;IADEA,gEAAA,MAAA0C,KAAA,UACF;;;;;;IAtER1C,4DAAA,aAA8E;IAIpEA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IACzBA,4DAAA,aAAmB;IACPA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,cAA2B;IAAAA,oDAAA,GAA+B;IAAAA,0DAAA,EAAO;IAIrEA,4DAAA,cAA0B;IACKA,oDAAA,IAAuE;IAAAA,0DAAA,EAAO;IAC3GA,4DAAA,gBAAwB;IAAAA,oDAAA,IAA4C;IAAAA,0DAAA,EAAO;IAE7EA,uDAAA,4BAA2E;IAC7EA,0DAAA,EAAM;IAGNA,4DAAA,oBAAgC;IACdA,oDAAA,IAAgD;IAAAA,0DAAA,EAAiB;IACjFA,4DAAA,wBAAkB;IAGdA,wDAAA,2BAAAkD,uEAAAC,MAAA;MAAAnD,2DAAA,CAAAoD,IAAA;MAAA,MAAAC,OAAA,GAAArD,2DAAA;MAAA,OAAAA,yDAAA,CAAAqD,OAAA,CAAA5B,eAAA,CAAA4B,OAAA,CAAA9D,IAAA,CAAAmC,SAAA,CAAA2B,OAAA,CAAA1B,oBAAA,EAAAtC,EAAA,IAAA8D,MAAA;IAAA,EAAsE;IAEtEnD,wDAAA,KAAAsD,gDAAA,+BAKmB;IACrBtD,0DAAA,EAAkB;IAKtBA,4DAAA,eAAgC;IACHA,wDAAA,mBAAAuD,sDAAA;MAAAvD,2DAAA,CAAAoD,IAAA;MAAA,MAAAI,OAAA,GAAAxD,2DAAA;MAAA,OAASA,yDAAA,CAAAwD,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IACrDzD,oDAAA,uCACF;IAAAA,0DAAA,EAAS;IAETA,wDAAA,KAAA0D,sCAAA,qBAKS;IAET1D,wDAAA,KAAA2D,sCAAA,qBAMS;IACX3D,0DAAA,EAAM;IAGNA,4DAAA,oBAAgC;IACdA,oDAAA,iCAAoB;IAAAA,0DAAA,EAAiB;IACrDA,4DAAA,wBAAkB;IAEdA,wDAAA,KAAA4D,sCAAA,qBAOS;IACX5D,0DAAA,EAAM;;;;IAnEFA,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA8D,MAAA,CAAAvE,IAAA,CAAAwE,KAAA,CAAgB;IAGS/D,uDAAA,GAA+B;IAA/BA,+DAAA,CAAA8D,MAAA,CAAAE,UAAA,CAAAF,MAAA,CAAAG,aAAA,EAA+B;IAK/BjE,uDAAA,GAAuE;IAAvEA,gEAAA,cAAA8D,MAAA,CAAAnC,oBAAA,eAAAmC,MAAA,CAAAvE,IAAA,CAAAmC,SAAA,CAAAyC,MAAA,KAAuE;IAC5EnE,uDAAA,GAA4C;IAA5CA,gEAAA,6BAAA8D,MAAA,CAAAvE,IAAA,CAAA6E,aAAA,MAA4C;IAEjCpE,uDAAA,GAAkB;IAAlBA,wDAAA,UAAA8D,MAAA,CAAAO,QAAA,CAAkB;IAKvCrE,uDAAA,GAAgD;IAAhDA,+DAAA,CAAA8D,MAAA,CAAAvE,IAAA,CAAAmC,SAAA,CAAAoC,MAAA,CAAAnC,oBAAA,EAAA2C,KAAA,CAAgD;IAI5DtE,uDAAA,GAAsE;IAAtEA,wDAAA,YAAA8D,MAAA,CAAArC,eAAA,CAAAqC,MAAA,CAAAvE,IAAA,CAAAmC,SAAA,CAAAoC,MAAA,CAAAnC,oBAAA,EAAAtC,EAAA,EAAsE;IAGjDW,uDAAA,GAAiD;IAAjDA,wDAAA,YAAA8D,MAAA,CAAAvE,IAAA,CAAAmC,SAAA,CAAAoC,MAAA,CAAAnC,oBAAA,EAAA4C,OAAA,CAAiD;IAWlBvE,uDAAA,GAAuC;IAAvCA,wDAAA,aAAA8D,MAAA,CAAAnC,oBAAA,OAAuC;IAOtF3B,uDAAA,GAAsD;IAAtDA,wDAAA,SAAA8D,MAAA,CAAAnC,oBAAA,GAAAmC,MAAA,CAAAvE,IAAA,CAAAmC,SAAA,CAAAyC,MAAA,KAAsD;IAOtDnE,uDAAA,GAAwD;IAAxDA,wDAAA,SAAA8D,MAAA,CAAAnC,oBAAA,KAAAmC,MAAA,CAAAvE,IAAA,CAAAmC,SAAA,CAAAyC,MAAA,KAAwD;IAW1BnE,uDAAA,GAAmB;IAAnBA,wDAAA,YAAA8D,MAAA,CAAAvE,IAAA,CAAAmC,SAAA,CAAmB;;;;;IAc5D1B,4DAAA,cAA+C;IAC7CA,uDAAA,kBAA2B;IAC3BA,4DAAA,QAAG;IAAAA,oDAAA,4BAAqB;IAAAA,0DAAA,EAAI;;;;;IAMtBA,4DAAA,eAAiC;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAW;;;;;IACxDA,4DAAA,eAAkC;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAW;;;;;IAgBrDA,4DAAA,cAAqD;IACzCA,oDAAA,mBAAY;IAAAA,0DAAA,EAAW;IACjCA,4DAAA,QAAG;IAAAA,oDAAA,iEAA2C;IAAAA,0DAAA,EAAI;;;;;;IAOlDA,4DAAA,iBAA0F;IAAxBA,wDAAA,mBAAAwE,6EAAA;MAAAxE,2DAAA,CAAAyE,IAAA;MAAA,MAAAC,OAAA,GAAA1E,2DAAA;MAAA,OAASA,yDAAA,CAAA0E,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACvF3E,oDAAA,oBACF;IAAAA,0DAAA,EAAS;;;;;IAQLA,4DAAA,mBAAmE;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAW;;;;;IAC1FA,4DAAA,mBAAwF;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAW;;;;;IAWvGA,4DAAA,eAAgE;IAAAA,oDAAA,gCAAe;IAAAA,0DAAA,EAAO;;;;;IACtFA,4DAAA,eAA6H;IAAAA,oDAAA,gCAAe;IAAAA,0DAAA,EAAO;;;;;;;;;;;IARrJA,4DAAA,cAKgC;IAC9BA,oDAAA,GACA;IAAAA,wDAAA,IAAA4E,8DAAA,mBAAsF;IACtF5E,wDAAA,IAAA6E,8DAAA,mBAAmJ;IACrJ7E,0DAAA,EAAM;;;;;;;;;IARDA,wDAAA,YAAAA,6DAAA,IAAA8E,GAAA,EAAAC,KAAA,KAAAC,YAAA,CAAAC,YAAA,EAAAC,OAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAL,KAAA,KAAAG,OAAA,CAAAC,WAAA,CAAAC,KAAA,EAAAC,cAAA,KAAAH,OAAA,CAAAC,WAAA,CAAAC,KAAA,EAAAE,WAAA,EAGE;IAELtF,uDAAA,GACA;IADAA,gEAAA,MAAAuF,UAAA,MACA;IAAOvF,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA+E,KAAA,KAAAC,YAAA,CAAAC,YAAA,CAAiC;IACjCjF,uDAAA,GAA4F;IAA5FA,wDAAA,SAAAkF,OAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAL,KAAA,KAAAG,OAAA,CAAAC,WAAA,CAAAC,KAAA,EAAAC,cAAA,KAAAH,OAAA,CAAAC,WAAA,CAAAC,KAAA,EAAAE,WAAA,CAA4F;;;;;IAhBzGtF,4DAAA,cAAgF;IAExEA,oDAAA,GAAoB;IAAAA,0DAAA,EAAK;IAC7BA,wDAAA,IAAAwF,4DAAA,uBAA0F;IAC1FxF,wDAAA,IAAAyF,4DAAA,uBAAyG;IAC3GzF,0DAAA,EAAM;IACNA,4DAAA,YAAyB;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAI;IACjDA,4DAAA,cAA4B;IAC1BA,wDAAA,IAAA0F,uDAAA,kBASM;IACR1F,0DAAA,EAAM;;;;;;;IAhBAA,uDAAA,GAAoB;IAApBA,gEAAA,cAAAoF,KAAA,SAAoB;IACbpF,uDAAA,GAAiC;IAAjCA,wDAAA,UAAA2F,OAAA,GAAAC,OAAA,CAAAT,WAAA,CAAAC,KAAA,oBAAAO,OAAA,CAAAL,WAAA,CAAiC;IACjCtF,uDAAA,GAAoD;IAApDA,wDAAA,SAAA4F,OAAA,CAAAT,WAAA,CAAAC,KAAA,MAAAQ,OAAA,CAAAT,WAAA,CAAAC,KAAA,EAAAE,WAAA,CAAoD;IAExCtF,uDAAA,GAAoB;IAApBA,+DAAA,CAAAgF,YAAA,CAAAV,KAAA,CAAoB;IAEnBtE,uDAAA,GAAqB;IAArBA,wDAAA,YAAAgF,YAAA,CAAAT,OAAA,CAAqB;;;;;;;;;;;;;;IA7CzDvE,4DAAA,cAA4E;IAIpEA,wDAAA,IAAA6F,qDAAA,uBAAwD;IACxD7F,wDAAA,IAAA8F,qDAAA,uBAAmD;IACrD9F,0DAAA,EAAM;IACNA,4DAAA,qBAAgB;IAAAA,oDAAA,GAA4D;IAAAA,0DAAA,EAAiB;IAC7FA,4DAAA,wBAAmB;IACjBA,oDAAA,GACF;IAAAA,0DAAA,EAAoB;IAGtBA,4DAAA,4BAA0C;IAEbA,oDAAA,IAA0B;IAAAA,0DAAA,EAAO;IAC1DA,4DAAA,aAAuB;IAAAA,oDAAA,IAAwE;IAAAA,0DAAA,EAAI;IAGrGA,uDAAA,4BAA+G;IAE/GA,wDAAA,KAAA+F,iDAAA,kBAGM;IAEN/F,4DAAA,eAA6B;IAEzBA,oDAAA,yBACF;IAAAA,0DAAA,EAAS;IACTA,wDAAA,KAAAgG,oDAAA,qBAES;IACXhG,0DAAA,EAAM;IAENA,4DAAA,eAA4B;IACtBA,oDAAA,uCAAqB;IAAAA,0DAAA,EAAK;IAC9BA,wDAAA,KAAAiG,iDAAA,mBAmBM;IACRjG,0DAAA,EAAM;;;;IAtD2BA,uDAAA,GAAgE;IAAhEA,wDAAA,YAAAA,6DAAA,KAAAkG,GAAA,EAAAC,OAAA,CAAAC,OAAA,CAAAC,MAAA,GAAAF,OAAA,CAAAC,OAAA,CAAAC,MAAA,EAAgE;IACpFrG,uDAAA,GAAoB;IAApBA,wDAAA,SAAAmG,OAAA,CAAAC,OAAA,CAAAC,MAAA,CAAoB;IACpBrG,uDAAA,GAAqB;IAArBA,wDAAA,UAAAmG,OAAA,CAAAC,OAAA,CAAAC,MAAA,CAAqB;IAElBrG,uDAAA,GAA4D;IAA5DA,+DAAA,CAAAmG,OAAA,CAAAC,OAAA,CAAAC,MAAA,mDAA4D;IAE1ErG,uDAAA,GACF;IADEA,gEAAA,MAAAmG,OAAA,CAAAC,OAAA,CAAAC,MAAA,0EAAAF,OAAA,CAAA5G,IAAA,CAAA6E,aAAA,uCACF;IAK2BpE,uDAAA,GAA0B;IAA1BA,gEAAA,KAAAmG,OAAA,CAAAC,OAAA,CAAAE,WAAA,MAA0B;IAC5BtG,uDAAA,GAAwE;IAAxEA,gEAAA,KAAAmG,OAAA,CAAAC,OAAA,CAAAG,KAAA,WAAAJ,OAAA,CAAAC,OAAA,CAAAI,cAAA,yBAAwE;IAG5DxG,uDAAA,GAA6B;IAA7BA,wDAAA,UAAAmG,OAAA,CAAAC,OAAA,CAAAE,WAAA,CAA6B;IAEnCtG,uDAAA,GAAoB;IAApBA,wDAAA,SAAAmG,OAAA,CAAAC,OAAA,CAAAC,MAAA,CAAoB;IAMtBrG,uDAAA,GAAyC;IAAzCA,wDAAA,eAAAA,6DAAA,KAAA0G,GAAA,EAAAP,OAAA,CAAA5G,IAAA,CAAAoH,OAAA,EAAyC;IAGzB3G,uDAAA,GAAqB;IAArBA,wDAAA,UAAAmG,OAAA,CAAAC,OAAA,CAAAC,MAAA,CAAqB;IAOtCrG,uDAAA,GAAmB;IAAnBA,wDAAA,YAAAmG,OAAA,CAAA5G,IAAA,CAAAmC,SAAA,CAAmB;;;;;IAzCrD1B,wDAAA,IAAA4G,0CAAA,kBAGM;IACN5G,wDAAA,IAAA6G,0CAAA,oBA4DM;;;;IAhEA7G,wDAAA,SAAA8G,MAAA,CAAAxE,SAAA,CAAe;IAIftC,uDAAA,GAA0C;IAA1CA,wDAAA,UAAA8G,MAAA,CAAAxE,SAAA,IAAAwE,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAV,OAAA,CAA0C;;;AA6ZlD,MAAOY,aAAa;EAYxBlI,YACUmI,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAdlB,KAAA1F,oBAAoB,GAAG,CAAC;IACxB,KAAAF,eAAe,GAA8B,EAAE,EAAC;IAChD,KAAAsF,WAAW,GAAG,KAAK;IACnB,KAAAX,OAAO,GAAwB,IAAI;IACnC,KAAA9D,SAAS,GAAG,IAAI;IAChB,KAAA2B,aAAa,GAAG,CAAC;EAUd;EAEHqD,QAAQA,CAAA;IACN,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACC,WAAW,GAAGD,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACR,KAAK,CAACU,QAAQ,CAACH,SAAS,CAAEI,MAAM,IAAI;MACvC,IAAI,CAAC9H,MAAM,GAAG+H,MAAM,CAACD,MAAM,CAACzI,GAAG,CAAC,IAAI,CAAC,CAAC;MACtC,IAAI,CAAC2I,QAAQ,EAAE;IACjB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACC,WAAW,EAAE;;EAExC;EAEAH,QAAQA,CAAA;IACN,IAAI,CAACxF,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAAC/C,IAAI,GAAG;MACVF,EAAE,EAAE,IAAI,CAACS,MAAM;MACfiE,KAAK,EAAE,uBAAuB;MAC9BmE,WAAW,EAAE,iEAAiE;MAC9E9D,aAAa,EAAE,EAAE;MACjB+D,YAAY,EAAE,EAAE;MAChBxB,OAAO,EAAE,CAAC;MACVyB,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,CAAC;MACR7G,SAAS,EAAE,CACT;QACErC,EAAE,EAAE,CAAC;QACLiF,KAAK,EAAE,uBAAuB;QAC9BC,OAAO,EAAE,CACP,kBAAkB,EAClB,mEAAmE,EACnE,gBAAgB,EAChB,6BAA6B,CAC9B;QACDU,YAAY,EAAE;OACf,EACD;QACE5F,EAAE,EAAE,CAAC;QACLiF,KAAK,EAAE,+BAA+B;QACtCC,OAAO,EAAE,CACP,uCAAuC,EACvC,wBAAwB,EACxB,wBAAwB,EACxB,gBAAgB,CACjB;QACDU,YAAY,EAAE;OACf,EACD;QACE5F,EAAE,EAAE,CAAC;QACLiF,KAAK,EAAE,mDAAmD;QAC1DC,OAAO,EAAE,CACP,2CAA2C,EAC3C,gDAAgD,EAChD,yCAAyC,EACzC,gCAAgC,CACjC;QACDU,YAAY,EAAE;OACf,EACD;QACE5F,EAAE,EAAE,CAAC;QACLiF,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE,CACP,qCAAqC,EACrC,qCAAqC,EACrC,kBAAkB,EAClB,qBAAqB,CACtB;QACDU,YAAY,EAAE;OACf,EACD;QACE5F,EAAE,EAAE,CAAC;QACLiF,KAAK,EAAE,sDAAsD;QAC7DC,OAAO,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,6BAA6B,EAAE,mBAAmB,CAAC;QACzGU,YAAY,EAAE;OACf;KAEJ;IACD,IAAI,CAAChB,aAAa,GAAG,CAAC,IAAI,CAAC1E,IAAI,EAAE4I,YAAY,IAAI,CAAC,IAAI,EAAE,EAAC;IACzD,IAAI,CAACK,UAAU,EAAE;IACjB,IAAI,CAAClG,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;EAgBF;;EAEAkG,UAAUA,CAAA;IACR,IAAI,CAACR,iBAAiB,GAAGzH,8CAAQ,CAAC,IAAI,CAAC,CAACiH,SAAS,CAAC,MAAK;MACrD,IAAI,IAAI,CAACvD,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC8C,WAAW,EAAE;QAC/C,IAAI,CAAC9C,aAAa,EAAE;OACrB,MAAM,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC8C,WAAW,EAAE;QACxD,IAAI,CAAC9E,UAAU,EAAE;QACjB,IAAI,CAAC+F,iBAAiB,CAACC,WAAW,EAAE;;IAExC,CAAC,CAAC;EACJ;EAEAxE,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC9B,oBAAoB,GAAG,CAAC,EAAE;MACjC,IAAI,CAACA,oBAAoB,EAAE;;EAE/B;EAEAJ,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,oBAAoB,GAAG,IAAI,CAACpC,IAAI,CAACmC,SAAS,CAACyC,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACxC,oBAAoB,EAAE;;EAE/B;EAEAkB,YAAYA,CAACF,KAAa;IACxB,IAAI,CAAChB,oBAAoB,GAAGgB,KAAK;EACnC;EAEAV,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC1C,IAAI,IAAI,CAAC,IAAI,CAACmI,WAAW,EAAE;IAErC,IAAI,CAACpF,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC0F,iBAAiB,CAACC,WAAW,EAAE;IAEpC,IAAI1B,KAAK,GAAG,CAAC;IACb,MAAMkC,QAAQ,GAA2E,EAAE;IAE3F,IAAI,CAAClJ,IAAI,CAACmC,SAAS,CAACgH,OAAO,CAAEC,QAAQ,IAAI;MACvC,MAAMC,cAAc,GAAG,IAAI,CAACnH,eAAe,CAACkH,QAAQ,CAACtJ,EAAE,CAAC;MACxD,MAAMwJ,SAAS,GAAGD,cAAc,KAAKD,QAAQ,CAAC1D,YAAY;MAC1D,IAAI4D,SAAS,EAAE;QACbtC,KAAK,EAAE;;MAETkC,QAAQ,CAACK,IAAI,CAAC;QACZC,UAAU,EAAEJ,QAAQ,CAACtJ,EAAE;QACvBgG,cAAc,EAAEuD,cAAc,IAAI,CAAC,CAAC;QACpCtD,WAAW,EAAEuD;OACd,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMvC,WAAW,GAAG0C,IAAI,CAACC,KAAK,CAAE1C,KAAK,GAAG,IAAI,CAAChH,IAAI,CAACmC,SAAS,CAACyC,MAAM,GAAI,GAAG,CAAC;IAC1E,MAAMkC,MAAM,GAAGC,WAAW,KAAK,IAAI,CAAC/G,IAAI,CAAC6E,aAAa,IAAI,CAAC,CAAC;IAE5D,MAAM8E,YAAY,GAAiB;MACjCC,QAAQ,EAAE,IAAI,CAACzB,WAAW,CAACrI,EAAE;MAC7BS,MAAM,EAAE,IAAI,CAACP,IAAI,CAACF,EAAE;MACpBkH,KAAK,EAAED,WAAW;MAClB8C,cAAc,EAAE,IAAIC,IAAI;KACzB;IAED;IACA,IAAI,CAACjD,OAAO,GAAG;MACbG,KAAK,EAAEA,KAAK;MACZC,cAAc,EAAE,IAAI,CAACjH,IAAI,CAACmC,SAAS,CAACyC,MAAM;MAC1CmC,WAAW,EAAEA,WAAW;MACxBD,MAAM,EAAEA,MAAM;MACdoC,QAAQ,EAAEA;KACJ,EAAC;IACT,IAAI,CAAC1B,WAAW,GAAG,IAAI;IACvB,IAAI,CAACzE,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;;;;;;EAqBF;;EAEAqC,WAAWA,CAAA;IACT,IAAI,CAAChD,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACF,eAAe,GAAG,EAAE;IACzB,IAAI,CAACsF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACX,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnC,aAAa,GAAG,CAAC,IAAI,CAAC1E,IAAI,EAAE4I,YAAY,IAAI,CAAC,IAAI,EAAE;IACxD,IAAI,CAACK,UAAU,EAAE;EACnB;EAEAxE,UAAUA,CAACsF,OAAe;IACxB,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGH,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA,IAAItF,QAAQA,CAAA;IACV,OAAQ,CAAC,IAAI,CAAC1C,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACpC,IAAI,CAACmC,SAAS,CAACyC,MAAM,GAAI,GAAG;EAC7E;EAEA;EACAgB,WAAWA,CAACxC,KAAa;IACvB,OAAO,IAAI,CAACyD,OAAO,EAAEqC,QAAQ,GAAG9F,KAAK,CAAC,IAAI,IAAI;EAChD;;;uBAnPWqE,aAAa,EAAAhH,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAE,mDAAA,GAAAF,+DAAA,CAAA+J,oEAAA,GAAA/J,+DAAA,CAAAgK,oEAAA,GAAAhK,+DAAA,CAAAkK,oEAAA;IAAA;EAAA;;;YAAblD,aAAa;MAAAoD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/etB1K,4DAAA,aAA4B;UAC1BA,wDAAA,IAAA4K,4BAAA,mBA0EM;UAEN5K,wDAAA,IAAA6K,oCAAA,gCAAA7K,oEAAA,CAkEc;UAChBA,0DAAA,EAAM;;;;UA/IuBA,uDAAA,GAA4B;UAA5BA,wDAAA,SAAA2K,GAAA,CAAApL,IAAA,KAAAoL,GAAA,CAAA5D,WAAA,CAA4B,aAAAgE,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbf;AACA;AACF,CAAC;AAE7C;AACsD;AACI;AACJ;AACe;AACb;AACqB;AAE7B;;;AAgB1C,MAAOU,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAXnBT,yDAAY,EACZE,uDAAW,EACXC,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,gFAAoB,EACpBC,mEAAc,EACdC,wFAAwB,EACxBP,0DAAY,CAACS,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,SAAS,EAAE5E,0DAAaA;MAAA,CAAE,CAAC,CAAC;IAAA;EAAA;;;sHAGzDyE,UAAU;IAAAI,YAAA,GAbN7E,0DAAa;IAAA8E,OAAA,GAE1Bd,yDAAY,EACZE,uDAAW,EACXC,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,gFAAoB,EACpBC,mEAAc,EACdC,wFAAwB,EAAAtL,0DAAA;EAAA;AAAA,K;;;;;;;;;;;;;;;;ACzBwB;AACpB;AACzB,SAASK,QAAQA,CAAC0L,MAAM,GAAG,CAAC,EAAEC,SAAS,GAAGH,4DAAc,EAAE;EAC7D,IAAIE,MAAM,GAAG,CAAC,EAAE;IACZA,MAAM,GAAG,CAAC;EACd;EACA,OAAOD,6CAAK,CAACC,MAAM,EAAEA,MAAM,EAAEC,SAAS,CAAC;AAC3C,C", "sources": ["./src/app/core/services/quiz.service.ts", "./src/app/features/quiz/quiz.component.ts", "./src/app/features/quiz/quiz.module.ts", "./node_modules/rxjs/dist/esm/internal/observable/interval.js"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Quiz } from \"../models/course.model\"\nimport { ResultatQuiz } from \"../models/resultat-quiz.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class QuizService {\n  private apiUrl = `${environment.urlApi}quiz`\n\n  constructor(private http: HttpClient) {}\n\n  // GET: Tous les quiz (correspond à GET /api/quiz)\n  getQuiz(): Observable<Quiz[]> {\n    return this.http.get<Quiz[]>(this.apiUrl)\n  }\n\n  // GET: Un quiz par ID (correspond à GET /api/quiz/{id})\n  getQuizById(id: number): Observable<Quiz> {\n    return this.http.get<Quiz>(`${this.apiUrl}/${id}`)\n  }\n\n  // POST: Créer un quiz (correspond à POST /api/quiz)\n  createQuiz(quiz: Quiz): Observable<Quiz> {\n    return this.http.post<Quiz>(this.apiUrl, quiz)\n  }\n\n  // PUT: Modifier un quiz (correspond à PUT /api/quiz/{id})\n  updateQuiz(id: number, quiz: Quiz): Observable<any> {\n    return this.http.put(`${this.apiUrl}/${id}`, quiz)\n  }\n\n  // DELETE: Supprimer un quiz (correspond à DELETE /api/quiz/{id})\n  deleteQuiz(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/${id}`)\n  }\n\n  // POST: Soumettre résultat de quiz (correspond à POST /api/quiz/{id}/soumettre)\n  soumettreResultat(quizId: number, resultat: ResultatQuiz): Observable<string> {\n    return this.http.post<string>(`${this.apiUrl}/${quizId}/soumettre`, resultat)\n  }\n}\n", "import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@angular/core\"\nimport { ActivatedRoute, Router } from \"@angular/router\"\nimport { QuizService } from \"../../core/services/quiz.service\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Quiz } from \"../../core/models/course.model\"\nimport { ResultatQuiz } from \"../../core/models/resultat-quiz.model\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { interval, Subscription } from \"rxjs\"\nimport { User } from \"../../core/models/user.model\" // Import User model\n\n@Component({\n  selector: \"app-quiz\",\n  template: `\n    <div class=\"quiz-container\">\n      <div class=\"quiz-wrapper\" *ngIf=\"quiz && !showResults; else resultsOrLoading\">\n        <!-- Header -->\n        <div class=\"quiz-header\">\n          <div class=\"title-row\">\n            <h1>{{ quiz.titre }}</h1>\n            <div class=\"timer\">\n              <mat-icon>timer</mat-icon>\n              <span class=\"time-display\">{{ formatTime(timeRemaining) }}</span>\n            </div>\n          </div>\n\n          <div class=\"progress-row\">\n            <span class=\"question-count\">Question {{ currentQuestionIndex + 1 }} sur {{ quiz.questions.length }}</span>\n            <span class=\"threshold\">Seuil de réussite: {{ quiz.seuilReussite }}%</span>\n          </div>\n          <mat-progress-bar mode=\"determinate\" [value]=\"progress\"></mat-progress-bar>\n        </div>\n\n        <!-- Question Card -->\n        <mat-card class=\"question-card\">\n          <mat-card-title>{{ quiz.questions[currentQuestionIndex].texte }}</mat-card-title>\n          <mat-card-content>\n            <mat-radio-group \n              aria-label=\"Sélectionnez une option\" \n              [(ngModel)]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id]\"\n              class=\"options-group\">\n              <mat-radio-button \n                *ngFor=\"let option of quiz.questions[currentQuestionIndex].options; let i = index\" \n                [value]=\"i\" \n                class=\"option-item\">\n                {{ option }}\n              </mat-radio-button>\n            </mat-radio-group>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Navigation Buttons -->\n        <div class=\"navigation-buttons\">\n          <button mat-stroked-button (click)=\"previousQuestion()\" [disabled]=\"currentQuestionIndex === 0\">\n            Question précédente\n          </button>\n\n          <button mat-raised-button color=\"primary\" \n                  (click)=\"nextQuestion()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined\"\n                  *ngIf=\"currentQuestionIndex < quiz.questions.length - 1\">\n            Question suivante\n          </button>\n\n          <button mat-raised-button color=\"accent\" \n                  (click)=\"submitQuiz()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined || isLoading\"\n                  *ngIf=\"currentQuestionIndex === quiz.questions.length - 1\">\n            <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n            <span *ngIf=\"!isLoading\">Terminer le quiz</span>\n          </button>\n        </div>\n\n        <!-- Questions Overview -->\n        <mat-card class=\"overview-card\">\n          <mat-card-title>Aperçu des questions</mat-card-title>\n          <mat-card-content>\n            <div class=\"question-dots\">\n              <button mat-mini-fab *ngFor=\"let q of quiz.questions; let i = index\" \n                      [ngClass]=\"{\n                        'current': i === currentQuestionIndex,\n                        'answered': selectedAnswers[q.id] !== undefined && i !== currentQuestionIndex\n                      }\"\n                      (click)=\"goToQuestion(i)\">\n                {{ i + 1 }}\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #resultsOrLoading>\n        <div *ngIf=\"isLoading\" class=\"loading-spinner\">\n          <mat-spinner></mat-spinner>\n          <p>Chargement du quiz...</p>\n        </div>\n        <div *ngIf=\"!isLoading && showResults && results\" class=\"results-container\">\n          <mat-card class=\"results-card\">\n            <mat-card-header class=\"results-header\">\n              <div class=\"result-icon-wrapper\" [ngClass]=\"{'success': results.reussi, 'fail': !results.reussi}\">\n                <mat-icon *ngIf=\"results.reussi\">check_circle</mat-icon>\n                <mat-icon *ngIf=\"!results.reussi\">cancel</mat-icon>\n              </div>\n              <mat-card-title>{{ results.reussi ? 'Félicitations !' : 'Quiz non réussi' }}</mat-card-title>\n              <mat-card-subtitle>\n                {{ results.reussi ? 'Vous avez réussi le quiz avec succès !' : 'Il vous faut ' + quiz.seuilReussite + '% pour réussir ce quiz.' }}\n              </mat-card-subtitle>\n            </mat-card-header>\n\n            <mat-card-content class=\"results-content\">\n              <div class=\"score-display\">\n                <span class=\"percentage\">{{ results.pourcentage }}%</span>\n                <p class=\"score-count\">{{ results.score }} sur {{ results.totalQuestions }} questions correctes</p>\n              </div>\n\n              <mat-progress-bar mode=\"determinate\" [value]=\"results.pourcentage\" class=\"results-progress\"></mat-progress-bar>\n\n              <div class=\"certificate-info\" *ngIf=\"results.reussi\">\n                <mat-icon>emoji_events</mat-icon>\n                <p>Un certificat sera généré automatiquement !</p>\n              </div>\n\n              <div class=\"results-actions\">\n                <button mat-stroked-button [routerLink]=\"['/courses', quiz.coursId]\">\n                  Retour au cours\n                </button>\n                <button mat-raised-button color=\"primary\" *ngIf=\"!results.reussi\" (click)=\"restartQuiz()\">\n                  Recommencer\n                </button>\n              </div>\n\n              <div class=\"answers-detail\">\n                <h3>Détail des réponses :</h3>\n                <div *ngFor=\"let question of quiz.questions; let i = index\" class=\"answer-item\">\n                  <div class=\"answer-header\">\n                    <h4>Question {{ i + 1 }}</h4>\n                    <mat-icon *ngIf=\"getResponse(i)?.estCorrecte\" class=\"correct-icon\">check_circle</mat-icon>\n                    <mat-icon *ngIf=\"getResponse(i) && !getResponse(i)!.estCorrecte\" class=\"incorrect-icon\">cancel</mat-icon>\n                  </div>\n                  <p class=\"question-text\">{{ question.texte }}</p>\n                  <div class=\"options-detail\">\n                    <div *ngFor=\"let option of question.options; let j = index\" \n                         [ngClass]=\"{\n                           'correct-option': j === question.bonneReponse,\n                           'user-incorrect-option': getResponse(i) && j === getResponse(i)!.reponseChoisie && !getResponse(i)!.estCorrecte\n                         }\"\n                         class=\"option-detail-item\">\n                      {{ option }}\n                      <span *ngIf=\"j === question.bonneReponse\" class=\"correct-label\">✓ Bonne réponse</span>\n                      <span *ngIf=\"getResponse(i) && j === getResponse(i)!.reponseChoisie && !getResponse(i)!.estCorrecte\" class=\"incorrect-label\">✗ Votre réponse</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [\n    `\n    .quiz-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .quiz-wrapper, .results-container {\n      width: 100%;\n      max-width: 800px;\n    }\n\n    .loading-spinner {\n      text-align: center;\n      padding: 4rem;\n    }\n\n    .quiz-header {\n      margin-bottom: 2rem;\n      background-color: #fff;\n      padding: 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n    }\n\n    .title-row {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .quiz-header h1 {\n      font-size: 1.8rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .timer {\n      display: flex;\n      align-items: center;\n      color: #d32f2f; /* Red */\n      font-weight: 500;\n    }\n\n    .timer mat-icon {\n      margin-right: 0.3rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .time-display {\n      font-size: 1.5rem;\n      font-family: 'monospace';\n    }\n\n    .progress-row {\n      display: flex;\n      justify-content: space-between;\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.8rem;\n    }\n\n    mat-progress-bar {\n      height: 8px;\n      border-radius: 4px;\n    }\n\n    .question-card {\n      margin-bottom: 2rem;\n      padding: 1.5rem;\n    }\n\n    .question-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .options-group {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .option-item {\n      padding: 0.8rem 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n    }\n\n    .option-item:hover {\n      background-color: #f5f5f5;\n      border-color: #bbb;\n    }\n\n    .option-item.mat-radio-checked {\n      border-color: #673ab7; /* Purple */\n      background-color: #ede7f6; /* Light purple */\n    }\n\n    .navigation-buttons {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 2rem;\n    }\n\n    .navigation-buttons button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .overview-card {\n      padding: 1.5rem;\n    }\n\n    .overview-card mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .question-dots {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n    }\n\n    .question-dots button {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #e0e0e0; /* Light gray */\n      color: #666;\n      font-weight: 500;\n      transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;\n    }\n\n    .question-dots button.current {\n      background-color: #673ab7; /* Purple */\n      color: white;\n    }\n\n    .question-dots button.answered {\n      background-color: #c8e6c9; /* Light green */\n      color: #388e3c; /* Dark green */\n    }\n\n    /* Results styles */\n    .results-card {\n      padding: 2rem;\n      text-align: center;\n    }\n\n    .results-header {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .result-icon-wrapper {\n      width: 64px;\n      height: 64px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 1rem;\n    }\n\n    .result-icon-wrapper mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n    }\n\n    .result-icon-wrapper.success {\n      background-color: #e8f5e9; /* Light green */\n    }\n    .result-icon-wrapper.success mat-icon {\n      color: #4caf50; /* Green */\n    }\n\n    .result-icon-wrapper.fail {\n      background-color: #ffebee; /* Light red */\n    }\n    .result-icon-wrapper.fail mat-icon {\n      color: #f44336; /* Red */\n    }\n\n    .results-card mat-card-title {\n      font-size: 2rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n    }\n\n    .results-card mat-card-subtitle {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .score-display {\n      margin-bottom: 1.5rem;\n    }\n\n    .percentage {\n      font-size: 3.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .score-count {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .results-progress {\n      margin-bottom: 1.5rem;\n    }\n\n    .certificate-info {\n      background-color: #e8f5e9;\n      border: 1px solid #c8e6c9;\n      border-radius: 8px;\n      padding: 1rem;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 0.8rem;\n      color: #388e3c;\n      font-weight: 500;\n      margin-bottom: 2rem;\n    }\n\n    .certificate-info mat-icon {\n      font-size: 1.8rem;\n      width: 1.8rem;\n      height: 1.8rem;\n    }\n\n    .results-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 2rem;\n    }\n\n    .results-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .answers-detail {\n      text-align: left;\n      margin-top: 2rem;\n      border-top: 1px solid #eee;\n      padding-top: 2rem;\n    }\n\n    .answers-detail h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .answer-item {\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 1.2rem;\n      margin-bottom: 1rem;\n      background-color: #fff;\n    }\n\n    .answer-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 0.8rem;\n    }\n\n    .answer-header h4 {\n      font-size: 1.1rem;\n      font-weight: 500;\n      color: #444;\n      margin: 0;\n    }\n\n    .correct-icon { color: #4caf50; }\n    .incorrect-icon { color: #f44336; }\n\n    .question-text {\n      font-size: 1rem;\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    .options-detail {\n      display: flex;\n      flex-direction: column;\n      gap: 0.6rem;\n    }\n\n    .option-detail-item {\n      padding: 0.6rem 1rem;\n      border-radius: 6px;\n      font-size: 0.9rem;\n      color: #555;\n      background-color: #f9f9f9;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .correct-option {\n      background-color: #e8f5e9;\n      color: #388e3c;\n      border: 1px solid #c8e6c9;\n    }\n\n    .user-incorrect-option {\n      background-color: #ffebee;\n      color: #d32f2f;\n      border: 1px solid #ffcdd2;\n    }\n\n    .correct-label {\n      color: #388e3c;\n      font-weight: 500;\n    }\n\n    .incorrect-label {\n      color: #d32f2f;\n      font-weight: 500;\n    }\n  `,\n  ],\n})\nexport class QuizComponent implements OnInit, OnDestroy {\n  quizId!: number\n  quiz!: Quiz\n  currentQuestionIndex = 0\n  selectedAnswers: { [key: number]: number } = {} // { questionId: selectedOptionIndex }\n  showResults = false\n  results: ResultatQuiz | null = null\n  isLoading = true\n  timeRemaining = 0\n  timerSubscription!: Subscription\n  currentUser!: User | null\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private quizService: QuizService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n    })\n\n    this.route.paramMap.subscribe((params) => {\n      this.quizId = Number(params.get(\"id\"))\n      this.loadQuiz()\n    })\n  }\n\n  ngOnDestroy(): void {\n    if (this.timerSubscription) {\n      this.timerSubscription.unsubscribe()\n    }\n  }\n\n  loadQuiz(): void {\n    this.isLoading = true\n    // Mock data for demonstration\n    this.quiz = {\n      id: this.quizId,\n      titre: \"Quiz - Bases de React\",\n      description: \"Testez vos connaissances sur les concepts fondamentaux de React\",\n      seuilReussite: 70,\n      dureeEstimee: 15, // in minutes\n      coursId: 1,\n      typeContenu: \"Quiz\",\n      estComplete: false,\n      estDebloque: true,\n      ordre: 3,\n      questions: [\n        {\n          id: 1,\n          texte: \"Qu'est-ce que React ?\",\n          options: [\n            \"Un framework CSS\",\n            \"Une bibliothèque JavaScript pour créer des interfaces utilisateur\",\n            \"Un serveur web\",\n            \"Un langage de programmation\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 2,\n          texte: \"Que sont les props en React ?\",\n          options: [\n            \"Des propriétés passées aux composants\",\n            \"Des méthodes de classe\",\n            \"Des variables globales\",\n            \"Des styles CSS\",\n          ],\n          bonneReponse: 0,\n        },\n        {\n          id: 3,\n          texte: \"Comment créer un composant fonctionnel en React ?\",\n          options: [\n            \"class MyComponent extends React.Component\",\n            \"function MyComponent() { return <div></div>; }\",\n            \"const MyComponent = React.createClass()\",\n            \"React.component('MyComponent')\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 4,\n          texte: \"Qu'est-ce que le JSX ?\",\n          options: [\n            \"Un nouveau langage de programmation\",\n            \"Une extension de syntaxe JavaScript\",\n            \"Un framework CSS\",\n            \"Une base de données\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 5,\n          texte: \"Comment gérer l'état dans un composant fonctionnel ?\",\n          options: [\"Avec this.state\", \"Avec le hook useState\", \"Avec des variables globales\", \"Avec localStorage\"],\n          bonneReponse: 1,\n        },\n      ],\n    }\n    this.timeRemaining = (this.quiz?.dureeEstimee || 0) * 60 // Convert minutes to seconds\n    this.startTimer()\n    this.isLoading = false\n\n    // Uncomment to fetch from API\n    /*\n    this.quizService.getQuizById(this.quizId).subscribe({\n      next: (data) => {\n        this.quiz = data;\n        this.timeRemaining = this.quiz.dureeEstimee * 60;\n        this.startTimer();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  startTimer(): void {\n    this.timerSubscription = interval(1000).subscribe(() => {\n      if (this.timeRemaining > 0 && !this.showResults) {\n        this.timeRemaining--\n      } else if (this.timeRemaining === 0 && !this.showResults) {\n        this.submitQuiz()\n        this.timerSubscription.unsubscribe()\n      }\n    })\n  }\n\n  previousQuestion(): void {\n    if (this.currentQuestionIndex > 0) {\n      this.currentQuestionIndex--\n    }\n  }\n\n  nextQuestion(): void {\n    if (this.currentQuestionIndex < this.quiz.questions.length - 1) {\n      this.currentQuestionIndex++\n    }\n  }\n\n  goToQuestion(index: number): void {\n    this.currentQuestionIndex = index\n  }\n\n  submitQuiz(): void {\n    if (!this.quiz || !this.currentUser) return\n\n    this.isLoading = true\n    this.timerSubscription.unsubscribe()\n\n    let score = 0\n    const reponses: { questionId: number; reponseChoisie: number; estCorrecte: boolean }[] = []\n\n    this.quiz.questions.forEach((question) => {\n      const selectedAnswer = this.selectedAnswers[question.id]\n      const isCorrect = selectedAnswer === question.bonneReponse\n      if (isCorrect) {\n        score++\n      }\n      reponses.push({\n        questionId: question.id,\n        reponseChoisie: selectedAnswer ?? -1,\n        estCorrecte: isCorrect,\n      })\n    })\n\n    const pourcentage = Math.round((score / this.quiz.questions.length) * 100)\n    const reussi = pourcentage >= (this.quiz.seuilReussite || 0)\n\n    const resultatQuiz: ResultatQuiz = {\n      clientId: this.currentUser.id,\n      quizId: this.quiz.id,\n      score: pourcentage, // Store percentage as score\n      dateSoumission: new Date(),\n    }\n\n    // Mock submission\n    this.results = {\n      score: score,\n      totalQuestions: this.quiz.questions.length,\n      pourcentage: pourcentage,\n      reussi: reussi,\n      reponses: reponses,\n    } as any // Cast to any because ResultatQuiz doesn't have all these properties\n    this.showResults = true\n    this.isLoading = false\n\n    // Uncomment to submit to API\n    /*\n    this.quizService.soumettreResultat(this.quiz.id, resultatQuiz).subscribe({\n      next: (res) => {\n        this.results = {\n          score: score,\n          totalQuestions: this.quiz.questions.length,\n          pourcentage: pourcentage,\n          reussi: reussi,\n          reponses: reponses\n        } as any; // Cast to any because ResultatQuiz doesn't have all these properties\n        this.showResults = true;\n        this.isLoading = false;\n        this.snackBar.open('Résultat du quiz enregistré !', 'Fermer', { duration: 3000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de la soumission du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n      }\n    });\n    */\n  }\n\n  restartQuiz(): void {\n    this.currentQuestionIndex = 0\n    this.selectedAnswers = {}\n    this.showResults = false\n    this.results = null\n    this.timeRemaining = (this.quiz?.dureeEstimee || 0) * 60\n    this.startTimer()\n  }\n\n  formatTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60)\n    const remainingSeconds = seconds % 60\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n  }\n\n  get progress(): number {\n    return ((this.currentQuestionIndex + 1) / this.quiz.questions.length) * 100\n  }\n\n  // Helper method to safely get response for a question\n  getResponse(index: number) {\n    return this.results?.reponses?.[index] || null;\n  }\n}\n", "import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // For ngModel in radio buttons\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\nimport { MatRadioModule } from \"@angular/material/radio\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { QuizComponent } from \"./quiz.component\"\n\n@NgModule({\n  declarations: [QuizComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressBarModule,\n    MatRadioModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([{ path: \":id\", component: QuizComponent }]),\n  ],\n})\nexport class QuizModule {}\n", "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n    if (period < 0) {\n        period = 0;\n    }\n    return timer(period, period, scheduler);\n}\n"], "names": ["environment", "QuizService", "constructor", "http", "apiUrl", "urlApi", "getQuiz", "get", "getQuizById", "id", "createQuiz", "quiz", "post", "updateQuiz", "put", "deleteQuiz", "delete", "soumettreResultat", "quizId", "resultat", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn", "interval", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r8", "ɵɵadvance", "ɵɵtextInterpolate1", "option_r7", "ɵɵlistener", "QuizComponent_div_1_button_25_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "nextQuestion", "ctx_r4", "selectedAnswer<PERSON>", "questions", "currentQuestionIndex", "undefined", "ɵɵelement", "QuizComponent_div_1_button_26_Template_button_click_0_listener", "_r14", "ctx_r13", "submitQuiz", "ɵɵtemplate", "QuizComponent_div_1_button_26_mat_spinner_1_Template", "QuizComponent_div_1_button_26_span_2_Template", "ctx_r5", "isLoading", "QuizComponent_div_1_button_32_Template_button_click_0_listener", "restoredCtx", "_r18", "i_r16", "index", "ctx_r17", "goToQuestion", "ɵɵpureFunction2", "_c0", "ctx_r6", "q_r15", "QuizComponent_div_1_Template_mat_radio_group_ngModelChange_20_listener", "$event", "_r20", "ctx_r19", "QuizComponent_div_1_mat_radio_button_21_Template", "QuizComponent_div_1_Template_button_click_23_listener", "ctx_r21", "previousQuestion", "QuizComponent_div_1_button_25_Template", "QuizComponent_div_1_button_26_Template", "QuizComponent_div_1_button_32_Template", "ɵɵtextInterpolate", "ctx_r0", "titre", "formatTime", "timeRemaining", "ɵɵtextInterpolate2", "length", "<PERSON>uil<PERSON><PERSON><PERSON>", "progress", "texte", "options", "QuizComponent_ng_template_2_div_1_button_21_Template_button_click_0_listener", "_r30", "ctx_r29", "restartQuiz", "QuizComponent_ng_template_2_div_1_div_25_div_9_span_2_Template", "QuizComponent_ng_template_2_div_1_div_25_div_9_span_3_Template", "_c1", "j_r37", "question_r31", "bonneReponse", "ctx_r35", "getResponse", "i_r32", "reponseChoisie", "estCorrecte", "option_r36", "QuizComponent_ng_template_2_div_1_div_25_mat_icon_4_Template", "QuizComponent_ng_template_2_div_1_div_25_mat_icon_5_Template", "QuizComponent_ng_template_2_div_1_div_25_div_9_Template", "tmp_1_0", "ctx_r28", "QuizComponent_ng_template_2_div_1_mat_icon_4_Template", "QuizComponent_ng_template_2_div_1_mat_icon_5_Template", "QuizComponent_ng_template_2_div_1_div_17_Template", "QuizComponent_ng_template_2_div_1_button_21_Template", "QuizComponent_ng_template_2_div_1_div_25_Template", "_c2", "ctx_r23", "results", "<PERSON><PERSON><PERSON>", "pourcentage", "score", "totalQuestions", "ɵɵpureFunction1", "_c3", "coursId", "QuizComponent_ng_template_2_div_0_Template", "QuizComponent_ng_template_2_div_1_Template", "ctx_r2", "showResults", "QuizComponent", "route", "router", "quizService", "authService", "snackBar", "ngOnInit", "currentUser$", "subscribe", "user", "currentUser", "paramMap", "params", "Number", "loadQuiz", "ngOnDestroy", "timerSubscription", "unsubscribe", "description", "du<PERSON><PERSON><PERSON><PERSON>", "typeContenu", "estComplete", "estDebloque", "ordre", "startTimer", "reponses", "for<PERSON>ach", "question", "<PERSON><PERSON><PERSON><PERSON>", "isCorrect", "push", "questionId", "Math", "round", "resultatQuiz", "clientId", "dateSoumission", "Date", "seconds", "minutes", "floor", "remainingSeconds", "toString", "padStart", "ɵɵdirectiveInject", "ActivatedRoute", "Router", "i2", "i3", "AuthService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "QuizComponent_Template", "rf", "ctx", "QuizComponent_div_1_Template", "QuizComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor", "_r1", "CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressBarModule", "MatRadioModule", "MatProgressSpinnerModule", "QuizModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "asyncScheduler", "timer", "period", "scheduler"], "sourceRoot": "webpack:///", "x_google_ignoreList": [3]}