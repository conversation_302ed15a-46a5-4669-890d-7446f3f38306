{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/toolbar\";\nimport * as i8 from \"@angular/material/chips\";\nfunction DashboardComponent_mat_card_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 23)(1, \"mat-card-content\")(2, \"div\", 24)(3, \"div\", 25)(4, \"p\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 27);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-icon\", 28);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r4 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(stat_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r4.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(stat_r4.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(stat_r4.icon);\n  }\n}\nfunction DashboardComponent_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 29)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Cr\\u00E9er un nouveau cours \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 30)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"people\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Voir les \\u00E9tudiants \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction DashboardComponent_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 31)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"book\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Parcourir les cours \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 32)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Voir mes certificats \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction DashboardComponent_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 33)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"admin_panel_settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" G\\u00E9rer les utilisateurs \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 34)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Voir les analyses \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n    this.stats = {\n      totalCourses: 12,\n      totalStudents: 245,\n      totalRevenue: 15420,\n      completedCertificates: 89,\n      pendingMessages: 5\n    };\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (!user) {\n        // Redirect to login if no user is logged in\n        this.router.navigate([\"/auth/login\"]);\n      }\n    });\n    // Mock user data if not logged in (for development)\n    if (!this.currentUser) {\n      const mockUser = {\n        id: 1,\n        email: \"<EMAIL>\",\n        firstName: \"John\",\n        lastName: \"Doe\",\n        nom: \"Doe\",\n        prenom: \"John\",\n        role: \"Formateur\" // Change this to 'Client' or 'Admin' to test different dashboards\n      };\n      // Simulate setting user and role in local storage for initial load\n      localStorage.setItem(\"token\", \"mock-jwt-token\");\n      localStorage.setItem(\"userRole\", mockUser.role);\n      this.authService[\"currentUserSubject\"].next(mockUser); // Directly update subject for mock\n      this.currentUser = mockUser;\n    }\n  }\n  logout() {\n    this.authService.logout();\n  }\n  getRoleTitle() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Tableau de bord Étudiant\";\n      case \"Formateur\":\n        return \"Tableau de bord Formateur\";\n      case \"Admin\":\n        return \"Tableau de bord Administrateur\";\n      default:\n        return \"Tableau de bord\";\n    }\n  }\n  getStatsForRole() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return [{\n          title: \"Cours inscrits\",\n          value: this.stats.totalCourses,\n          icon: \"book\",\n          color: \"blue\"\n        }, {\n          title: \"Certificats obtenus\",\n          value: this.stats.completedCertificates,\n          icon: \"emoji_events\",\n          color: \"yellow\"\n        }, {\n          title: \"Messages\",\n          value: this.stats.pendingMessages,\n          icon: \"message\",\n          color: \"green\"\n        }];\n      case \"Formateur\":\n        return [{\n          title: \"Cours publiés\",\n          value: this.stats.totalCourses,\n          icon: \"book\",\n          color: \"blue\"\n        }, {\n          title: \"Total étudiants\",\n          value: this.stats.totalStudents,\n          icon: \"people\",\n          color: \"purple\"\n        }, {\n          title: \"Revenus (€)\",\n          value: this.stats.totalRevenue,\n          icon: \"euro_symbol\",\n          color: \"green\"\n        }, {\n          title: \"Certificats émis\",\n          value: this.stats.completedCertificates,\n          icon: \"emoji_events\",\n          color: \"yellow\"\n        }];\n      case \"Admin\":\n        return [{\n          title: \"Total cours\",\n          value: this.stats.totalCourses,\n          icon: \"book\",\n          color: \"blue\"\n        }, {\n          title: \"Total utilisateurs\",\n          value: this.stats.totalStudents,\n          icon: \"people\",\n          color: \"purple\"\n        }, {\n          title: \"Revenus plateforme (€)\",\n          value: this.stats.totalRevenue,\n          icon: \"trending_up\",\n          color: \"green\"\n        }, {\n          title: \"Certificats générés\",\n          value: this.stats.completedCertificates,\n          icon: \"emoji_events\",\n          color: \"yellow\"\n        }];\n      default:\n        return [];\n    }\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 60,\n      vars: 6,\n      consts: [[1, \"dashboard-container\"], [\"color\", \"primary\", 1, \"dashboard-header\"], [1, \"header-content\"], [1, \"logo\"], [1, \"user-info\"], [1, \"role-chip\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"dashboard-content\"], [1, \"welcome-section\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"dashboard-grid\"], [1, \"actions-card\"], [1, \"actions-list\"], [4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/messages\", 1, \"action-btn\"], [1, \"activity-card\"], [1, \"activity-list\"], [1, \"activity-item\"], [1, \"activity-dot\", \"green\"], [1, \"activity-dot\", \"blue\"], [1, \"activity-dot\", \"yellow\"], [1, \"activity-dot\", \"purple\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-info\"], [1, \"stat-label\"], [1, \"stat-value\"], [1, \"stat-icon\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/courses/create\", 1, \"action-btn\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/dashboard/students\", 1, \"action-btn\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/courses\", 1, \"action-btn\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/certificates\", 1, \"action-btn\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/admin/users\", 1, \"action-btn\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/admin/analytics\", 1, \"action-btn\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"school\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7, \"Training Platform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 4)(9, \"mat-chip-set\")(10, \"mat-chip\", 5);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_12_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" D\\u00E9connexion \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 7)(17, \"div\", 8)(18, \"h1\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\");\n          i0.ɵɵtext(21, \"Bienvenue ! Voici un aper\\u00E7u de votre activit\\u00E9.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 9);\n          i0.ɵɵtemplate(23, DashboardComponent_mat_card_23_Template, 10, 5, \"mat-card\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 11)(25, \"mat-card\", 12)(26, \"mat-card-header\")(27, \"mat-card-title\");\n          i0.ɵɵtext(28, \"Actions rapides\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"mat-card-content\")(30, \"div\", 13);\n          i0.ɵɵtemplate(31, DashboardComponent_ng_container_31_Template, 9, 0, \"ng-container\", 14);\n          i0.ɵɵtemplate(32, DashboardComponent_ng_container_32_Template, 9, 0, \"ng-container\", 14);\n          i0.ɵɵtemplate(33, DashboardComponent_ng_container_33_Template, 9, 0, \"ng-container\", 14);\n          i0.ɵɵelementStart(34, \"button\", 15)(35, \"mat-icon\");\n          i0.ɵɵtext(36, \"message\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \" Messages \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"mat-card\", 16)(39, \"mat-card-header\")(40, \"mat-card-title\");\n          i0.ɵɵtext(41, \"Activit\\u00E9 r\\u00E9cente\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"mat-card-content\")(43, \"div\", 17)(44, \"div\", 18);\n          i0.ɵɵelement(45, \"div\", 19);\n          i0.ɵɵelementStart(46, \"p\");\n          i0.ɵɵtext(47, \"Nouvel \\u00E9tudiant inscrit \\u00E0 \\\"React Fundamentals\\\"\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 18);\n          i0.ɵɵelement(49, \"div\", 20);\n          i0.ɵɵelementStart(50, \"p\");\n          i0.ɵɵtext(51, \"Certificat g\\u00E9n\\u00E9r\\u00E9 pour John Doe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 18);\n          i0.ɵɵelement(53, \"div\", 21);\n          i0.ɵɵelementStart(54, \"p\");\n          i0.ɵɵtext(55, \"Paiement re\\u00E7u : 299\\u20AC\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"div\", 18);\n          i0.ɵɵelement(57, \"div\", 22);\n          i0.ɵɵelementStart(58, \"p\");\n          i0.ɵɵtext(59, \"Nouveau message d'un \\u00E9tudiant\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.currentUser == null ? null : ctx.currentUser.role);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getRoleTitle());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getStatsForRole());\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", (ctx.currentUser == null ? null : ctx.currentUser.role) === \"Formateur\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.currentUser == null ? null : ctx.currentUser.role) === \"Client\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.currentUser == null ? null : ctx.currentUser.role) === \"Admin\");\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardTitle, i5.MatButton, i6.MatIcon, i7.MatToolbar, i8.MatChip, i8.MatChipSet, i2.RouterLink],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.role-chip[_ngcontent-%COMP%] {\\n  text-transform: capitalize;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 2rem;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  color: #333;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n}\\n\\n.stat-icon.blue[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.stat-icon.purple[_ngcontent-%COMP%] {\\n  color: #9c27b0;\\n}\\n\\n.stat-icon.green[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.stat-icon.yellow[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n.dashboard-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 2rem;\\n}\\n\\n.actions-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  padding: 1rem;\\n  text-align: left;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n\\n.activity-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.activity-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n\\n.activity-dot.green[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.activity-dot.blue[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n}\\n\\n.activity-dot.yellow[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n}\\n\\n.activity-dot.purple[_ngcontent-%COMP%] {\\n  background-color: #9c27b0;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .dashboard-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "stat_r4", "title", "value", "ɵɵclassMap", "color", "icon", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "DashboardComponent", "constructor", "authService", "router", "currentUser", "stats", "totalCourses", "totalStudents", "totalRevenue", "completedCertificates", "pendingMessages", "ngOnInit", "currentUser$", "subscribe", "user", "navigate", "mockUser", "id", "email", "firstName", "lastName", "nom", "prenom", "role", "localStorage", "setItem", "next", "logout", "getRoleTitle", "getStatsForRole", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "DashboardComponent_Template_button_click_12_listener", "ɵɵtemplate", "DashboardComponent_mat_card_23_Template", "DashboardComponent_ng_container_31_Template", "DashboardComponent_ng_container_32_Template", "DashboardComponent_ng_container_33_Template", "ɵɵelement", "ɵɵproperty"], "sources": ["C:\\e-learning\\src\\app\\features\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { Router } from \"@angular/router\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { User } from \"../../core/models/user.model\"\n\ninterface DashboardStats {\n  totalCourses: number\n  totalStudents: number\n  totalRevenue: number\n  completedCertificates: number\n  pendingMessages: number\n}\n\n@Component({\n  selector: \"app-dashboard\",\n  template: `\n    <div class=\"dashboard-container\">\n      <!-- Header -->\n      <mat-toolbar color=\"primary\" class=\"dashboard-header\">\n        <div class=\"header-content\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <span>Training Platform</span>\n          </div>\n          <div class=\"user-info\">\n            <mat-chip-set>\n              <mat-chip class=\"role-chip\">{{ currentUser?.role }}</mat-chip>\n            </mat-chip-set>\n            <button mat-button (click)=\"logout()\">\n              <mat-icon>logout</mat-icon>\n              Déconnexion\n            </button>\n          </div>\n        </div>\n      </mat-toolbar>\n\n      <!-- Main Content -->\n      <div class=\"dashboard-content\">\n        <div class=\"welcome-section\">\n          <h1>{{ getRoleTitle() }}</h1>\n          <p>Bienvenue ! Voici un aperçu de votre activité.</p>\n        </div>\n\n        <!-- Stats Grid -->\n        <div class=\"stats-grid\">\n          <mat-card *ngFor=\"let stat of getStatsForRole()\" class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-info\">\n                  <p class=\"stat-label\">{{ stat.title }}</p>\n                  <p class=\"stat-value\">{{ stat.value }}</p>\n                </div>\n                <mat-icon [class]=\"stat.color\" class=\"stat-icon\">{{ stat.icon }}</mat-icon>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Quick Actions & Recent Activity -->\n        <div class=\"dashboard-grid\">\n          <mat-card class=\"actions-card\">\n            <mat-card-header>\n              <mat-card-title>Actions rapides</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"actions-list\">\n                <ng-container *ngIf=\"currentUser?.role === 'Formateur'\">\n                  <button mat-stroked-button routerLink=\"/courses/create\" class=\"action-btn\">\n                    <mat-icon>add</mat-icon>\n                    Créer un nouveau cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/dashboard/students\" class=\"action-btn\">\n                    <mat-icon>people</mat-icon>\n                    Voir les étudiants\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Client'\">\n                  <button mat-stroked-button routerLink=\"/courses\" class=\"action-btn\">\n                    <mat-icon>book</mat-icon>\n                    Parcourir les cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/certificates\" class=\"action-btn\">\n                    <mat-icon>emoji_events</mat-icon>\n                    Voir mes certificats\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Admin'\">\n                  <button mat-stroked-button routerLink=\"/admin/users\" class=\"action-btn\">\n                    <mat-icon>admin_panel_settings</mat-icon>\n                    Gérer les utilisateurs\n                  </button>\n                  <button mat-stroked-button routerLink=\"/admin/analytics\" class=\"action-btn\">\n                    <mat-icon>analytics</mat-icon>\n                    Voir les analyses\n                  </button>\n                </ng-container>\n                \n                <button mat-stroked-button routerLink=\"/messages\" class=\"action-btn\">\n                  <mat-icon>message</mat-icon>\n                  Messages\n                </button>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"activity-card\">\n            <mat-card-header>\n              <mat-card-title>Activité récente</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"activity-list\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot green\"></div>\n                  <p>Nouvel étudiant inscrit à \"React Fundamentals\"</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot blue\"></div>\n                  <p>Certificat généré pour John Doe</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot yellow\"></div>\n                  <p>Paiement reçu : 299€</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot purple\"></div>\n                  <p>Nouveau message d'un étudiant</p>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .dashboard-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n    }\n\n    .dashboard-header {\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n    }\n\n    .header-content {\n      width: 100%;\n      max-width: 1200px;\n      margin: 0 auto;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 1.5rem;\n      font-weight: bold;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .role-chip {\n      text-transform: capitalize;\n    }\n\n    .dashboard-content {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 2rem;\n    }\n\n    .welcome-section {\n      margin-bottom: 2rem;\n    }\n\n    .welcome-section h1 {\n      font-size: 2rem;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .welcome-section p {\n      color: #666;\n      font-size: 1.1rem;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1.5rem;\n      margin-bottom: 2rem;\n    }\n\n    .stat-card {\n      padding: 1rem;\n    }\n\n    .stat-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .stat-label {\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.5rem;\n    }\n\n    .stat-value {\n      font-size: 2rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .stat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .stat-icon.blue { color: #2196f3; }\n    .stat-icon.purple { color: #9c27b0; }\n    .stat-icon.green { color: #4caf50; }\n    .stat-icon.yellow { color: #ff9800; }\n\n    .dashboard-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 2rem;\n    }\n\n    .actions-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .action-btn {\n      justify-content: flex-start;\n      padding: 1rem;\n      text-align: left;\n    }\n\n    .action-btn mat-icon {\n      margin-right: 1rem;\n    }\n\n    .activity-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .activity-item {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .activity-dot {\n      width: 8px;\n      height: 8px;\n      border-radius: 50%;\n    }\n\n    .activity-dot.green { background-color: #4caf50; }\n    .activity-dot.blue { background-color: #2196f3; }\n    .activity-dot.yellow { background-color: #ff9800; }\n    .activity-dot.purple { background-color: #9c27b0; }\n\n    .activity-item p {\n      margin: 0;\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .dashboard-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `,\n  ],\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: User | null = null\n  stats: DashboardStats = {\n    totalCourses: 12,\n    totalStudents: 245,\n    totalRevenue: 15420,\n    completedCertificates: 89,\n    pendingMessages: 5,\n  }\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (!user) {\n        // Redirect to login if no user is logged in\n        this.router.navigate([\"/auth/login\"])\n      }\n    })\n\n    // Mock user data if not logged in (for development)\n    if (!this.currentUser) {\n      const mockUser: User = {\n        id: 1,\n        email: \"<EMAIL>\",\n        firstName: \"John\",\n        lastName: \"Doe\",\n        nom: \"Doe\",\n        prenom: \"John\",\n        role: \"Formateur\", // Change this to 'Client' or 'Admin' to test different dashboards\n      }\n      // Simulate setting user and role in local storage for initial load\n      localStorage.setItem(\"token\", \"mock-jwt-token\")\n      localStorage.setItem(\"userRole\", mockUser.role)\n      this.authService[\"currentUserSubject\"].next(mockUser) // Directly update subject for mock\n      this.currentUser = mockUser\n    }\n  }\n\n  logout(): void {\n    this.authService.logout()\n  }\n\n  getRoleTitle(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Tableau de bord Étudiant\"\n      case \"Formateur\":\n        return \"Tableau de bord Formateur\"\n      case \"Admin\":\n        return \"Tableau de bord Administrateur\"\n      default:\n        return \"Tableau de bord\"\n    }\n  }\n\n  getStatsForRole() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return [\n          { title: \"Cours inscrits\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          {\n            title: \"Certificats obtenus\",\n            value: this.stats.completedCertificates,\n            icon: \"emoji_events\",\n            color: \"yellow\",\n          },\n          { title: \"Messages\", value: this.stats.pendingMessages, icon: \"message\", color: \"green\" },\n        ]\n      case \"Formateur\":\n        return [\n          { title: \"Cours publiés\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          { title: \"Total étudiants\", value: this.stats.totalStudents, icon: \"people\", color: \"purple\" },\n          { title: \"Revenus (€)\", value: this.stats.totalRevenue, icon: \"euro_symbol\", color: \"green\" },\n          { title: \"Certificats émis\", value: this.stats.completedCertificates, icon: \"emoji_events\", color: \"yellow\" },\n        ]\n      case \"Admin\":\n        return [\n          { title: \"Total cours\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          { title: \"Total utilisateurs\", value: this.stats.totalStudents, icon: \"people\", color: \"purple\" },\n          { title: \"Revenus plateforme (€)\", value: this.stats.totalRevenue, icon: \"trending_up\", color: \"green\" },\n          {\n            title: \"Certificats générés\",\n            value: this.stats.completedCertificates,\n            icon: \"emoji_events\",\n            color: \"yellow\",\n          },\n        ]\n      default:\n        return []\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;IA6CUA,EAAA,CAAAC,cAAA,mBAAmE;IAIrCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1CH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE5CH,EAAA,CAAAC,cAAA,mBAAiD;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAHnDH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAE,KAAA,CAAgB;IAE9BR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAS,UAAA,CAAAH,OAAA,CAAAI,KAAA,CAAoB;IAAmBV,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAK,IAAA,CAAe;;;;;IAchEX,EAAA,CAAAY,uBAAA,GAAwD;IACtDZ,EAAA,CAAAC,cAAA,iBAA2E;IAC/DD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA+E;IACnED,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACXH,EAAA,CAAAa,qBAAA,EAAe;;;;;IAEfb,EAAA,CAAAY,uBAAA,GAAqD;IACnDZ,EAAA,CAAAC,cAAA,iBAAoE;IACxDD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAyE;IAC7DD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACXH,EAAA,CAAAa,qBAAA,EAAe;;;;;IAEfb,EAAA,CAAAY,uBAAA,GAAoD;IAClDZ,EAAA,CAAAC,cAAA,iBAAwE;IAC5DD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA4E;IAChED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACXH,EAAA,CAAAa,qBAAA,EAAe;;;AA0M/B,OAAM,MAAOC,kBAAkB;EAU7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAXhB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,KAAK,GAAmB;MACtBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE,KAAK;MACnBC,qBAAqB,EAAE,EAAE;MACzBC,eAAe,EAAE;KAClB;EAKE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,CAACU,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACV,WAAW,GAAGU,IAAI;MACvB,IAAI,CAACA,IAAI,EAAE;QACT;QACA,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;IAEzC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACrB,MAAMY,QAAQ,GAAS;QACrBC,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,kBAAkB;QACzBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,WAAW,CAAE;OACpB;MACD;MACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC;MAC/CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAET,QAAQ,CAACO,IAAI,CAAC;MAC/C,IAAI,CAACrB,WAAW,CAAC,oBAAoB,CAAC,CAACwB,IAAI,CAACV,QAAQ,CAAC,EAAC;MACtD,IAAI,CAACZ,WAAW,GAAGY,QAAQ;;EAE/B;EAEAW,MAAMA,CAAA;IACJ,IAAI,CAACzB,WAAW,CAACyB,MAAM,EAAE;EAC3B;EAEAC,YAAYA,CAAA;IACV,QAAQ,IAAI,CAACxB,WAAW,EAAEmB,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,0BAA0B;MACnC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,OAAO;QACV,OAAO,gCAAgC;MACzC;QACE,OAAO,iBAAiB;;EAE9B;EAEAM,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACzB,WAAW,EAAEmB,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,CACL;UAAE9B,KAAK,EAAE,gBAAgB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACC,YAAY;UAAET,IAAI,EAAE,MAAM;UAAED,KAAK,EAAE;QAAM,CAAE,EACxF;UACEH,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACI,qBAAqB;UACvCZ,IAAI,EAAE,cAAc;UACpBD,KAAK,EAAE;SACR,EACD;UAAEH,KAAK,EAAE,UAAU;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACK,eAAe;UAAEb,IAAI,EAAE,SAAS;UAAED,KAAK,EAAE;QAAO,CAAE,CAC1F;MACH,KAAK,WAAW;QACd,OAAO,CACL;UAAEH,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACC,YAAY;UAAET,IAAI,EAAE,MAAM;UAAED,KAAK,EAAE;QAAM,CAAE,EACvF;UAAEH,KAAK,EAAE,iBAAiB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACE,aAAa;UAAEV,IAAI,EAAE,QAAQ;UAAED,KAAK,EAAE;QAAQ,CAAE,EAC9F;UAAEH,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACG,YAAY;UAAEX,IAAI,EAAE,aAAa;UAAED,KAAK,EAAE;QAAO,CAAE,EAC7F;UAAEH,KAAK,EAAE,kBAAkB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACI,qBAAqB;UAAEZ,IAAI,EAAE,cAAc;UAAED,KAAK,EAAE;QAAQ,CAAE,CAC9G;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEH,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACC,YAAY;UAAET,IAAI,EAAE,MAAM;UAAED,KAAK,EAAE;QAAM,CAAE,EACrF;UAAEH,KAAK,EAAE,oBAAoB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACE,aAAa;UAAEV,IAAI,EAAE,QAAQ;UAAED,KAAK,EAAE;QAAQ,CAAE,EACjG;UAAEH,KAAK,EAAE,wBAAwB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACG,YAAY;UAAEX,IAAI,EAAE,aAAa;UAAED,KAAK,EAAE;QAAO,CAAE,EACxG;UACEH,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACI,qBAAqB;UACvCZ,IAAI,EAAE,cAAc;UACpBD,KAAK,EAAE;SACR,CACF;MACH;QACE,OAAO,EAAE;;EAEf;;;uBA/FWI,kBAAkB,EAAAd,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBlC,kBAAkB;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3R3BvD,EAAA,CAAAC,cAAA,aAAiC;UAKfD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEhCH,EAAA,CAAAC,cAAA,aAAuB;UAESD,EAAA,CAAAE,MAAA,IAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEhEH,EAAA,CAAAC,cAAA,iBAAsC;UAAnBD,EAAA,CAAAyD,UAAA,mBAAAC,qDAAA;YAAA,OAASF,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UACnCzC,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,cAA+B;UAEvBD,EAAA,CAAAE,MAAA,IAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,gEAA8C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIvDH,EAAA,CAAAC,cAAA,cAAwB;UACtBD,EAAA,CAAA2D,UAAA,KAAAC,uCAAA,wBAUW;UACb5D,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA4B;UAGND,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAElDH,EAAA,CAAAC,cAAA,wBAAkB;UAEdD,EAAA,CAAA2D,UAAA,KAAAE,2CAAA,2BASe;UAEf7D,EAAA,CAAA2D,UAAA,KAAAG,2CAAA,2BASe;UAEf9D,EAAA,CAAA2D,UAAA,KAAAI,2CAAA,2BASe;UAEf/D,EAAA,CAAAC,cAAA,kBAAqE;UACzDD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKfH,EAAA,CAAAC,cAAA,oBAAgC;UAEZD,EAAA,CAAAE,MAAA,kCAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAEnDH,EAAA,CAAAC,cAAA,wBAAkB;UAGZD,EAAA,CAAAgE,SAAA,eAAsC;UACtChE,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kEAA8C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEvDH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAgE,SAAA,eAAqC;UACrChE,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sDAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAExCH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAgE,SAAA,eAAuC;UACvChE,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7BH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAgE,SAAA,eAAuC;UACvChE,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UArGZH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAAK,iBAAA,CAAAmD,GAAA,CAAAtC,WAAA,kBAAAsC,GAAA,CAAAtC,WAAA,CAAAmB,IAAA,CAAuB;UAanDrC,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAK,iBAAA,CAAAmD,GAAA,CAAAd,YAAA,GAAoB;UAMG1C,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAiE,UAAA,YAAAT,GAAA,CAAAb,eAAA,GAAoB;UAqB1B3C,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAiE,UAAA,UAAAT,GAAA,CAAAtC,WAAA,kBAAAsC,GAAA,CAAAtC,WAAA,CAAAmB,IAAA,kBAAuC;UAWvCrC,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAiE,UAAA,UAAAT,GAAA,CAAAtC,WAAA,kBAAAsC,GAAA,CAAAtC,WAAA,CAAAmB,IAAA,eAAoC;UAWpCrC,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAiE,UAAA,UAAAT,GAAA,CAAAtC,WAAA,kBAAAsC,GAAA,CAAAtC,WAAA,CAAAmB,IAAA,cAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}