{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../core/services/course.service\";\nimport * as i4 from \"../../core/services/payment.service\";\nimport * as i5 from \"../../core/services/auth.service\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/divider\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nfunction PaymentComponent_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Format d'email invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le num\\u00E9ro de carte est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Num\\u00E9ro de carte invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Date requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Format MM/AA invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"CVV requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"CVV invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_error_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentComponent_mat_spinner_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 44);\n  }\n}\nconst _c0 = function (a0) {\n  return [a0, \"EUR\", \"symbol\", \"1.2-2\", \"fr\"];\n};\nfunction PaymentComponent_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"euro_symbol\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"currency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Payer \", i0.ɵɵpipeBindV(4, 1, i0.ɵɵpureFunction1(7, _c0, ctx_r10.course.prix)), \" \");\n  }\n}\nfunction PaymentComponent_div_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-card\", 46)(2, \"mat-card-content\")(3, \"div\", 47)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"check_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"h2\");\n    i0.ɵɵtext(7, \"Paiement r\\u00E9ussi !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Votre achat a \\u00E9t\\u00E9 trait\\u00E9 avec succ\\u00E8s. Vous allez \\u00EAtre redirig\\u00E9 vers le cours.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 48)(11, \"p\", 49);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 50);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"currency\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r11.course.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBindV(15, 2, i0.ɵɵpureFunction1(8, _c0, ctx_r11.course.prix)));\n  }\n}\nexport class PaymentComponent {\n  constructor(route, router, fb, courseService, paymentService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.fb = fb;\n    this.courseService = courseService;\n    this.paymentService = paymentService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.isProcessing = false;\n    this.paymentSuccess = false;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.route.paramMap.subscribe(params => {\n      this.courseId = Number(params.get(\"courseId\"));\n      this.loadCourseDetails();\n    });\n    this.initPaymentForm();\n  }\n  initPaymentForm() {\n    this.paymentForm = this.fb.group({\n      email: [this.currentUser?.email || \"\", [Validators.required, Validators.email]],\n      cardNumber: [\"\", [Validators.required, Validators.pattern(/^\\d{4}\\s\\d{4}\\s\\d{4}\\s\\d{4}$/)]],\n      expiryDate: [\"\", [Validators.required, Validators.pattern(/^(0[1-9]|1[0-2])\\/\\d{2}$/)]],\n      cvv: [\"\", [Validators.required, Validators.pattern(/^\\d{3}$/)]],\n      cardName: [\"\", Validators.required]\n    });\n  }\n  loadCourseDetails() {\n    // Mock data for demonstration\n    this.course = {\n      id: this.courseId,\n      titre: \"React Fundamentals\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"<EMAIL>\",\n        role: \"Formateur\"\n      },\n      contenus: [],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false\n    };\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(this.courseId).subscribe({\n      next: (data) => {\n        this.course = data;\n        this.paymentForm.patchValue({ email: this.currentUser?.email || '' });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  formatCardNumber(event) {\n    const input = event.target;\n    let value = input.value.replace(/\\s/g, \"\");\n    if (value.length > 0) {\n      value = value.match(/.{1,4}/g)?.join(\" \") || \"\";\n    }\n    this.paymentForm.get(\"cardNumber\")?.setValue(value, {\n      emitEvent: false\n    });\n  }\n  formatExpiryDate(event) {\n    const input = event.target;\n    let value = input.value.replace(/\\D/g, \"\");\n    if (value.length > 2) {\n      value = value.substring(0, 2) + \"/\" + value.substring(2, 4);\n    }\n    this.paymentForm.get(\"expiryDate\")?.setValue(value, {\n      emitEvent: false\n    });\n  }\n  formatCvv(event) {\n    const input = event.target;\n    const value = input.value.replace(/\\D/g, \"\").substring(0, 3);\n    this.paymentForm.get(\"cvv\")?.setValue(value, {\n      emitEvent: false\n    });\n  }\n  onSubmit() {\n    if (this.paymentForm.valid && this.currentUser && this.course) {\n      this.isProcessing = true;\n      const paiementData = {\n        clientId: this.currentUser.id,\n        coursId: this.course.id,\n        montant: this.course.prix\n      };\n      // Mock payment processing\n      setTimeout(() => {\n        this.isProcessing = false;\n        this.paymentSuccess = true;\n        this.snackBar.open(\"Paiement réussi !\", \"Fermer\", {\n          duration: 3000\n        });\n        setTimeout(() => {\n          this.router.navigate([\"/courses\", this.course.id]);\n        }, 3000);\n      }, 2000);\n      // Uncomment to use API\n      /*\n      this.paymentService.effectuerPaiement(paiementData).subscribe({\n        next: (response) => {\n          this.isProcessing = false;\n          this.paymentSuccess = true;\n          this.snackBar.open('Paiement réussi !', 'Fermer', { duration: 3000 });\n          setTimeout(() => {\n            this.router.navigate(['/courses', this.course.id]);\n          }, 3000);\n        },\n        error: (err) => {\n          this.isProcessing = false;\n          this.snackBar.open('Erreur lors du paiement. Veuillez réessayer.', 'Fermer', { duration: 5000 });\n          console.error(err);\n        }\n      });\n      */\n    } else {\n      this.snackBar.open(\"Veuillez remplir correctement toutes les informations de paiement.\", \"Fermer\", {\n        duration: 5000\n      });\n      this.paymentForm.markAllAsTouched();\n    }\n  }\n  get trainerEarnings() {\n    return this.course ? this.course.prix * 0.7 : 0;\n  }\n  get platformFee() {\n    return this.course ? this.course.prix * 0.3 : 0;\n  }\n  static {\n    this.ɵfac = function PaymentComponent_Factory(t) {\n      return new (t || PaymentComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.CourseService), i0.ɵɵdirectiveInject(i4.PaymentService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaymentComponent,\n      selectors: [[\"app-payment\"]],\n      decls: 137,\n      vars: 65,\n      consts: [[1, \"payment-container\"], [1, \"content-wrapper\"], [1, \"payment-form-section\"], [1, \"payment-card\"], [1, \"card-title-with-icon\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [4, \"ngIf\"], [1, \"form-divider\"], [\"matInput\", \"\", \"formControlName\", \"cardNumber\", \"placeholder\", \"1234 5678 9012 3456\", 3, \"input\"], [1, \"row-fields\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"expiryDate\", \"placeholder\", \"MM/AA\", 3, \"input\"], [\"matInput\", \"\", \"formControlName\", \"cvv\", \"placeholder\", \"123\", 3, \"input\"], [\"matInput\", \"\", \"formControlName\", \"cardName\", \"placeholder\", \"Jean Dupont\"], [1, \"secure-payment-info\"], [1, \"secure-text\"], [1, \"secure-description\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width-btn\", \"submit-btn\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"summary-section\"], [1, \"summary-card\"], [1, \"course-summary-item\"], [1, \"course-image-placeholder\"], [1, \"course-details\"], [1, \"course-instructor\"], [1, \"course-meta-summary\"], [1, \"meta-item\"], [1, \"star-icon\"], [1, \"summary-divider\"], [1, \"price-breakdown\"], [1, \"price-item\"], [1, \"price-item\", \"sub-item\"], [1, \"total-price\"], [1, \"earnings-card\"], [1, \"earnings-item\"], [1, \"trainer-earnings\"], [1, \"platform-fee\"], [1, \"earnings-item\", \"total-earnings\"], [1, \"guarantee-card\"], [1, \"guarantee-header\"], [1, \"guarantee-text\"], [\"class\", \"payment-success-overlay\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"payment-success-overlay\"], [1, \"success-card\"], [1, \"success-icon-wrapper\"], [1, \"purchased-course-info\"], [1, \"course-title\"], [1, \"course-price\"]],\n      template: function PaymentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-card\", 3)(4, \"mat-card-header\")(5, \"mat-card-title\", 4)(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"credit_card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" Informations de paiement \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function PaymentComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"mat-form-field\", 6)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Adresse e-mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 7);\n          i0.ɵɵtemplate(15, PaymentComponent_mat_error_15_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(16, PaymentComponent_mat_error_16_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"mat-divider\", 9);\n          i0.ɵɵelementStart(18, \"mat-form-field\", 6)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Num\\u00E9ro de carte\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 10);\n          i0.ɵɵlistener(\"input\", function PaymentComponent_Template_input_input_21_listener($event) {\n            return ctx.formatCardNumber($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, PaymentComponent_mat_error_22_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(23, PaymentComponent_mat_error_23_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 11)(25, \"mat-form-field\", 12)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Date d'expiration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 13);\n          i0.ɵɵlistener(\"input\", function PaymentComponent_Template_input_input_28_listener($event) {\n            return ctx.formatExpiryDate($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(29, PaymentComponent_mat_error_29_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(30, PaymentComponent_mat_error_30_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"mat-form-field\", 12)(32, \"mat-label\");\n          i0.ɵɵtext(33, \"CVV\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"input\", 14);\n          i0.ɵɵlistener(\"input\", function PaymentComponent_Template_input_input_34_listener($event) {\n            return ctx.formatCvv($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, PaymentComponent_mat_error_35_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(36, PaymentComponent_mat_error_36_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 6)(38, \"mat-label\");\n          i0.ɵɵtext(39, \"Nom sur la carte\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 15);\n          i0.ɵɵtemplate(41, PaymentComponent_mat_error_41_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 16)(43, \"mat-icon\");\n          i0.ɵɵtext(44, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"span\", 17);\n          i0.ɵɵtext(46, \"Paiement s\\u00E9curis\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"p\", 18);\n          i0.ɵɵtext(48, \"Vos informations sont prot\\u00E9g\\u00E9es par un cryptage SSL 256 bits.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"button\", 19);\n          i0.ɵɵtemplate(50, PaymentComponent_mat_spinner_50_Template, 1, 0, \"mat-spinner\", 20);\n          i0.ɵɵtemplate(51, PaymentComponent_span_51_Template, 5, 9, \"span\", 8);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(52, \"div\", 21)(53, \"mat-card\", 22)(54, \"mat-card-header\")(55, \"mat-card-title\");\n          i0.ɵɵtext(56, \"R\\u00E9sum\\u00E9 de la commande\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"mat-card-content\")(58, \"div\", 23)(59, \"div\", 24)(60, \"mat-icon\");\n          i0.ɵɵtext(61, \"credit_card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"div\", 25)(63, \"h3\");\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"p\", 26);\n          i0.ɵɵtext(66);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 27)(68, \"div\", 28)(69, \"mat-icon\");\n          i0.ɵɵtext(70, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\");\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 28)(74, \"mat-icon\");\n          i0.ɵɵtext(75, \"group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"span\");\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"div\", 28)(79, \"mat-icon\", 29);\n          i0.ɵɵtext(80, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"span\");\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(83, \"mat-divider\", 30);\n          i0.ɵɵelementStart(84, \"div\", 31)(85, \"div\", 32)(86, \"span\");\n          i0.ɵɵtext(87, \"Prix du cours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"span\");\n          i0.ɵɵtext(89);\n          i0.ɵɵpipe(90, \"currency\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 33)(92, \"span\");\n          i0.ɵɵtext(93, \"TVA incluse\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"span\");\n          i0.ɵɵtext(95, \"0\\u20AC\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(96, \"mat-divider\", 30);\n          i0.ɵɵelementStart(97, \"div\", 34)(98, \"span\");\n          i0.ɵɵtext(99, \"Total\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"span\");\n          i0.ɵɵtext(101);\n          i0.ɵɵpipe(102, \"currency\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(103, \"mat-card\", 35)(104, \"mat-card-header\")(105, \"mat-card-title\");\n          i0.ɵɵtext(106, \"R\\u00E9partition des gains\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"mat-card-content\")(108, \"div\", 36)(109, \"span\");\n          i0.ɵɵtext(110, \"Formateur (70%)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"span\", 37);\n          i0.ɵɵtext(112);\n          i0.ɵɵpipe(113, \"currency\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(114, \"div\", 36)(115, \"span\");\n          i0.ɵɵtext(116, \"Plateforme (30%)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"span\", 38);\n          i0.ɵɵtext(118);\n          i0.ɵɵpipe(119, \"currency\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(120, \"mat-divider\", 30);\n          i0.ɵɵelementStart(121, \"div\", 39)(122, \"span\");\n          i0.ɵɵtext(123, \"Total\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"span\");\n          i0.ɵɵtext(125);\n          i0.ɵɵpipe(126, \"currency\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(127, \"mat-card\", 40)(128, \"mat-card-content\")(129, \"div\", 41)(130, \"mat-icon\");\n          i0.ɵɵtext(131, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"span\");\n          i0.ɵɵtext(133, \"Garantie 30 jours\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(134, \"p\", 42);\n          i0.ɵɵtext(135, \" Si vous n'\\u00EAtes pas satisfait du cours, nous vous remboursons int\\u00E9gralement sous 30 jours. \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(136, PaymentComponent_div_136_Template, 16, 10, \"div\", 43);\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.paymentForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.paymentForm.get(\"email\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.paymentForm.get(\"email\")) == null ? null : tmp_2_0.hasError(\"email\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.paymentForm.get(\"cardNumber\")) == null ? null : tmp_3_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.paymentForm.get(\"cardNumber\")) == null ? null : tmp_4_0.hasError(\"pattern\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.paymentForm.get(\"expiryDate\")) == null ? null : tmp_5_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.paymentForm.get(\"expiryDate\")) == null ? null : tmp_6_0.hasError(\"pattern\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx.paymentForm.get(\"cvv\")) == null ? null : tmp_7_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.paymentForm.get(\"cvv\")) == null ? null : tmp_8_0.hasError(\"pattern\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx.paymentForm.get(\"cardName\")) == null ? null : tmp_9_0.hasError(\"required\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.paymentForm.invalid || ctx.isProcessing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isProcessing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isProcessing);\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate(ctx.course.titre);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\"Par \", ctx.course.formateur.prenom, \" \", ctx.course.formateur.nom, \"\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"\", ctx.course.duree, \" min\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", ctx.course.nombreEtudiants, \" \\u00E9tudiants\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.course.note);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBindV(90, 25, i0.ɵɵpureFunction1(55, _c0, ctx.course.prix)));\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBindV(102, 31, i0.ɵɵpureFunction1(57, _c0, ctx.course.prix)));\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBindV(113, 37, i0.ɵɵpureFunction1(59, _c0, ctx.trainerEarnings)));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBindV(119, 43, i0.ɵɵpureFunction1(61, _c0, ctx.platformFee)));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBindV(126, 49, i0.ɵɵpureFunction1(63, _c0, ctx.course.prix)));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.paymentSuccess);\n        }\n      },\n      dependencies: [i7.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, i9.MatButton, i10.MatIcon, i11.MatInput, i12.MatFormField, i12.MatLabel, i12.MatError, i13.MatDivider, i14.MatProgressSpinner, i7.CurrencyPipe],\n      styles: [\".payment-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n  padding: 2rem;\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1.5fr 1fr;\\n  gap: 2rem;\\n  max-width: 1200px;\\n  width: 100%;\\n}\\n\\n@media (max-width: 960px) {\\n  .content-wrapper[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.payment-card[_ngcontent-%COMP%], .summary-card[_ngcontent-%COMP%], .earnings-card[_ngcontent-%COMP%], .guarantee-card[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.card-title-with-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n}\\n\\n.card-title-with-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n\\n.row-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.form-divider[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n}\\n\\n.secure-payment-info[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd; \\n\\n  border: 1px solid #bbdefb; \\n\\n  border-radius: 8px;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n  color: #1565c0; \\n\\n}\\n\\n.secure-payment-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n\\n.secure-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n}\\n\\n.secure-description[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin-top: 0.5rem;\\n  line-height: 1.4;\\n}\\n\\n.full-width-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.8rem 1rem;\\n  font-size: 1.1rem;\\n  height: 48px;\\n}\\n\\n.full-width-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.course-summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.course-image-placeholder[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  background-color: #e1bee7; \\n\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.course-image-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  color: #8e24aa; \\n\\n}\\n\\n.course-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 0.2rem;\\n}\\n\\n.course-instructor[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.course-meta-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.8rem;\\n  font-size: 0.85rem;\\n  color: #777;\\n}\\n\\n.course-meta-summary[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.2rem;\\n}\\n\\n.course-meta-summary[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: #999;\\n}\\n\\n.course-meta-summary[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%] {\\n  color: #ffc107; \\n\\n}\\n\\n.summary-divider[_ngcontent-%COMP%] {\\n  margin: 1rem 0;\\n}\\n\\n.price-breakdown[_ngcontent-%COMP%], .earnings-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.95rem;\\n  color: #444;\\n}\\n\\n.price-breakdown[_ngcontent-%COMP%]   .sub-item[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #777;\\n}\\n\\n.total-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 1.3rem;\\n  font-weight: bold;\\n  margin-top: 1rem;\\n}\\n\\n.trainer-earnings[_ngcontent-%COMP%] {\\n  color: #388e3c; \\n\\n  font-weight: 500;\\n}\\n\\n.platform-fee[_ngcontent-%COMP%] {\\n  color: #673ab7; \\n\\n  font-weight: 500;\\n}\\n\\n.total-earnings[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: bold;\\n}\\n\\n.guarantee-card[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9; \\n\\n  border: 1px solid #c8e6c9; \\n\\n  color: #388e3c; \\n\\n}\\n\\n.guarantee-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-weight: 500;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.guarantee-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.guarantee-text[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  line-height: 1.4;\\n}\\n\\n\\n\\n.payment-success-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.6);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n\\n.success-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  max-width: 400px;\\n  width: 100%;\\n}\\n\\n.success-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background-color: #e8f5e9;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 1.5rem;\\n}\\n\\n.success-icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #4caf50;\\n}\\n\\n.success-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: bold;\\n  margin-bottom: 0.5rem;\\n  color: #333;\\n}\\n\\n.success-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.purchased-course-info[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  border-radius: 8px;\\n  padding: 1rem;\\n}\\n\\n.purchased-course-info[_ngcontent-%COMP%]   .course-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n  color: #333;\\n}\\n\\n.purchased-course-info[_ngcontent-%COMP%]   .course-price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  color: #4caf50; \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBindV", "ɵɵpureFunction1", "_c0", "ctx_r10", "course", "prix", "ɵɵtextInterpolate", "ctx_r11", "titre", "PaymentComponent", "constructor", "route", "router", "fb", "courseService", "paymentService", "authService", "snackBar", "isProcessing", "paymentSuccess", "ngOnInit", "currentUser$", "subscribe", "user", "currentUser", "paramMap", "params", "courseId", "Number", "get", "loadCourseDetails", "initPaymentForm", "paymentForm", "group", "email", "required", "cardNumber", "pattern", "expiryDate", "cvv", "cardName", "id", "description", "duree", "niveau", "formateurId", "formateur", "nom", "prenom", "role", "contenus", "nombreEtudiants", "note", "estGratuit", "formatCardNumber", "event", "input", "target", "value", "replace", "length", "match", "join", "setValue", "emitEvent", "formatExpiryDate", "substring", "formatCvv", "onSubmit", "valid", "paiementData", "clientId", "coursId", "montant", "setTimeout", "open", "duration", "navigate", "mark<PERSON>llAsTouched", "trainer<PERSON><PERSON><PERSON><PERSON>", "platformFee", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "FormBuilder", "i3", "CourseService", "i4", "PaymentService", "i5", "AuthService", "i6", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "PaymentComponent_Template", "rf", "ctx", "ɵɵlistener", "PaymentComponent_Template_form_ngSubmit_10_listener", "ɵɵtemplate", "PaymentComponent_mat_error_15_Template", "PaymentComponent_mat_error_16_Template", "PaymentComponent_Template_input_input_21_listener", "$event", "PaymentComponent_mat_error_22_Template", "PaymentComponent_mat_error_23_Template", "PaymentComponent_Template_input_input_28_listener", "PaymentComponent_mat_error_29_Template", "PaymentComponent_mat_error_30_Template", "PaymentComponent_Template_input_input_34_listener", "PaymentComponent_mat_error_35_Template", "PaymentComponent_mat_error_36_Template", "PaymentComponent_mat_error_41_Template", "PaymentComponent_mat_spinner_50_Template", "PaymentComponent_span_51_Template", "PaymentComponent_div_136_Template", "ɵɵproperty", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "invalid", "ɵɵtextInterpolate2"], "sources": ["C:\\e-learning\\src\\app\\features\\payment\\payment.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from \"@angular/forms\"\nimport { ActivatedRoute, Router } from \"@angular/router\"\nimport { CourseService } from \"../../core/services/course.service\"\nimport { PaymentService } from \"../../core/services/payment.service\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Course } from \"../../core/models/course.model\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { User } from \"../../core/models/user.model\"\n\n@Component({\n  selector: \"app-payment\",\n  template: `\n    <div class=\"payment-container\">\n      <div class=\"content-wrapper\">\n        <!-- Payment Form -->\n        <div class=\"payment-form-section\">\n          <mat-card class=\"payment-card\">\n            <mat-card-header>\n              <mat-card-title class=\"card-title-with-icon\">\n                <mat-icon>credit_card</mat-icon>\n                Informations de paiement\n              </mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <form [formGroup]=\"paymentForm\" (ngSubmit)=\"onSubmit()\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Adresse e-mail</mat-label>\n                  <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                  <mat-error *ngIf=\"paymentForm.get('email')?.hasError('required')\">L'email est requis</mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('email')?.hasError('email')\">Format d'email invalide</mat-error>\n                </mat-form-field>\n\n                <mat-divider class=\"form-divider\"></mat-divider>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Numéro de carte</mat-label>\n                  <input matInput formControlName=\"cardNumber\" placeholder=\"1234 5678 9012 3456\"\n                         (input)=\"formatCardNumber($event)\">\n                  <mat-error *ngIf=\"paymentForm.get('cardNumber')?.hasError('required')\">Le numéro de carte est requis</mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('cardNumber')?.hasError('pattern')\">Numéro de carte invalide</mat-error>\n                </mat-form-field>\n\n                <div class=\"row-fields\">\n                  <mat-form-field appearance=\"outline\" class=\"half-width\">\n                    <mat-label>Date d'expiration</mat-label>\n                    <input matInput formControlName=\"expiryDate\" placeholder=\"MM/AA\"\n                           (input)=\"formatExpiryDate($event)\">\n                    <mat-error *ngIf=\"paymentForm.get('expiryDate')?.hasError('required')\">Date requise</mat-error>\n                    <mat-error *ngIf=\"paymentForm.get('expiryDate')?.hasError('pattern')\">Format MM/AA invalide</mat-error>\n                  </mat-form-field>\n                  <mat-form-field appearance=\"outline\" class=\"half-width\">\n                    <mat-label>CVV</mat-label>\n                    <input matInput formControlName=\"cvv\" placeholder=\"123\"\n                           (input)=\"formatCvv($event)\">\n                    <mat-error *ngIf=\"paymentForm.get('cvv')?.hasError('required')\">CVV requis</mat-error>\n                    <mat-error *ngIf=\"paymentForm.get('cvv')?.hasError('pattern')\">CVV invalide</mat-error>\n                  </mat-form-field>\n                </div>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Nom sur la carte</mat-label>\n                  <input matInput formControlName=\"cardName\" placeholder=\"Jean Dupont\">\n                  <mat-error *ngIf=\"paymentForm.get('cardName')?.hasError('required')\">Le nom est requis</mat-error>\n                </mat-form-field>\n\n                <div class=\"secure-payment-info\">\n                  <mat-icon>lock</mat-icon>\n                  <span class=\"secure-text\">Paiement sécurisé</span>\n                  <p class=\"secure-description\">Vos informations sont protégées par un cryptage SSL 256 bits.</p>\n                </div>\n\n                <button mat-raised-button color=\"primary\" type=\"submit\" \n                        [disabled]=\"paymentForm.invalid || isProcessing\" class=\"full-width-btn submit-btn\">\n                  <mat-spinner diameter=\"20\" *ngIf=\"isProcessing\"></mat-spinner>\n                  <span *ngIf=\"!isProcessing\">\n                    <mat-icon>euro_symbol</mat-icon>\n                    Payer {{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}\n                  </span>\n                </button>\n              </form>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Order Summary & Details -->\n        <div class=\"summary-section\">\n          <mat-card class=\"summary-card\">\n            <mat-card-header>\n              <mat-card-title>Résumé de la commande</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"course-summary-item\">\n                <div class=\"course-image-placeholder\">\n                  <mat-icon>credit_card</mat-icon>\n                </div>\n                <div class=\"course-details\">\n                  <h3>{{ course.titre }}</h3>\n                  <p class=\"course-instructor\">Par {{ course.formateur.prenom }} {{ course.formateur.nom }}</p>\n                  <div class=\"course-meta-summary\">\n                    <div class=\"meta-item\">\n                      <mat-icon>schedule</mat-icon>\n                      <span>{{ course.duree }} min</span>\n                    </div>\n                    <div class=\"meta-item\">\n                      <mat-icon>group</mat-icon>\n                      <span>{{ course.nombreEtudiants }} étudiants</span>\n                    </div>\n                    <div class=\"meta-item\">\n                      <mat-icon class=\"star-icon\">star</mat-icon>\n                      <span>{{ course.note }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <mat-divider class=\"summary-divider\"></mat-divider>\n\n              <div class=\"price-breakdown\">\n                <div class=\"price-item\">\n                  <span>Prix du cours</span>\n                  <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n                </div>\n                <div class=\"price-item sub-item\">\n                  <span>TVA incluse</span>\n                  <span>0€</span>\n                </div>\n              </div>\n\n              <mat-divider class=\"summary-divider\"></mat-divider>\n\n              <div class=\"total-price\">\n                <span>Total</span>\n                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Earnings Distribution -->\n          <mat-card class=\"earnings-card\">\n            <mat-card-header>\n              <mat-card-title>Répartition des gains</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"earnings-item\">\n                <span>Formateur (70%)</span>\n                <span class=\"trainer-earnings\">{{ trainerEarnings | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <div class=\"earnings-item\">\n                <span>Plateforme (30%)</span>\n                <span class=\"platform-fee\">{{ platformFee | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <mat-divider class=\"summary-divider\"></mat-divider>\n              <div class=\"earnings-item total-earnings\">\n                <span>Total</span>\n                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Guarantee -->\n          <mat-card class=\"guarantee-card\">\n            <mat-card-content>\n              <div class=\"guarantee-header\">\n                <mat-icon>check_circle</mat-icon>\n                <span>Garantie 30 jours</span>\n              </div>\n              <p class=\"guarantee-text\">\n                Si vous n'êtes pas satisfait du cours, nous vous remboursons intégralement sous 30 jours.\n              </p>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"payment-success-overlay\" *ngIf=\"paymentSuccess\">\n      <mat-card class=\"success-card\">\n        <mat-card-content>\n          <div class=\"success-icon-wrapper\">\n            <mat-icon>check_circle</mat-icon>\n          </div>\n          <h2>Paiement réussi !</h2>\n          <p>Votre achat a été traité avec succès. Vous allez être redirigé vers le cours.</p>\n          <div class=\"purchased-course-info\">\n            <p class=\"course-title\">{{ course.titre }}</p>\n            <p class=\"course-price\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [\n    `\n    .payment-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      padding: 2rem;\n      display: flex;\n      justify-content: center;\n      align-items: flex-start;\n    }\n\n    .content-wrapper {\n      display: grid;\n      grid-template-columns: 1.5fr 1fr;\n      gap: 2rem;\n      max-width: 1200px;\n      width: 100%;\n    }\n\n    @media (max-width: 960px) {\n      .content-wrapper {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    .payment-card, .summary-card, .earnings-card, .guarantee-card {\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .card-title-with-icon {\n      display: flex;\n      align-items: center;\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .card-title-with-icon mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .row-fields {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n    }\n\n    .form-divider {\n      margin: 1.5rem 0;\n    }\n\n    .secure-payment-info {\n      background-color: #e3f2fd; /* Light blue */\n      border: 1px solid #bbdefb; /* Lighter blue */\n      border-radius: 8px;\n      padding: 1rem;\n      margin-bottom: 1.5rem;\n      color: #1565c0; /* Darker blue */\n    }\n\n    .secure-payment-info mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n    }\n\n    .secure-text {\n      font-weight: 500;\n      font-size: 0.95rem;\n    }\n\n    .secure-description {\n      font-size: 0.85rem;\n      margin-top: 0.5rem;\n      line-height: 1.4;\n    }\n\n    .full-width-btn {\n      width: 100%;\n      padding: 0.8rem 1rem;\n      font-size: 1.1rem;\n      height: 48px;\n    }\n\n    .full-width-btn mat-icon {\n      margin-right: 0.5rem;\n    }\n\n    .submit-btn mat-spinner {\n      margin-right: 0.5rem;\n    }\n\n    .summary-section {\n      display: flex;\n      flex-direction: column;\n      gap: 1.5rem;\n    }\n\n    .course-summary-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .course-image-placeholder {\n      width: 64px;\n      height: 64px;\n      background-color: #e1bee7; /* Light purple */\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-shrink: 0;\n    }\n\n    .course-image-placeholder mat-icon {\n      font-size: 2.5rem;\n      width: 2.5rem;\n      height: 2.5rem;\n      color: #8e24aa; /* Dark purple */\n    }\n\n    .course-details h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .course-instructor {\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-meta-summary {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.8rem;\n      font-size: 0.85rem;\n      color: #777;\n    }\n\n    .course-meta-summary .meta-item {\n      display: flex;\n      align-items: center;\n      gap: 0.2rem;\n    }\n\n    .course-meta-summary .meta-item mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      color: #999;\n    }\n\n    .course-meta-summary .meta-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .summary-divider {\n      margin: 1rem 0;\n    }\n\n    .price-breakdown, .earnings-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 0.5rem;\n      font-size: 0.95rem;\n      color: #444;\n    }\n\n    .price-breakdown .sub-item {\n      font-size: 0.85rem;\n      color: #777;\n    }\n\n    .total-price {\n      display: flex;\n      justify-content: space-between;\n      font-size: 1.3rem;\n      font-weight: bold;\n      margin-top: 1rem;\n    }\n\n    .trainer-earnings {\n      color: #388e3c; /* Green */\n      font-weight: 500;\n    }\n\n    .platform-fee {\n      color: #673ab7; /* Purple */\n      font-weight: 500;\n    }\n\n    .total-earnings {\n      font-size: 1.1rem;\n      font-weight: bold;\n    }\n\n    .guarantee-card {\n      background-color: #e8f5e9; /* Light green */\n      border: 1px solid #c8e6c9; /* Lighter green */\n      color: #388e3c; /* Dark green */\n    }\n\n    .guarantee-header {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 500;\n      margin-bottom: 0.5rem;\n    }\n\n    .guarantee-header mat-icon {\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .guarantee-text {\n      font-size: 0.85rem;\n      line-height: 1.4;\n    }\n\n    /* Payment Success Overlay */\n    .payment-success-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.6);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 1000;\n    }\n\n    .success-card {\n      text-align: center;\n      padding: 2rem;\n      max-width: 400px;\n      width: 100%;\n    }\n\n    .success-icon-wrapper {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background-color: #e8f5e9;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 1.5rem;\n    }\n\n    .success-icon-wrapper mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      color: #4caf50;\n    }\n\n    .success-card h2 {\n      font-size: 1.8rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .success-card p {\n      font-size: 1rem;\n      color: #666;\n      margin-bottom: 1.5rem;\n    }\n\n    .purchased-course-info {\n      background-color: #f5f5f5;\n      border-radius: 8px;\n      padding: 1rem;\n    }\n\n    .purchased-course-info .course-title {\n      font-weight: 500;\n      font-size: 1.1rem;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .purchased-course-info .course-price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #4caf50; /* Green */\n    }\n  `,\n  ],\n})\nexport class PaymentComponent implements OnInit {\n  courseId!: number\n  course!: Course\n  paymentForm!: FormGroup\n  isProcessing = false\n  paymentSuccess = false\n  currentUser!: User | null\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private fb: FormBuilder,\n    private courseService: CourseService,\n    private paymentService: PaymentService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n    })\n\n    this.route.paramMap.subscribe((params) => {\n      this.courseId = Number(params.get(\"courseId\"))\n      this.loadCourseDetails()\n    })\n\n    this.initPaymentForm()\n  }\n\n  initPaymentForm(): void {\n    this.paymentForm = this.fb.group({\n      email: [this.currentUser?.email || \"\", [Validators.required, Validators.email]],\n      cardNumber: [\"\", [Validators.required, Validators.pattern(/^\\d{4}\\s\\d{4}\\s\\d{4}\\s\\d{4}$/)]],\n      expiryDate: [\"\", [Validators.required, Validators.pattern(/^(0[1-9]|1[0-2])\\/\\d{2}$/)]],\n      cvv: [\"\", [Validators.required, Validators.pattern(/^\\d{3}$/)]],\n      cardName: [\"\", Validators.required],\n    })\n  }\n\n  loadCourseDetails(): void {\n    // Mock data for demonstration\n    this.course = {\n      id: this.courseId,\n      titre: \"React Fundamentals\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\", email: \"<EMAIL>\", role: \"Formateur\" },\n      contenus: [],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n    }\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(this.courseId).subscribe({\n      next: (data) => {\n        this.course = data;\n        this.paymentForm.patchValue({ email: this.currentUser?.email || '' });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  formatCardNumber(event: Event): void {\n    const input = event.target as HTMLInputElement\n    let value = input.value.replace(/\\s/g, \"\")\n    if (value.length > 0) {\n      value = value.match(/.{1,4}/g)?.join(\" \") || \"\"\n    }\n    this.paymentForm.get(\"cardNumber\")?.setValue(value, { emitEvent: false })\n  }\n\n  formatExpiryDate(event: Event): void {\n    const input = event.target as HTMLInputElement\n    let value = input.value.replace(/\\D/g, \"\")\n    if (value.length > 2) {\n      value = value.substring(0, 2) + \"/\" + value.substring(2, 4)\n    }\n    this.paymentForm.get(\"expiryDate\")?.setValue(value, { emitEvent: false })\n  }\n\n  formatCvv(event: Event): void {\n    const input = event.target as HTMLInputElement\n    const value = input.value.replace(/\\D/g, \"\").substring(0, 3)\n    this.paymentForm.get(\"cvv\")?.setValue(value, { emitEvent: false })\n  }\n\n  onSubmit(): void {\n    if (this.paymentForm.valid && this.currentUser && this.course) {\n      this.isProcessing = true\n      const paiementData = {\n        clientId: this.currentUser.id,\n        coursId: this.course.id,\n        montant: this.course.prix,\n      }\n\n      // Mock payment processing\n      setTimeout(() => {\n        this.isProcessing = false\n        this.paymentSuccess = true\n        this.snackBar.open(\"Paiement réussi !\", \"Fermer\", { duration: 3000 })\n        setTimeout(() => {\n          this.router.navigate([\"/courses\", this.course.id])\n        }, 3000)\n      }, 2000)\n\n      // Uncomment to use API\n      /*\n      this.paymentService.effectuerPaiement(paiementData).subscribe({\n        next: (response) => {\n          this.isProcessing = false;\n          this.paymentSuccess = true;\n          this.snackBar.open('Paiement réussi !', 'Fermer', { duration: 3000 });\n          setTimeout(() => {\n            this.router.navigate(['/courses', this.course.id]);\n          }, 3000);\n        },\n        error: (err) => {\n          this.isProcessing = false;\n          this.snackBar.open('Erreur lors du paiement. Veuillez réessayer.', 'Fermer', { duration: 5000 });\n          console.error(err);\n        }\n      });\n      */\n    } else {\n      this.snackBar.open(\"Veuillez remplir correctement toutes les informations de paiement.\", \"Fermer\", {\n        duration: 5000,\n      })\n      this.paymentForm.markAllAsTouched()\n    }\n  }\n\n  get trainerEarnings(): number {\n    return this.course ? this.course.prix * 0.7 : 0\n  }\n\n  get platformFee(): number {\n    return this.course ? this.course.prix * 0.3 : 0\n  }\n}\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;IA4BjDC,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAChGH,EAAA,CAAAC,cAAA,gBAA+D;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASlGH,EAAA,CAAAC,cAAA,gBAAuE;IAAAD,EAAA,CAAAE,MAAA,yCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAChHH,EAAA,CAAAC,cAAA,gBAAsE;IAAAD,EAAA,CAAAE,MAAA,oCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQxGH,EAAA,CAAAC,cAAA,gBAAuE;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC/FH,EAAA,CAAAC,cAAA,gBAAsE;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMvGH,EAAA,CAAAC,cAAA,gBAAgE;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACtFH,EAAA,CAAAC,cAAA,gBAA+D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOzFH,EAAA,CAAAC,cAAA,gBAAqE;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWlGH,EAAA,CAAAI,SAAA,sBAA8D;;;;;;;;IAC9DJ,EAAA,CAAAC,cAAA,WAA4B;IAChBD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,YAAAN,EAAA,CAAAO,WAAA,OAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,MAAA,CAAAC,IAAA,QACF;;;;;IAkGdZ,EAAA,CAAAC,cAAA,cAA4D;IAI1CD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kHAA6E;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACpFH,EAAA,CAAAC,cAAA,eAAmC;IACTD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9CH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAwD;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAD5DH,EAAA,CAAAK,SAAA,IAAkB;IAAlBL,EAAA,CAAAa,iBAAA,CAAAC,OAAA,CAAAH,MAAA,CAAAI,KAAA,CAAkB;IAClBf,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAO,WAAA,QAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAAK,OAAA,CAAAH,MAAA,CAAAC,IAAA,GAAwD;;;AA8T5F,OAAM,MAAOI,gBAAgB;EAQ3BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,EAAe,EACfC,aAA4B,EAC5BC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IANrB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAXlB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,cAAc,GAAG,KAAK;EAWnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACC,WAAW,GAAGD,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACZ,KAAK,CAACc,QAAQ,CAACH,SAAS,CAAEI,MAAM,IAAI;MACvC,IAAI,CAACC,QAAQ,GAAGC,MAAM,CAACF,MAAM,CAACG,GAAG,CAAC,UAAU,CAAC,CAAC;MAC9C,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACC,WAAW,GAAG,IAAI,CAACnB,EAAE,CAACoB,KAAK,CAAC;MAC/BC,KAAK,EAAE,CAAC,IAAI,CAACV,WAAW,EAAEU,KAAK,IAAI,EAAE,EAAE,CAAC1C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC0C,KAAK,CAAC,CAAC;MAC/EE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC6C,OAAO,CAAC,8BAA8B,CAAC,CAAC,CAAC;MAC3FC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC6C,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;MACvFE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC/C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC6C,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;MAC/DG,QAAQ,EAAE,CAAC,EAAE,EAAEhD,UAAU,CAAC2C,QAAQ;KACnC,CAAC;EACJ;EAEAL,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC1B,MAAM,GAAG;MACZqC,EAAE,EAAE,IAAI,CAACd,QAAQ;MACjBnB,KAAK,EAAE,oBAAoB;MAC3BkC,WAAW,EAAE,kFAAkF;MAC/FrC,IAAI,EAAE,KAAK;MACXsC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,UAAU;MAClBC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAEL,EAAE,EAAE,CAAC;QAAEM,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEd,KAAK,EAAE,yBAAyB;QAAEe,IAAI,EAAE;MAAW,CAAE;MACxGC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTC,UAAU,EAAE;KACb;IAED;IACA;;;;;;;;;;;;;EAaF;;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC1C,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MACpBF,KAAK,GAAGA,KAAK,CAACG,KAAK,CAAC,SAAS,CAAC,EAAEC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;;IAEjD,IAAI,CAAC9B,WAAW,CAACH,GAAG,CAAC,YAAY,CAAC,EAAEkC,QAAQ,CAACL,KAAK,EAAE;MAAEM,SAAS,EAAE;IAAK,CAAE,CAAC;EAC3E;EAEAC,gBAAgBA,CAACV,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC1C,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MACpBF,KAAK,GAAGA,KAAK,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGR,KAAK,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE7D,IAAI,CAAClC,WAAW,CAACH,GAAG,CAAC,YAAY,CAAC,EAAEkC,QAAQ,CAACL,KAAK,EAAE;MAAEM,SAAS,EAAE;IAAK,CAAE,CAAC;EAC3E;EAEAG,SAASA,CAACZ,KAAY;IACpB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,MAAMC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACO,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAClC,WAAW,CAACH,GAAG,CAAC,KAAK,CAAC,EAAEkC,QAAQ,CAACL,KAAK,EAAE;MAAEM,SAAS,EAAE;IAAK,CAAE,CAAC;EACpE;EAEAI,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpC,WAAW,CAACqC,KAAK,IAAI,IAAI,CAAC7C,WAAW,IAAI,IAAI,CAACpB,MAAM,EAAE;MAC7D,IAAI,CAACc,YAAY,GAAG,IAAI;MACxB,MAAMoD,YAAY,GAAG;QACnBC,QAAQ,EAAE,IAAI,CAAC/C,WAAW,CAACiB,EAAE;QAC7B+B,OAAO,EAAE,IAAI,CAACpE,MAAM,CAACqC,EAAE;QACvBgC,OAAO,EAAE,IAAI,CAACrE,MAAM,CAACC;OACtB;MAED;MACAqE,UAAU,CAAC,MAAK;QACd,IAAI,CAACxD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACF,QAAQ,CAAC0D,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACrEF,UAAU,CAAC,MAAK;UACd,IAAI,CAAC9D,MAAM,CAACiE,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACzE,MAAM,CAACqC,EAAE,CAAC,CAAC;QACpD,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,IAAI,CAAC;MAER;MACA;;;;;;;;;;;;;;;;;KAiBD,MAAM;MACL,IAAI,CAACxB,QAAQ,CAAC0D,IAAI,CAAC,oEAAoE,EAAE,QAAQ,EAAE;QACjGC,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAAC5C,WAAW,CAAC8C,gBAAgB,EAAE;;EAEvC;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC3E,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,GAAG,GAAG,GAAG,CAAC;EACjD;EAEA,IAAI2E,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC5E,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,GAAG,GAAG,GAAG,CAAC;EACjD;;;uBArJWI,gBAAgB,EAAAhB,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1F,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA3F,EAAA,CAAAwF,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA7F,EAAA,CAAAwF,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA/F,EAAA,CAAAwF,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAjG,EAAA,CAAAwF,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAAnG,EAAA,CAAAwF,iBAAA,CAAAY,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBrF,gBAAgB;MAAAsF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3ezB5G,EAAA,CAAAC,cAAA,aAA+B;UAOTD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,iCACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAEnBH,EAAA,CAAAC,cAAA,uBAAkB;UACgBD,EAAA,CAAA8G,UAAA,sBAAAC,oDAAA;YAAA,OAAYF,GAAA,CAAAlC,QAAA,EAAU;UAAA,EAAC;UACrD3E,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAI,SAAA,gBAAmF;UACnFJ,EAAA,CAAAgH,UAAA,KAAAC,sCAAA,uBAAgG;UAChGjH,EAAA,CAAAgH,UAAA,KAAAE,sCAAA,uBAAkG;UACpGlH,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAI,SAAA,sBAAgD;UAEhDJ,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,iBAC0C;UAAnCD,EAAA,CAAA8G,UAAA,mBAAAK,kDAAAC,MAAA;YAAA,OAASP,GAAA,CAAAhD,gBAAA,CAAAuD,MAAA,CAAwB;UAAA,EAAC;UADzCpH,EAAA,CAAAG,YAAA,EAC0C;UAC1CH,EAAA,CAAAgH,UAAA,KAAAK,sCAAA,uBAAgH;UAChHrH,EAAA,CAAAgH,UAAA,KAAAM,sCAAA,uBAA0G;UAC5GtH,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,eAAwB;UAETD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,iBAC0C;UAAnCD,EAAA,CAAA8G,UAAA,mBAAAS,kDAAAH,MAAA;YAAA,OAASP,GAAA,CAAArC,gBAAA,CAAA4C,MAAA,CAAwB;UAAA,EAAC;UADzCpH,EAAA,CAAAG,YAAA,EAC0C;UAC1CH,EAAA,CAAAgH,UAAA,KAAAQ,sCAAA,uBAA+F;UAC/FxH,EAAA,CAAAgH,UAAA,KAAAS,sCAAA,uBAAuG;UACzGzH,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC1BH,EAAA,CAAAC,cAAA,iBACmC;UAA5BD,EAAA,CAAA8G,UAAA,mBAAAY,kDAAAN,MAAA;YAAA,OAASP,GAAA,CAAAnC,SAAA,CAAA0C,MAAA,CAAiB;UAAA,EAAC;UADlCpH,EAAA,CAAAG,YAAA,EACmC;UACnCH,EAAA,CAAAgH,UAAA,KAAAW,sCAAA,uBAAsF;UACtF3H,EAAA,CAAAgH,UAAA,KAAAY,sCAAA,uBAAuF;UACzF5H,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAI,SAAA,iBAAqE;UACrEJ,EAAA,CAAAgH,UAAA,KAAAa,sCAAA,uBAAkG;UACpG7H,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,eAAiC;UACrBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,mCAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClDH,EAAA,CAAAC,cAAA,aAA8B;UAAAD,EAAA,CAAAE,MAAA,+EAA6D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGjGH,EAAA,CAAAC,cAAA,kBAC2F;UACzFD,EAAA,CAAAgH,UAAA,KAAAc,wCAAA,0BAA8D;UAC9D9H,EAAA,CAAAgH,UAAA,KAAAe,iCAAA,kBAGO;UACT/H,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,eAA6B;UAGPD,EAAA,CAAAE,MAAA,uCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAExDH,EAAA,CAAAC,cAAA,wBAAkB;UAGFD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAElCH,EAAA,CAAAC,cAAA,eAA4B;UACtBD,EAAA,CAAAE,MAAA,IAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,aAA6B;UAAAD,EAAA,CAAAE,MAAA,IAA4D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7FH,EAAA,CAAAC,cAAA,eAAiC;UAEnBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAErCH,EAAA,CAAAC,cAAA,eAAuB;UACXD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAErDH,EAAA,CAAAC,cAAA,eAAuB;UACOD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3CH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMtCH,EAAA,CAAAI,SAAA,uBAAmD;UAEnDJ,EAAA,CAAAC,cAAA,eAA6B;UAEnBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAwD;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvEH,EAAA,CAAAC,cAAA,eAAiC;UACzBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAInBH,EAAA,CAAAI,SAAA,uBAAmD;UAEnDJ,EAAA,CAAAC,cAAA,eAAyB;UACjBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,KAAwD;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAM3EH,EAAA,CAAAC,cAAA,qBAAgC;UAEZD,EAAA,CAAAE,MAAA,mCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAExDH,EAAA,CAAAC,cAAA,yBAAkB;UAERD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAE,MAAA,KAA4D;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEpGH,EAAA,CAAAC,cAAA,gBAA2B;UACnBD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7BH,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,KAAwD;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5FH,EAAA,CAAAI,SAAA,wBAAmD;UACnDJ,EAAA,CAAAC,cAAA,gBAA0C;UAClCD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,KAAwD;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAM3EH,EAAA,CAAAC,cAAA,qBAAiC;UAGjBD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjCH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEhCH,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAAE,MAAA,8GACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAOdH,EAAA,CAAAgH,UAAA,MAAAgB,iCAAA,oBAcM;;;;;;;;;;;;UArKUhI,EAAA,CAAAK,SAAA,IAAyB;UAAzBL,EAAA,CAAAiI,UAAA,cAAApB,GAAA,CAAAtE,WAAA,CAAyB;UAIfvC,EAAA,CAAAK,SAAA,GAAoD;UAApDL,EAAA,CAAAiI,UAAA,UAAAC,OAAA,GAAArB,GAAA,CAAAtE,WAAA,CAAAH,GAAA,4BAAA8F,OAAA,CAAAC,QAAA,aAAoD;UACpDnI,EAAA,CAAAK,SAAA,GAAiD;UAAjDL,EAAA,CAAAiI,UAAA,UAAAG,OAAA,GAAAvB,GAAA,CAAAtE,WAAA,CAAAH,GAAA,4BAAAgG,OAAA,CAAAD,QAAA,UAAiD;UASjDnI,EAAA,CAAAK,SAAA,GAAyD;UAAzDL,EAAA,CAAAiI,UAAA,UAAAI,OAAA,GAAAxB,GAAA,CAAAtE,WAAA,CAAAH,GAAA,iCAAAiG,OAAA,CAAAF,QAAA,aAAyD;UACzDnI,EAAA,CAAAK,SAAA,GAAwD;UAAxDL,EAAA,CAAAiI,UAAA,UAAAK,OAAA,GAAAzB,GAAA,CAAAtE,WAAA,CAAAH,GAAA,iCAAAkG,OAAA,CAAAH,QAAA,YAAwD;UAQtDnI,EAAA,CAAAK,SAAA,GAAyD;UAAzDL,EAAA,CAAAiI,UAAA,UAAAM,OAAA,GAAA1B,GAAA,CAAAtE,WAAA,CAAAH,GAAA,iCAAAmG,OAAA,CAAAJ,QAAA,aAAyD;UACzDnI,EAAA,CAAAK,SAAA,GAAwD;UAAxDL,EAAA,CAAAiI,UAAA,UAAAO,OAAA,GAAA3B,GAAA,CAAAtE,WAAA,CAAAH,GAAA,iCAAAoG,OAAA,CAAAL,QAAA,YAAwD;UAMxDnI,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAiI,UAAA,UAAAQ,OAAA,GAAA5B,GAAA,CAAAtE,WAAA,CAAAH,GAAA,0BAAAqG,OAAA,CAAAN,QAAA,aAAkD;UAClDnI,EAAA,CAAAK,SAAA,GAAiD;UAAjDL,EAAA,CAAAiI,UAAA,UAAAS,OAAA,GAAA7B,GAAA,CAAAtE,WAAA,CAAAH,GAAA,0BAAAsG,OAAA,CAAAP,QAAA,YAAiD;UAOnDnI,EAAA,CAAAK,SAAA,GAAuD;UAAvDL,EAAA,CAAAiI,UAAA,UAAAU,OAAA,GAAA9B,GAAA,CAAAtE,WAAA,CAAAH,GAAA,+BAAAuG,OAAA,CAAAR,QAAA,aAAuD;UAU7DnI,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAAiI,UAAA,aAAApB,GAAA,CAAAtE,WAAA,CAAAqG,OAAA,IAAA/B,GAAA,CAAApF,YAAA,CAAgD;UAC1BzB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAiI,UAAA,SAAApB,GAAA,CAAApF,YAAA,CAAkB;UACvCzB,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAiI,UAAA,UAAApB,GAAA,CAAApF,YAAA,CAAmB;UAsBtBzB,EAAA,CAAAK,SAAA,IAAkB;UAAlBL,EAAA,CAAAa,iBAAA,CAAAgG,GAAA,CAAAlG,MAAA,CAAAI,KAAA,CAAkB;UACOf,EAAA,CAAAK,SAAA,GAA4D;UAA5DL,EAAA,CAAA6I,kBAAA,SAAAhC,GAAA,CAAAlG,MAAA,CAAA0C,SAAA,CAAAE,MAAA,OAAAsD,GAAA,CAAAlG,MAAA,CAAA0C,SAAA,CAAAC,GAAA,KAA4D;UAI/EtD,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,kBAAA,KAAAuG,GAAA,CAAAlG,MAAA,CAAAuC,KAAA,SAAsB;UAItBlD,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAM,kBAAA,KAAAuG,GAAA,CAAAlG,MAAA,CAAA+C,eAAA,oBAAsC;UAItC1D,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAa,iBAAA,CAAAgG,GAAA,CAAAlG,MAAA,CAAAgD,IAAA,CAAiB;UAWrB3D,EAAA,CAAAK,SAAA,GAAwD;UAAxDL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAO,WAAA,SAAAP,EAAA,CAAAQ,eAAA,KAAAC,GAAA,EAAAoG,GAAA,CAAAlG,MAAA,CAAAC,IAAA,GAAwD;UAY1DZ,EAAA,CAAAK,SAAA,IAAwD;UAAxDL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAO,WAAA,UAAAP,EAAA,CAAAQ,eAAA,KAAAC,GAAA,EAAAoG,GAAA,CAAAlG,MAAA,CAAAC,IAAA,GAAwD;UAa/BZ,EAAA,CAAAK,SAAA,IAA4D;UAA5DL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAO,WAAA,UAAAP,EAAA,CAAAQ,eAAA,KAAAC,GAAA,EAAAoG,GAAA,CAAAvB,eAAA,GAA4D;UAIhEtF,EAAA,CAAAK,SAAA,GAAwD;UAAxDL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAO,WAAA,UAAAP,EAAA,CAAAQ,eAAA,KAAAC,GAAA,EAAAoG,GAAA,CAAAtB,WAAA,GAAwD;UAK7EvF,EAAA,CAAAK,SAAA,GAAwD;UAAxDL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAO,WAAA,UAAAP,EAAA,CAAAQ,eAAA,KAAAC,GAAA,EAAAoG,GAAA,CAAAlG,MAAA,CAAAC,IAAA,GAAwD;UAqBpCZ,EAAA,CAAAK,SAAA,IAAoB;UAApBL,EAAA,CAAAiI,UAAA,SAAApB,GAAA,CAAAnF,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}