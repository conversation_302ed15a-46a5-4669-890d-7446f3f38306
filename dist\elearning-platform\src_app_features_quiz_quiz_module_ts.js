"use strict";
(self["webpackChunkelearning_platform"] = self["webpackChunkelearning_platform"] || []).push([["src_app_features_quiz_quiz_module_ts"],{

/***/ 7101:
/*!***********************************************!*\
  !*** ./src/app/core/services/quiz.service.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuizService: () => (/* binding */ QuizService)
/* harmony export */ });
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common/http */ 6443);



class QuizService {
  constructor(http) {
    this.http = http;
    this.apiUrl = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlApi}quiz`;
  }
  // GET: Tous les quiz (correspond à GET /api/quiz)
  getQuiz() {
    return this.http.get(this.apiUrl);
  }
  // GET: Un quiz par ID (correspond à GET /api/quiz/{id})
  getQuizById(id) {
    return this.http.get(`${this.apiUrl}/${id}`);
  }
  // POST: Créer un quiz (correspond à POST /api/quiz)
  createQuiz(quiz) {
    return this.http.post(this.apiUrl, quiz);
  }
  // PUT: Modifier un quiz (correspond à PUT /api/quiz/{id})
  updateQuiz(id, quiz) {
    return this.http.put(`${this.apiUrl}/${id}`, quiz);
  }
  // DELETE: Supprimer un quiz (correspond à DELETE /api/quiz/{id})
  deleteQuiz(id) {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
  // POST: Soumettre résultat de quiz (correspond à POST /api/quiz/{id}/soumettre)
  soumettreResultat(quizId, resultat) {
    return this.http.post(`${this.apiUrl}/${quizId}/soumettre`, resultat);
  }
  static {
    this.ɵfac = function QuizService_Factory(t) {
      return new (t || QuizService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: QuizService,
      factory: QuizService.ɵfac,
      providedIn: "root"
    });
  }
}

/***/ }),

/***/ 1890:
/*!*************************************************!*\
  !*** ./src/app/features/quiz/quiz.component.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuizComponent: () => (/* binding */ QuizComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ 9240);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _core_services_quiz_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../core/services/quiz.service */ 7101);
/* harmony import */ var _core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core/services/auth.service */ 8010);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/snack-bar */ 3347);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/progress-bar */ 6354);
/* harmony import */ var _angular_material_radio__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/radio */ 3804);
/* harmony import */ var _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/progress-spinner */ 1134);














function QuizComponent_div_1_mat_radio_button_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-radio-button", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const option_r7 = ctx.$implicit;
    const i_r8 = ctx.index;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", i_r8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", option_r7, " ");
  }
}
function QuizComponent_div_1_button_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function QuizComponent_div_1_button_25_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r10);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r9.nextQuestion());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Question suivante ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", ctx_r4.selectedAnswers[ctx_r4.quiz.questions[ctx_r4.currentQuestionIndex].id] === undefined);
  }
}
function QuizComponent_div_1_button_26_mat_spinner_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](0, "mat-spinner", 27);
  }
}
function QuizComponent_div_1_button_26_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Terminer le quiz");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function QuizComponent_div_1_button_26_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function QuizComponent_div_1_button_26_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r13.submitQuiz());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, QuizComponent_div_1_button_26_mat_spinner_1_Template, 1, 0, "mat-spinner", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, QuizComponent_div_1_button_26_span_2_Template, 2, 0, "span", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", ctx_r5.selectedAnswers[ctx_r5.quiz.questions[ctx_r5.currentQuestionIndex].id] === undefined || ctx_r5.isLoading);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r5.isLoading);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx_r5.isLoading);
  }
}
const _c0 = function (a0, a1) {
  return {
    "current": a0,
    "answered": a1
  };
};
function QuizComponent_div_1_button_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function QuizComponent_div_1_button_32_Template_button_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r18);
      const i_r16 = restoredCtx.index;
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r17.goToQuestion(i_r16));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const q_r15 = ctx.$implicit;
    const i_r16 = ctx.index;
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction2"](2, _c0, i_r16 === ctx_r6.currentQuestionIndex, ctx_r6.selectedAnswers[q_r15.id] !== undefined && i_r16 !== ctx_r6.currentQuestionIndex));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", i_r16 + 1, " ");
  }
}
function QuizComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 3)(1, "div", 4)(2, "div", 5)(3, "h1");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 6)(6, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, "timer");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "span", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "div", 8)(11, "span", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "span", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](15, "mat-progress-bar", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](16, "mat-card", 12)(17, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "mat-card-content")(20, "mat-radio-group", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("ngModelChange", function QuizComponent_div_1_Template_mat_radio_group_ngModelChange_20_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r20);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r19.selectedAnswers[ctx_r19.quiz.questions[ctx_r19.currentQuestionIndex].id] = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](21, QuizComponent_div_1_mat_radio_button_21_Template, 2, 2, "mat-radio-button", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "div", 15)(23, "button", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function QuizComponent_div_1_Template_button_click_23_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r20);
      const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r21.previousQuestion());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](24, " Question pr\u00E9c\u00E9dente ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](25, QuizComponent_div_1_button_25_Template, 2, 1, "button", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](26, QuizComponent_div_1_button_26_Template, 3, 3, "button", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](27, "mat-card", 19)(28, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](29, "Aper\u00E7u des questions");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](30, "mat-card-content")(31, "div", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](32, QuizComponent_div_1_button_32_Template, 2, 5, "button", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.quiz.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.formatTime(ctx_r0.timeRemaining));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"]("Question ", ctx_r0.currentQuestionIndex + 1, " sur ", ctx_r0.quiz.questions.length, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("Seuil de r\u00E9ussite: ", ctx_r0.quiz.seuilReussite, "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx_r0.progress);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.quiz.questions[ctx_r0.currentQuestionIndex].texte);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngModel", ctx_r0.selectedAnswers[ctx_r0.quiz.questions[ctx_r0.currentQuestionIndex].id]);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r0.quiz.questions[ctx_r0.currentQuestionIndex].options);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", ctx_r0.currentQuestionIndex === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r0.currentQuestionIndex < ctx_r0.quiz.questions.length - 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r0.currentQuestionIndex === ctx_r0.quiz.questions.length - 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r0.quiz.questions);
  }
}
function QuizComponent_ng_template_2_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "mat-spinner");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Chargement du quiz...");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function QuizComponent_ng_template_2_div_1_mat_icon_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "check_circle");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function QuizComponent_ng_template_2_div_1_mat_icon_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "cancel");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function QuizComponent_ng_template_2_div_1_div_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 47)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "emoji_events");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "Un certificat sera g\u00E9n\u00E9r\u00E9 automatiquement !");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function QuizComponent_ng_template_2_div_1_button_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function QuizComponent_ng_template_2_div_1_button_21_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r30);
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r29.restartQuiz());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Recommencer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function QuizComponent_ng_template_2_div_1_div_25_mat_icon_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-icon", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "check_circle");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function QuizComponent_ng_template_2_div_1_div_25_mat_icon_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-icon", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "cancel");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function QuizComponent_ng_template_2_div_1_div_25_div_9_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "span", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "\u2713 Bonne r\u00E9ponse");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function QuizComponent_ng_template_2_div_1_div_25_div_9_span_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "span", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "\u2717 Votre r\u00E9ponse");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
const _c1 = function (a0, a1) {
  return {
    "correct-option": a0,
    "user-incorrect-option": a1
  };
};
function QuizComponent_ng_template_2_div_1_div_25_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, QuizComponent_ng_template_2_div_1_div_25_div_9_span_2_Template, 2, 0, "span", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](3, QuizComponent_ng_template_2_div_1_div_25_div_9_span_3_Template, 2, 0, "span", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const option_r36 = ctx.$implicit;
    const j_r37 = ctx.index;
    const ctx_r40 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    const question_r31 = ctx_r40.$implicit;
    const i_r32 = ctx_r40.index;
    const ctx_r35 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction2"](4, _c1, j_r37 === question_r31.bonneReponse, ctx_r35.getResponse(i_r32) && j_r37 === ctx_r35.getResponse(i_r32).reponseChoisie && !ctx_r35.getResponse(i_r32).estCorrecte));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", option_r36, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", j_r37 === question_r31.bonneReponse);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r35.getResponse(i_r32) && j_r37 === ctx_r35.getResponse(i_r32).reponseChoisie && !ctx_r35.getResponse(i_r32).estCorrecte);
  }
}
function QuizComponent_ng_template_2_div_1_div_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 49)(1, "div", 50)(2, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, QuizComponent_ng_template_2_div_1_div_25_mat_icon_4_Template, 2, 0, "mat-icon", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](5, QuizComponent_ng_template_2_div_1_div_25_mat_icon_5_Template, 2, 0, "mat-icon", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "p", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "div", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](9, QuizComponent_ng_template_2_div_1_div_25_div_9_Template, 4, 7, "div", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const question_r31 = ctx.$implicit;
    const i_r32 = ctx.index;
    const ctx_r28 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](3);
    let tmp_1_0;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("Question ", i_r32 + 1, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", (tmp_1_0 = ctx_r28.getResponse(i_r32)) == null ? null : tmp_1_0.estCorrecte);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r28.getResponse(i_r32) && !ctx_r28.getResponse(i_r32).estCorrecte);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](question_r31.texte);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", question_r31.options);
  }
}
const _c2 = function (a0, a1) {
  return {
    "success": a0,
    "fail": a1
  };
};
const _c3 = function (a1) {
  return ["/courses", a1];
};
function QuizComponent_ng_template_2_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 32)(1, "mat-card", 33)(2, "mat-card-header", 34)(3, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, QuizComponent_ng_template_2_div_1_mat_icon_4_Template, 2, 0, "mat-icon", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](5, QuizComponent_ng_template_2_div_1_mat_icon_5_Template, 2, 0, "mat-icon", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "mat-card-subtitle");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "mat-card-content", 36)(11, "div", 37)(12, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "p", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](16, "mat-progress-bar", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](17, QuizComponent_ng_template_2_div_1_div_17_Template, 5, 0, "div", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "div", 42)(19, "button", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](20, " Retour au cours ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](21, QuizComponent_ng_template_2_div_1_button_21_Template, 2, 0, "button", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "div", 45)(23, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](24, "D\u00E9tail des r\u00E9ponses :");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](25, QuizComponent_ng_template_2_div_1_div_25_Template, 10, 5, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction2"](13, _c2, ctx_r23.results.reussi, !ctx_r23.results.reussi));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r23.results.reussi);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx_r23.results.reussi);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r23.results.reussi ? "F\u00E9licitations !" : "Quiz non r\u00E9ussi");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r23.results.reussi ? "Vous avez r\u00E9ussi le quiz avec succ\u00E8s !" : "Il vous faut " + ctx_r23.quiz.seuilReussite + "% pour r\u00E9ussir ce quiz.", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r23.results.pourcentage, "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"]("", ctx_r23.results.score, " sur ", ctx_r23.results.totalQuestions, " questions correctes");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx_r23.results.pourcentage);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r23.results.reussi);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction1"](16, _c3, ctx_r23.quiz.coursId));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx_r23.results.reussi);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r23.quiz.questions);
  }
}
function QuizComponent_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](0, QuizComponent_ng_template_2_div_0_Template, 4, 0, "div", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, QuizComponent_ng_template_2_div_1_Template, 26, 18, "div", 30);
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r2.isLoading);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx_r2.isLoading && ctx_r2.showResults && ctx_r2.results);
  }
}
class QuizComponent {
  constructor(route, router, quizService, authService, snackBar) {
    this.route = route;
    this.router = router;
    this.quizService = quizService;
    this.authService = authService;
    this.snackBar = snackBar;
    this.currentQuestionIndex = 0;
    this.selectedAnswers = {}; // { questionId: selectedOptionIndex }
    this.showResults = false;
    this.results = null;
    this.isLoading = true;
    this.timeRemaining = 0;
  }
  ngOnInit() {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
    this.route.paramMap.subscribe(params => {
      this.quizId = Number(params.get("id"));
      this.loadQuiz();
    });
  }
  ngOnDestroy() {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
  }
  loadQuiz() {
    this.isLoading = true;
    // Mock data for demonstration
    this.quiz = {
      id: this.quizId,
      titre: "Quiz - Bases de React",
      description: "Testez vos connaissances sur les concepts fondamentaux de React",
      seuilReussite: 70,
      dureeEstimee: 15,
      coursId: 1,
      typeContenu: "Quiz",
      estComplete: false,
      estDebloque: true,
      ordre: 3,
      questions: [{
        id: 1,
        texte: "Qu'est-ce que React ?",
        options: ["Un framework CSS", "Une bibliothèque JavaScript pour créer des interfaces utilisateur", "Un serveur web", "Un langage de programmation"],
        bonneReponse: 1
      }, {
        id: 2,
        texte: "Que sont les props en React ?",
        options: ["Des propriétés passées aux composants", "Des méthodes de classe", "Des variables globales", "Des styles CSS"],
        bonneReponse: 0
      }, {
        id: 3,
        texte: "Comment créer un composant fonctionnel en React ?",
        options: ["class MyComponent extends React.Component", "function MyComponent() { return <div></div>; }", "const MyComponent = React.createClass()", "React.component('MyComponent')"],
        bonneReponse: 1
      }, {
        id: 4,
        texte: "Qu'est-ce que le JSX ?",
        options: ["Un nouveau langage de programmation", "Une extension de syntaxe JavaScript", "Un framework CSS", "Une base de données"],
        bonneReponse: 1
      }, {
        id: 5,
        texte: "Comment gérer l'état dans un composant fonctionnel ?",
        options: ["Avec this.state", "Avec le hook useState", "Avec des variables globales", "Avec localStorage"],
        bonneReponse: 1
      }]
    };
    this.timeRemaining = (this.quiz?.dureeEstimee || 0) * 60; // Convert minutes to seconds
    this.startTimer();
    this.isLoading = false;
    // Uncomment to fetch from API
    /*
    this.quizService.getQuizById(this.quizId).subscribe({
      next: (data) => {
        this.quiz = data;
        this.timeRemaining = this.quiz.dureeEstimee * 60;
        this.startTimer();
        this.isLoading = false;
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement du quiz.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.isLoading = false;
        this.router.navigate(['/courses']);
      }
    });
    */
  }

  startTimer() {
    this.timerSubscription = (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.interval)(1000).subscribe(() => {
      if (this.timeRemaining > 0 && !this.showResults) {
        this.timeRemaining--;
      } else if (this.timeRemaining === 0 && !this.showResults) {
        this.submitQuiz();
        this.timerSubscription.unsubscribe();
      }
    });
  }
  previousQuestion() {
    if (this.currentQuestionIndex > 0) {
      this.currentQuestionIndex--;
    }
  }
  nextQuestion() {
    if (this.currentQuestionIndex < this.quiz.questions.length - 1) {
      this.currentQuestionIndex++;
    }
  }
  goToQuestion(index) {
    this.currentQuestionIndex = index;
  }
  submitQuiz() {
    if (!this.quiz || !this.currentUser) return;
    this.isLoading = true;
    this.timerSubscription.unsubscribe();
    let score = 0;
    const reponses = [];
    this.quiz.questions.forEach(question => {
      const selectedAnswer = this.selectedAnswers[question.id];
      const isCorrect = selectedAnswer === question.bonneReponse;
      if (isCorrect) {
        score++;
      }
      reponses.push({
        questionId: question.id,
        reponseChoisie: selectedAnswer ?? -1,
        estCorrecte: isCorrect
      });
    });
    const pourcentage = Math.round(score / this.quiz.questions.length * 100);
    const reussi = pourcentage >= (this.quiz.seuilReussite || 0);
    const resultatQuiz = {
      clientId: this.currentUser.id,
      quizId: this.quiz.id,
      score: pourcentage,
      dateSoumission: new Date()
    };
    // Mock submission
    this.results = {
      score: score,
      totalQuestions: this.quiz.questions.length,
      pourcentage: pourcentage,
      reussi: reussi,
      reponses: reponses
    }; // Cast to any because ResultatQuiz doesn't have all these properties
    this.showResults = true;
    this.isLoading = false;
    // Uncomment to submit to API
    /*
    this.quizService.soumettreResultat(this.quiz.id, resultatQuiz).subscribe({
      next: (res) => {
        this.results = {
          score: score,
          totalQuestions: this.quiz.questions.length,
          pourcentage: pourcentage,
          reussi: reussi,
          reponses: reponses
        } as any; // Cast to any because ResultatQuiz doesn't have all these properties
        this.showResults = true;
        this.isLoading = false;
        this.snackBar.open('Résultat du quiz enregistré !', 'Fermer', { duration: 3000 });
      },
      error: (err) => {
        this.snackBar.open('Erreur lors de la soumission du quiz.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.isLoading = false;
      }
    });
    */
  }

  restartQuiz() {
    this.currentQuestionIndex = 0;
    this.selectedAnswers = {};
    this.showResults = false;
    this.results = null;
    this.timeRemaining = (this.quiz?.dureeEstimee || 0) * 60;
    this.startTimer();
  }
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }
  get progress() {
    return (this.currentQuestionIndex + 1) / this.quiz.questions.length * 100;
  }
  // Helper method to safely get response for a question
  getResponse(index) {
    return this.results?.reponses?.[index] || null;
  }
  static {
    this.ɵfac = function QuizComponent_Factory(t) {
      return new (t || QuizComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_quiz_service__WEBPACK_IMPORTED_MODULE_0__.QuizService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_5__.MatSnackBar));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: QuizComponent,
      selectors: [["app-quiz"]],
      decls: 4,
      vars: 2,
      consts: [[1, "quiz-container"], ["class", "quiz-wrapper", 4, "ngIf", "ngIfElse"], ["resultsOrLoading", ""], [1, "quiz-wrapper"], [1, "quiz-header"], [1, "title-row"], [1, "timer"], [1, "time-display"], [1, "progress-row"], [1, "question-count"], [1, "threshold"], ["mode", "determinate", 3, "value"], [1, "question-card"], ["aria-label", "S\u00E9lectionnez une option", 1, "options-group", 3, "ngModel", "ngModelChange"], ["class", "option-item", 3, "value", 4, "ngFor", "ngForOf"], [1, "navigation-buttons"], ["mat-stroked-button", "", 3, "disabled", "click"], ["mat-raised-button", "", "color", "primary", 3, "disabled", "click", 4, "ngIf"], ["mat-raised-button", "", "color", "accent", 3, "disabled", "click", 4, "ngIf"], [1, "overview-card"], [1, "question-dots"], ["mat-mini-fab", "", 3, "ngClass", "click", 4, "ngFor", "ngForOf"], [1, "option-item", 3, "value"], ["mat-raised-button", "", "color", "primary", 3, "disabled", "click"], ["mat-raised-button", "", "color", "accent", 3, "disabled", "click"], ["diameter", "20", 4, "ngIf"], [4, "ngIf"], ["diameter", "20"], ["mat-mini-fab", "", 3, "ngClass", "click"], ["class", "loading-spinner", 4, "ngIf"], ["class", "results-container", 4, "ngIf"], [1, "loading-spinner"], [1, "results-container"], [1, "results-card"], [1, "results-header"], [1, "result-icon-wrapper", 3, "ngClass"], [1, "results-content"], [1, "score-display"], [1, "percentage"], [1, "score-count"], ["mode", "determinate", 1, "results-progress", 3, "value"], ["class", "certificate-info", 4, "ngIf"], [1, "results-actions"], ["mat-stroked-button", "", 3, "routerLink"], ["mat-raised-button", "", "color", "primary", 3, "click", 4, "ngIf"], [1, "answers-detail"], ["class", "answer-item", 4, "ngFor", "ngForOf"], [1, "certificate-info"], ["mat-raised-button", "", "color", "primary", 3, "click"], [1, "answer-item"], [1, "answer-header"], ["class", "correct-icon", 4, "ngIf"], ["class", "incorrect-icon", 4, "ngIf"], [1, "question-text"], [1, "options-detail"], ["class", "option-detail-item", 3, "ngClass", 4, "ngFor", "ngForOf"], [1, "correct-icon"], [1, "incorrect-icon"], [1, "option-detail-item", 3, "ngClass"], ["class", "correct-label", 4, "ngIf"], ["class", "incorrect-label", 4, "ngIf"], [1, "correct-label"], [1, "incorrect-label"]],
      template: function QuizComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, QuizComponent_div_1_Template, 33, 13, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, QuizComponent_ng_template_2_Template, 2, 2, "ng-template", null, 2, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.quiz && !ctx.showResults)("ngIfElse", _r1);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgModel, _angular_material_card__WEBPACK_IMPORTED_MODULE_8__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_8__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_8__.MatCardHeader, _angular_material_card__WEBPACK_IMPORTED_MODULE_8__.MatCardSubtitle, _angular_material_card__WEBPACK_IMPORTED_MODULE_8__.MatCardTitle, _angular_material_button__WEBPACK_IMPORTED_MODULE_9__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_9__.MatMiniFabButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_10__.MatIcon, _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_11__.MatProgressBar, _angular_material_radio__WEBPACK_IMPORTED_MODULE_12__.MatRadioGroup, _angular_material_radio__WEBPACK_IMPORTED_MODULE_12__.MatRadioButton, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_13__.MatProgressSpinner, _angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterLink],
      styles: [".quiz-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n}\n\n.quiz-wrapper[_ngcontent-%COMP%], .results-container[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 800px;\n}\n\n.loading-spinner[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 4rem;\n}\n\n.quiz-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  background-color: #fff;\n  padding: 1.5rem;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.title-row[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.quiz-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 1.8rem;\n  font-weight: bold;\n  color: #333;\n  margin: 0;\n}\n\n.timer[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  color: #d32f2f; \n\n  font-weight: 500;\n}\n\n.timer[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  margin-right: 0.3rem;\n  font-size: 1.5rem;\n  width: 1.5rem;\n  height: 1.5rem;\n}\n\n.time-display[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-family: \"monospace\";\n}\n\n.progress-row[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.8rem;\n}\n\nmat-progress-bar[_ngcontent-%COMP%] {\n  height: 8px;\n  border-radius: 4px;\n}\n\n.question-card[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n}\n\n.question-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.4rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n  color: #333;\n}\n\n.options-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.option-item[_ngcontent-%COMP%] {\n  padding: 0.8rem 1rem;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background-color: #fff;\n  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n}\n\n.option-item[_ngcontent-%COMP%]:hover {\n  background-color: #f5f5f5;\n  border-color: #bbb;\n}\n\n.option-item.mat-radio-checked[_ngcontent-%COMP%] {\n  border-color: #673ab7; \n\n  background-color: #ede7f6; \n\n}\n\n.navigation-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 2rem;\n}\n\n.navigation-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  padding: 0.8rem 1.5rem;\n  font-size: 1rem;\n}\n\n.overview-card[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n}\n\n.overview-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n}\n\n.question-dots[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n}\n\n.question-dots[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #e0e0e0; \n\n  color: #666;\n  font-weight: 500;\n  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;\n}\n\n.question-dots[_ngcontent-%COMP%]   button.current[_ngcontent-%COMP%] {\n  background-color: #673ab7; \n\n  color: white;\n}\n\n.question-dots[_ngcontent-%COMP%]   button.answered[_ngcontent-%COMP%] {\n  background-color: #c8e6c9; \n\n  color: #388e3c; \n\n}\n\n\n\n.results-card[_ngcontent-%COMP%] {\n  padding: 2rem;\n  text-align: center;\n}\n\n.results-header[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.result-icon-wrapper[_ngcontent-%COMP%] {\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 1rem;\n}\n\n.result-icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n}\n\n.result-icon-wrapper.success[_ngcontent-%COMP%] {\n  background-color: #e8f5e9; \n\n}\n\n.result-icon-wrapper.success[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #4caf50; \n\n}\n\n.result-icon-wrapper.fail[_ngcontent-%COMP%] {\n  background-color: #ffebee; \n\n}\n\n.result-icon-wrapper.fail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #f44336; \n\n}\n\n.results-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  font-weight: bold;\n  margin-bottom: 0.5rem;\n}\n\n.results-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: #666;\n}\n\n.score-display[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n\n.percentage[_ngcontent-%COMP%] {\n  font-size: 3.5rem;\n  font-weight: bold;\n  color: #673ab7; \n\n}\n\n.score-count[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: #666;\n}\n\n.results-progress[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n\n.certificate-info[_ngcontent-%COMP%] {\n  background-color: #e8f5e9;\n  border: 1px solid #c8e6c9;\n  border-radius: 8px;\n  padding: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.8rem;\n  color: #388e3c;\n  font-weight: 500;\n  margin-bottom: 2rem;\n}\n\n.certificate-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1.8rem;\n  width: 1.8rem;\n  height: 1.8rem;\n}\n\n.results-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-bottom: 2rem;\n}\n\n.results-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  padding: 0.8rem 1.5rem;\n  font-size: 1rem;\n}\n\n.answers-detail[_ngcontent-%COMP%] {\n  text-align: left;\n  margin-top: 2rem;\n  border-top: 1px solid #eee;\n  padding-top: 2rem;\n}\n\n.answers-detail[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n  color: #333;\n}\n\n.answer-item[_ngcontent-%COMP%] {\n  border: 1px solid #eee;\n  border-radius: 8px;\n  padding: 1.2rem;\n  margin-bottom: 1rem;\n  background-color: #fff;\n}\n\n.answer-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.8rem;\n}\n\n.answer-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  font-weight: 500;\n  color: #444;\n  margin: 0;\n}\n\n.correct-icon[_ngcontent-%COMP%] {\n  color: #4caf50;\n}\n\n.incorrect-icon[_ngcontent-%COMP%] {\n  color: #f44336;\n}\n\n.question-text[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: #333;\n  margin-bottom: 1rem;\n}\n\n.options-detail[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.6rem;\n}\n\n.option-detail-item[_ngcontent-%COMP%] {\n  padding: 0.6rem 1rem;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  color: #555;\n  background-color: #f9f9f9;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.correct-option[_ngcontent-%COMP%] {\n  background-color: #e8f5e9;\n  color: #388e3c;\n  border: 1px solid #c8e6c9;\n}\n\n.user-incorrect-option[_ngcontent-%COMP%] {\n  background-color: #ffebee;\n  color: #d32f2f;\n  border: 1px solid #ffcdd2;\n}\n\n.correct-label[_ngcontent-%COMP%] {\n  color: #388e3c;\n  font-weight: 500;\n}\n\n.incorrect-label[_ngcontent-%COMP%] {\n  color: #d32f2f;\n  font-weight: 500;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 181:
/*!**********************************************!*\
  !*** ./src/app/features/quiz/quiz.module.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuizModule: () => (/* binding */ QuizModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/progress-bar */ 6354);
/* harmony import */ var _angular_material_radio__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/radio */ 3804);
/* harmony import */ var _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/progress-spinner */ 1134);
/* harmony import */ var _quiz_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quiz.component */ 1890);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


 // For ngModel in radio buttons
// Angular Material









class QuizModule {
  static {
    this.ɵfac = function QuizModule_Factory(t) {
      return new (t || QuizModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: QuizModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormsModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_4__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_5__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIconModule, _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_7__.MatProgressBarModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_8__.MatRadioModule, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_9__.MatProgressSpinnerModule, _angular_router__WEBPACK_IMPORTED_MODULE_10__.RouterModule.forChild([{
        path: ":id",
        component: _quiz_component__WEBPACK_IMPORTED_MODULE_0__.QuizComponent
      }])]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](QuizModule, {
    declarations: [_quiz_component__WEBPACK_IMPORTED_MODULE_0__.QuizComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormsModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_4__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_5__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIconModule, _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_7__.MatProgressBarModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_8__.MatRadioModule, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_9__.MatProgressSpinnerModule, _angular_router__WEBPACK_IMPORTED_MODULE_10__.RouterModule]
  });
})();

/***/ }),

/***/ 9240:
/*!********************************************************************!*\
  !*** ./node_modules/rxjs/dist/esm/internal/observable/interval.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   interval: () => (/* binding */ interval)
/* harmony export */ });
/* harmony import */ var _scheduler_async__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../scheduler/async */ 8473);
/* harmony import */ var _timer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timer */ 4876);


function interval(period = 0, scheduler = _scheduler_async__WEBPACK_IMPORTED_MODULE_0__.asyncScheduler) {
  if (period < 0) {
    period = 0;
  }
  return (0,_timer__WEBPACK_IMPORTED_MODULE_1__.timer)(period, period, scheduler);
}

/***/ })

}]);
//# sourceMappingURL=src_app_features_quiz_quiz_module_ts.js.map