{"version": 3, "file": "src_app_features_messages_messages_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AAG+D;;;AAMzD,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,kEAAW,CAACK,MAAM,UAAU;EAET;EAEvC;EACAC,cAAcA,CAACC,OAAgB;IAC7B,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAS,IAAI,CAACJ,MAAM,EAAEG,OAAO,CAAC;EACrD;EAEA;EACAE,WAAWA,CAACC,GAAW,EAAEC,GAAW;IAClC,OAAO,IAAI,CAACR,IAAI,CAACS,GAAG,CAAY,GAAG,IAAI,CAACR,MAAM,UAAUM,GAAG,IAAIC,GAAG,EAAE,CAAC;EACvE;;;uBAbWV,cAAc,EAAAY,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAdd,cAAc;MAAAgB,OAAA,EAAdhB,cAAc,CAAAiB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC+CNN,4DAAA,2BAA+D;IAC7BA,oDAAA,GAAsB;IAAAA,0DAAA,EAAW;;;;IAAjCA,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAY,OAAA,CAAAC,WAAA,CAAsB;;;;;;;;;;;IAjB1Db,4DAAA,wBAEoG;IADrFA,wDAAA,mBAAAe,2EAAA;MAAA,MAAAC,WAAA,GAAAhB,2DAAA,CAAAkB,GAAA;MAAA,MAAAN,OAAA,GAAAI,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAApB,2DAAA;MAAA,OAASA,yDAAA,CAAAoB,MAAA,CAAAG,kBAAA,CAAAX,OAAA,CAAAY,aAAA,CAAsC;IAAA,EAAC;IAE7DxB,4DAAA,cAAkD;IAC1CA,oDAAA,GAAuC;IAAAA,0DAAA,EAAO;IAEtDA,4DAAA,cAAiD;IACzCA,oDAAA,GAA0B;IAAAA,0DAAA,EAAO;IACvCA,4DAAA,uBAAkB;IAC2CA,oDAAA,GAA0B;IAAAA,0DAAA,EAAW;IAGpGA,4DAAA,eAAuD;IACvBA,oDAAA,IAA6B;IAAAA,0DAAA,EAAO;IAClEA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAsC;IAAAA,0DAAA,EAAO;IAE1EA,wDAAA,KAAA0B,+DAAA,+BAEmB;IACnB1B,uDAAA,mBAA2B;IAC7BA,0DAAA,EAAgB;;;;;IAlBDA,wDAAA,YAAAA,6DAAA,IAAA8B,GAAA,EAAAC,MAAA,CAAAC,sBAAA,KAAApB,OAAA,CAAAY,aAAA,EAAoF;IAEzFxB,uDAAA,GAAuC;IAAvCA,+DAAA,CAAA+B,MAAA,CAAAE,WAAA,CAAArB,OAAA,CAAAsB,eAAA,EAAuC;IAGvClC,uDAAA,GAA0B;IAA1BA,+DAAA,CAAAY,OAAA,CAAAsB,eAAA,CAA0B;IAEpBlC,uDAAA,GAAgD;IAAhDA,wDAAA,CAAA+B,MAAA,CAAAK,gBAAA,CAAAxB,OAAA,CAAAyB,eAAA,EAAgD;IAACrC,uDAAA,GAA0B;IAA1BA,+DAAA,CAAAY,OAAA,CAAAyB,eAAA,CAA0B;IAIzDrC,uDAAA,GAA6B;IAA7BA,+DAAA,CAAAY,OAAA,CAAA0B,kBAAA,CAA6B;IAChCtC,uDAAA,GAAsC;IAAtCA,+DAAA,CAAA+B,MAAA,CAAAQ,UAAA,CAAA3B,OAAA,CAAA4B,eAAA,EAAsC;IAEhDxC,uDAAA,GAA0B;IAA1BA,wDAAA,SAAAY,OAAA,CAAAC,WAAA,KAA0B;;;;;IAK/Cb,4DAAA,cAAyE;IAC7DA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAW;IACxCA,4DAAA,QAAG;IAAAA,oDAAA,wCAA4B;IAAAA,0DAAA,EAAI;;;;;;;;;;;IAyBnCA,4DAAA,cAE6H;IAE9FA,oDAAA,GAAiB;IAAAA,0DAAA,EAAI;IAChDA,4DAAA,eAAgC;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAO;;;;;IAHtEA,wDAAA,YAAAA,6DAAA,IAAA0C,GAAA,EAAAC,OAAA,CAAAC,YAAA,MAAAC,OAAA,CAAAC,WAAA,kBAAAD,OAAA,CAAAC,WAAA,CAAAC,EAAA,GAAAJ,OAAA,CAAAC,YAAA,MAAAC,OAAA,CAAAC,WAAA,kBAAAD,OAAA,CAAAC,WAAA,CAAAC,EAAA,GAAuH;IAE7F/C,uDAAA,GAAiB;IAAjBA,+DAAA,CAAA2C,OAAA,CAAAK,OAAA,CAAiB;IACZhD,uDAAA,GAAgC;IAAhCA,+DAAA,CAAA6C,OAAA,CAAAN,UAAA,CAAAI,OAAA,CAAAM,SAAA,EAAgC;;;;;;IAvBxEjD,qEAAA,GAA0E;IACxEA,4DAAA,0BAAqC;IAGzBA,oDAAA,GAA6D;IAAAA,0DAAA,EAAO;IAE5EA,4DAAA,UAAK;IACaA,oDAAA,GAA0C;IAAAA,0DAAA,EAAiB;IAC3EA,4DAAA,uBAAkB;IAEdA,oDAAA,IACF;IAAAA,0DAAA,EAAW;IAMnBA,4DAAA,4BAAgD;IAC9CA,wDAAA,KAAAmD,iDAAA,kBAOM;IACRnD,0DAAA,EAAmB;IAEnBA,4DAAA,4BAA6C;IAG/BA,wDAAA,2BAAAoD,8EAAAC,MAAA;MAAArD,2DAAA,CAAAsD,IAAA;MAAA,MAAAC,OAAA,GAAAvD,2DAAA;MAAA,OAAAA,yDAAA,CAAAuD,OAAA,CAAAC,iBAAA,GAAAH,MAAA;IAAA,EAA+B,2BAAAI,8EAAAJ,MAAA;MAAArD,2DAAA,CAAAsD,IAAA;MAAA,MAAAI,OAAA,GAAA1D,2DAAA;MAAA,OACdA,yDAAA,CAAA0D,OAAA,CAAAC,WAAA,CAAAN,MAAA,CAAmB;IAAA,EADL;IAEtBrD,0DAAA,EAAW;IAEhCA,4DAAA,kBAAoG;IAA/DA,wDAAA,mBAAA4D,oEAAA;MAAA5D,2DAAA,CAAAsD,IAAA;MAAA,MAAAO,OAAA,GAAA7D,2DAAA;MAAA,OAASA,yDAAA,CAAA6D,OAAA,CAAAF,WAAA,EAAa;IAAA,EAAC;IAC1D3D,4DAAA,gBAAU;IAAAA,oDAAA,YAAI;IAAAA,0DAAA,EAAW;IAG/BA,mEAAA,EAAe;;;;IAnCDA,uDAAA,GAA6D;IAA7DA,+DAAA,CAAA+D,MAAA,CAAA9B,WAAA,EAAA8B,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA9B,eAAA,SAA6D;IAGnDlC,uDAAA,GAA0C;IAA1CA,+DAAA,CAAA+D,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA9B,eAAA,CAA0C;IAE9ClC,uDAAA,GAA4E;IAA5EA,wDAAA,CAAA+D,MAAA,CAAA3B,gBAAA,EAAA2B,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA3B,eAAA,eAA4E;IACpFrC,uDAAA,GACF;IADEA,gEAAA,MAAA+D,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA3B,eAAA,MACF;IAOerC,uDAAA,GAAW;IAAXA,wDAAA,YAAA+D,MAAA,CAAAG,QAAA,CAAW;IAapBlE,uDAAA,GAA+B;IAA/BA,wDAAA,YAAA+D,MAAA,CAAAP,iBAAA,CAA+B;IAIkBxD,uDAAA,GAAsC;IAAtCA,wDAAA,cAAA+D,MAAA,CAAAP,iBAAA,CAAAW,IAAA,GAAsC;;;;;IAOrGnE,4DAAA,cAAsC;IAC1BA,oDAAA,WAAI;IAAAA,0DAAA,EAAW;IACzBA,4DAAA,QAAG;IAAAA,oDAAA,wDAA4C;IAAAA,0DAAA,EAAI;;;AAuR3D,MAAOoE,iBAAiB;EAU5B/E,YACUgF,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAZlB,KAAAC,aAAa,GAA0B,EAAE;IACzC,KAAAC,qBAAqB,GAA0B,EAAE;IACjD,KAAAzC,sBAAsB,GAAkB,IAAI;IAC5C,KAAAkC,QAAQ,GAAc,EAAE;IACxB,KAAAV,iBAAiB,GAAG,EAAE;IACtB,KAAAkB,UAAU,GAAG,EAAE;IACf,KAAA5B,WAAW,GAAgB,IAAI;IAC/B,KAAAkB,mBAAmB,GAA+B,IAAI;EAMnD;EAEHW,QAAQA,CAAA;IACN,IAAI,CAACL,WAAW,CAACM,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAAChC,WAAW,GAAGgC,IAAI;MACvB,IAAI,IAAI,CAAChC,WAAW,EAAE;QACpB,IAAI,CAACiC,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACP,aAAa,GAAG,CACnB;MACEhD,aAAa,EAAE,CAAC;MAChBU,eAAe,EAAE,aAAa;MAC9BG,eAAe,EAAE,WAAW;MAC5BC,kBAAkB,EAAE,uDAAuD;MAC3EE,eAAe,EAAE,IAAIwC,IAAI,CAAC,sBAAsB,CAAC;MACjDnE,WAAW,EAAE;KACd,EACD;MACEW,aAAa,EAAE,CAAC;MAChBU,eAAe,EAAE,gBAAgB;MACjCG,eAAe,EAAE,QAAQ;MACzBC,kBAAkB,EAAE,yBAAyB;MAC7CE,eAAe,EAAE,IAAIwC,IAAI,CAAC,sBAAsB,CAAC;MACjDnE,WAAW,EAAE;KACd,EACD;MACEW,aAAa,EAAE,CAAC;MAChBU,eAAe,EAAE,cAAc;MAC/BG,eAAe,EAAE,OAAO;MACxBC,kBAAkB,EAAE,8BAA8B;MAClDE,eAAe,EAAE,IAAIwC,IAAI,CAAC,sBAAsB,CAAC;MACjDnE,WAAW,EAAE;KACd,CACF;IACD,IAAI,CAACoE,WAAW,EAAE;IAElB;IACA;;;;EAIF;;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACR,qBAAqB,GAAG,IAAI,CAACD,aAAa,CAACU,MAAM,CAAEC,IAAI,IAC1DA,IAAI,CAACjD,eAAe,CAACkD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACX,UAAU,CAACU,WAAW,EAAE,CAAC,CAC3E;EACH;EAEA7D,kBAAkBA,CAACC,aAAqB;IACtC,IAAI,CAACQ,sBAAsB,GAAGR,aAAa;IAC3C,IAAI,CAACwC,mBAAmB,GAAG,IAAI,CAACQ,aAAa,CAACc,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC/D,aAAa,KAAKA,aAAa,CAAC,IAAI,IAAI;IACpG,IAAI,CAACgE,YAAY,CAAChE,aAAa,CAAC;EAClC;EAEAgE,YAAYA,CAAChE,aAAqB;IAChC,IAAI,CAAC,IAAI,CAACsB,WAAW,EAAE;IAEvB;IACA,IAAI,CAACoB,QAAQ,GAAG,CACd;MACEnB,EAAE,EAAE,CAAC;MACLH,YAAY,EAAEpB,aAAa;MAC3BiE,cAAc,EAAE,IAAI,CAAC3C,WAAW,CAACC,EAAE;MACnCC,OAAO,EAAE,uDAAuD;MAChEC,SAAS,EAAE,IAAI+B,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE;QAAE3C,EAAE,EAAEvB,aAAa;QAAEmE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAACjD;KACpB,EACD;MACEC,EAAE,EAAE,CAAC;MACLH,YAAY,EAAE,IAAI,CAACE,WAAW,CAACC,EAAE;MACjC0C,cAAc,EAAEjE,aAAa;MAC7BwB,OAAO,EAAE,6EAA6E;MACtFC,SAAS,EAAE,IAAI+B,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE,IAAI,CAAC5C,WAAW;MAC5BiD,YAAY,EAAE;QAAEhD,EAAE,EAAEvB,aAAa;QAAEmE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW;KAC/F,EACD;MACE/C,EAAE,EAAE,CAAC;MACLH,YAAY,EAAEpB,aAAa;MAC3BiE,cAAc,EAAE,IAAI,CAAC3C,WAAW,CAACC,EAAE;MACnCC,OAAO,EACL,wGAAwG;MAC1GC,SAAS,EAAE,IAAI+B,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE;QAAE3C,EAAE,EAAEvB,aAAa;QAAEmE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAACjD;KACpB,EACD;MACEC,EAAE,EAAE,CAAC;MACLH,YAAY,EAAEpB,aAAa;MAC3BiE,cAAc,EAAE,IAAI,CAAC3C,WAAW,CAACC,EAAE;MACnCC,OAAO,EAAE,uDAAuD;MAChEC,SAAS,EAAE,IAAI+B,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE;QAAE3C,EAAE,EAAEvB,aAAa;QAAEmE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAACjD;KACpB,CACF;IAED;IACA;;;;;;;;;;;;EAYF;;EAEAa,WAAWA,CAACqC,KAAa;IACvB,MAAMC,aAAa,GAAGD,KAAsB;IAC5C,IAAIC,aAAa,IAAIA,aAAa,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,aAAa,CAACE,QAAQ,EAAE;MAC7EF,aAAa,CAACG,cAAc,EAAE;KAC/B,MAAM,IAAIH,aAAa,IAAIA,aAAa,CAACC,GAAG,KAAK,OAAO,EAAE;MACzD,OAAM,CAAC;;;IAGT,IAAI,CAAC,IAAI,CAAC1C,iBAAiB,CAACW,IAAI,EAAE,IAAI,CAAC,IAAI,CAACnC,sBAAsB,IAAI,CAAC,IAAI,CAACc,WAAW,EAAE;IAEzF,MAAMpD,OAAO,GAAY;MACvBkD,YAAY,EAAE,IAAI,CAACE,WAAW,CAACC,EAAE;MACjC0C,cAAc,EAAE,IAAI,CAACzD,sBAAsB;MAC3CgB,OAAO,EAAE,IAAI,CAACQ,iBAAiB;MAC/BP,SAAS,EAAE,IAAI+B,IAAI;KACpB;IAED;IACA,IAAI,CAACd,QAAQ,CAACmC,IAAI,CAAC3G,OAAO,CAAC;IAC3B,IAAI,CAAC8D,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACe,QAAQ,CAAC+B,IAAI,CAAC,2BAA2B,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAE7E;IACA;;;;;;;;;;;;;EAaF;;EAEAtE,WAAWA,CAACuE,IAAY;IACtB,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;MACrB,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACG,WAAW,EAAE;KACpD,MAAM,IAAIH,KAAK,CAACE,MAAM,KAAK,CAAC,IAAIF,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;MACpD,OAAOF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACG,WAAW,EAAE;;IAElC,OAAO,EAAE;EACX;EAEArE,UAAUA,CAACsE,IAAU;IACnB,MAAMC,CAAC,GAAG,IAAI9B,IAAI,CAAC6B,IAAI,CAAC;IACxB,MAAME,GAAG,GAAG,IAAI/B,IAAI,EAAE;IACtB,MAAMgC,WAAW,GAAG,CAACD,GAAG,CAACE,OAAO,EAAE,GAAGH,CAAC,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEpE,IAAID,WAAW,GAAG,EAAE,IAAIF,CAAC,CAACI,OAAO,EAAE,KAAKH,GAAG,CAACG,OAAO,EAAE,EAAE;MACrD,OAAOJ,CAAC,CAACK,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE,CAAC;KAC7E,MAAM,IAAIL,WAAW,GAAG,EAAE,IAAIF,CAAC,CAACI,OAAO,EAAE,KAAKH,GAAG,CAACG,OAAO,EAAE,GAAG,CAAC,EAAE;MAChE,OAAO,MAAM;KACd,MAAM;MACL,OAAOJ,CAAC,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAS,CAAE,CAAC;;EAE9E;EAEApF,gBAAgBA,CAAC0D,IAAY;IAC3B,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,kBAAkB;MAC3B,KAAK,WAAW;QACd,OAAO,qBAAqB;MAC9B,KAAK,OAAO;QACV,OAAO,iBAAiB;MAC1B;QACE,OAAO,EAAE;;EAEf;;;uBA9MW1B,iBAAiB,EAAApE,+DAAA,CAAAE,0EAAA,GAAAF,+DAAA,CAAA0H,oEAAA,GAAA1H,+DAAA,CAAA4H,oEAAA;IAAA;EAAA;;;YAAjBxD,iBAAiB;MAAA0D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApX1BpI,4DAAA,aAAgC;UAMZA,oDAAA,cAAO;UAAAA,0DAAA,EAAW;UAC5BA,oDAAA,iBACF;UAAAA,0DAAA,EAAiB;UACjBA,4DAAA,gBAAwB;UAAUA,oDAAA,WAAG;UAAAA,0DAAA,EAAW;UAElDA,4DAAA,2BAA8C;UAE/BA,oDAAA,sCAA8B;UAAAA,0DAAA,EAAY;UACrDA,4DAAA,gBAAiE;UAAjDA,wDAAA,2BAAAsI,2DAAAjF,MAAA;YAAA,OAAAgF,GAAA,CAAA3D,UAAA,GAAArB,MAAA;UAAA,EAAwB,mBAAAkF,mDAAA;YAAA,OAAUF,GAAA,CAAApD,WAAA,EAAa;UAAA,EAAvB;UAAxCjF,0DAAA,EAAiE;UACjEA,4DAAA,mBAAoB;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UAGzCA,4DAAA,uBAAgD;UAC9CA,wDAAA,KAAAwI,2CAAA,8BAoBgB;UAChBxI,wDAAA,KAAAyI,iCAAA,kBAGM;UACRzI,0DAAA,EAAe;UAIjBA,4DAAA,oBAAiC;UAC/BA,wDAAA,KAAA0I,0CAAA,4BAuCe;UAEf1I,wDAAA,KAAA2I,yCAAA,iCAAA3I,oEAAA,CAKc;UAChBA,0DAAA,EAAW;;;;UAlFWA,uDAAA,IAAwB;UAAxBA,wDAAA,YAAAqI,GAAA,CAAA3D,UAAA,CAAwB;UAKV1E,uDAAA,GAAwB;UAAxBA,wDAAA,YAAAqI,GAAA,CAAA5D,qBAAA,CAAwB;UAqBlDzE,uDAAA,GAAwC;UAAxCA,wDAAA,SAAAqI,GAAA,CAAA5D,qBAAA,CAAAkC,MAAA,OAAwC;UASjC3G,uDAAA,GAA8B;UAA9BA,wDAAA,SAAAqI,GAAA,CAAArG,sBAAA,CAA8B,aAAA6G,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnET;AACA;AACF,CAAC;AAE7C;AACsD;AACI;AACJ;AACE;AACS;AACX;AACE;AACA;AACI;AAEJ;;;AAmBlD,MAAOa,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAdvBZ,yDAAY,EACZE,uDAAW,EACXC,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,mEAAc,EACdC,4EAAkB,EAClBC,iEAAa,EACbC,oEAAc,EACdC,oEAAc,EACdC,wEAAgB,EAChBV,0DAAY,CAACY,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEzF,kEAAiBA;MAAA,CAAE,CAAC,CAAC;IAAA;EAAA;;;sHAG1DsF,cAAc;IAAAI,YAAA,GAhBV1F,kEAAiB;IAAA2F,OAAA,GAE9BjB,yDAAY,EACZE,uDAAW,EACXC,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,mEAAc,EACdC,4EAAkB,EAClBC,iEAAa,EACbC,oEAAc,EACdC,oEAAc,EACdC,wEAAgB,EAAAvJ,0DAAA;EAAA;AAAA,K", "sources": ["./src/app/core/services/message.service.ts", "./src/app/features/messages/messages.component.ts", "./src/app/features/messages/messages.module.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Message } from \"../models/message.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class MessageService {\n  private apiUrl = `${environment.urlApi}messages`\n\n  constructor(private http: HttpClient) {}\n\n  // POST: Envoyer un message (correspond à POST /api/messages)\n  envoyerMessage(message: Message): Observable<string> {\n    return this.http.post<string>(this.apiUrl, message)\n  }\n\n  // GET: Messages entre deux utilisateurs (correspond à GET /api/messages/entre/{id1}/{id2})\n  getMessages(id1: number, id2: number): Observable<Message[]> {\n    return this.http.get<Message[]>(`${this.apiUrl}/entre/${id1}/${id2}`)\n  }\n}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { MessageService } from \"../../core/services/message.service\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { Message } from \"../../core/models/message.model\"\nimport { User } from \"../../core/models/user.model\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\n\ninterface ConversationPreview {\n  participantId: number\n  participantName: string\n  participantRole: \"Client\" | \"Formateur\" | \"Admin\"\n  lastMessageContent: string\n  lastMessageDate: Date\n  unreadCount: number\n}\n\n@Component({\n  selector: \"app-messages\",\n  template: `\n    <div class=\"messages-container\">\n      <div class=\"messages-wrapper\">\n        <!-- Conversation List -->\n        <mat-card class=\"conversation-list-card\">\n          <mat-card-header>\n            <mat-card-title class=\"card-title-with-icon\">\n              <mat-icon>message</mat-icon>\n              Messages\n            </mat-card-title>\n            <button mat-icon-button><mat-icon>add</mat-icon></button>\n          </mat-card-header>\n          <mat-card-content class=\"conversation-search\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Rechercher une conversation...</mat-label>\n              <input matInput [(ngModel)]=\"searchTerm\" (input)=\"applySearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n          </mat-card-content>\n          <mat-nav-list class=\"conversations-scroll-area\">\n            <mat-list-item *ngFor=\"let conv of filteredConversations\" \n                           (click)=\"selectConversation(conv.participantId)\"\n                           [ngClass]=\"{'selected-conversation': selectedConversationId === conv.participantId}\">\n              <div matListItemAvatar class=\"avatar-placeholder\">\n                <span>{{ getInitials(conv.participantName) }}</span>\n              </div>\n              <div matListItemTitle class=\"conversation-title\">\n                <span>{{ conv.participantName }}</span>\n                <mat-chip-listbox>\n                  <mat-chip [class]=\"getRoleChipClass(conv.participantRole)\">{{ conv.participantRole }}</mat-chip>\n                </mat-chip-listbox>\n              </div>\n              <div matListItemLine class=\"conversation-last-message\">\n                <span class=\"message-content\">{{ conv.lastMessageContent }}</span>\n                <span class=\"message-date\">{{ formatDate(conv.lastMessageDate) }}</span>\n              </div>\n              <mat-chip-listbox *ngIf=\"conv.unreadCount > 0\" matListItemMeta>\n                <mat-chip color=\"warn\" selected>{{ conv.unreadCount }}</mat-chip>\n              </mat-chip-listbox>\n              <mat-divider></mat-divider>\n            </mat-list-item>\n            <div *ngIf=\"filteredConversations.length === 0\" class=\"no-conversations\">\n              <mat-icon>chat_bubble_outline</mat-icon>\n              <p>Aucune conversation trouvée.</p>\n            </div>\n          </mat-nav-list>\n        </mat-card>\n\n        <!-- Chat Area -->\n        <mat-card class=\"chat-area-card\">\n          <ng-container *ngIf=\"selectedConversationId; else noConversationSelected\">\n            <mat-card-header class=\"chat-header\">\n              <div class=\"chat-header-info\">\n                <div matListItemAvatar class=\"avatar-placeholder\">\n                  <span>{{ getInitials(selectedParticipant?.participantName || '') }}</span>\n                </div>\n                <div>\n                  <mat-card-title>{{ selectedParticipant?.participantName }}</mat-card-title>\n                  <mat-chip-listbox>\n                    <mat-chip [class]=\"getRoleChipClass(selectedParticipant?.participantRole || 'Client')\">\n                      {{ selectedParticipant?.participantRole }}\n                    </mat-chip>\n                  </mat-chip-listbox>\n                </div>\n              </div>\n            </mat-card-header>\n\n            <mat-card-content class=\"messages-display-area\">\n              <div *ngFor=\"let msg of messages\" \n                   class=\"message-bubble-wrapper\" \n                   [ngClass]=\"{'my-message': msg.expediteurId === currentUser?.id, 'other-message': msg.expediteurId !== currentUser?.id}\">\n                <div class=\"message-bubble\">\n                  <p class=\"message-content\">{{ msg.contenu }}</p>\n                  <span class=\"message-timestamp\">{{ formatDate(msg.dateEnvoi!) }}</span>\n                </div>\n              </div>\n            </mat-card-content>\n\n            <mat-card-actions class=\"message-input-area\">\n              <mat-form-field appearance=\"outline\" class=\"full-width message-input-field\">\n                <textarea matInput placeholder=\"Tapez votre message...\" \n                          [(ngModel)]=\"newMessageContent\" \n                          (keydown.enter)=\"sendMessage($event)\"\n                          rows=\"1\"></textarea>\n              </mat-form-field>\n              <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!newMessageContent.trim()\">\n                <mat-icon>send</mat-icon>\n              </button>\n            </mat-card-actions>\n          </ng-container>\n\n          <ng-template #noConversationSelected>\n            <div class=\"no-conversation-selected\">\n              <mat-icon>chat</mat-icon>\n              <p>Sélectionnez une conversation pour commencer</p>\n            </div>\n          </ng-template>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .messages-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      padding: 2rem;\n    }\n\n    .messages-wrapper {\n      display: grid;\n      grid-template-columns: 1fr 2fr;\n      gap: 1.5rem;\n      max-width: 1400px;\n      margin: 0 auto;\n      height: calc(100vh - 4rem); /* Adjust height to fit viewport */\n    }\n\n    @media (max-width: 960px) {\n      .messages-wrapper {\n        grid-template-columns: 1fr;\n        height: auto;\n      }\n      .conversation-list-card {\n        height: auto;\n        min-height: 300px;\n      }\n      .chat-area-card {\n        height: 600px; /* Fixed height for chat on small screens */\n      }\n    }\n\n    .conversation-list-card, .chat-area-card {\n      display: flex;\n      flex-direction: column;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .conversation-list-card mat-card-header {\n      padding-bottom: 0;\n    }\n\n    .card-title-with-icon {\n      display: flex;\n      align-items: center;\n      font-size: 1.4rem;\n      font-weight: 600;\n    }\n\n    .card-title-with-icon mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .conversation-search {\n      padding: 1rem 1.5rem 0.5rem;\n    }\n\n    .conversation-search .full-width {\n      width: 100%;\n    }\n\n    .conversations-scroll-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n\n    .mat-list-item {\n      height: auto !important;\n      padding: 1rem 1.5rem;\n      cursor: pointer;\n      transition: background-color 0.2s ease-in-out;\n    }\n\n    .mat-list-item:hover {\n      background-color: #f5f5f5;\n    }\n\n    .mat-list-item.selected-conversation {\n      background-color: #ede7f6; /* Light purple */\n      border-left: 4px solid #673ab7; /* Purple */\n    }\n\n    .avatar-placeholder {\n      width: 48px;\n      height: 48px;\n      border-radius: 50%;\n      background-color: #e1bee7; /* Light purple */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 1.1rem;\n      font-weight: bold;\n      color: #8e24aa; /* Dark purple */\n      flex-shrink: 0;\n    }\n\n    .conversation-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 500;\n      font-size: 1rem;\n    }\n\n    .conversation-last-message {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-size: 0.85rem;\n      color: #777;\n      margin-top: 0.2rem;\n    }\n\n    .message-content {\n      flex-grow: 1;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      margin-right: 0.5rem;\n    }\n\n    .message-date {\n      flex-shrink: 0;\n    }\n\n    .mat-chip {\n      font-size: 0.7rem;\n      padding: 0.2rem 0.5rem;\n      height: auto;\n    }\n\n    .role-chip-Client { background-color: #e0e0e0; color: #424242; }\n    .role-chip-Formateur { background-color: #bbdefb; color: #1976d2; }\n    .role-chip-Admin { background-color: #e1bee7; color: #8e24aa; }\n\n    .no-conversations {\n      text-align: center;\n      padding: 2rem;\n      color: #999;\n    }\n\n    .no-conversations mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    /* Chat Area */\n    .chat-area-card {\n      background-color: #fff;\n    }\n\n    .chat-header {\n      border-bottom: 1px solid #eee;\n      padding: 1rem 1.5rem;\n    }\n\n    .chat-header-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .chat-header-info mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .messages-display-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 1.5rem;\n      background-color: #fcfcfc;\n    }\n\n    .message-bubble-wrapper {\n      display: flex;\n      margin-bottom: 1rem;\n    }\n\n    .message-bubble-wrapper.my-message {\n      justify-content: flex-end;\n    }\n\n    .message-bubble-wrapper.other-message {\n      justify-content: flex-start;\n    }\n\n    .message-bubble {\n      max-width: 70%;\n      padding: 0.8rem 1.2rem;\n      border-radius: 18px;\n      position: relative;\n      word-wrap: break-word;\n    }\n\n    .my-message .message-bubble {\n      background-color: #673ab7; /* Purple */\n      color: white;\n      border-bottom-right-radius: 4px;\n    }\n\n    .other-message .message-bubble {\n      background-color: #e0e0e0; /* Light gray */\n      color: #333;\n      border-bottom-left-radius: 4px;\n    }\n\n    .message-bubble p {\n      margin: 0;\n      font-size: 0.95rem;\n      line-height: 1.4;\n    }\n\n    .message-timestamp {\n      font-size: 0.75rem;\n      margin-top: 0.5rem;\n      display: block;\n      text-align: right;\n      color: rgba(255, 255, 255, 0.7); /* For my messages */\n    }\n\n    .other-message .message-timestamp {\n      color: #777;\n    }\n\n    .message-input-area {\n      border-top: 1px solid #eee;\n      padding: 1rem 1.5rem;\n      display: flex;\n      align-items: flex-end;\n      gap: 0.8rem;\n    }\n\n    .message-input-field {\n      flex-grow: 1;\n    }\n\n    .message-input-field textarea {\n      min-height: 40px;\n      max-height: 120px;\n      overflow-y: auto;\n    }\n\n    .message-input-area button {\n      flex-shrink: 0;\n    }\n\n    .no-conversation-selected {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      color: #999;\n      text-align: center;\n    }\n\n    .no-conversation-selected mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n    }\n  `,\n  ],\n})\nexport class MessagesComponent implements OnInit {\n  conversations: ConversationPreview[] = []\n  filteredConversations: ConversationPreview[] = []\n  selectedConversationId: number | null = null\n  messages: Message[] = []\n  newMessageContent = \"\"\n  searchTerm = \"\"\n  currentUser: User | null = null\n  selectedParticipant: ConversationPreview | null = null\n\n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (this.currentUser) {\n        this.loadConversations()\n      }\n    })\n  }\n\n  loadConversations(): void {\n    // Mock data for demonstration\n    this.conversations = [\n      {\n        participantId: 2, // Formateur Jean Dupont\n        participantName: \"Jean Dupont\",\n        participantRole: \"Formateur\",\n        lastMessageContent: \"Bonjour, avez-vous des questions sur le cours React ?\",\n        lastMessageDate: new Date(\"2024-01-15T10:30:00Z\"),\n        unreadCount: 2,\n      },\n      {\n        participantId: 3, // Client Sophie Bernard\n        participantName: \"Sophie Bernard\",\n        participantRole: \"Client\",\n        lastMessageContent: \"Merci pour votre aide !\",\n        lastMessageDate: new Date(\"2024-01-14T15:45:00Z\"),\n        unreadCount: 0,\n      },\n      {\n        participantId: 4, // Admin Admin System\n        participantName: \"Admin System\",\n        participantRole: \"Admin\",\n        lastMessageContent: \"Votre demande a été traitée.\",\n        lastMessageDate: new Date(\"2024-01-13T11:00:00Z\"),\n        unreadCount: 0,\n      },\n    ]\n    this.applySearch()\n\n    // Uncomment to fetch from API\n    /*\n    // This would require a backend endpoint to get conversation previews for a user\n    // For example: this.messageService.getConversations(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  applySearch(): void {\n    this.filteredConversations = this.conversations.filter((conv) =>\n      conv.participantName.toLowerCase().includes(this.searchTerm.toLowerCase()),\n    )\n  }\n\n  selectConversation(participantId: number): void {\n    this.selectedConversationId = participantId\n    this.selectedParticipant = this.conversations.find((c) => c.participantId === participantId) || null\n    this.loadMessages(participantId)\n  }\n\n  loadMessages(participantId: number): void {\n    if (!this.currentUser) return\n\n    // Mock messages for the selected conversation\n    this.messages = [\n      {\n        id: 1,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu: \"Bonjour, j'espère que vous appréciez le cours React !\",\n        dateEnvoi: new Date(\"2024-01-15T09:00:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n      {\n        id: 2,\n        expediteurId: this.currentUser.id,\n        destinataireId: participantId,\n        contenu: \"Bonjour Jean, oui c'est très intéressant ! J'ai une question sur les hooks.\",\n        dateEnvoi: new Date(\"2024-01-15T09:15:00Z\"),\n        expediteur: this.currentUser,\n        destinataire: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n      },\n      {\n        id: 3,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu:\n          \"Parfait ! N'hésitez pas à me poser toutes vos questions. Les hooks peuvent sembler complexes au début.\",\n        dateEnvoi: new Date(\"2024-01-15T09:30:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n      {\n        id: 4,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu: \"Bonjour, avez-vous des questions sur le cours React ?\",\n        dateEnvoi: new Date(\"2024-01-15T10:30:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n    ]\n\n    // Uncomment to fetch from API\n    /*\n    this.messageService.getMessages(this.currentUser.id, participantId).subscribe({\n      next: (data) => {\n        this.messages = data;\n        // Mark messages as read\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des messages.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  sendMessage(event?: Event): void {\n    const keyboardEvent = event as KeyboardEvent;\n    if (keyboardEvent && keyboardEvent.key === \"Enter\" && !keyboardEvent.shiftKey) {\n      keyboardEvent.preventDefault()\n    } else if (keyboardEvent && keyboardEvent.key !== \"Enter\") {\n      return // Only proceed on Enter key press\n    }\n\n    if (!this.newMessageContent.trim() || !this.selectedConversationId || !this.currentUser) return\n\n    const message: Message = {\n      expediteurId: this.currentUser.id,\n      destinataireId: this.selectedConversationId,\n      contenu: this.newMessageContent,\n      dateEnvoi: new Date(),\n    }\n\n    // Mock message sending\n    this.messages.push(message)\n    this.newMessageContent = \"\"\n    this.snackBar.open(\"Message envoyé (simulé) !\", \"Fermer\", { duration: 1000 })\n\n    // Uncomment to send via API\n    /*\n    this.messageService.envoyerMessage(message).subscribe({\n      next: (res) => {\n        this.messages.push(message); // Add to local list after successful send\n        this.newMessageContent = '';\n        this.snackBar.open('Message envoyé !', 'Fermer', { duration: 1000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de l\\'envoi du message.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  getInitials(name: string): string {\n    const parts = name.split(\" \")\n    if (parts.length >= 2) {\n      return `${parts[0][0]}${parts[1][0]}`.toUpperCase()\n    } else if (parts.length === 1 && parts[0].length > 0) {\n      return parts[0][0].toUpperCase()\n    }\n    return \"\"\n  }\n\n  formatDate(date: Date): string {\n    const d = new Date(date)\n    const now = new Date()\n    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60)\n\n    if (diffInHours < 24 && d.getDate() === now.getDate()) {\n      return d.toLocaleTimeString(\"fr-FR\", { hour: \"2-digit\", minute: \"2-digit\" })\n    } else if (diffInHours < 48 && d.getDate() === now.getDate() - 1) {\n      return \"Hier\"\n    } else {\n      return d.toLocaleDateString(\"fr-FR\", { day: \"2-digit\", month: \"2-digit\" })\n    }\n  }\n\n  getRoleChipClass(role: string): string {\n    switch (role) {\n      case \"Client\":\n        return \"role-chip-Client\"\n      case \"Formateur\":\n        return \"role-chip-Formateur\"\n      case \"Admin\":\n        return \"role-chip-Admin\"\n      default:\n        return \"\"\n    }\n  }\n}\n", "import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // For ngModel in textarea\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatListModule } from \"@angular/material/list\"\nimport { MatBadgeModule } from \"@angular/material/badge\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatDividerModule } from \"@angular/material/divider\"\n\nimport { MessagesComponent } from \"./messages.component\"\n\n@NgModule({\n  declarations: [MessagesComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatForm<PERSON>ieldModule,\n    MatListModule,\n    MatBadgeModule,\n    MatChipsModule,\n    MatDividerModule,\n    RouterModule.forChild([{ path: \"\", component: MessagesComponent }]),\n  ],\n})\nexport class MessagesModule {}\n"], "names": ["environment", "MessageService", "constructor", "http", "apiUrl", "urlApi", "envoyer<PERSON>essage", "message", "post", "getMessages", "id1", "id2", "get", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "conv_r5", "unreadCount", "ɵɵlistener", "MessagesComponent_mat_list_item_19_Template_mat_list_item_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "selectConversation", "participantId", "ɵɵtemplate", "MessagesComponent_mat_list_item_19_mat_chip_listbox_15_Template", "ɵɵelement", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "selectedConversationId", "getInitials", "participantName", "ɵɵclassMap", "getRoleChipClass", "participantRole", "lastMessageContent", "formatDate", "lastMessageDate", "ɵɵpureFunction2", "_c1", "msg_r11", "expediteurId", "ctx_r10", "currentUser", "id", "contenu", "dateEnvoi", "ɵɵelementContainerStart", "MessagesComponent_ng_container_22_div_13_Template", "MessagesComponent_ng_container_22_Template_textarea_ngModelChange_16_listener", "$event", "_r13", "ctx_r12", "newMessageContent", "MessagesComponent_ng_container_22_Template_textarea_keydown_enter_16_listener", "ctx_r14", "sendMessage", "MessagesComponent_ng_container_22_Template_button_click_17_listener", "ctx_r15", "ɵɵelementContainerEnd", "ctx_r2", "selectedParticipant", "ɵɵtextInterpolate1", "messages", "trim", "MessagesComponent", "messageService", "authService", "snackBar", "conversations", "filteredConversations", "searchTerm", "ngOnInit", "currentUser$", "subscribe", "user", "loadConversations", "Date", "applySearch", "filter", "conv", "toLowerCase", "includes", "find", "c", "loadMessages", "destinataireId", "expediteur", "nom", "prenom", "email", "role", "destina<PERSON>", "event", "keyboardEvent", "key", "shift<PERSON>ey", "preventDefault", "push", "open", "duration", "name", "parts", "split", "length", "toUpperCase", "date", "d", "now", "diffInHours", "getTime", "getDate", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "day", "month", "ɵɵdirectiveInject", "i2", "AuthService", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "MessagesComponent_Template", "rf", "ctx", "MessagesComponent_Template_input_ngModelChange_15_listener", "MessagesComponent_Template_input_input_15_listener", "MessagesComponent_mat_list_item_19_Template", "MessagesComponent_div_20_Template", "MessagesComponent_ng_container_22_Template", "MessagesComponent_ng_template_23_Template", "ɵɵtemplateRefExtractor", "_r3", "CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatListModule", "MatBadgeModule", "MatChipsModule", "MatDividerModule", "MessagesModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}