{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { FormsModule } from \"@angular/forms\"; // For ngModel in radio buttons\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\";\nimport { MatRadioModule } from \"@angular/material/radio\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { QuizComponent } from \"./quiz.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class QuizModule {\n  static {\n    this.ɵfac = function QuizModule_Factory(t) {\n      return new (t || QuizModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: QuizModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatProgressBarModule, MatRadioModule, MatProgressSpinnerModule, RouterModule.forChild([{\n        path: \":id\",\n        component: QuizComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(QuizModule, {\n    declarations: [QuizComponent],\n    imports: [CommonModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatProgressBarModule, MatRadioModule, MatProgressSpinnerModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressBarModule", "MatRadioModule", "MatProgressSpinnerModule", "QuizComponent", "QuizModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\quiz\\quiz.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // For ngModel in radio buttons\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\nimport { MatRadioModule } from \"@angular/material/radio\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { QuizComponent } from \"./quiz.component\"\n\n@NgModule({\n  declarations: [QuizComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressBarModule,\n    MatRadioModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([{ path: \":id\", component: QuizComponent }]),\n  ],\n})\nexport class QuizModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB,EAAC;AAE7C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,aAAa,QAAQ,kBAAkB;;;AAgBhD,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAXnBV,YAAY,EACZE,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,wBAAwB,EACxBP,YAAY,CAACU,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,SAAS,EAAEJ;MAAa,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAGzDC,UAAU;IAAAI,YAAA,GAbNL,aAAa;IAAAM,OAAA,GAE1Bf,YAAY,EACZE,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,wBAAwB,EAAAQ,EAAA,CAAAf,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}