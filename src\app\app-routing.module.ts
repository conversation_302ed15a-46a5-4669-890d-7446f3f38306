import { NgModule } from "@angular/core"
import { RouterModule, type Routes } from "@angular/router"
import { AuthGuard } from "./core/guards/auth.guard"

const routes: Routes = [
  {
    path: "",
    loadChildren: () => import("./features/home/<USER>").then((m) => m.HomeModule),
  },
  {
    path: "auth",
    loadChildren: () => import("./features/auth/auth.module").then((m) => m.AuthModule),
  },
  {
    path: "dashboard",
    loadChildren: () => import("./features/dashboard/dashboard.module").then((m) => m.DashboardModule),
    canActivate: [AuthGuard],
  },
  {
    path: "courses",
    loadChildren: () => import("./features/courses/courses.module").then((m) => m.CoursesModule),
  },
  {
    path: "quiz",
    loadChildren: () => import("./features/quiz/quiz.module").then((m) => m.QuizModule),
    canActivate: [AuthGuard],
  },
  {
    path: "payment",
    loadChildren: () => import("./features/payment/payment.module").then((m) => m.PaymentModule),
    canActivate: [AuthGuard],
  },
  {
    path: "messages",
    loadChildren: () => import("./features/messages/messages.module").then((m) => m.MessagesModule),
    canActivate: [AuthGuard],
  },
  {
    path: "certificates",
    loadChildren: () => import("./features/certificates/certificates.module").then((m) => m.CertificatesModule),
    canActivate: [AuthGuard],
  },
  {
    path: "**",
    redirectTo: "",
  },
]

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
