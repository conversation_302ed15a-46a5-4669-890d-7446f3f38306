import { Component, OnInit } from "@angular/core"
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms"
import { Router } from "@angular/router"
import { MatSnackBar } from "@angular/material/snack-bar"
import { AuthService } from "../../../core/services/auth.service"
import { ClientService } from "../../../core/services/client.service"

@Component({
  selector: "app-register",
  template: `
    <div class="auth-container">
      <div class="auth-wrapper">
        <mat-card class="auth-card">
          <mat-card-header>
            <mat-card-title>Inscription</mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
              <div class="name-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Prénom</mat-label>
                  <input matInput formControlName="firstName" placeholder="Votre prénom">
                  <mat-error *ngIf="registerForm.get('firstName')?.hasError('required')">
                    Le prénom est requis
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Nom</mat-label>
                  <input matInput formControlName="lastName" placeholder="Votre nom">
                  <mat-error *ngIf="registerForm.get('lastName')?.hasError('required')">
                    Le nom est requis
                  </mat-error>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>E-mail</mat-label>
                <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                  L'email est requis
                </mat-error>
                <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                  Format d'email invalide
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Mot de passe</mat-label>
                <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password">
                <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                </button>
                <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                  Le mot de passe est requis
                </mat-error>
                <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                  Le mot de passe doit contenir au moins 6 caractères
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Confirmer le mot de passe</mat-label>
                <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword">
                <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
                  <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                </button>
                <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                  La confirmation est requise
                </mat-error>
                <mat-error *ngIf="registerForm.hasError('passwordMismatch')">
                  Les mots de passe ne correspondent pas
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Type de compte</mat-label>
                <mat-select formControlName="accountType">
                  <mat-option value="client">Client (Apprenant)</mat-option>
                  <mat-option value="formateur">Formateur</mat-option>
                  <mat-option value="admin">Administrateur</mat-option>
                </mat-select>
                <mat-error *ngIf="registerForm.get('accountType')?.hasError('required')">
                  Sélectionnez un type de compte
                </mat-error>
              </mat-form-field>

              <div class="checkbox-container">
                <mat-checkbox formControlName="acceptTerms">
                  J'accepte les <a href="/terms" target="_blank" class="link">conditions d'utilisation</a>
                </mat-checkbox>
                <mat-error *ngIf="registerForm.get('acceptTerms')?.hasError('required')">
                  Vous devez accepter les conditions
                </mat-error>
              </div>

              <button mat-raised-button color="primary" type="submit" 
                      [disabled]="registerForm.invalid || isLoading" class="full-width submit-btn">
                <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
                <span *ngIf="!isLoading">S'inscrire</span>
              </button>
            </form>

            <div class="auth-links">
              <p>
                Déjà un compte ? 
                <a routerLink="/auth/login" class="link">Se connecter</a>
              </p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [
    `
    .auth-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
    }

    .auth-wrapper {
      width: 100%;
      max-width: 500px;
    }

    .auth-card {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .name-row {
      display: flex;
      gap: 1rem;
    }

    .full-width {
      width: 100%;
      margin-bottom: 1rem;
    }

    .half-width {
      flex: 1;
      margin-bottom: 1rem;
    }

    .checkbox-container {
      margin-bottom: 1.5rem;
    }

    .submit-btn {
      height: 48px;
      font-size: 1.1rem;
    }

    .auth-links {
      text-align: center;
      margin-top: 1.5rem;
    }

    .link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }

    .link:hover {
      text-decoration: underline;
    }
  `,
  ],
})
export class RegisterComponent implements OnInit {
  registerForm!: FormGroup
  hidePassword = true
  hideConfirmPassword = true
  isLoading = false

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private clientService: ClientService,
    private router: Router,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.registerForm = this.fb.group(
      {
        firstName: ["", Validators.required],
        lastName: ["", Validators.required],
        email: ["", [Validators.required, Validators.email]],
        password: ["", [Validators.required, Validators.minLength(6)]],
        confirmPassword: ["", Validators.required],
        accountType: ["", Validators.required],
        acceptTerms: [false, Validators.requiredTrue],
      },
      { validators: this.passwordMatchValidator },
    )
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get("password")
    const confirmPassword = form.get("confirmPassword")

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true }
    }
    return null
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.isLoading = true
      const formData = this.registerForm.value
      console.log("Données du formulaire avant envoi:", JSON.stringify(formData, null, 2))

      // Utiliser directement ClientService pour plus de contrôle
      if (formData.accountType === 'client') {
        // ✅ Données minimales requises seulement
        const clientData = {
          email: formData.email,
          nom: formData.lastName,
          prenom: formData.firstName
        };

        console.log("Données client à envoyer:", JSON.stringify(clientData, null, 2));

        this.clientService.createClient(clientData).subscribe({
          next: (response) => {
            this.isLoading = false
            this.snackBar.open("Inscription réussie !", "Fermer", { duration: 3000 })
            this.router.navigate(["/dashboard"])
          },
          error: (error) => {
            this.isLoading = false
            console.error("Erreur ClientService:", error)
            console.error("Validation errors:", error.error?.errors)

            let errorMessage = "Erreur lors de l'inscription"
            if (error.error?.errors) {
              const validationErrors = Object.values(error.error.errors).flat()
              errorMessage = validationErrors.join(', ')
            }

            this.snackBar.open(errorMessage, "Fermer", { duration: 5000 })
          }
        });
      } else {
        // Pour les formateurs, utiliser AuthService
        this.authService.register(formData).subscribe({
          next: (response) => {
            this.isLoading = false
            this.snackBar.open("Inscription réussie !", "Fermer", { duration: 3000 })
            this.router.navigate(["/dashboard"])
          },
          error: (error) => {
            this.isLoading = false
            console.error("Erreur AuthService:", error)
            console.error("Validation errors:", error.error?.errors)

            let errorMessage = "Erreur lors de l'inscription"
            if (error.error?.errors) {
              const validationErrors = Object.values(error.error.errors).flat()
              errorMessage = validationErrors.join(', ')
            }

            this.snackBar.open(errorMessage, "Fermer", { duration: 5000 })
          }
        });
      }
    }
  }
}
