import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Quiz } from "../models/course.model"
import { ResultatQuiz } from "../models/resultat-quiz.model"

@Injectable({
  providedIn: "root",
})
export class QuizService {
  private apiUrl = `${environment.urlApi}quiz`

  constructor(private http: HttpClient) {}

  // GET: Tous les quiz (correspond à GET /api/quiz)
  getQuiz(): Observable<Quiz[]> {
    return this.http.get<Quiz[]>(this.apiUrl)
  }

  // GET: Un quiz par ID (correspond à GET /api/quiz/{id})
  getQuizById(id: number): Observable<Quiz> {
    return this.http.get<Quiz>(`${this.apiUrl}/${id}`)
  }

  // POST: Créer un quiz (correspond à POST /api/quiz)
  createQuiz(quiz: Quiz): Observable<Quiz> {
    return this.http.post<Quiz>(this.apiUrl, quiz)
  }

  // PUT: Modifier un quiz (correspond à PUT /api/quiz/{id})
  updateQuiz(id: number, quiz: Quiz): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, quiz)
  }

  // DELETE: Supprimer un quiz (correspond à DELETE /api/quiz/{id})
  deleteQuiz(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`)
  }

  // POST: Soumettre résultat de quiz (correspond à POST /api/quiz/{id}/soumettre)
  soumettreResultat(quizId: number, resultat: ResultatQuiz): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/${quizId}/soumettre`, resultat)
  }
}
