import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Quiz, ResultatQuiz } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class QuizService {
  constructor(private http: HttpClient) {}

  // GET: Tous les quiz
  getQuiz(): Observable<Quiz[]> {
    return this.http.get<Quiz[]>(`${environment.urlApi}quiz`)
  }

  // GET: Un quiz par ID
  getQuizById(id: number): Observable<Quiz> {
    return this.http.get<Quiz>(`${environment.urlApi}quiz/${id}`)
  }

  // POST: Créer un quiz
  createQuiz(quiz: Quiz): Observable<Quiz> {
    return this.http.post<Quiz>(`${environment.urlApi}quiz`, quiz)
  }

  // PUT: Modifier un quiz
  updateQuiz(id: number, quiz: Quiz): Observable<any> {
    return this.http.put(`${environment.urlApi}quiz/${id}`, quiz)
  }

  // DELETE: Supprimer un quiz
  deleteQuiz(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}quiz/${id}`)
  }

  // POST: Soumettre résultat de quiz
  soumettreResultat(quizId: number, resultat: ResultatQuiz): Observable<any> {
    return this.http.post(`${environment.urlApi}quiz/${quizId}/soumettre`, resultat)
  }
}
