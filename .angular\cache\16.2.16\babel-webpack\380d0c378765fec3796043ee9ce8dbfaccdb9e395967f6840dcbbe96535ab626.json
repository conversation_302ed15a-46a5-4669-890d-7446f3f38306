{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { AuthGuard } from \"./core/guards/auth.guard\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: \"\",\n  loadChildren: () => import(\"./features/home/<USER>\").then(m => m.HomeModule)\n}, {\n  path: \"auth\",\n  loadChildren: () => import(\"./features/auth/auth.module\").then(m => m.AuthModule)\n}, {\n  path: \"dashboard\",\n  loadChildren: () => import(\"./features/dashboard/dashboard.module\").then(m => m.DashboardModule),\n  canActivate: [AuthGuard]\n}, {\n  path: \"courses\",\n  loadChildren: () => import(\"./features/courses/courses.module\").then(m => m.CoursesModule)\n}, {\n  path: \"quiz\",\n  loadChildren: () => import(\"./features/quiz/quiz.module\").then(m => m.QuizModule),\n  canActivate: [AuthGuard]\n}, {\n  path: \"payment\",\n  loadChildren: () => import(\"./features/payment/payment.module\").then(m => m.PaymentModule),\n  canActivate: [AuthGuard]\n}, {\n  path: \"messages\",\n  loadChildren: () => import(\"./features/messages/messages.module\").then(m => m.MessagesModule),\n  canActivate: [AuthGuard]\n}, {\n  path: \"certificates\",\n  loadChildren: () => import(\"./features/certificates/certificates.module\").then(m => m.CertificatesModule),\n  canActivate: [AuthGuard]\n}, {\n  path: \"**\",\n  redirectTo: \"\"\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "loadChildren", "then", "m", "HomeModule", "AuthModule", "DashboardModule", "canActivate", "CoursesModule", "QuizModule", "PaymentModule", "MessagesModule", "CertificatesModule", "redirectTo", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\e-learning\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { RouterModule, type Routes } from \"@angular/router\"\nimport { AuthGuard } from \"./core/guards/auth.guard\"\n\nconst routes: Routes = [\n  {\n    path: \"\",\n    loadChildren: () => import(\"./features/home/<USER>\").then((m) => m.HomeModule),\n  },\n  {\n    path: \"auth\",\n    loadChildren: () => import(\"./features/auth/auth.module\").then((m) => m.AuthModule),\n  },\n  {\n    path: \"dashboard\",\n    loadChildren: () => import(\"./features/dashboard/dashboard.module\").then((m) => m.DashboardModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"courses\",\n    loadChildren: () => import(\"./features/courses/courses.module\").then((m) => m.CoursesModule),\n  },\n  {\n    path: \"quiz\",\n    loadChildren: () => import(\"./features/quiz/quiz.module\").then((m) => m.QuizModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"payment\",\n    loadChildren: () => import(\"./features/payment/payment.module\").then((m) => m.PaymentModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"messages\",\n    loadChildren: () => import(\"./features/messages/messages.module\").then((m) => m.MessagesModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"certificates\",\n    loadChildren: () => import(\"./features/certificates/certificates.module\").then((m) => m.CertificatesModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"**\",\n    redirectTo: \"\",\n  },\n]\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule],\n})\nexport class AppRoutingModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAqB,iBAAiB;AAC3D,SAASC,SAAS,QAAQ,0BAA0B;;;AAEpD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;CACnF,EACD;EACEJ,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,UAAU;CACnF,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,eAAe,CAAC;EAClGC,WAAW,EAAE,CAACT,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,aAAa;CAC5F,EACD;EACER,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,UAAU,CAAC;EACnFF,WAAW,EAAE,CAACT,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACO,aAAa,CAAC;EAC5FH,WAAW,EAAE,CAACT,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACQ,cAAc,CAAC;EAC/FJ,WAAW,EAAE,CAACT,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,cAAc;EACpBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,kBAAkB,CAAC;EAC3GL,WAAW,EAAE,CAACT,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,IAAI;EACVa,UAAU,EAAE;CACb,CACF;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBjB,YAAY,CAACkB,OAAO,CAAChB,MAAM,CAAC,EAC5BF,YAAY;IAAA;EAAA;;;2EAEXiB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAApB,YAAA;IAAAqB,OAAA,GAFjBrB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}