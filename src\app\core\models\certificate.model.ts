// Correspond à la classe Certificat en .NET
export interface Certificat {
  id?: number
  nomClient: string         // Correspond à NomClient en .NET (AdminController)
  dateObtention: Date       // Correspond à DateObtention en .NET (AdminController)
  // Propriétés étendues pour le modèle complet
  coursId?: number
  cours?: {
    id: number
    titre: string
    formateur: {
      id: number
      nom: string
      prenom: string
    }
  }
  clientId?: number
  client?: {
    id: number
    nom: string
    prenom: string
  }
  adminId?: number
  admin?: {
    id: number
    nom: string
    prenom: string
  }
  dateGeneration?: Date
  scoreQuiz?: number
  seuilReussite?: number
  numeroSerie?: string
}

export interface CertificateRequest {
  nomClient: string
  prenomClient: string
  titreCours: string
  score: number
}
