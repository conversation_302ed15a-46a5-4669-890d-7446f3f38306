{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { FormsModule } from \"@angular/forms\"; // For ngModel in textarea\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatListModule } from \"@angular/material/list\";\nimport { MatBadgeModule } from \"@angular/material/badge\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatDividerModule } from \"@angular/material/divider\";\nimport { MessagesComponent } from \"./messages.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class MessagesModule {\n  static {\n    this.ɵfac = function MessagesModule_Factory(t) {\n      return new (t || MessagesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MessagesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatListModule, MatBadgeModule, MatChipsModule, MatDividerModule, RouterModule.forChild([{\n        path: \"\",\n        component: MessagesComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MessagesModule, {\n    declarations: [MessagesComponent],\n    imports: [CommonModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatListModule, MatBadgeModule, MatChipsModule, MatDividerModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatListModule", "MatBadgeModule", "MatChipsModule", "MatDividerModule", "MessagesComponent", "MessagesModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\messages\\messages.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // For ngModel in textarea\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatListModule } from \"@angular/material/list\"\nimport { MatBadgeModule } from \"@angular/material/badge\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatDividerModule } from \"@angular/material/divider\"\n\nimport { MessagesComponent } from \"./messages.component\"\n\n@NgModule({\n  declarations: [MessagesComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatForm<PERSON>ieldModule,\n    MatListModule,\n    MatBadgeModule,\n    MatChipsModule,\n    MatDividerModule,\n    RouterModule.forChild([{ path: \"\", component: MessagesComponent }]),\n  ],\n})\nexport class MessagesModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB,EAAC;AAE7C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,iBAAiB,QAAQ,sBAAsB;;;AAmBxD,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAdvBb,YAAY,EACZE,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBV,YAAY,CAACa,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAiB,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAG1DC,cAAc;IAAAI,YAAA,GAhBVL,iBAAiB;IAAAM,OAAA,GAE9BlB,YAAY,EACZE,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAAAQ,EAAA,CAAAlB,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}