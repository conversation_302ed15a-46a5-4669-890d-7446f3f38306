{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, EventEmitter, Optional, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3 from '@angular/cdk/platform';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, distinctUntilChanged, take, startWith, debounceTime } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"content\"];\nfunction MatDrawerContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatDrawerContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\nfunction MatDrawerContainer_mat_drawer_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-drawer-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = [[[\"mat-drawer\"]], [[\"mat-drawer-content\"]], \"*\"];\nconst _c3 = [\"mat-drawer\", \"mat-drawer-content\", \"*\"];\nfunction MatSidenavContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatSidenavContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\nfunction MatSidenavContainer_mat_sidenav_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-sidenav-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c4 = [[[\"mat-sidenav\"]], [[\"mat-sidenav-content\"]], \"*\"];\nconst _c5 = [\"mat-sidenav\", \"mat-sidenav-content\", \"*\"];\nconst _c6 = \".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\";\nconst matDrawerAnimations = {\n  /** Animation that slides a drawer in and out. */\n  transformDrawer: trigger('transform', [\n  // We remove the `transform` here completely, rather than setting it to zero, because:\n  // 1. Having a transform can cause elements with ripples or an animated\n  //    transform to shift around in Chrome with an RTL layout (see #10023).\n  // 2. 3d transforms causes text to appear blurry on IE and Edge.\n  state('open, open-instant', style({\n    'transform': 'none',\n    'visibility': 'visible'\n  })), state('void', style({\n    // Avoids the shadow showing up when closed in SSR.\n    'box-shadow': 'none',\n    'visibility': 'hidden'\n  })), transition('void => open-instant', animate('0ms')), transition('void <=> open, open-instant => void', animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'))])\n};\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nfunction throwMatDuplicatedDrawerError(position) {\n  throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n  providedIn: 'root',\n  factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/** @docs-private */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n  return false;\n}\nclass MatDrawerContent extends CdkScrollable {\n  constructor(_changeDetectorRef, _container, elementRef, scrollDispatcher, ngZone) {\n    super(elementRef, scrollDispatcher, ngZone);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._container = _container;\n  }\n  ngAfterContentInit() {\n    this._container._contentMarginChanges.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  static {\n    this.ɵfac = function MatDrawerContent_Factory(t) {\n      return new (t || MatDrawerContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatDrawerContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDrawerContent,\n      selectors: [[\"mat-drawer-content\"]],\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-drawer-content\"],\n      hostVars: 4,\n      hostBindings: function MatDrawerContent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkScrollable,\n        useExisting: MatDrawerContent\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatDrawerContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content',\n        '[style.margin-left.px]': '_container._contentMargins.left',\n        '[style.margin-right.px]': '_container._contentMargins.right',\n        'ngSkipHydration': ''\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatDrawerContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatDrawerContainer,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatDrawerContainer)]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nclass MatDrawer {\n  /** The side that the drawer is attached to. */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    // Make sure we have a valid value.\n    value = value === 'end' ? 'end' : 'start';\n    if (value !== this._position) {\n      // Static inputs in Ivy are set before the element is in the DOM.\n      if (this._isAttached) {\n        this._updatePositionInParent(value);\n      }\n      this._position = value;\n      this.onPositionChanged.emit();\n    }\n  }\n  /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n  get mode() {\n    return this._mode;\n  }\n  set mode(value) {\n    this._mode = value;\n    this._updateFocusTrapState();\n    this._modeChanged.next();\n  }\n  /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n  get disableClose() {\n    return this._disableClose;\n  }\n  set disableClose(value) {\n    this._disableClose = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the drawer should focus the first focusable element automatically when opened.\n   * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n   * enabled, focus will be moved into the sidenav in `side` mode as well.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n   * instead.\n   */\n  get autoFocus() {\n    const value = this._autoFocus;\n    // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n    // because we don't know how the sidenav is being used, but in some cases it still makes\n    // sense to do it. The consumer can explicitly set `autoFocus`.\n    if (value == null) {\n      if (this.mode === 'side') {\n        return 'dialog';\n      } else {\n        return 'first-tabbable';\n      }\n    }\n    return value;\n  }\n  set autoFocus(value) {\n    if (value === 'true' || value === 'false' || value == null) {\n      value = coerceBooleanProperty(value);\n    }\n    this._autoFocus = value;\n  }\n  /**\n   * Whether the drawer is opened. We overload this because we trigger an event when it\n   * starts or end.\n   */\n  get opened() {\n    return this._opened;\n  }\n  set opened(value) {\n    this.toggle(coerceBooleanProperty(value));\n  }\n  constructor(_elementRef, _focusTrapFactory, _focusMonitor, _platform, _ngZone, _interactivityChecker, _doc, _container) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    this._focusMonitor = _focusMonitor;\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._interactivityChecker = _interactivityChecker;\n    this._doc = _doc;\n    this._container = _container;\n    this._elementFocusedBeforeDrawerWasOpened = null;\n    /** Whether the drawer is initialized. Used for disabling the initial animation. */\n    this._enableAnimations = false;\n    this._position = 'start';\n    this._mode = 'over';\n    this._disableClose = false;\n    this._opened = false;\n    /** Emits whenever the drawer has started animating. */\n    this._animationStarted = new Subject();\n    /** Emits whenever the drawer is done animating. */\n    this._animationEnd = new Subject();\n    /** Current state of the sidenav animation. */\n    this._animationState = 'void';\n    /** Event emitted when the drawer open state is changed. */\n    this.openedChange =\n    // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n    new EventEmitter( /* isAsync */true);\n    /** Event emitted when the drawer has been opened. */\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the drawer has started opening. */\n    this.openedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0), mapTo(undefined));\n    /** Event emitted when the drawer has been closed. */\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the drawer has started closing. */\n    this.closedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState === 'void'), mapTo(undefined));\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Event emitted when the drawer's position changes. */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onPositionChanged = new EventEmitter();\n    /**\n     * An observable that emits when the drawer mode changes. This is used by the drawer container to\n     * to know when to when the mode changes so it can adapt the margins on the content.\n     */\n    this._modeChanged = new Subject();\n    this.openedChange.subscribe(opened => {\n      if (opened) {\n        if (this._doc) {\n          this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n        }\n        this._takeFocus();\n      } else if (this._isFocusWithinDrawer()) {\n        this._restoreFocus(this._openedVia || 'program');\n      }\n    });\n    /**\n     * Listen to `keydown` events outside the zone so that change detection is not run every\n     * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n     * and we don't have close disabled.\n     */\n    this._ngZone.runOutsideAngular(() => {\n      fromEvent(this._elementRef.nativeElement, 'keydown').pipe(filter(event => {\n        return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n      }), takeUntil(this._destroyed)).subscribe(event => this._ngZone.run(() => {\n        this.close();\n        event.stopPropagation();\n        event.preventDefault();\n      }));\n    });\n    // We need a Subject with distinctUntilChanged, because the `done` event\n    // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n    this._animationEnd.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      const {\n        fromState,\n        toState\n      } = event;\n      if (toState.indexOf('open') === 0 && fromState === 'void' || toState === 'void' && fromState.indexOf('open') === 0) {\n        this.openedChange.emit(this._opened);\n      }\n    });\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves focus into the drawer. Note that this works even if\n   * the focus trap is disabled in `side` mode.\n   */\n  _takeFocus() {\n    if (!this._focusTrap) {\n      return;\n    }\n    const element = this._elementRef.nativeElement;\n    // When autoFocus is not on the sidenav, if the element cannot be focused or does\n    // not exist, focus the sidenav itself so the keyboard navigation still works.\n    // We need to check that `focus` is a function due to Universal.\n    switch (this.autoFocus) {\n      case false:\n      case 'dialog':\n        return;\n      case true:\n      case 'first-tabbable':\n        this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n          if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n            element.focus();\n          }\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this.autoFocus);\n        break;\n    }\n  }\n  /**\n   * Restores focus to the element that was originally focused when the drawer opened.\n   * If no element was focused at that time, the focus will be restored to the drawer.\n   */\n  _restoreFocus(focusOrigin) {\n    if (this.autoFocus === 'dialog') {\n      return;\n    }\n    if (this._elementFocusedBeforeDrawerWasOpened) {\n      this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n    } else {\n      this._elementRef.nativeElement.blur();\n    }\n    this._elementFocusedBeforeDrawerWasOpened = null;\n  }\n  /** Whether focus is currently within the drawer. */\n  _isFocusWithinDrawer() {\n    const activeEl = this._doc.activeElement;\n    return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n  }\n  ngAfterViewInit() {\n    this._isAttached = true;\n    this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n    this._updateFocusTrapState();\n    // Only update the DOM position when the sidenav is positioned at\n    // the end since we project the sidenav before the content by default.\n    if (this._position === 'end') {\n      this._updatePositionInParent('end');\n    }\n  }\n  ngAfterContentChecked() {\n    // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n    // drawers that are open by default. When we're on the server, we shouldn't enable the\n    // animations, because we don't want the drawer to animate the first time the user sees\n    // the page.\n    if (this._platform.isBrowser) {\n      this._enableAnimations = true;\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n    this._anchor?.remove();\n    this._anchor = null;\n    this._animationStarted.complete();\n    this._animationEnd.complete();\n    this._modeChanged.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Open the drawer.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  open(openedVia) {\n    return this.toggle(true, openedVia);\n  }\n  /** Close the drawer. */\n  close() {\n    return this.toggle(false);\n  }\n  /** Closes the drawer with context that the backdrop was clicked. */\n  _closeViaBackdropClick() {\n    // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n    // don't need to check whether focus is currently in the drawer, as clicking on the\n    // backdrop causes blurs the active element.\n    return this._setOpen( /* isOpen */false, /* restoreFocus */true, 'mouse');\n  }\n  /**\n   * Toggle this drawer.\n   * @param isOpen Whether the drawer should be open.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  toggle(isOpen = !this.opened, openedVia) {\n    // If the focus is currently inside the drawer content and we are closing the drawer,\n    // restore the focus to the initially focused element (when the drawer opened).\n    if (isOpen && openedVia) {\n      this._openedVia = openedVia;\n    }\n    const result = this._setOpen(isOpen, /* restoreFocus */!isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n    if (!isOpen) {\n      this._openedVia = null;\n    }\n    return result;\n  }\n  /**\n   * Toggles the opened state of the drawer.\n   * @param isOpen Whether the drawer should open or close.\n   * @param restoreFocus Whether focus should be restored on close.\n   * @param focusOrigin Origin to use when restoring focus.\n   */\n  _setOpen(isOpen, restoreFocus, focusOrigin) {\n    this._opened = isOpen;\n    if (isOpen) {\n      this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n    } else {\n      this._animationState = 'void';\n      if (restoreFocus) {\n        this._restoreFocus(focusOrigin);\n      }\n    }\n    this._updateFocusTrapState();\n    return new Promise(resolve => {\n      this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n    });\n  }\n  _getWidth() {\n    return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n  }\n  /** Updates the enabled state of the focus trap. */\n  _updateFocusTrapState() {\n    if (this._focusTrap) {\n      // Trap focus only if the backdrop is enabled. Otherwise, allow end user to interact with the\n      // sidenav content.\n      this._focusTrap.enabled = !!this._container?.hasBackdrop;\n    }\n  }\n  /**\n   * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n   * when it's in the `end` position so that it comes after the content and the visual order\n   * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n   * started off as `end` and was changed to `start`.\n   */\n  _updatePositionInParent(newPosition) {\n    const element = this._elementRef.nativeElement;\n    const parent = element.parentNode;\n    if (newPosition === 'end') {\n      if (!this._anchor) {\n        this._anchor = this._doc.createComment('mat-drawer-anchor');\n        parent.insertBefore(this._anchor, element);\n      }\n      parent.appendChild(element);\n    } else if (this._anchor) {\n      this._anchor.parentNode.insertBefore(element, this._anchor);\n    }\n  }\n  static {\n    this.ɵfac = function MatDrawer_Factory(t) {\n      return new (t || MatDrawer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusTrapFactory), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.InteractivityChecker), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MAT_DRAWER_CONTAINER, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDrawer,\n      selectors: [[\"mat-drawer\"]],\n      viewQuery: function MatDrawer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n        }\n      },\n      hostAttrs: [\"tabIndex\", \"-1\", \"ngSkipHydration\", \"\", 1, \"mat-drawer\"],\n      hostVars: 12,\n      hostBindings: function MatDrawer_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵsyntheticHostListener(\"@transform.start\", function MatDrawer_animation_transform_start_HostBindingHandler($event) {\n            return ctx._animationStarted.next($event);\n          })(\"@transform.done\", function MatDrawer_animation_transform_done_HostBindingHandler($event) {\n            return ctx._animationEnd.next($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"align\", null);\n          i0.ɵɵsyntheticHostProperty(\"@transform\", ctx._animationState);\n          i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened);\n        }\n      },\n      inputs: {\n        position: \"position\",\n        mode: \"mode\",\n        disableClose: \"disableClose\",\n        autoFocus: \"autoFocus\",\n        opened: \"opened\"\n      },\n      outputs: {\n        openedChange: \"openedChange\",\n        _openedStream: \"opened\",\n        openedStart: \"openedStart\",\n        _closedStream: \"closed\",\n        closedStart: \"closedStart\",\n        onPositionChanged: \"positionChanged\"\n      },\n      exportAs: [\"matDrawer\"],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n      template: function MatDrawer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.CdkScrollable],\n      encapsulation: 2,\n      data: {\n        animation: [matDrawerAnimations.transformDrawer]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer',\n      exportAs: 'matDrawer',\n      animations: [matDrawerAnimations.transformDrawer],\n      host: {\n        'class': 'mat-drawer',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        '[class.mat-drawer-opened]': 'opened',\n        'tabIndex': '-1',\n        '[@transform]': '_animationState',\n        '(@transform.start)': '_animationStarted.next($event)',\n        '(@transform.done)': '_animationEnd.next($event)',\n        'ngSkipHydration': ''\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i2.FocusTrapFactory\n    }, {\n      type: i2.FocusMonitor\n    }, {\n      type: i3.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.InteractivityChecker\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: MatDrawerContainer,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_DRAWER_CONTAINER]\n      }]\n    }];\n  }, {\n    position: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    disableClose: [{\n      type: Input\n    }],\n    autoFocus: [{\n      type: Input\n    }],\n    opened: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    openedStart: [{\n      type: Output\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    closedStart: [{\n      type: Output\n    }],\n    onPositionChanged: [{\n      type: Output,\n      args: ['positionChanged']\n    }],\n    _content: [{\n      type: ViewChild,\n      args: ['content']\n    }]\n  });\n})();\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nclass MatDrawerContainer {\n  /** The drawer child with the `start` position. */\n  get start() {\n    return this._start;\n  }\n  /** The drawer child with the `end` position. */\n  get end() {\n    return this._end;\n  }\n  /**\n   * Whether to automatically resize the container whenever\n   * the size of any of its drawers changes.\n   *\n   * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n   * the drawers on every change detection cycle. Can be configured globally via the\n   * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n   */\n  get autosize() {\n    return this._autosize;\n  }\n  set autosize(value) {\n    this._autosize = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n   * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n   * mode as well.\n   */\n  get hasBackdrop() {\n    return this._drawerHasBackdrop(this._start) || this._drawerHasBackdrop(this._end);\n  }\n  set hasBackdrop(value) {\n    this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n  }\n  /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n  get scrollable() {\n    return this._userContent || this._content;\n  }\n  constructor(_dir, _element, _ngZone, _changeDetectorRef, viewportRuler, defaultAutosize = false, _animationMode) {\n    this._dir = _dir;\n    this._element = _element;\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** Drawers that belong to this container. */\n    this._drawers = new QueryList();\n    /** Event emitted when the drawer backdrop is clicked. */\n    this.backdropClick = new EventEmitter();\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Emits on every ngDoCheck. Used for debouncing reflows. */\n    this._doCheckSubject = new Subject();\n    /**\n     * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n     * drawer is open. We use margin rather than transform even for push mode because transform breaks\n     * fixed position elements inside of the transformed element.\n     */\n    this._contentMargins = {\n      left: null,\n      right: null\n    };\n    this._contentMarginChanges = new Subject();\n    // If a `Dir` directive exists up the tree, listen direction changes\n    // and update the left/right properties to point to the proper start/end.\n    if (_dir) {\n      _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._validateDrawers();\n        this.updateContentMargins();\n      });\n    }\n    // Since the minimum width of the sidenav depends on the viewport width,\n    // we need to recompute the margins if the viewport changes.\n    viewportRuler.change().pipe(takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    this._autosize = defaultAutosize;\n  }\n  ngAfterContentInit() {\n    this._allDrawers.changes.pipe(startWith(this._allDrawers), takeUntil(this._destroyed)).subscribe(drawer => {\n      this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n      this._drawers.notifyOnChanges();\n    });\n    this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n      this._validateDrawers();\n      this._drawers.forEach(drawer => {\n        this._watchDrawerToggle(drawer);\n        this._watchDrawerPosition(drawer);\n        this._watchDrawerMode(drawer);\n      });\n      if (!this._drawers.length || this._isDrawerOpen(this._start) || this._isDrawerOpen(this._end)) {\n        this.updateContentMargins();\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n    // Avoid hitting the NgZone through the debounce timeout.\n    this._ngZone.runOutsideAngular(() => {\n      this._doCheckSubject.pipe(debounceTime(10),\n      // Arbitrary debounce time, less than a frame at 60fps\n      takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    });\n  }\n  ngOnDestroy() {\n    this._contentMarginChanges.complete();\n    this._doCheckSubject.complete();\n    this._drawers.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Calls `open` of both start and end drawers */\n  open() {\n    this._drawers.forEach(drawer => drawer.open());\n  }\n  /** Calls `close` of both start and end drawers */\n  close() {\n    this._drawers.forEach(drawer => drawer.close());\n  }\n  /**\n   * Recalculates and updates the inline styles for the content. Note that this should be used\n   * sparingly, because it causes a reflow.\n   */\n  updateContentMargins() {\n    // 1. For drawers in `over` mode, they don't affect the content.\n    // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n    //    left margin (for left drawer) or right margin (for right the drawer).\n    // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n    //    adding to the left or right margin and simultaneously subtracting the same amount of\n    //    margin from the other side.\n    let left = 0;\n    let right = 0;\n    if (this._left && this._left.opened) {\n      if (this._left.mode == 'side') {\n        left += this._left._getWidth();\n      } else if (this._left.mode == 'push') {\n        const width = this._left._getWidth();\n        left += width;\n        right -= width;\n      }\n    }\n    if (this._right && this._right.opened) {\n      if (this._right.mode == 'side') {\n        right += this._right._getWidth();\n      } else if (this._right.mode == 'push') {\n        const width = this._right._getWidth();\n        right += width;\n        left -= width;\n      }\n    }\n    // If either `right` or `left` is zero, don't set a style to the element. This\n    // allows users to specify a custom size via CSS class in SSR scenarios where the\n    // measured widths will always be zero. Note that we reset to `null` here, rather\n    // than below, in order to ensure that the types in the `if` below are consistent.\n    left = left || null;\n    right = right || null;\n    if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n      this._contentMargins = {\n        left,\n        right\n      };\n      // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n      // to do it only when something changed, otherwise we can end up hitting the zone too often.\n      this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n    }\n  }\n  ngDoCheck() {\n    // If users opted into autosizing, do a check every change detection cycle.\n    if (this._autosize && this._isPushed()) {\n      // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n      this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n    }\n  }\n  /**\n   * Subscribes to drawer events in order to set a class on the main container element when the\n   * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n   * is properly hidden.\n   */\n  _watchDrawerToggle(drawer) {\n    drawer._animationStarted.pipe(filter(event => event.fromState !== event.toState), takeUntil(this._drawers.changes)).subscribe(event => {\n      // Set the transition class on the container so that the animations occur. This should not\n      // be set initially because animations should only be triggered via a change in state.\n      if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n        this._element.nativeElement.classList.add('mat-drawer-transition');\n      }\n      this.updateContentMargins();\n      this._changeDetectorRef.markForCheck();\n    });\n    if (drawer.mode !== 'side') {\n      drawer.openedChange.pipe(takeUntil(this._drawers.changes)).subscribe(() => this._setContainerClass(drawer.opened));\n    }\n  }\n  /**\n   * Subscribes to drawer onPositionChanged event in order to\n   * re-validate drawers when the position changes.\n   */\n  _watchDrawerPosition(drawer) {\n    if (!drawer) {\n      return;\n    }\n    // NOTE: We need to wait for the microtask queue to be empty before validating,\n    // since both drawers may be swapping positions at the same time.\n    drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n      this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n        this._validateDrawers();\n      });\n    });\n  }\n  /** Subscribes to changes in drawer mode so we can run change detection. */\n  _watchDrawerMode(drawer) {\n    if (drawer) {\n      drawer._modeChanged.pipe(takeUntil(merge(this._drawers.changes, this._destroyed))).subscribe(() => {\n        this.updateContentMargins();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n  _setContainerClass(isAdd) {\n    const classList = this._element.nativeElement.classList;\n    const className = 'mat-drawer-container-has-open';\n    if (isAdd) {\n      classList.add(className);\n    } else {\n      classList.remove(className);\n    }\n  }\n  /** Validate the state of the drawer children components. */\n  _validateDrawers() {\n    this._start = this._end = null;\n    // Ensure that we have at most one start and one end drawer.\n    this._drawers.forEach(drawer => {\n      if (drawer.position == 'end') {\n        if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('end');\n        }\n        this._end = drawer;\n      } else {\n        if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('start');\n        }\n        this._start = drawer;\n      }\n    });\n    this._right = this._left = null;\n    // Detect if we're LTR or RTL.\n    if (this._dir && this._dir.value === 'rtl') {\n      this._left = this._end;\n      this._right = this._start;\n    } else {\n      this._left = this._start;\n      this._right = this._end;\n    }\n  }\n  /** Whether the container is being pushed to the side by one of the drawers. */\n  _isPushed() {\n    return this._isDrawerOpen(this._start) && this._start.mode != 'over' || this._isDrawerOpen(this._end) && this._end.mode != 'over';\n  }\n  _onBackdropClicked() {\n    this.backdropClick.emit();\n    this._closeModalDrawersViaBackdrop();\n  }\n  _closeModalDrawersViaBackdrop() {\n    // Close all open drawers where closing is not disabled and the mode is not `side`.\n    [this._start, this._end].filter(drawer => drawer && !drawer.disableClose && this._drawerHasBackdrop(drawer)).forEach(drawer => drawer._closeViaBackdropClick());\n  }\n  _isShowingBackdrop() {\n    return this._isDrawerOpen(this._start) && this._drawerHasBackdrop(this._start) || this._isDrawerOpen(this._end) && this._drawerHasBackdrop(this._end);\n  }\n  _isDrawerOpen(drawer) {\n    return drawer != null && drawer.opened;\n  }\n  // Whether argument drawer should have a backdrop when it opens\n  _drawerHasBackdrop(drawer) {\n    if (this._backdropOverride == null) {\n      return !!drawer && drawer.mode !== 'side';\n    }\n    return this._backdropOverride;\n  }\n  static {\n    this.ɵfac = function MatDrawerContainer_Factory(t) {\n      return new (t || MatDrawerContainer)(i0.ɵɵdirectiveInject(i4.Directionality, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(MAT_DRAWER_DEFAULT_AUTOSIZE), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDrawerContainer,\n      selectors: [[\"mat-drawer-container\"]],\n      contentQueries: function MatDrawerContainer_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatDrawerContent, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatDrawer, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n        }\n      },\n      viewQuery: function MatDrawerContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatDrawerContent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._userContent = _t.first);\n        }\n      },\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-drawer-container\"],\n      hostVars: 2,\n      hostBindings: function MatDrawerContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n        }\n      },\n      inputs: {\n        autosize: \"autosize\",\n        hasBackdrop: \"hasBackdrop\"\n      },\n      outputs: {\n        backdropClick: \"backdropClick\"\n      },\n      exportAs: [\"matDrawerContainer\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatDrawerContainer\n      }])],\n      ngContentSelectors: _c3,\n      decls: 4,\n      vars: 2,\n      consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n      template: function MatDrawerContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵtemplate(0, MatDrawerContainer_div_0_Template, 1, 2, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵtemplate(3, MatDrawerContainer_mat_drawer_content_3_Template, 2, 0, \"mat-drawer-content\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx._content);\n        }\n      },\n      dependencies: [i5.NgIf, MatDrawerContent],\n      styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-container',\n      exportAs: 'matDrawerContainer',\n      host: {\n        'class': 'mat-drawer-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n        'ngSkipHydration': ''\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatDrawerContainer\n      }],\n      template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"]\n    }]\n  }], function () {\n    return [{\n      type: i4.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_DRAWER_DEFAULT_AUTOSIZE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatDrawer, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatDrawerContent]\n    }],\n    _userContent: [{\n      type: ViewChild,\n      args: [MatDrawerContent]\n    }],\n    autosize: [{\n      type: Input\n    }],\n    hasBackdrop: [{\n      type: Input\n    }],\n    backdropClick: [{\n      type: Output\n    }]\n  });\n})();\nclass MatSidenavContent extends MatDrawerContent {\n  constructor(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone) {\n    super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n  }\n  static {\n    this.ɵfac = function MatSidenavContent_Factory(t) {\n      return new (t || MatSidenavContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatSidenavContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSidenavContent,\n      selectors: [[\"mat-sidenav-content\"]],\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-drawer-content\", \"mat-sidenav-content\"],\n      hostVars: 4,\n      hostBindings: function MatSidenavContent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkScrollable,\n        useExisting: MatSidenavContent\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatSidenavContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content mat-sidenav-content',\n        '[style.margin-left.px]': '_container._contentMargins.left',\n        '[style.margin-right.px]': '_container._contentMargins.right',\n        'ngSkipHydration': ''\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatSidenavContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatSidenavContainer,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatSidenavContainer)]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass MatSidenav extends MatDrawer {\n  constructor() {\n    super(...arguments);\n    this._fixedInViewport = false;\n    this._fixedTopGap = 0;\n    this._fixedBottomGap = 0;\n  }\n  /** Whether the sidenav is fixed in the viewport. */\n  get fixedInViewport() {\n    return this._fixedInViewport;\n  }\n  set fixedInViewport(value) {\n    this._fixedInViewport = coerceBooleanProperty(value);\n  }\n  /**\n   * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n   * mode.\n   */\n  get fixedTopGap() {\n    return this._fixedTopGap;\n  }\n  set fixedTopGap(value) {\n    this._fixedTopGap = coerceNumberProperty(value);\n  }\n  /**\n   * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n   * fixed mode.\n   */\n  get fixedBottomGap() {\n    return this._fixedBottomGap;\n  }\n  set fixedBottomGap(value) {\n    this._fixedBottomGap = coerceNumberProperty(value);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatSidenav_BaseFactory;\n      return function MatSidenav_Factory(t) {\n        return (ɵMatSidenav_BaseFactory || (ɵMatSidenav_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenav)))(t || MatSidenav);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSidenav,\n      selectors: [[\"mat-sidenav\"]],\n      hostAttrs: [\"tabIndex\", \"-1\", \"ngSkipHydration\", \"\", 1, \"mat-drawer\", \"mat-sidenav\"],\n      hostVars: 17,\n      hostBindings: function MatSidenav_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"align\", null);\n          i0.ɵɵstyleProp(\"top\", ctx.fixedInViewport ? ctx.fixedTopGap : null, \"px\")(\"bottom\", ctx.fixedInViewport ? ctx.fixedBottomGap : null, \"px\");\n          i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened)(\"mat-sidenav-fixed\", ctx.fixedInViewport);\n        }\n      },\n      inputs: {\n        fixedInViewport: \"fixedInViewport\",\n        fixedTopGap: \"fixedTopGap\",\n        fixedBottomGap: \"fixedBottomGap\"\n      },\n      exportAs: [\"matSidenav\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n      template: function MatSidenav_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.CdkScrollable],\n      encapsulation: 2,\n      data: {\n        animation: [matDrawerAnimations.transformDrawer]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenav, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav',\n      exportAs: 'matSidenav',\n      animations: [matDrawerAnimations.transformDrawer],\n      host: {\n        'class': 'mat-drawer mat-sidenav',\n        'tabIndex': '-1',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        '[class.mat-drawer-opened]': 'opened',\n        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null',\n        'ngSkipHydration': ''\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], null, {\n    fixedInViewport: [{\n      type: Input\n    }],\n    fixedTopGap: [{\n      type: Input\n    }],\n    fixedBottomGap: [{\n      type: Input\n    }]\n  });\n})();\nclass MatSidenavContainer extends MatDrawerContainer {\n  constructor() {\n    super(...arguments);\n    this._allDrawers = undefined;\n    // We need an initializer here to avoid a TS error.\n    this._content = undefined;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatSidenavContainer_BaseFactory;\n      return function MatSidenavContainer_Factory(t) {\n        return (ɵMatSidenavContainer_BaseFactory || (ɵMatSidenavContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContainer)))(t || MatSidenavContainer);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSidenavContainer,\n      selectors: [[\"mat-sidenav-container\"]],\n      contentQueries: function MatSidenavContainer_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatSidenavContent, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatSidenav, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n        }\n      },\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-drawer-container\", \"mat-sidenav-container\"],\n      hostVars: 2,\n      hostBindings: function MatSidenavContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n        }\n      },\n      exportAs: [\"matSidenavContainer\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatSidenavContainer\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 4,\n      vars: 2,\n      consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n      template: function MatSidenavContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c4);\n          i0.ɵɵtemplate(0, MatSidenavContainer_div_0_Template, 1, 2, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵtemplate(3, MatSidenavContainer_mat_sidenav_content_3_Template, 2, 0, \"mat-sidenav-content\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx._content);\n        }\n      },\n      dependencies: [i5.NgIf, MatSidenavContent],\n      styles: [_c6],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-container',\n      exportAs: 'matSidenavContainer',\n      host: {\n        'class': 'mat-drawer-container mat-sidenav-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n        'ngSkipHydration': ''\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatSidenavContainer\n      }],\n      template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"]\n    }]\n  }], null, {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatSidenav, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatSidenavContent]\n    }]\n  });\n})();\nclass MatSidenavModule {\n  static {\n    this.ɵfac = function MatSidenavModule_Factory(t) {\n      return new (t || MatSidenavModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSidenavModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, MatCommonModule, CdkScrollableModule, CdkScrollableModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, CdkScrollableModule],\n      exports: [CdkScrollableModule, MatCommonModule, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent],\n      declarations: [MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };", "map": {"version": 3, "names": ["i1", "CdkScrollable", "CdkScrollableModule", "i5", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "EventEmitter", "Optional", "Input", "Output", "ViewChild", "QueryList", "ContentChildren", "ContentChild", "NgModule", "MatCommonModule", "i2", "i4", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "i3", "Subject", "fromEvent", "merge", "filter", "map", "mapTo", "takeUntil", "distinctUntilChanged", "take", "startWith", "debounceTime", "trigger", "state", "style", "transition", "animate", "ANIMATION_MODULE_TYPE", "_c0", "_c1", "MatDrawerContainer_div_0_Template", "rf", "ctx", "_r3", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatDrawerContainer_div_0_Template_div_click_0_listener", "ɵɵrestoreView", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "_onBackdropClicked", "ɵɵelementEnd", "ctx_r0", "ɵɵclassProp", "_isShowingBackdrop", "MatDrawerContainer_mat_drawer_content_3_Template", "ɵɵprojection", "_c2", "_c3", "MatSidenavContainer_div_0_Template", "MatSidenavContainer_div_0_Template_div_click_0_listener", "MatSidenavContainer_mat_sidenav_content_3_Template", "_c4", "_c5", "_c6", "matDrawerAnimations", "transformDrawer", "throwMatDuplicatedDrawerError", "position", "Error", "MAT_DRAWER_DEFAULT_AUTOSIZE", "providedIn", "factory", "MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY", "MAT_DRAWER_CONTAINER", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_changeDetectorRef", "_container", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "ngAfterContentInit", "_contentMarginChanges", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "MatDrawerContent_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "Mat<PERSON>rawerContainer", "ElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NgZone", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatDrawerContent_HostBindings", "ɵɵstyleProp", "_contentMargins", "left", "right", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "decls", "vars", "template", "MatDrawerContent_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "providers", "decorators", "<PERSON><PERSON><PERSON><PERSON>", "_position", "value", "_isAttached", "_updatePositionInParent", "onPositionChanged", "emit", "mode", "_mode", "_updateFocusTrapState", "_modeChanged", "next", "disableClose", "_disableClose", "autoFocus", "_autoFocus", "opened", "_opened", "toggle", "_elementRef", "_focusTrapFactory", "_focusMonitor", "_platform", "_ngZone", "_interactivityC<PERSON>cker", "_doc", "_elementFocusedBeforeDrawerWasOpened", "_enableAnimations", "_animationStarted", "_animationEnd", "_animationState", "openedChange", "_openedStream", "pipe", "o", "openedStart", "e", "fromState", "toState", "indexOf", "undefined", "_closedStream", "closedStart", "_destroyed", "activeElement", "_takeFocus", "_isFocusWithinDrawer", "_restoreFocus", "_openedVia", "runOutsideAngular", "nativeElement", "event", "keyCode", "run", "close", "stopPropagation", "preventDefault", "x", "y", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "callback", "removeEventListener", "removeAttribute", "addEventListener", "focus", "_focusByCssSelector", "elementToFocus", "querySelector", "_focusTrap", "focusInitialElementWhenReady", "then", "hasMovedFocus", "<PERSON><PERSON><PERSON><PERSON>", "focusVia", "blur", "activeEl", "contains", "ngAfterViewInit", "create", "ngAfterContentChecked", "<PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "destroy", "_anchor", "remove", "complete", "open", "openedVia", "_closeViaBackdropClick", "_setOpen", "isOpen", "result", "restoreFocus", "Promise", "resolve", "_getWidth", "offsetWidth", "enabled", "hasBackdrop", "newPosition", "parent", "parentNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "MatDrawer_Factory", "FocusTrapFactory", "FocusMonitor", "Platform", "InteractivityChecker", "viewQuery", "MatDrawer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "_content", "first", "MatDrawer_HostBindings", "ɵɵsyntheticHostListener", "MatDrawer_animation_transform_start_HostBindingHandler", "$event", "Mat<PERSON>rawer_animation_transform_done_HostBindingHandler", "ɵɵattribute", "ɵɵsyntheticHostProperty", "inputs", "outputs", "exportAs", "consts", "MatDrawer_Template", "dependencies", "data", "animation", "animations", "start", "_start", "end", "_end", "autosize", "_autosize", "_drawerHasBackdrop", "_backdropOverride", "scrollable", "_userContent", "_dir", "_element", "viewportRuler", "defaultAutosize", "_animationMode", "_drawers", "backdropClick", "_doCheckSubject", "change", "_validateDrawers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_allDrawers", "changes", "drawer", "reset", "item", "notifyOn<PERSON><PERSON>es", "for<PERSON>ach", "_watchDrawerToggle", "_watchDrawerPosition", "_watchDrawerMode", "length", "_isDrawerOpen", "_left", "width", "_right", "ngDoCheck", "_isPushed", "classList", "add", "_setContainerClass", "onMicrotaskEmpty", "isAdd", "className", "_closeModalDrawersViaBackdrop", "MatDrawerContainer_Factory", "Directionality", "ViewportRuler", "contentQueries", "MatDrawerContainer_ContentQueries", "dirIndex", "ɵɵcontentQuery", "MatDrawerContainer_Query", "MatDrawerContainer_HostBindings", "MatDrawerContainer_Template", "ɵɵtemplate", "ɵɵproperty", "ɵɵadvance", "NgIf", "styles", "descendants", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeDetectorRef", "container", "MatSidenavContent_Factory", "Mat<PERSON>idenav<PERSON><PERSON>r", "MatSidenavContent_HostBindings", "MatSidenavContent_Template", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "_fixedInViewport", "_fixedTopGap", "_fixedBottomGap", "fixedInViewport", "fixedTopGap", "fixedBottomGap", "ɵMatSidenav_BaseFactory", "MatSidenav_Factory", "ɵɵgetInheritedFactory", "MatSidenav_HostBindings", "MatSidenav_Template", "ɵMatSidenavContainer_BaseFactory", "MatSidenavContainer_Factory", "MatSidenavContainer_ContentQueries", "MatSidenavContainer_HostBindings", "MatSidenavContainer_Template", "MatSidenavModule", "MatSidenavModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/e-learning/node_modules/@angular/material/fesm2022/sidenav.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, EventEmitter, Optional, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3 from '@angular/cdk/platform';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, distinctUntilChanged, take, startWith, debounceTime } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\nconst matDrawerAnimations = {\n    /** Animation that slides a drawer in and out. */\n    transformDrawer: trigger('transform', [\n        // We remove the `transform` here completely, rather than setting it to zero, because:\n        // 1. Having a transform can cause elements with ripples or an animated\n        //    transform to shift around in Chrome with an RTL layout (see #10023).\n        // 2. 3d transforms causes text to appear blurry on IE and Edge.\n        state('open, open-instant', style({\n            'transform': 'none',\n            'visibility': 'visible',\n        })),\n        state('void', style({\n            // Avoids the shadow showing up when closed in SSR.\n            'box-shadow': 'none',\n            'visibility': 'hidden',\n        })),\n        transition('void => open-instant', animate('0ms')),\n        transition('void <=> open, open-instant => void', animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)')),\n    ]),\n};\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nfunction throwMatDuplicatedDrawerError(position) {\n    throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n    providedIn: 'root',\n    factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY,\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/** @docs-private */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n    return false;\n}\nclass MatDrawerContent extends CdkScrollable {\n    constructor(_changeDetectorRef, _container, elementRef, scrollDispatcher, ngZone) {\n        super(elementRef, scrollDispatcher, ngZone);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._container = _container;\n    }\n    ngAfterContentInit() {\n        this._container._contentMarginChanges.subscribe(() => {\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDrawerContent, deps: [{ token: i0.ChangeDetectorRef }, { token: forwardRef(() => MatDrawerContainer) }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDrawerContent, selector: \"mat-drawer-content\", host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"style.margin-left.px\": \"_container._contentMargins.left\", \"style.margin-right.px\": \"_container._contentMargins.right\" }, classAttribute: \"mat-drawer-content\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useExisting: MatDrawerContent,\n            },\n        ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDrawerContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-drawer-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content',\n                        '[style.margin-left.px]': '_container._contentMargins.left',\n                        '[style.margin-right.px]': '_container._contentMargins.right',\n                        'ngSkipHydration': '',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatDrawerContent,\n                        },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: MatDrawerContainer, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatDrawerContainer)]\n                }] }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.NgZone }]; } });\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nclass MatDrawer {\n    /** The side that the drawer is attached to. */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        // Make sure we have a valid value.\n        value = value === 'end' ? 'end' : 'start';\n        if (value !== this._position) {\n            // Static inputs in Ivy are set before the element is in the DOM.\n            if (this._isAttached) {\n                this._updatePositionInParent(value);\n            }\n            this._position = value;\n            this.onPositionChanged.emit();\n        }\n    }\n    /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n    get mode() {\n        return this._mode;\n    }\n    set mode(value) {\n        this._mode = value;\n        this._updateFocusTrapState();\n        this._modeChanged.next();\n    }\n    /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n    get disableClose() {\n        return this._disableClose;\n    }\n    set disableClose(value) {\n        this._disableClose = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer should focus the first focusable element automatically when opened.\n     * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n     * enabled, focus will be moved into the sidenav in `side` mode as well.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n     * instead.\n     */\n    get autoFocus() {\n        const value = this._autoFocus;\n        // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n        // because we don't know how the sidenav is being used, but in some cases it still makes\n        // sense to do it. The consumer can explicitly set `autoFocus`.\n        if (value == null) {\n            if (this.mode === 'side') {\n                return 'dialog';\n            }\n            else {\n                return 'first-tabbable';\n            }\n        }\n        return value;\n    }\n    set autoFocus(value) {\n        if (value === 'true' || value === 'false' || value == null) {\n            value = coerceBooleanProperty(value);\n        }\n        this._autoFocus = value;\n    }\n    /**\n     * Whether the drawer is opened. We overload this because we trigger an event when it\n     * starts or end.\n     */\n    get opened() {\n        return this._opened;\n    }\n    set opened(value) {\n        this.toggle(coerceBooleanProperty(value));\n    }\n    constructor(_elementRef, _focusTrapFactory, _focusMonitor, _platform, _ngZone, _interactivityChecker, _doc, _container) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        this._focusMonitor = _focusMonitor;\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._interactivityChecker = _interactivityChecker;\n        this._doc = _doc;\n        this._container = _container;\n        this._elementFocusedBeforeDrawerWasOpened = null;\n        /** Whether the drawer is initialized. Used for disabling the initial animation. */\n        this._enableAnimations = false;\n        this._position = 'start';\n        this._mode = 'over';\n        this._disableClose = false;\n        this._opened = false;\n        /** Emits whenever the drawer has started animating. */\n        this._animationStarted = new Subject();\n        /** Emits whenever the drawer is done animating. */\n        this._animationEnd = new Subject();\n        /** Current state of the sidenav animation. */\n        this._animationState = 'void';\n        /** Event emitted when the drawer open state is changed. */\n        this.openedChange = \n        // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n        new EventEmitter(/* isAsync */ true);\n        /** Event emitted when the drawer has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the drawer has started opening. */\n        this.openedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0), mapTo(undefined));\n        /** Event emitted when the drawer has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the drawer has started closing. */\n        this.closedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState === 'void'), mapTo(undefined));\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Event emitted when the drawer's position changes. */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onPositionChanged = new EventEmitter();\n        /**\n         * An observable that emits when the drawer mode changes. This is used by the drawer container to\n         * to know when to when the mode changes so it can adapt the margins on the content.\n         */\n        this._modeChanged = new Subject();\n        this.openedChange.subscribe((opened) => {\n            if (opened) {\n                if (this._doc) {\n                    this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n                }\n                this._takeFocus();\n            }\n            else if (this._isFocusWithinDrawer()) {\n                this._restoreFocus(this._openedVia || 'program');\n            }\n        });\n        /**\n         * Listen to `keydown` events outside the zone so that change detection is not run every\n         * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n         * and we don't have close disabled.\n         */\n        this._ngZone.runOutsideAngular(() => {\n            fromEvent(this._elementRef.nativeElement, 'keydown')\n                .pipe(filter(event => {\n                return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n            }), takeUntil(this._destroyed))\n                .subscribe(event => this._ngZone.run(() => {\n                this.close();\n                event.stopPropagation();\n                event.preventDefault();\n            }));\n        });\n        // We need a Subject with distinctUntilChanged, because the `done` event\n        // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n        this._animationEnd\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe((event) => {\n            const { fromState, toState } = event;\n            if ((toState.indexOf('open') === 0 && fromState === 'void') ||\n                (toState === 'void' && fromState.indexOf('open') === 0)) {\n                this.openedChange.emit(this._opened);\n            }\n        });\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    element.removeEventListener('blur', callback);\n                    element.removeEventListener('mousedown', callback);\n                    element.removeAttribute('tabindex');\n                };\n                element.addEventListener('blur', callback);\n                element.addEventListener('mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves focus into the drawer. Note that this works even if\n     * the focus trap is disabled in `side` mode.\n     */\n    _takeFocus() {\n        if (!this._focusTrap) {\n            return;\n        }\n        const element = this._elementRef.nativeElement;\n        // When autoFocus is not on the sidenav, if the element cannot be focused or does\n        // not exist, focus the sidenav itself so the keyboard navigation still works.\n        // We need to check that `focus` is a function due to Universal.\n        switch (this.autoFocus) {\n            case false:\n            case 'dialog':\n                return;\n            case true:\n            case 'first-tabbable':\n                this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n                    if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n                        element.focus();\n                    }\n                });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this.autoFocus);\n                break;\n        }\n    }\n    /**\n     * Restores focus to the element that was originally focused when the drawer opened.\n     * If no element was focused at that time, the focus will be restored to the drawer.\n     */\n    _restoreFocus(focusOrigin) {\n        if (this.autoFocus === 'dialog') {\n            return;\n        }\n        if (this._elementFocusedBeforeDrawerWasOpened) {\n            this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n        }\n        else {\n            this._elementRef.nativeElement.blur();\n        }\n        this._elementFocusedBeforeDrawerWasOpened = null;\n    }\n    /** Whether focus is currently within the drawer. */\n    _isFocusWithinDrawer() {\n        const activeEl = this._doc.activeElement;\n        return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n    }\n    ngAfterViewInit() {\n        this._isAttached = true;\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        this._updateFocusTrapState();\n        // Only update the DOM position when the sidenav is positioned at\n        // the end since we project the sidenav before the content by default.\n        if (this._position === 'end') {\n            this._updatePositionInParent('end');\n        }\n    }\n    ngAfterContentChecked() {\n        // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n        // drawers that are open by default. When we're on the server, we shouldn't enable the\n        // animations, because we don't want the drawer to animate the first time the user sees\n        // the page.\n        if (this._platform.isBrowser) {\n            this._enableAnimations = true;\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n        this._anchor?.remove();\n        this._anchor = null;\n        this._animationStarted.complete();\n        this._animationEnd.complete();\n        this._modeChanged.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Open the drawer.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    open(openedVia) {\n        return this.toggle(true, openedVia);\n    }\n    /** Close the drawer. */\n    close() {\n        return this.toggle(false);\n    }\n    /** Closes the drawer with context that the backdrop was clicked. */\n    _closeViaBackdropClick() {\n        // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n        // don't need to check whether focus is currently in the drawer, as clicking on the\n        // backdrop causes blurs the active element.\n        return this._setOpen(/* isOpen */ false, /* restoreFocus */ true, 'mouse');\n    }\n    /**\n     * Toggle this drawer.\n     * @param isOpen Whether the drawer should be open.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    toggle(isOpen = !this.opened, openedVia) {\n        // If the focus is currently inside the drawer content and we are closing the drawer,\n        // restore the focus to the initially focused element (when the drawer opened).\n        if (isOpen && openedVia) {\n            this._openedVia = openedVia;\n        }\n        const result = this._setOpen(isOpen, \n        /* restoreFocus */ !isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n        if (!isOpen) {\n            this._openedVia = null;\n        }\n        return result;\n    }\n    /**\n     * Toggles the opened state of the drawer.\n     * @param isOpen Whether the drawer should open or close.\n     * @param restoreFocus Whether focus should be restored on close.\n     * @param focusOrigin Origin to use when restoring focus.\n     */\n    _setOpen(isOpen, restoreFocus, focusOrigin) {\n        this._opened = isOpen;\n        if (isOpen) {\n            this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n        }\n        else {\n            this._animationState = 'void';\n            if (restoreFocus) {\n                this._restoreFocus(focusOrigin);\n            }\n        }\n        this._updateFocusTrapState();\n        return new Promise(resolve => {\n            this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n        });\n    }\n    _getWidth() {\n        return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n    }\n    /** Updates the enabled state of the focus trap. */\n    _updateFocusTrapState() {\n        if (this._focusTrap) {\n            // Trap focus only if the backdrop is enabled. Otherwise, allow end user to interact with the\n            // sidenav content.\n            this._focusTrap.enabled = !!this._container?.hasBackdrop;\n        }\n    }\n    /**\n     * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n     * when it's in the `end` position so that it comes after the content and the visual order\n     * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n     * started off as `end` and was changed to `start`.\n     */\n    _updatePositionInParent(newPosition) {\n        const element = this._elementRef.nativeElement;\n        const parent = element.parentNode;\n        if (newPosition === 'end') {\n            if (!this._anchor) {\n                this._anchor = this._doc.createComment('mat-drawer-anchor');\n                parent.insertBefore(this._anchor, element);\n            }\n            parent.appendChild(element);\n        }\n        else if (this._anchor) {\n            this._anchor.parentNode.insertBefore(element, this._anchor);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDrawer, deps: [{ token: i0.ElementRef }, { token: i2.FocusTrapFactory }, { token: i2.FocusMonitor }, { token: i3.Platform }, { token: i0.NgZone }, { token: i2.InteractivityChecker }, { token: DOCUMENT, optional: true }, { token: MAT_DRAWER_CONTAINER, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDrawer, selector: \"mat-drawer\", inputs: { position: \"position\", mode: \"mode\", disableClose: \"disableClose\", autoFocus: \"autoFocus\", opened: \"opened\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", openedStart: \"openedStart\", _closedStream: \"closed\", closedStart: \"closedStart\", onPositionChanged: \"positionChanged\" }, host: { attributes: { \"tabIndex\": \"-1\", \"ngSkipHydration\": \"\" }, listeners: { \"@transform.start\": \"_animationStarted.next($event)\", \"@transform.done\": \"_animationEnd.next($event)\" }, properties: { \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"class.mat-drawer-opened\": \"opened\", \"@transform\": \"_animationState\" }, classAttribute: \"mat-drawer\" }, viewQueries: [{ propertyName: \"_content\", first: true, predicate: [\"content\"], descendants: true }], exportAs: [\"matDrawer\"], ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matDrawerAnimations.transformDrawer], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDrawer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer', exportAs: 'matDrawer', animations: [matDrawerAnimations.transformDrawer], host: {\n                        'class': 'mat-drawer',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        '[class.mat-drawer-opened]': 'opened',\n                        'tabIndex': '-1',\n                        '[@transform]': '_animationState',\n                        '(@transform.start)': '_animationStarted.next($event)',\n                        '(@transform.done)': '_animationEnd.next($event)',\n                        'ngSkipHydration': '',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i2.FocusTrapFactory }, { type: i2.FocusMonitor }, { type: i3.Platform }, { type: i0.NgZone }, { type: i2.InteractivityChecker }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: MatDrawerContainer, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DRAWER_CONTAINER]\n                }] }]; }, propDecorators: { position: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], disableClose: [{\n                type: Input\n            }], autoFocus: [{\n                type: Input\n            }], opened: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], openedStart: [{\n                type: Output\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], closedStart: [{\n                type: Output\n            }], onPositionChanged: [{\n                type: Output,\n                args: ['positionChanged']\n            }], _content: [{\n                type: ViewChild,\n                args: ['content']\n            }] } });\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nclass MatDrawerContainer {\n    /** The drawer child with the `start` position. */\n    get start() {\n        return this._start;\n    }\n    /** The drawer child with the `end` position. */\n    get end() {\n        return this._end;\n    }\n    /**\n     * Whether to automatically resize the container whenever\n     * the size of any of its drawers changes.\n     *\n     * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n     * the drawers on every change detection cycle. Can be configured globally via the\n     * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n     */\n    get autosize() {\n        return this._autosize;\n    }\n    set autosize(value) {\n        this._autosize = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n     * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n     * mode as well.\n     */\n    get hasBackdrop() {\n        return this._drawerHasBackdrop(this._start) || this._drawerHasBackdrop(this._end);\n    }\n    set hasBackdrop(value) {\n        this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n    }\n    /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n    get scrollable() {\n        return this._userContent || this._content;\n    }\n    constructor(_dir, _element, _ngZone, _changeDetectorRef, viewportRuler, defaultAutosize = false, _animationMode) {\n        this._dir = _dir;\n        this._element = _element;\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** Drawers that belong to this container. */\n        this._drawers = new QueryList();\n        /** Event emitted when the drawer backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Emits on every ngDoCheck. Used for debouncing reflows. */\n        this._doCheckSubject = new Subject();\n        /**\n         * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n         * drawer is open. We use margin rather than transform even for push mode because transform breaks\n         * fixed position elements inside of the transformed element.\n         */\n        this._contentMargins = { left: null, right: null };\n        this._contentMarginChanges = new Subject();\n        // If a `Dir` directive exists up the tree, listen direction changes\n        // and update the left/right properties to point to the proper start/end.\n        if (_dir) {\n            _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                this._validateDrawers();\n                this.updateContentMargins();\n            });\n        }\n        // Since the minimum width of the sidenav depends on the viewport width,\n        // we need to recompute the margins if the viewport changes.\n        viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.updateContentMargins());\n        this._autosize = defaultAutosize;\n    }\n    ngAfterContentInit() {\n        this._allDrawers.changes\n            .pipe(startWith(this._allDrawers), takeUntil(this._destroyed))\n            .subscribe((drawer) => {\n            this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n            this._drawers.notifyOnChanges();\n        });\n        this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n            this._validateDrawers();\n            this._drawers.forEach((drawer) => {\n                this._watchDrawerToggle(drawer);\n                this._watchDrawerPosition(drawer);\n                this._watchDrawerMode(drawer);\n            });\n            if (!this._drawers.length ||\n                this._isDrawerOpen(this._start) ||\n                this._isDrawerOpen(this._end)) {\n                this.updateContentMargins();\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n        // Avoid hitting the NgZone through the debounce timeout.\n        this._ngZone.runOutsideAngular(() => {\n            this._doCheckSubject\n                .pipe(debounceTime(10), // Arbitrary debounce time, less than a frame at 60fps\n            takeUntil(this._destroyed))\n                .subscribe(() => this.updateContentMargins());\n        });\n    }\n    ngOnDestroy() {\n        this._contentMarginChanges.complete();\n        this._doCheckSubject.complete();\n        this._drawers.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Calls `open` of both start and end drawers */\n    open() {\n        this._drawers.forEach(drawer => drawer.open());\n    }\n    /** Calls `close` of both start and end drawers */\n    close() {\n        this._drawers.forEach(drawer => drawer.close());\n    }\n    /**\n     * Recalculates and updates the inline styles for the content. Note that this should be used\n     * sparingly, because it causes a reflow.\n     */\n    updateContentMargins() {\n        // 1. For drawers in `over` mode, they don't affect the content.\n        // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n        //    left margin (for left drawer) or right margin (for right the drawer).\n        // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n        //    adding to the left or right margin and simultaneously subtracting the same amount of\n        //    margin from the other side.\n        let left = 0;\n        let right = 0;\n        if (this._left && this._left.opened) {\n            if (this._left.mode == 'side') {\n                left += this._left._getWidth();\n            }\n            else if (this._left.mode == 'push') {\n                const width = this._left._getWidth();\n                left += width;\n                right -= width;\n            }\n        }\n        if (this._right && this._right.opened) {\n            if (this._right.mode == 'side') {\n                right += this._right._getWidth();\n            }\n            else if (this._right.mode == 'push') {\n                const width = this._right._getWidth();\n                right += width;\n                left -= width;\n            }\n        }\n        // If either `right` or `left` is zero, don't set a style to the element. This\n        // allows users to specify a custom size via CSS class in SSR scenarios where the\n        // measured widths will always be zero. Note that we reset to `null` here, rather\n        // than below, in order to ensure that the types in the `if` below are consistent.\n        left = left || null;\n        right = right || null;\n        if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n            this._contentMargins = { left, right };\n            // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n            // to do it only when something changed, otherwise we can end up hitting the zone too often.\n            this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n        }\n    }\n    ngDoCheck() {\n        // If users opted into autosizing, do a check every change detection cycle.\n        if (this._autosize && this._isPushed()) {\n            // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n            this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n        }\n    }\n    /**\n     * Subscribes to drawer events in order to set a class on the main container element when the\n     * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n     * is properly hidden.\n     */\n    _watchDrawerToggle(drawer) {\n        drawer._animationStarted\n            .pipe(filter((event) => event.fromState !== event.toState), takeUntil(this._drawers.changes))\n            .subscribe((event) => {\n            // Set the transition class on the container so that the animations occur. This should not\n            // be set initially because animations should only be triggered via a change in state.\n            if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n                this._element.nativeElement.classList.add('mat-drawer-transition');\n            }\n            this.updateContentMargins();\n            this._changeDetectorRef.markForCheck();\n        });\n        if (drawer.mode !== 'side') {\n            drawer.openedChange\n                .pipe(takeUntil(this._drawers.changes))\n                .subscribe(() => this._setContainerClass(drawer.opened));\n        }\n    }\n    /**\n     * Subscribes to drawer onPositionChanged event in order to\n     * re-validate drawers when the position changes.\n     */\n    _watchDrawerPosition(drawer) {\n        if (!drawer) {\n            return;\n        }\n        // NOTE: We need to wait for the microtask queue to be empty before validating,\n        // since both drawers may be swapping positions at the same time.\n        drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n            this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n                this._validateDrawers();\n            });\n        });\n    }\n    /** Subscribes to changes in drawer mode so we can run change detection. */\n    _watchDrawerMode(drawer) {\n        if (drawer) {\n            drawer._modeChanged\n                .pipe(takeUntil(merge(this._drawers.changes, this._destroyed)))\n                .subscribe(() => {\n                this.updateContentMargins();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n    _setContainerClass(isAdd) {\n        const classList = this._element.nativeElement.classList;\n        const className = 'mat-drawer-container-has-open';\n        if (isAdd) {\n            classList.add(className);\n        }\n        else {\n            classList.remove(className);\n        }\n    }\n    /** Validate the state of the drawer children components. */\n    _validateDrawers() {\n        this._start = this._end = null;\n        // Ensure that we have at most one start and one end drawer.\n        this._drawers.forEach(drawer => {\n            if (drawer.position == 'end') {\n                if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('end');\n                }\n                this._end = drawer;\n            }\n            else {\n                if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('start');\n                }\n                this._start = drawer;\n            }\n        });\n        this._right = this._left = null;\n        // Detect if we're LTR or RTL.\n        if (this._dir && this._dir.value === 'rtl') {\n            this._left = this._end;\n            this._right = this._start;\n        }\n        else {\n            this._left = this._start;\n            this._right = this._end;\n        }\n    }\n    /** Whether the container is being pushed to the side by one of the drawers. */\n    _isPushed() {\n        return ((this._isDrawerOpen(this._start) && this._start.mode != 'over') ||\n            (this._isDrawerOpen(this._end) && this._end.mode != 'over'));\n    }\n    _onBackdropClicked() {\n        this.backdropClick.emit();\n        this._closeModalDrawersViaBackdrop();\n    }\n    _closeModalDrawersViaBackdrop() {\n        // Close all open drawers where closing is not disabled and the mode is not `side`.\n        [this._start, this._end]\n            .filter(drawer => drawer && !drawer.disableClose && this._drawerHasBackdrop(drawer))\n            .forEach(drawer => drawer._closeViaBackdropClick());\n    }\n    _isShowingBackdrop() {\n        return ((this._isDrawerOpen(this._start) && this._drawerHasBackdrop(this._start)) ||\n            (this._isDrawerOpen(this._end) && this._drawerHasBackdrop(this._end)));\n    }\n    _isDrawerOpen(drawer) {\n        return drawer != null && drawer.opened;\n    }\n    // Whether argument drawer should have a backdrop when it opens\n    _drawerHasBackdrop(drawer) {\n        if (this._backdropOverride == null) {\n            return !!drawer && drawer.mode !== 'side';\n        }\n        return this._backdropOverride;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDrawerContainer, deps: [{ token: i4.Directionality, optional: true }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: MAT_DRAWER_DEFAULT_AUTOSIZE }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDrawerContainer, selector: \"mat-drawer-container\", inputs: { autosize: \"autosize\", hasBackdrop: \"hasBackdrop\" }, outputs: { backdropClick: \"backdropClick\" }, host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container\" }, providers: [\n            {\n                provide: MAT_DRAWER_CONTAINER,\n                useExisting: MatDrawerContainer,\n            },\n        ], queries: [{ propertyName: \"_content\", first: true, predicate: MatDrawerContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatDrawer, descendants: true }], viewQueries: [{ propertyName: \"_userContent\", first: true, predicate: MatDrawerContent, descendants: true }], exportAs: [\"matDrawerContainer\"], ngImport: i0, template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"], dependencies: [{ kind: \"directive\", type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatDrawerContent, selector: \"mat-drawer-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDrawerContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer-container', exportAs: 'matDrawerContainer', host: {\n                        'class': 'mat-drawer-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                        'ngSkipHydration': '',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatDrawerContainer,\n                        },\n                    ], template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"] }]\n        }], ctorParameters: function () { return [{ type: i4.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_DRAWER_DEFAULT_AUTOSIZE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatDrawer, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatDrawerContent]\n            }], _userContent: [{\n                type: ViewChild,\n                args: [MatDrawerContent]\n            }], autosize: [{\n                type: Input\n            }], hasBackdrop: [{\n                type: Input\n            }], backdropClick: [{\n                type: Output\n            }] } });\n\nclass MatSidenavContent extends MatDrawerContent {\n    constructor(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone) {\n        super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavContent, deps: [{ token: i0.ChangeDetectorRef }, { token: forwardRef(() => MatSidenavContainer) }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSidenavContent, selector: \"mat-sidenav-content\", host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"style.margin-left.px\": \"_container._contentMargins.left\", \"style.margin-right.px\": \"_container._contentMargins.right\" }, classAttribute: \"mat-drawer-content mat-sidenav-content\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useExisting: MatSidenavContent,\n            },\n        ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-sidenav-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content mat-sidenav-content',\n                        '[style.margin-left.px]': '_container._contentMargins.left',\n                        '[style.margin-right.px]': '_container._contentMargins.right',\n                        'ngSkipHydration': '',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatSidenavContent,\n                        },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: MatSidenavContainer, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatSidenavContainer)]\n                }] }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.NgZone }]; } });\nclass MatSidenav extends MatDrawer {\n    constructor() {\n        super(...arguments);\n        this._fixedInViewport = false;\n        this._fixedTopGap = 0;\n        this._fixedBottomGap = 0;\n    }\n    /** Whether the sidenav is fixed in the viewport. */\n    get fixedInViewport() {\n        return this._fixedInViewport;\n    }\n    set fixedInViewport(value) {\n        this._fixedInViewport = coerceBooleanProperty(value);\n    }\n    /**\n     * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n     * mode.\n     */\n    get fixedTopGap() {\n        return this._fixedTopGap;\n    }\n    set fixedTopGap(value) {\n        this._fixedTopGap = coerceNumberProperty(value);\n    }\n    /**\n     * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n     * fixed mode.\n     */\n    get fixedBottomGap() {\n        return this._fixedBottomGap;\n    }\n    set fixedBottomGap(value) {\n        this._fixedBottomGap = coerceNumberProperty(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenav, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSidenav, selector: \"mat-sidenav\", inputs: { fixedInViewport: \"fixedInViewport\", fixedTopGap: \"fixedTopGap\", fixedBottomGap: \"fixedBottomGap\" }, host: { attributes: { \"tabIndex\": \"-1\", \"ngSkipHydration\": \"\" }, properties: { \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"class.mat-drawer-opened\": \"opened\", \"class.mat-sidenav-fixed\": \"fixedInViewport\", \"style.top.px\": \"fixedInViewport ? fixedTopGap : null\", \"style.bottom.px\": \"fixedInViewport ? fixedBottomGap : null\" }, classAttribute: \"mat-drawer mat-sidenav\" }, exportAs: [\"matSidenav\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matDrawerAnimations.transformDrawer], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenav, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav', exportAs: 'matSidenav', animations: [matDrawerAnimations.transformDrawer], host: {\n                        'class': 'mat-drawer mat-sidenav',\n                        'tabIndex': '-1',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        '[class.mat-drawer-opened]': 'opened',\n                        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n                        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n                        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null',\n                        'ngSkipHydration': '',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], propDecorators: { fixedInViewport: [{\n                type: Input\n            }], fixedTopGap: [{\n                type: Input\n            }], fixedBottomGap: [{\n                type: Input\n            }] } });\nclass MatSidenavContainer extends MatDrawerContainer {\n    constructor() {\n        super(...arguments);\n        this._allDrawers = undefined;\n        // We need an initializer here to avoid a TS error.\n        this._content = undefined;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavContainer, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSidenavContainer, selector: \"mat-sidenav-container\", host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container mat-sidenav-container\" }, providers: [\n            {\n                provide: MAT_DRAWER_CONTAINER,\n                useExisting: MatSidenavContainer,\n            },\n        ], queries: [{ propertyName: \"_content\", first: true, predicate: MatSidenavContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatSidenav, descendants: true }], exportAs: [\"matSidenavContainer\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"], dependencies: [{ kind: \"directive\", type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatSidenavContent, selector: \"mat-sidenav-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav-container', exportAs: 'matSidenavContainer', host: {\n                        'class': 'mat-drawer-container mat-sidenav-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                        'ngSkipHydration': '',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatSidenavContainer,\n                        },\n                    ], template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color);background-color:var(--mat-sidenav-content-background-color);box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color)}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);position:relative;z-index:4;--mat-sidenav-container-shape:0;color:var(--mat-sidenav-container-text-color);background-color:var(--mat-sidenav-container-background-color);border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape);border-bottom-left-radius:var(--mat-sidenav-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape);border-bottom-right-radius:var(--mat-sidenav-container-shape);border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"] }]\n        }], propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatSidenav, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatSidenavContent]\n            }] } });\n\nclass MatSidenavModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavModule, declarations: [MatDrawer,\n            MatDrawerContainer,\n            MatDrawerContent,\n            MatSidenav,\n            MatSidenavContainer,\n            MatSidenavContent], imports: [CommonModule, MatCommonModule, CdkScrollableModule], exports: [CdkScrollableModule,\n            MatCommonModule,\n            MatDrawer,\n            MatDrawerContainer,\n            MatDrawerContent,\n            MatSidenav,\n            MatSidenavContainer,\n            MatSidenavContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavModule, imports: [CommonModule, MatCommonModule, CdkScrollableModule, CdkScrollableModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSidenavModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule, CdkScrollableModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatCommonModule,\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                    declarations: [\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC3E,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC/N,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,OAAO,EAAEC,SAAS,EAAEC,KAAK,QAAQ,MAAM;AAChD,SAASC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,IAAI,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AACnH,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,qBAAqB,QAAQ,sCAAsC;;AAE5E;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,kCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAyDoG9C,EAAE,CAAA+C,gBAAA;IAAF/C,EAAE,CAAAgD,cAAA,YAuvB+Y,CAAC;IAvvBlZhD,EAAE,CAAAiD,UAAA,mBAAAC,uDAAA;MAAFlD,EAAE,CAAAmD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;MAAA,OAAFrD,EAAE,CAAAsD,WAAA,CAuvB2SF,MAAA,CAAAG,kBAAA,CAAmB,EAAC;IAAA,CAAC,CAAC;IAvvBnUvD,EAAE,CAAAwD,YAAA,CAuvBqZ,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,MAAA,GAvvBxZzD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAA0D,WAAA,qBAAAD,MAAA,CAAAE,kBAAA,EAuvB8Y,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvvBjZ5C,EAAE,CAAAgD,cAAA,wBAuvB+iB,CAAC;IAvvBljBhD,EAAE,CAAA6D,YAAA,KAuvB4kB,CAAC;IAvvB/kB7D,EAAE,CAAAwD,YAAA,CAuvBmmB,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,mCAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAvvBtmB9C,EAAE,CAAA+C,gBAAA;IAAF/C,EAAE,CAAAgD,cAAA,YAk5B2T,CAAC;IAl5B9ThD,EAAE,CAAAiD,UAAA,mBAAAgB,wDAAA;MAAFjE,EAAE,CAAAmD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFpD,EAAE,CAAAqD,aAAA;MAAA,OAAFrD,EAAE,CAAAsD,WAAA,CAk5BuNF,MAAA,CAAAG,kBAAA,CAAmB,EAAC;IAAA,CAAC,CAAC;IAl5B/OvD,EAAE,CAAAwD,YAAA,CAk5BiU,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,MAAA,GAl5BpUzD,EAAE,CAAAqD,aAAA;IAAFrD,EAAE,CAAA0D,WAAA,qBAAAD,MAAA,CAAAE,kBAAA,EAk5B0T,CAAC;EAAA;AAAA;AAAA,SAAAO,mDAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAl5B7T5C,EAAE,CAAAgD,cAAA,yBAk5B8d,CAAC;IAl5BjehD,EAAE,CAAA6D,YAAA,KAk5B2f,CAAC;IAl5B9f7D,EAAE,CAAAwD,YAAA,CAk5BmhB,CAAC;EAAA;AAAA;AAAA,MAAAW,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAv8B1nB,MAAMC,mBAAmB,GAAG;EACxB;EACAC,eAAe,EAAEpC,OAAO,CAAC,WAAW,EAAE;EAClC;EACA;EACA;EACA;EACAC,KAAK,CAAC,oBAAoB,EAAEC,KAAK,CAAC;IAC9B,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE;EAClB,CAAC,CAAC,CAAC,EACHD,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChB;IACA,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAClB,CAAC,CAAC,CAAC,EACHC,UAAU,CAAC,sBAAsB,EAAEC,OAAO,CAAC,KAAK,CAAC,CAAC,EAClDD,UAAU,CAAC,qCAAqC,EAAEC,OAAO,CAAC,wCAAwC,CAAC,CAAC,CACvG;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASiC,6BAA6BA,CAACC,QAAQ,EAAE;EAC7C,MAAMC,KAAK,CAAE,gDAA+CD,QAAS,IAAG,CAAC;AAC7E;AACA;AACA,MAAME,2BAA2B,GAAG,IAAI1E,cAAc,CAAC,6BAA6B,EAAE;EAClF2E,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,IAAI9E,cAAc,CAAC,sBAAsB,CAAC;AACvE;AACA,SAAS6E,mCAAmCA,CAAA,EAAG;EAC3C,OAAO,KAAK;AAChB;AACA,MAAME,gBAAgB,SAASrF,aAAa,CAAC;EACzCsF,WAAWA,CAACC,kBAAkB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;IAC9E,KAAK,CAACF,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,CAAC;IAC3C,IAAI,CAACJ,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAI,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACJ,UAAU,CAACK,qBAAqB,CAACC,SAAS,CAAC,MAAM;MAClD,IAAI,CAACP,kBAAkB,CAACQ,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFb,gBAAgB,EAA1BhF,EAAE,CAAA8F,iBAAA,CAA0C9F,EAAE,CAAC+F,iBAAiB,GAAhE/F,EAAE,CAAA8F,iBAAA,CAA2E5F,UAAU,CAAC,MAAM8F,kBAAkB,CAAC,GAAjHhG,EAAE,CAAA8F,iBAAA,CAA4H9F,EAAE,CAACiG,UAAU,GAA3IjG,EAAE,CAAA8F,iBAAA,CAAsJpG,EAAE,CAACwG,gBAAgB,GAA3KlG,EAAE,CAAA8F,iBAAA,CAAsL9F,EAAE,CAACmG,MAAM;IAAA,CAA4C;EAAE;EAC/U;IAAS,IAAI,CAACC,IAAI,kBAD8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EACJtB,gBAAgB;MAAAuB,SAAA;MAAAC,SAAA,sBAA2E,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAA/D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD3F5C,EAAE,CAAA4G,WAAA,gBAAA/D,GAAA,CAAAsC,UAAA,CAAA0B,eAAA,CAAAC,IAAA,wBAAAjE,GAAA,CAAAsC,UAAA,CAAA0B,eAAA,CAAAE,KAAA;QAAA;MAAA;MAAAC,QAAA,GAAFhH,EAAE,CAAAiH,kBAAA,CACwR,CAClX;QACIC,OAAO,EAAEvH,aAAa;QACtBwH,WAAW,EAAEnC;MACjB,CAAC,CACJ,GAN2FhF,EAAE,CAAAoH,0BAAA;MAAAC,kBAAA,EAAA5E,GAAA;MAAA6E,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAA7E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAA0H,eAAA;UAAF1H,EAAE,CAAA6D,YAAA,EAMnB,CAAC;QAAA;MAAA;MAAA8D,aAAA;MAAAC,eAAA;IAAA,EAAkH;EAAE;AACxM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KARoG7H,EAAE,CAAA8H,iBAAA,CAQX9C,gBAAgB,EAAc,CAAC;IAC9GsB,IAAI,EAAEnG,SAAS;IACf4H,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BR,QAAQ,EAAE,2BAA2B;MACrCS,IAAI,EAAE;QACF,OAAO,EAAE,oBAAoB;QAC7B,wBAAwB,EAAE,iCAAiC;QAC3D,yBAAyB,EAAE,kCAAkC;QAC7D,iBAAiB,EAAE;MACvB,CAAC;MACDL,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAC/CP,aAAa,EAAEtH,iBAAiB,CAAC8H,IAAI;MACrCC,SAAS,EAAE,CACP;QACIlB,OAAO,EAAEvH,aAAa;QACtBwH,WAAW,EAAEnC;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEsB,IAAI,EAAEtG,EAAE,CAAC+F;IAAkB,CAAC,EAAE;MAAEO,IAAI,EAAEN,kBAAkB;MAAEqC,UAAU,EAAE,CAAC;QACvG/B,IAAI,EAAEhG,MAAM;QACZyH,IAAI,EAAE,CAAC7H,UAAU,CAAC,MAAM8F,kBAAkB,CAAC;MAC/C,CAAC;IAAE,CAAC,EAAE;MAAEM,IAAI,EAAEtG,EAAE,CAACiG;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAE5G,EAAE,CAACwG;IAAiB,CAAC,EAAE;MAAEI,IAAI,EAAEtG,EAAE,CAACmG;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACrG;AACA;AACA;AACA,MAAMmC,SAAS,CAAC;EACZ;EACA,IAAI7D,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC8D,SAAS;EACzB;EACA,IAAI9D,QAAQA,CAAC+D,KAAK,EAAE;IAChB;IACAA,KAAK,GAAGA,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,OAAO;IACzC,IAAIA,KAAK,KAAK,IAAI,CAACD,SAAS,EAAE;MAC1B;MACA,IAAI,IAAI,CAACE,WAAW,EAAE;QAClB,IAAI,CAACC,uBAAuB,CAACF,KAAK,CAAC;MACvC;MACA,IAAI,CAACD,SAAS,GAAGC,KAAK;MACtB,IAAI,CAACG,iBAAiB,CAACC,IAAI,CAAC,CAAC;IACjC;EACJ;EACA;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACL,KAAK,EAAE;IACZ,IAAI,CAACM,KAAK,GAAGN,KAAK;IAClB,IAAI,CAACO,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACV,KAAK,EAAE;IACpB,IAAI,CAACW,aAAa,GAAGhI,qBAAqB,CAACqH,KAAK,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIY,SAASA,CAAA,EAAG;IACZ,MAAMZ,KAAK,GAAG,IAAI,CAACa,UAAU;IAC7B;IACA;IACA;IACA,IAAIb,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,IAAI,CAACK,IAAI,KAAK,MAAM,EAAE;QACtB,OAAO,QAAQ;MACnB,CAAC,MACI;QACD,OAAO,gBAAgB;MAC3B;IACJ;IACA,OAAOL,KAAK;EAChB;EACA,IAAIY,SAASA,CAACZ,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,IAAI,IAAI,EAAE;MACxDA,KAAK,GAAGrH,qBAAqB,CAACqH,KAAK,CAAC;IACxC;IACA,IAAI,CAACa,UAAU,GAAGb,KAAK;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIc,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACd,KAAK,EAAE;IACd,IAAI,CAACgB,MAAM,CAACrI,qBAAqB,CAACqH,KAAK,CAAC,CAAC;EAC7C;EACAvD,WAAWA,CAACwE,WAAW,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,IAAI,EAAE5E,UAAU,EAAE;IACpH,IAAI,CAACsE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC5E,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC6E,oCAAoC,GAAG,IAAI;IAChD;IACA,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC1B,SAAS,GAAG,OAAO;IACxB,IAAI,CAACO,KAAK,GAAG,MAAM;IACnB,IAAI,CAACK,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACI,OAAO,GAAG,KAAK;IACpB;IACA,IAAI,CAACW,iBAAiB,GAAG,IAAI1I,OAAO,CAAC,CAAC;IACtC;IACA,IAAI,CAAC2I,aAAa,GAAG,IAAI3I,OAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAAC4I,eAAe,GAAG,MAAM;IAC7B;IACA,IAAI,CAACC,YAAY;IACjB;IACA,IAAI9J,YAAY,EAAC,aAAc,IAAI,CAAC;IACpC;IACA,IAAI,CAAC+J,aAAa,GAAG,IAAI,CAACD,YAAY,CAACE,IAAI,CAAC5I,MAAM,CAAC6I,CAAC,IAAIA,CAAC,CAAC,EAAE5I,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC3E;IACA,IAAI,CAAC6I,WAAW,GAAG,IAAI,CAACP,iBAAiB,CAACK,IAAI,CAAC5I,MAAM,CAAC+I,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKD,CAAC,CAACE,OAAO,IAAIF,CAAC,CAACE,OAAO,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAEhJ,KAAK,CAACiJ,SAAS,CAAC,CAAC;IAC3I;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACV,YAAY,CAACE,IAAI,CAAC5I,MAAM,CAAC6I,CAAC,IAAI,CAACA,CAAC,CAAC,EAAE5I,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC5E;IACA,IAAI,CAACoJ,WAAW,GAAG,IAAI,CAACd,iBAAiB,CAACK,IAAI,CAAC5I,MAAM,CAAC+I,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKD,CAAC,CAACE,OAAO,IAAIF,CAAC,CAACE,OAAO,KAAK,MAAM,CAAC,EAAE/I,KAAK,CAACiJ,SAAS,CAAC,CAAC;IAChI;IACA,IAAI,CAACG,UAAU,GAAG,IAAIzJ,OAAO,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,CAACmH,iBAAiB,GAAG,IAAIpI,YAAY,CAAC,CAAC;IAC3C;AACR;AACA;AACA;IACQ,IAAI,CAACyI,YAAY,GAAG,IAAIxH,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC6I,YAAY,CAAC5E,SAAS,CAAE6D,MAAM,IAAK;MACpC,IAAIA,MAAM,EAAE;QACR,IAAI,IAAI,CAACS,IAAI,EAAE;UACX,IAAI,CAACC,oCAAoC,GAAG,IAAI,CAACD,IAAI,CAACmB,aAAa;QACvE;QACA,IAAI,CAACC,UAAU,CAAC,CAAC;MACrB,CAAC,MACI,IAAI,IAAI,CAACC,oBAAoB,CAAC,CAAC,EAAE;QAClC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACC,UAAU,IAAI,SAAS,CAAC;MACpD;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACzB,OAAO,CAAC0B,iBAAiB,CAAC,MAAM;MACjC9J,SAAS,CAAC,IAAI,CAACgI,WAAW,CAAC+B,aAAa,EAAE,SAAS,CAAC,CAC/CjB,IAAI,CAAC5I,MAAM,CAAC8J,KAAK,IAAI;QACtB,OAAOA,KAAK,CAACC,OAAO,KAAKrK,MAAM,IAAI,CAAC,IAAI,CAAC6H,YAAY,IAAI,CAAC5H,cAAc,CAACmK,KAAK,CAAC;MACnF,CAAC,CAAC,EAAE3J,SAAS,CAAC,IAAI,CAACmJ,UAAU,CAAC,CAAC,CAC1BxF,SAAS,CAACgG,KAAK,IAAI,IAAI,CAAC5B,OAAO,CAAC8B,GAAG,CAAC,MAAM;QAC3C,IAAI,CAACC,KAAK,CAAC,CAAC;QACZH,KAAK,CAACI,eAAe,CAAC,CAAC;QACvBJ,KAAK,CAACK,cAAc,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAAC3B,aAAa,CACbI,IAAI,CAACxI,oBAAoB,CAAC,CAACgK,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACpB,SAAS,KAAKqB,CAAC,CAACrB,SAAS,IAAIoB,CAAC,CAACnB,OAAO,KAAKoB,CAAC,CAACpB,OAAO;IACjE,CAAC,CAAC,CAAC,CACEnF,SAAS,CAAEgG,KAAK,IAAK;MACtB,MAAM;QAAEd,SAAS;QAAEC;MAAQ,CAAC,GAAGa,KAAK;MACpC,IAAKb,OAAO,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAIF,SAAS,KAAK,MAAM,IACrDC,OAAO,KAAK,MAAM,IAAID,SAAS,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAE,EAAE;QACzD,IAAI,CAACR,YAAY,CAACzB,IAAI,CAAC,IAAI,CAACW,OAAO,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI0C,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACrC,qBAAqB,CAACsC,WAAW,CAACF,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAACxC,OAAO,CAAC0B,iBAAiB,CAAC,MAAM;QACjC,MAAMe,QAAQ,GAAGA,CAAA,KAAM;UACnBJ,OAAO,CAACK,mBAAmB,CAAC,MAAM,EAAED,QAAQ,CAAC;UAC7CJ,OAAO,CAACK,mBAAmB,CAAC,WAAW,EAAED,QAAQ,CAAC;UAClDJ,OAAO,CAACM,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACDN,OAAO,CAACO,gBAAgB,CAAC,MAAM,EAAEH,QAAQ,CAAC;QAC1CJ,OAAO,CAACO,gBAAgB,CAAC,WAAW,EAAEH,QAAQ,CAAC;MACnD,CAAC,CAAC;IACN;IACAJ,OAAO,CAACQ,KAAK,CAACP,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIQ,mBAAmBA,CAAC3E,QAAQ,EAAEmE,OAAO,EAAE;IACnC,IAAIS,cAAc,GAAG,IAAI,CAACnD,WAAW,CAAC+B,aAAa,CAACqB,aAAa,CAAC7E,QAAQ,CAAC;IAC3E,IAAI4E,cAAc,EAAE;MAChB,IAAI,CAACX,WAAW,CAACW,cAAc,EAAET,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIhB,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAC2B,UAAU,EAAE;MAClB;IACJ;IACA,MAAMZ,OAAO,GAAG,IAAI,CAACzC,WAAW,CAAC+B,aAAa;IAC9C;IACA;IACA;IACA,QAAQ,IAAI,CAACpC,SAAS;MAClB,KAAK,KAAK;MACV,KAAK,QAAQ;QACT;MACJ,KAAK,IAAI;MACT,KAAK,gBAAgB;QACjB,IAAI,CAAC0D,UAAU,CAACC,4BAA4B,CAAC,CAAC,CAACC,IAAI,CAACC,aAAa,IAAI;UACjE,IAAI,CAACA,aAAa,IAAI,OAAO,IAAI,CAACxD,WAAW,CAAC+B,aAAa,CAACkB,KAAK,KAAK,UAAU,EAAE;YAC9ER,OAAO,CAACQ,KAAK,CAAC,CAAC;UACnB;QACJ,CAAC,CAAC;QACF;MACJ,KAAK,eAAe;QAChB,IAAI,CAACC,mBAAmB,CAAC,0CAA0C,CAAC;QACpE;MACJ;QACI,IAAI,CAACA,mBAAmB,CAAC,IAAI,CAACvD,SAAS,CAAC;QACxC;IACR;EACJ;EACA;AACJ;AACA;AACA;EACIiC,aAAaA,CAAC6B,WAAW,EAAE;IACvB,IAAI,IAAI,CAAC9D,SAAS,KAAK,QAAQ,EAAE;MAC7B;IACJ;IACA,IAAI,IAAI,CAACY,oCAAoC,EAAE;MAC3C,IAAI,CAACL,aAAa,CAACwD,QAAQ,CAAC,IAAI,CAACnD,oCAAoC,EAAEkD,WAAW,CAAC;IACvF,CAAC,MACI;MACD,IAAI,CAACzD,WAAW,CAAC+B,aAAa,CAAC4B,IAAI,CAAC,CAAC;IACzC;IACA,IAAI,CAACpD,oCAAoC,GAAG,IAAI;EACpD;EACA;EACAoB,oBAAoBA,CAAA,EAAG;IACnB,MAAMiC,QAAQ,GAAG,IAAI,CAACtD,IAAI,CAACmB,aAAa;IACxC,OAAO,CAAC,CAACmC,QAAQ,IAAI,IAAI,CAAC5D,WAAW,CAAC+B,aAAa,CAAC8B,QAAQ,CAACD,QAAQ,CAAC;EAC1E;EACAE,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC9E,WAAW,GAAG,IAAI;IACvB,IAAI,CAACqE,UAAU,GAAG,IAAI,CAACpD,iBAAiB,CAAC8D,MAAM,CAAC,IAAI,CAAC/D,WAAW,CAAC+B,aAAa,CAAC;IAC/E,IAAI,CAACzC,qBAAqB,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,IAAI,CAACR,SAAS,KAAK,KAAK,EAAE;MAC1B,IAAI,CAACG,uBAAuB,CAAC,KAAK,CAAC;IACvC;EACJ;EACA+E,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC7D,SAAS,CAAC8D,SAAS,EAAE;MAC1B,IAAI,CAACzD,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA0D,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACb,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACc,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC,CAAC;IACtB,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC3D,iBAAiB,CAAC6D,QAAQ,CAAC,CAAC;IACjC,IAAI,CAAC5D,aAAa,CAAC4D,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC/E,YAAY,CAAC+E,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC9C,UAAU,CAAChC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACgC,UAAU,CAAC8C,QAAQ,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIC,IAAIA,CAACC,SAAS,EAAE;IACZ,OAAO,IAAI,CAACzE,MAAM,CAAC,IAAI,EAAEyE,SAAS,CAAC;EACvC;EACA;EACArC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACpC,MAAM,CAAC,KAAK,CAAC;EAC7B;EACA;EACA0E,sBAAsBA,CAAA,EAAG;IACrB;IACA;IACA;IACA,OAAO,IAAI,CAACC,QAAQ,EAAC,YAAa,KAAK,EAAE,kBAAmB,IAAI,EAAE,OAAO,CAAC;EAC9E;EACA;AACJ;AACA;AACA;AACA;AACA;EACI3E,MAAMA,CAAC4E,MAAM,GAAG,CAAC,IAAI,CAAC9E,MAAM,EAAE2E,SAAS,EAAE;IACrC;IACA;IACA,IAAIG,MAAM,IAAIH,SAAS,EAAE;MACrB,IAAI,CAAC3C,UAAU,GAAG2C,SAAS;IAC/B;IACA,MAAMI,MAAM,GAAG,IAAI,CAACF,QAAQ,CAACC,MAAM,EACnC,kBAAmB,CAACA,MAAM,IAAI,IAAI,CAAChD,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAACE,UAAU,IAAI,SAAS,CAAC;IACxF,IAAI,CAAC8C,MAAM,EAAE;MACT,IAAI,CAAC9C,UAAU,GAAG,IAAI;IAC1B;IACA,OAAO+C,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,QAAQA,CAACC,MAAM,EAAEE,YAAY,EAAEpB,WAAW,EAAE;IACxC,IAAI,CAAC3D,OAAO,GAAG6E,MAAM;IACrB,IAAIA,MAAM,EAAE;MACR,IAAI,CAAChE,eAAe,GAAG,IAAI,CAACH,iBAAiB,GAAG,MAAM,GAAG,cAAc;IAC3E,CAAC,MACI;MACD,IAAI,CAACG,eAAe,GAAG,MAAM;MAC7B,IAAIkE,YAAY,EAAE;QACd,IAAI,CAACjD,aAAa,CAAC6B,WAAW,CAAC;MACnC;IACJ;IACA,IAAI,CAACnE,qBAAqB,CAAC,CAAC;IAC5B,OAAO,IAAIwF,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACnE,YAAY,CAACE,IAAI,CAACvI,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyD,SAAS,CAACuI,IAAI,IAAIQ,OAAO,CAACR,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC;IACvF,CAAC,CAAC;EACN;EACAS,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChF,WAAW,CAAC+B,aAAa,GAAG,IAAI,CAAC/B,WAAW,CAAC+B,aAAa,CAACkD,WAAW,IAAI,CAAC,GAAG,CAAC;EAC/F;EACA;EACA3F,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC+D,UAAU,EAAE;MACjB;MACA;MACA,IAAI,CAACA,UAAU,CAAC6B,OAAO,GAAG,CAAC,CAAC,IAAI,CAACxJ,UAAU,EAAEyJ,WAAW;IAC5D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIlG,uBAAuBA,CAACmG,WAAW,EAAE;IACjC,MAAM3C,OAAO,GAAG,IAAI,CAACzC,WAAW,CAAC+B,aAAa;IAC9C,MAAMsD,MAAM,GAAG5C,OAAO,CAAC6C,UAAU;IACjC,IAAIF,WAAW,KAAK,KAAK,EAAE;MACvB,IAAI,CAAC,IAAI,CAAChB,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC9D,IAAI,CAACiF,aAAa,CAAC,mBAAmB,CAAC;QAC3DF,MAAM,CAACG,YAAY,CAAC,IAAI,CAACpB,OAAO,EAAE3B,OAAO,CAAC;MAC9C;MACA4C,MAAM,CAACI,WAAW,CAAChD,OAAO,CAAC;IAC/B,CAAC,MACI,IAAI,IAAI,CAAC2B,OAAO,EAAE;MACnB,IAAI,CAACA,OAAO,CAACkB,UAAU,CAACE,YAAY,CAAC/C,OAAO,EAAE,IAAI,CAAC2B,OAAO,CAAC;IAC/D;EACJ;EACA;IAAS,IAAI,CAAClI,IAAI,YAAAwJ,kBAAAtJ,CAAA;MAAA,YAAAA,CAAA,IAAwFyC,SAAS,EA7YnBtI,EAAE,CAAA8F,iBAAA,CA6YmC9F,EAAE,CAACiG,UAAU,GA7YlDjG,EAAE,CAAA8F,iBAAA,CA6Y6D7E,EAAE,CAACmO,gBAAgB,GA7YlFpP,EAAE,CAAA8F,iBAAA,CA6Y6F7E,EAAE,CAACoO,YAAY,GA7Y9GrP,EAAE,CAAA8F,iBAAA,CA6YyHvE,EAAE,CAAC+N,QAAQ,GA7YtItP,EAAE,CAAA8F,iBAAA,CA6YiJ9F,EAAE,CAACmG,MAAM,GA7Y5JnG,EAAE,CAAA8F,iBAAA,CA6YuK7E,EAAE,CAACsO,oBAAoB,GA7YhMvP,EAAE,CAAA8F,iBAAA,CA6Y2MhG,QAAQ,MA7YrNE,EAAE,CAAA8F,iBAAA,CA6YgPf,oBAAoB;IAAA,CAA4D;EAAE;EACpa;IAAS,IAAI,CAACqB,IAAI,kBA9Y8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EA8YJgC,SAAS;MAAA/B,SAAA;MAAAiJ,SAAA,WAAAC,gBAAA7M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9YP5C,EAAE,CAAA0P,WAAA,CAAAhN,GAAA;QAAA;QAAA,IAAAE,EAAA;UAAA,IAAA+M,EAAA;UAAF3P,EAAE,CAAA4P,cAAA,CAAAD,EAAA,GAAF3P,EAAE,CAAA6P,WAAA,QAAAhN,GAAA,CAAAiN,QAAA,GAAAH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAvJ,SAAA,eA8YoX,IAAI,qBAAqB,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAsJ,uBAAApN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9YjZ5C,EAAE,CAAAiQ,uBAAA,8BAAAC,uDAAAC,MAAA;YAAA,OA8YJtN,GAAA,CAAAqH,iBAAA,CAAAjB,IAAA,CAAAkH,MAA6B,CAAC;UAAA,+BAAAC,sDAAAD,MAAA;YAAA,OAA9BtN,GAAA,CAAAsH,aAAA,CAAAlB,IAAA,CAAAkH,MAAyB,CAAC;UAAA;QAAA;QAAA,IAAAvN,EAAA;UA9YxB5C,EAAE,CAAAqQ,WAAA;UAAFrQ,EAAE,CAAAsQ,uBAAA,eAAAzN,GAAA,CAAAuH,eAAA;UAAFpK,EAAE,CAAA0D,WAAA,mBAAAb,GAAA,CAAA4B,QAAA,+BAAA5B,GAAA,CAAAgG,IAAA,gCAAAhG,GAAA,CAAAgG,IAAA,gCAAAhG,GAAA,CAAAgG,IAAA,kCAAAhG,GAAA,CAAAyG,MAAA;QAAA;MAAA;MAAAiH,MAAA;QAAA9L,QAAA;QAAAoE,IAAA;QAAAK,YAAA;QAAAE,SAAA;QAAAE,MAAA;MAAA;MAAAkH,OAAA;QAAAnG,YAAA;QAAAC,aAAA;QAAAG,WAAA;QAAAM,aAAA;QAAAC,WAAA;QAAArC,iBAAA;MAAA;MAAA8H,QAAA;MAAApJ,kBAAA,EAAA5E,GAAA;MAAA6E,KAAA;MAAAC,IAAA;MAAAmJ,MAAA;MAAAlJ,QAAA,WAAAmJ,mBAAA/N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAA0H,eAAA;UAAF1H,EAAE,CAAAgD,cAAA,eA8YsiC,CAAC;UA9YziChD,EAAE,CAAA6D,YAAA,EA8YqkC,CAAC;UA9YxkC7D,EAAE,CAAAwD,YAAA,CA8Y+kC,CAAC;QAAA;MAAA;MAAAoN,YAAA,GAAiDlR,EAAE,CAACC,aAAa;MAAAgI,aAAA;MAAAkJ,IAAA;QAAAC,SAAA,EAAgE,CAACxM,mBAAmB,CAACC,eAAe;MAAC;MAAAqD,eAAA;IAAA,EAAiG;EAAE;AAC/7C;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhZoG7H,EAAE,CAAA8H,iBAAA,CAgZXQ,SAAS,EAAc,CAAC;IACvGhC,IAAI,EAAEnG,SAAS;IACf4H,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEyI,QAAQ,EAAE,WAAW;MAAEM,UAAU,EAAE,CAACzM,mBAAmB,CAACC,eAAe,CAAC;MAAE0D,IAAI,EAAE;QACrG,OAAO,EAAE,YAAY;QACrB;QACA,cAAc,EAAE,MAAM;QACtB,wBAAwB,EAAE,oBAAoB;QAC9C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,2BAA2B,EAAE,QAAQ;QACrC,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,iBAAiB;QACjC,oBAAoB,EAAE,gCAAgC;QACtD,mBAAmB,EAAE,4BAA4B;QACjD,iBAAiB,EAAE;MACvB,CAAC;MAAEL,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAAEP,aAAa,EAAEtH,iBAAiB,CAAC8H,IAAI;MAAEX,QAAQ,EAAE;IAAiH,CAAC;EACnO,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElB,IAAI,EAAEtG,EAAE,CAACiG;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAErF,EAAE,CAACmO;IAAiB,CAAC,EAAE;MAAE9I,IAAI,EAAErF,EAAE,CAACoO;IAAa,CAAC,EAAE;MAAE/I,IAAI,EAAE/E,EAAE,CAAC+N;IAAS,CAAC,EAAE;MAAEhJ,IAAI,EAAEtG,EAAE,CAACmG;IAAO,CAAC,EAAE;MAAEG,IAAI,EAAErF,EAAE,CAACsO;IAAqB,CAAC,EAAE;MAAEjJ,IAAI,EAAEwE,SAAS;MAAEzC,UAAU,EAAE,CAAC;QAChO/B,IAAI,EAAE9F;MACV,CAAC,EAAE;QACC8F,IAAI,EAAEhG,MAAM;QACZyH,IAAI,EAAE,CAACjI,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEwG,IAAI,EAAEN,kBAAkB;MAAEqC,UAAU,EAAE,CAAC;QAC3C/B,IAAI,EAAE9F;MACV,CAAC,EAAE;QACC8F,IAAI,EAAEhG,MAAM;QACZyH,IAAI,EAAE,CAAChD,oBAAoB;MAC/B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEN,QAAQ,EAAE,CAAC;MACvC6B,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEoI,IAAI,EAAE,CAAC;MACPvC,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEyI,YAAY,EAAE,CAAC;MACf5C,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE2I,SAAS,EAAE,CAAC;MACZ9C,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE6I,MAAM,EAAE,CAAC;MACThD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE4J,YAAY,EAAE,CAAC;MACf/D,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE4J,aAAa,EAAE,CAAC;MAChBhE,IAAI,EAAE5F,MAAM;MACZqH,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE0C,WAAW,EAAE,CAAC;MACdnE,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAEqK,aAAa,EAAE,CAAC;MAChBzE,IAAI,EAAE5F,MAAM;MACZqH,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEiD,WAAW,EAAE,CAAC;MACd1E,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAEiI,iBAAiB,EAAE,CAAC;MACpBrC,IAAI,EAAE5F,MAAM;MACZqH,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE+H,QAAQ,EAAE,CAAC;MACXxJ,IAAI,EAAE3F,SAAS;MACfoH,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM/B,kBAAkB,CAAC;EACrB;EACA,IAAIgL,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA;EACA,IAAIC,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC5I,KAAK,EAAE;IAChB,IAAI,CAAC6I,SAAS,GAAGlQ,qBAAqB,CAACqH,KAAK,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIoG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC0C,kBAAkB,CAAC,IAAI,CAACL,MAAM,CAAC,IAAI,IAAI,CAACK,kBAAkB,CAAC,IAAI,CAACH,IAAI,CAAC;EACrF;EACA,IAAIvC,WAAWA,CAACpG,KAAK,EAAE;IACnB,IAAI,CAAC+I,iBAAiB,GAAG/I,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGrH,qBAAqB,CAACqH,KAAK,CAAC;EAChF;EACA;EACA,IAAIgJ,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI,CAAC3B,QAAQ;EAC7C;EACA7K,WAAWA,CAACyM,IAAI,EAAEC,QAAQ,EAAE9H,OAAO,EAAE3E,kBAAkB,EAAE0M,aAAa,EAAEC,eAAe,GAAG,KAAK,EAAEC,cAAc,EAAE;IAC7G,IAAI,CAACJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC9H,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC3E,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC4M,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,QAAQ,GAAG,IAAInR,SAAS,CAAC,CAAC;IAC/B;IACA,IAAI,CAACoR,aAAa,GAAG,IAAIzR,YAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAAC0K,UAAU,GAAG,IAAIzJ,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACyQ,eAAe,GAAG,IAAIzQ,OAAO,CAAC,CAAC;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACqF,eAAe,GAAG;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;IAClD,IAAI,CAACvB,qBAAqB,GAAG,IAAIhE,OAAO,CAAC,CAAC;IAC1C;IACA;IACA,IAAIkQ,IAAI,EAAE;MACNA,IAAI,CAACQ,MAAM,CAAC3H,IAAI,CAACzI,SAAS,CAAC,IAAI,CAACmJ,UAAU,CAAC,CAAC,CAACxF,SAAS,CAAC,MAAM;QACzD,IAAI,CAAC0M,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACN;IACA;IACA;IACAR,aAAa,CACRM,MAAM,CAAC,CAAC,CACR3H,IAAI,CAACzI,SAAS,CAAC,IAAI,CAACmJ,UAAU,CAAC,CAAC,CAChCxF,SAAS,CAAC,MAAM,IAAI,CAAC2M,oBAAoB,CAAC,CAAC,CAAC;IACjD,IAAI,CAACf,SAAS,GAAGQ,eAAe;EACpC;EACAtM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC8M,WAAW,CAACC,OAAO,CACnB/H,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACoQ,WAAW,CAAC,EAAEvQ,SAAS,CAAC,IAAI,CAACmJ,UAAU,CAAC,CAAC,CAC7DxF,SAAS,CAAE8M,MAAM,IAAK;MACvB,IAAI,CAACR,QAAQ,CAACS,KAAK,CAACD,MAAM,CAAC5Q,MAAM,CAAC8Q,IAAI,IAAI,CAACA,IAAI,CAACtN,UAAU,IAAIsN,IAAI,CAACtN,UAAU,KAAK,IAAI,CAAC,CAAC;MACxF,IAAI,CAAC4M,QAAQ,CAACW,eAAe,CAAC,CAAC;IACnC,CAAC,CAAC;IACF,IAAI,CAACX,QAAQ,CAACO,OAAO,CAAC/H,IAAI,CAACtI,SAAS,CAAC,IAAI,CAAC,CAAC,CAACwD,SAAS,CAAC,MAAM;MACxD,IAAI,CAAC0M,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACJ,QAAQ,CAACY,OAAO,CAAEJ,MAAM,IAAK;QAC9B,IAAI,CAACK,kBAAkB,CAACL,MAAM,CAAC;QAC/B,IAAI,CAACM,oBAAoB,CAACN,MAAM,CAAC;QACjC,IAAI,CAACO,gBAAgB,CAACP,MAAM,CAAC;MACjC,CAAC,CAAC;MACF,IAAI,CAAC,IAAI,CAACR,QAAQ,CAACgB,MAAM,IACrB,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC/B,MAAM,CAAC,IAC/B,IAAI,CAAC+B,aAAa,CAAC,IAAI,CAAC7B,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACiB,oBAAoB,CAAC,CAAC;MAC/B;MACA,IAAI,CAAClN,kBAAkB,CAACQ,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;IACA,IAAI,CAACmE,OAAO,CAAC0B,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC0G,eAAe,CACf1H,IAAI,CAACrI,YAAY,CAAC,EAAE,CAAC;MAAE;MAC5BJ,SAAS,CAAC,IAAI,CAACmJ,UAAU,CAAC,CAAC,CACtBxF,SAAS,CAAC,MAAM,IAAI,CAAC2M,oBAAoB,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC;EACN;EACAzE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnI,qBAAqB,CAACuI,QAAQ,CAAC,CAAC;IACrC,IAAI,CAACkE,eAAe,CAAClE,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACgE,QAAQ,CAACnE,OAAO,CAAC,CAAC;IACvB,IAAI,CAAC3C,UAAU,CAAChC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACgC,UAAU,CAAC8C,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC+D,QAAQ,CAACY,OAAO,CAACJ,MAAM,IAAIA,MAAM,CAACvE,IAAI,CAAC,CAAC,CAAC;EAClD;EACA;EACApC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACmG,QAAQ,CAACY,OAAO,CAACJ,MAAM,IAAIA,MAAM,CAAC3G,KAAK,CAAC,CAAC,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACIwG,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA;IACA;IACA;IACA;IACA,IAAItL,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACkM,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC3J,MAAM,EAAE;MACjC,IAAI,IAAI,CAAC2J,KAAK,CAACpK,IAAI,IAAI,MAAM,EAAE;QAC3B/B,IAAI,IAAI,IAAI,CAACmM,KAAK,CAACxE,SAAS,CAAC,CAAC;MAClC,CAAC,MACI,IAAI,IAAI,CAACwE,KAAK,CAACpK,IAAI,IAAI,MAAM,EAAE;QAChC,MAAMqK,KAAK,GAAG,IAAI,CAACD,KAAK,CAACxE,SAAS,CAAC,CAAC;QACpC3H,IAAI,IAAIoM,KAAK;QACbnM,KAAK,IAAImM,KAAK;MAClB;IACJ;IACA,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC7J,MAAM,EAAE;MACnC,IAAI,IAAI,CAAC6J,MAAM,CAACtK,IAAI,IAAI,MAAM,EAAE;QAC5B9B,KAAK,IAAI,IAAI,CAACoM,MAAM,CAAC1E,SAAS,CAAC,CAAC;MACpC,CAAC,MACI,IAAI,IAAI,CAAC0E,MAAM,CAACtK,IAAI,IAAI,MAAM,EAAE;QACjC,MAAMqK,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC1E,SAAS,CAAC,CAAC;QACrC1H,KAAK,IAAImM,KAAK;QACdpM,IAAI,IAAIoM,KAAK;MACjB;IACJ;IACA;IACA;IACA;IACA;IACApM,IAAI,GAAGA,IAAI,IAAI,IAAI;IACnBC,KAAK,GAAGA,KAAK,IAAI,IAAI;IACrB,IAAID,IAAI,KAAK,IAAI,CAACD,eAAe,CAACC,IAAI,IAAIC,KAAK,KAAK,IAAI,CAACF,eAAe,CAACE,KAAK,EAAE;MAC5E,IAAI,CAACF,eAAe,GAAG;QAAEC,IAAI;QAAEC;MAAM,CAAC;MACtC;MACA;MACA,IAAI,CAAC8C,OAAO,CAAC8B,GAAG,CAAC,MAAM,IAAI,CAACnG,qBAAqB,CAACyD,IAAI,CAAC,IAAI,CAACpC,eAAe,CAAC,CAAC;IACjF;EACJ;EACAuM,SAASA,CAAA,EAAG;IACR;IACA,IAAI,IAAI,CAAC/B,SAAS,IAAI,IAAI,CAACgC,SAAS,CAAC,CAAC,EAAE;MACpC;MACA,IAAI,CAACxJ,OAAO,CAAC0B,iBAAiB,CAAC,MAAM,IAAI,CAAC0G,eAAe,CAAChJ,IAAI,CAAC,CAAC,CAAC;IACrE;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2J,kBAAkBA,CAACL,MAAM,EAAE;IACvBA,MAAM,CAACrI,iBAAiB,CACnBK,IAAI,CAAC5I,MAAM,CAAE8J,KAAK,IAAKA,KAAK,CAACd,SAAS,KAAKc,KAAK,CAACb,OAAO,CAAC,EAAE9I,SAAS,CAAC,IAAI,CAACiQ,QAAQ,CAACO,OAAO,CAAC,CAAC,CAC5F7M,SAAS,CAAEgG,KAAK,IAAK;MACtB;MACA;MACA,IAAIA,KAAK,CAACb,OAAO,KAAK,cAAc,IAAI,IAAI,CAACkH,cAAc,KAAK,gBAAgB,EAAE;QAC9E,IAAI,CAACH,QAAQ,CAACnG,aAAa,CAAC8H,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtE;MACA,IAAI,CAACnB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAClN,kBAAkB,CAACQ,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF,IAAI6M,MAAM,CAAC1J,IAAI,KAAK,MAAM,EAAE;MACxB0J,MAAM,CAAClI,YAAY,CACdE,IAAI,CAACzI,SAAS,CAAC,IAAI,CAACiQ,QAAQ,CAACO,OAAO,CAAC,CAAC,CACtC7M,SAAS,CAAC,MAAM,IAAI,CAAC+N,kBAAkB,CAACjB,MAAM,CAACjJ,MAAM,CAAC,CAAC;IAChE;EACJ;EACA;AACJ;AACA;AACA;EACIuJ,oBAAoBA,CAACN,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA;IACA;IACAA,MAAM,CAAC5J,iBAAiB,CAAC4B,IAAI,CAACzI,SAAS,CAAC,IAAI,CAACiQ,QAAQ,CAACO,OAAO,CAAC,CAAC,CAAC7M,SAAS,CAAC,MAAM;MAC5E,IAAI,CAACoE,OAAO,CAAC4J,gBAAgB,CAAClJ,IAAI,CAACvI,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyD,SAAS,CAAC,MAAM;QACxD,IAAI,CAAC0M,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAW,gBAAgBA,CAACP,MAAM,EAAE;IACrB,IAAIA,MAAM,EAAE;MACRA,MAAM,CAACvJ,YAAY,CACduB,IAAI,CAACzI,SAAS,CAACJ,KAAK,CAAC,IAAI,CAACqQ,QAAQ,CAACO,OAAO,EAAE,IAAI,CAACrH,UAAU,CAAC,CAAC,CAAC,CAC9DxF,SAAS,CAAC,MAAM;QACjB,IAAI,CAAC2M,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAAClN,kBAAkB,CAACQ,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;EACA8N,kBAAkBA,CAACE,KAAK,EAAE;IACtB,MAAMJ,SAAS,GAAG,IAAI,CAAC3B,QAAQ,CAACnG,aAAa,CAAC8H,SAAS;IACvD,MAAMK,SAAS,GAAG,+BAA+B;IACjD,IAAID,KAAK,EAAE;MACPJ,SAAS,CAACC,GAAG,CAACI,SAAS,CAAC;IAC5B,CAAC,MACI;MACDL,SAAS,CAACxF,MAAM,CAAC6F,SAAS,CAAC;IAC/B;EACJ;EACA;EACAxB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAClB,MAAM,GAAG,IAAI,CAACE,IAAI,GAAG,IAAI;IAC9B;IACA,IAAI,CAACY,QAAQ,CAACY,OAAO,CAACJ,MAAM,IAAI;MAC5B,IAAIA,MAAM,CAAC9N,QAAQ,IAAI,KAAK,EAAE;QAC1B,IAAI,IAAI,CAAC0M,IAAI,IAAI,IAAI,KAAK,OAAOtJ,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;UACtErD,6BAA6B,CAAC,KAAK,CAAC;QACxC;QACA,IAAI,CAAC2M,IAAI,GAAGoB,MAAM;MACtB,CAAC,MACI;QACD,IAAI,IAAI,CAACtB,MAAM,IAAI,IAAI,KAAK,OAAOpJ,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;UACxErD,6BAA6B,CAAC,OAAO,CAAC;QAC1C;QACA,IAAI,CAACyM,MAAM,GAAGsB,MAAM;MACxB;IACJ,CAAC,CAAC;IACF,IAAI,CAACY,MAAM,GAAG,IAAI,CAACF,KAAK,GAAG,IAAI;IAC/B;IACA,IAAI,IAAI,CAACvB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAClJ,KAAK,KAAK,KAAK,EAAE;MACxC,IAAI,CAACyK,KAAK,GAAG,IAAI,CAAC9B,IAAI;MACtB,IAAI,CAACgC,MAAM,GAAG,IAAI,CAAClC,MAAM;IAC7B,CAAC,MACI;MACD,IAAI,CAACgC,KAAK,GAAG,IAAI,CAAChC,MAAM;MACxB,IAAI,CAACkC,MAAM,GAAG,IAAI,CAAChC,IAAI;IAC3B;EACJ;EACA;EACAkC,SAASA,CAAA,EAAG;IACR,OAAS,IAAI,CAACL,aAAa,CAAC,IAAI,CAAC/B,MAAM,CAAC,IAAI,IAAI,CAACA,MAAM,CAACpI,IAAI,IAAI,MAAM,IACjE,IAAI,CAACmK,aAAa,CAAC,IAAI,CAAC7B,IAAI,CAAC,IAAI,IAAI,CAACA,IAAI,CAACtI,IAAI,IAAI,MAAO;EACnE;EACAtF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACyO,aAAa,CAACpJ,IAAI,CAAC,CAAC;IACzB,IAAI,CAACgL,6BAA6B,CAAC,CAAC;EACxC;EACAA,6BAA6BA,CAAA,EAAG;IAC5B;IACA,CAAC,IAAI,CAAC3C,MAAM,EAAE,IAAI,CAACE,IAAI,CAAC,CACnBxP,MAAM,CAAC4Q,MAAM,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACrJ,YAAY,IAAI,IAAI,CAACoI,kBAAkB,CAACiB,MAAM,CAAC,CAAC,CACnFI,OAAO,CAACJ,MAAM,IAAIA,MAAM,CAACrE,sBAAsB,CAAC,CAAC,CAAC;EAC3D;EACAvK,kBAAkBA,CAAA,EAAG;IACjB,OAAS,IAAI,CAACqP,aAAa,CAAC,IAAI,CAAC/B,MAAM,CAAC,IAAI,IAAI,CAACK,kBAAkB,CAAC,IAAI,CAACL,MAAM,CAAC,IAC3E,IAAI,CAAC+B,aAAa,CAAC,IAAI,CAAC7B,IAAI,CAAC,IAAI,IAAI,CAACG,kBAAkB,CAAC,IAAI,CAACH,IAAI,CAAE;EAC7E;EACA6B,aAAaA,CAACT,MAAM,EAAE;IAClB,OAAOA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACjJ,MAAM;EAC1C;EACA;EACAgI,kBAAkBA,CAACiB,MAAM,EAAE;IACvB,IAAI,IAAI,CAAChB,iBAAiB,IAAI,IAAI,EAAE;MAChC,OAAO,CAAC,CAACgB,MAAM,IAAIA,MAAM,CAAC1J,IAAI,KAAK,MAAM;IAC7C;IACA,OAAO,IAAI,CAAC0I,iBAAiB;EACjC;EACA;IAAS,IAAI,CAAC5L,IAAI,YAAAkO,2BAAAhO,CAAA;MAAA,YAAAA,CAAA,IAAwFG,kBAAkB,EAjvB5BhG,EAAE,CAAA8F,iBAAA,CAivB4C5E,EAAE,CAAC4S,cAAc,MAjvB/D9T,EAAE,CAAA8F,iBAAA,CAivB0F9F,EAAE,CAACiG,UAAU,GAjvBzGjG,EAAE,CAAA8F,iBAAA,CAivBoH9F,EAAE,CAACmG,MAAM,GAjvB/HnG,EAAE,CAAA8F,iBAAA,CAivB0I9F,EAAE,CAAC+F,iBAAiB,GAjvBhK/F,EAAE,CAAA8F,iBAAA,CAivB2KpG,EAAE,CAACqU,aAAa,GAjvB7L/T,EAAE,CAAA8F,iBAAA,CAivBwMnB,2BAA2B,GAjvBrO3E,EAAE,CAAA8F,iBAAA,CAivBgPtD,qBAAqB;IAAA,CAA4D;EAAE;EACra;IAAS,IAAI,CAAC4D,IAAI,kBAlvB8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EAkvBJN,kBAAkB;MAAAO,SAAA;MAAAyN,cAAA,WAAAC,kCAAArR,EAAA,EAAAC,GAAA,EAAAqR,QAAA;QAAA,IAAAtR,EAAA;UAlvBhB5C,EAAE,CAAAmU,cAAA,CAAAD,QAAA,EAuvB7BlP,gBAAgB;UAvvBWhF,EAAE,CAAAmU,cAAA,CAAAD,QAAA,EAuvBoD5L,SAAS;QAAA;QAAA,IAAA1F,EAAA;UAAA,IAAA+M,EAAA;UAvvB/D3P,EAAE,CAAA4P,cAAA,CAAAD,EAAA,GAAF3P,EAAE,CAAA6P,WAAA,QAAAhN,GAAA,CAAAiN,QAAA,GAAAH,EAAA,CAAAI,KAAA;UAAF/P,EAAE,CAAA4P,cAAA,CAAAD,EAAA,GAAF3P,EAAE,CAAA6P,WAAA,QAAAhN,GAAA,CAAAwP,WAAA,GAAA1C,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAA4E,yBAAAxR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAA0P,WAAA,CAuvB2J1K,gBAAgB;QAAA;QAAA,IAAApC,EAAA;UAAA,IAAA+M,EAAA;UAvvB7K3P,EAAE,CAAA4P,cAAA,CAAAD,EAAA,GAAF3P,EAAE,CAAA6P,WAAA,QAAAhN,GAAA,CAAA4O,YAAA,GAAA9B,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAvJ,SAAA,sBAkvBsM,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAA2N,gCAAAzR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlvB1M5C,EAAE,CAAA0D,WAAA,2CAAAb,GAAA,CAAA0O,iBAAA;QAAA;MAAA;MAAAhB,MAAA;QAAAa,QAAA;QAAAxC,WAAA;MAAA;MAAA4B,OAAA;QAAAwB,aAAA;MAAA;MAAAvB,QAAA;MAAAzJ,QAAA,GAAFhH,EAAE,CAAAiH,kBAAA,CAkvBsV,CAChb;QACIC,OAAO,EAAEnC,oBAAoB;QAC7BoC,WAAW,EAAEnB;MACjB,CAAC,CACJ;MAAAqB,kBAAA,EAAAtD,GAAA;MAAAuD,KAAA;MAAAC,IAAA;MAAAmJ,MAAA;MAAAlJ,QAAA,WAAA8M,4BAAA1R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvvB2F5C,EAAE,CAAA0H,eAAA,CAAA5D,GAAA;UAAF9D,EAAE,CAAAuU,UAAA,IAAA5R,iCAAA,gBAuvBqZ,CAAC;UAvvBxZ3C,EAAE,CAAA6D,YAAA,EAuvBwc,CAAC;UAvvB3c7D,EAAE,CAAA6D,YAAA,KAuvBqgB,CAAC;UAvvBxgB7D,EAAE,CAAAuU,UAAA,IAAA3Q,gDAAA,+BAuvBmmB,CAAC;QAAA;QAAA,IAAAhB,EAAA;UAvvBtmB5C,EAAE,CAAAwU,UAAA,SAAA3R,GAAA,CAAA+L,WAuvBoV,CAAC;UAvvBvV5O,EAAE,CAAAyU,SAAA,EAuvB4iB,CAAC;UAvvB/iBzU,EAAE,CAAAwU,UAAA,UAAA3R,GAAA,CAAAiN,QAuvB4iB,CAAC;QAAA;MAAA;MAAAc,YAAA,GAAw8H/Q,EAAE,CAAC6U,IAAI,EAA6F1P,gBAAgB;MAAA2P,MAAA;MAAAhN,aAAA;MAAAC,eAAA;IAAA,EAAoI;EAAE;AACr1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzvBoG7H,EAAE,CAAA8H,iBAAA,CAyvBX9B,kBAAkB,EAAc,CAAC;IAChHM,IAAI,EAAEnG,SAAS;IACf4H,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEyI,QAAQ,EAAE,oBAAoB;MAAExI,IAAI,EAAE;QACrE,OAAO,EAAE,sBAAsB;QAC/B,gDAAgD,EAAE,mBAAmB;QACrE,iBAAiB,EAAE;MACvB,CAAC;MAAEL,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAAEP,aAAa,EAAEtH,iBAAiB,CAAC8H,IAAI;MAAEC,SAAS,EAAE,CAClG;QACIlB,OAAO,EAAEnC,oBAAoB;QAC7BoC,WAAW,EAAEnB;MACjB,CAAC,CACJ;MAAEwB,QAAQ,EAAE,0WAA0W;MAAEmN,MAAM,EAAE,CAAC,s1HAAs1H;IAAE,CAAC;EACvuI,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErO,IAAI,EAAEpF,EAAE,CAAC4S,cAAc;MAAEzL,UAAU,EAAE,CAAC;QACtE/B,IAAI,EAAE9F;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8F,IAAI,EAAEtG,EAAE,CAACiG;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAEtG,EAAE,CAACmG;IAAO,CAAC,EAAE;MAAEG,IAAI,EAAEtG,EAAE,CAAC+F;IAAkB,CAAC,EAAE;MAAEO,IAAI,EAAE5G,EAAE,CAACqU;IAAc,CAAC,EAAE;MAAEzN,IAAI,EAAEwE,SAAS;MAAEzC,UAAU,EAAE,CAAC;QAC5I/B,IAAI,EAAEhG,MAAM;QACZyH,IAAI,EAAE,CAACpD,2BAA2B;MACtC,CAAC;IAAE,CAAC,EAAE;MAAE2B,IAAI,EAAEwE,SAAS;MAAEzC,UAAU,EAAE,CAAC;QAClC/B,IAAI,EAAE9F;MACV,CAAC,EAAE;QACC8F,IAAI,EAAEhG,MAAM;QACZyH,IAAI,EAAE,CAACvF,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6P,WAAW,EAAE,CAAC;MAC1C/L,IAAI,EAAEzF,eAAe;MACrBkH,IAAI,EAAE,CAACO,SAAS,EAAE;QACV;QACA;QACAsM,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAE9E,QAAQ,EAAE,CAAC;MACXxJ,IAAI,EAAExF,YAAY;MAClBiH,IAAI,EAAE,CAAC/C,gBAAgB;IAC3B,CAAC,CAAC;IAAEyM,YAAY,EAAE,CAAC;MACfnL,IAAI,EAAE3F,SAAS;MACfoH,IAAI,EAAE,CAAC/C,gBAAgB;IAC3B,CAAC,CAAC;IAAEoM,QAAQ,EAAE,CAAC;MACX9K,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEmO,WAAW,EAAE,CAAC;MACdtI,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEuR,aAAa,EAAE,CAAC;MAChB1L,IAAI,EAAE5F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmU,iBAAiB,SAAS7P,gBAAgB,CAAC;EAC7CC,WAAWA,CAAC6P,iBAAiB,EAAEC,SAAS,EAAE3P,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;IAC5E,KAAK,CAACwP,iBAAiB,EAAEC,SAAS,EAAE3P,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,CAAC;EAC7E;EACA;IAAS,IAAI,CAACK,IAAI,YAAAqP,0BAAAnP,CAAA;MAAA,YAAAA,CAAA,IAAwFgP,iBAAiB,EAxyB3B7U,EAAE,CAAA8F,iBAAA,CAwyB2C9F,EAAE,CAAC+F,iBAAiB,GAxyBjE/F,EAAE,CAAA8F,iBAAA,CAwyB4E5F,UAAU,CAAC,MAAM+U,mBAAmB,CAAC,GAxyBnHjV,EAAE,CAAA8F,iBAAA,CAwyB8H9F,EAAE,CAACiG,UAAU,GAxyB7IjG,EAAE,CAAA8F,iBAAA,CAwyBwJpG,EAAE,CAACwG,gBAAgB,GAxyB7KlG,EAAE,CAAA8F,iBAAA,CAwyBwL9F,EAAE,CAACmG,MAAM;IAAA,CAA4C;EAAE;EACjV;IAAS,IAAI,CAACC,IAAI,kBAzyB8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EAyyBJuO,iBAAiB;MAAAtO,SAAA;MAAAC,SAAA,sBAA4E,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAwO,+BAAAtS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzyB7F5C,EAAE,CAAA4G,WAAA,gBAAA/D,GAAA,CAAAsC,UAAA,CAAA0B,eAAA,CAAAC,IAAA,wBAAAjE,GAAA,CAAAsC,UAAA,CAAA0B,eAAA,CAAAE,KAAA;QAAA;MAAA;MAAAC,QAAA,GAAFhH,EAAE,CAAAiH,kBAAA,CAyyB8S,CACxY;QACIC,OAAO,EAAEvH,aAAa;QACtBwH,WAAW,EAAE0N;MACjB,CAAC,CACJ,GA9yB2F7U,EAAE,CAAAoH,0BAAA;MAAAC,kBAAA,EAAA5E,GAAA;MAAA6E,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAA2N,2BAAAvS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAA0H,eAAA;UAAF1H,EAAE,CAAA6D,YAAA,EA8yBnB,CAAC;QAAA;MAAA;MAAA8D,aAAA;MAAAC,eAAA;IAAA,EAAkH;EAAE;AACxM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhzBoG7H,EAAE,CAAA8H,iBAAA,CAgzBX+M,iBAAiB,EAAc,CAAC;IAC/GvO,IAAI,EAAEnG,SAAS;IACf4H,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BR,QAAQ,EAAE,2BAA2B;MACrCS,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,wBAAwB,EAAE,iCAAiC;QAC3D,yBAAyB,EAAE,kCAAkC;QAC7D,iBAAiB,EAAE;MACvB,CAAC;MACDL,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAC/CP,aAAa,EAAEtH,iBAAiB,CAAC8H,IAAI;MACrCC,SAAS,EAAE,CACP;QACIlB,OAAO,EAAEvH,aAAa;QACtBwH,WAAW,EAAE0N;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvO,IAAI,EAAEtG,EAAE,CAAC+F;IAAkB,CAAC,EAAE;MAAEO,IAAI,EAAE2O,mBAAmB;MAAE5M,UAAU,EAAE,CAAC;QACxG/B,IAAI,EAAEhG,MAAM;QACZyH,IAAI,EAAE,CAAC7H,UAAU,CAAC,MAAM+U,mBAAmB,CAAC;MAChD,CAAC;IAAE,CAAC,EAAE;MAAE3O,IAAI,EAAEtG,EAAE,CAACiG;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAE5G,EAAE,CAACwG;IAAiB,CAAC,EAAE;MAAEI,IAAI,EAAEtG,EAAE,CAACmG;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACrG,MAAMiP,UAAU,SAAS9M,SAAS,CAAC;EAC/BrD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGoQ,SAAS,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,eAAe,GAAG,CAAC;EAC5B;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACH,gBAAgB;EAChC;EACA,IAAIG,eAAeA,CAACjN,KAAK,EAAE;IACvB,IAAI,CAAC8M,gBAAgB,GAAGnU,qBAAqB,CAACqH,KAAK,CAAC;EACxD;EACA;AACJ;AACA;AACA;EACI,IAAIkN,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACH,YAAY;EAC5B;EACA,IAAIG,WAAWA,CAAClN,KAAK,EAAE;IACnB,IAAI,CAAC+M,YAAY,GAAGnU,oBAAoB,CAACoH,KAAK,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACI,IAAImN,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACH,eAAe;EAC/B;EACA,IAAIG,cAAcA,CAACnN,KAAK,EAAE;IACtB,IAAI,CAACgN,eAAe,GAAGpU,oBAAoB,CAACoH,KAAK,CAAC;EACtD;EACA;IAAS,IAAI,CAAC7C,IAAI;MAAA,IAAAiQ,uBAAA;MAAA,gBAAAC,mBAAAhQ,CAAA;QAAA,QAAA+P,uBAAA,KAAAA,uBAAA,GA12B8E5V,EAAE,CAAA8V,qBAAA,CA02BQV,UAAU,IAAAvP,CAAA,IAAVuP,UAAU;MAAA;IAAA,GAAqD;EAAE;EAC3K;IAAS,IAAI,CAAChP,IAAI,kBA32B8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EA22BJ8O,UAAU;MAAA7O,SAAA;MAAAC,SAAA,eAA2K,IAAI,qBAAqB,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAqP,wBAAAnT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA32B9M5C,EAAE,CAAAqQ,WAAA;UAAFrQ,EAAE,CAAA4G,WAAA,QAAA/D,GAAA,CAAA4S,eAAA,GAAA5S,GAAA,CAAA6S,WAAA,yBAAA7S,GAAA,CAAA4S,eAAA,GAAA5S,GAAA,CAAA8S,cAAA;UAAF3V,EAAE,CAAA0D,WAAA,mBAAAb,GAAA,CAAA4B,QAAA,+BAAA5B,GAAA,CAAAgG,IAAA,gCAAAhG,GAAA,CAAAgG,IAAA,gCAAAhG,GAAA,CAAAgG,IAAA,kCAAAhG,GAAA,CAAAyG,MAAA,uBAAAzG,GAAA,CAAA4S,eAAA;QAAA;MAAA;MAAAlF,MAAA;QAAAkF,eAAA;QAAAC,WAAA;QAAAC,cAAA;MAAA;MAAAlF,QAAA;MAAAzJ,QAAA,GAAFhH,EAAE,CAAAoH,0BAAA;MAAAC,kBAAA,EAAA5E,GAAA;MAAA6E,KAAA;MAAAC,IAAA;MAAAmJ,MAAA;MAAAlJ,QAAA,WAAAwO,oBAAApT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAA0H,eAAA;UAAF1H,EAAE,CAAAgD,cAAA,eA22B+yB,CAAC;UA32BlzBhD,EAAE,CAAA6D,YAAA,EA22B80B,CAAC;UA32Bj1B7D,EAAE,CAAAwD,YAAA,CA22Bw1B,CAAC;QAAA;MAAA;MAAAoN,YAAA,GAAiDlR,EAAE,CAACC,aAAa;MAAAgI,aAAA;MAAAkJ,IAAA;QAAAC,SAAA,EAAgE,CAACxM,mBAAmB,CAACC,eAAe;MAAC;MAAAqD,eAAA;IAAA,EAAiG;EAAE;AACxsC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA72BoG7H,EAAE,CAAA8H,iBAAA,CA62BXsN,UAAU,EAAc,CAAC;IACxG9O,IAAI,EAAEnG,SAAS;IACf4H,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEyI,QAAQ,EAAE,YAAY;MAAEM,UAAU,EAAE,CAACzM,mBAAmB,CAACC,eAAe,CAAC;MAAE0D,IAAI,EAAE;QACvG,OAAO,EAAE,wBAAwB;QACjC,UAAU,EAAE,IAAI;QAChB;QACA,cAAc,EAAE,MAAM;QACtB,wBAAwB,EAAE,oBAAoB;QAC9C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,2BAA2B,EAAE,QAAQ;QACrC,2BAA2B,EAAE,iBAAiB;QAC9C,gBAAgB,EAAE,sCAAsC;QACxD,mBAAmB,EAAE,yCAAyC;QAC9D,iBAAiB,EAAE;MACvB,CAAC;MAAEL,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAAEP,aAAa,EAAEtH,iBAAiB,CAAC8H,IAAI;MAAEX,QAAQ,EAAE;IAAiH,CAAC;EACnO,CAAC,CAAC,QAAkB;IAAEiO,eAAe,EAAE,CAAC;MAChCnP,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEiV,WAAW,EAAE,CAAC;MACdpP,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEkV,cAAc,EAAE,CAAC;MACjBrP,IAAI,EAAE7F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwU,mBAAmB,SAASjP,kBAAkB,CAAC;EACjDf,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGoQ,SAAS,CAAC;IACnB,IAAI,CAAChD,WAAW,GAAGvH,SAAS;IAC5B;IACA,IAAI,CAACgF,QAAQ,GAAGhF,SAAS;EAC7B;EACA;IAAS,IAAI,CAACnF,IAAI;MAAA,IAAAsQ,gCAAA;MAAA,gBAAAC,4BAAArQ,CAAA;QAAA,QAAAoQ,gCAAA,KAAAA,gCAAA,GA54B8EjW,EAAE,CAAA8V,qBAAA,CA44BQb,mBAAmB,IAAApP,CAAA,IAAnBoP,mBAAmB;MAAA;IAAA,GAAqD;EAAE;EACpL;IAAS,IAAI,CAAC7O,IAAI,kBA74B8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EA64BJ2O,mBAAmB;MAAA1O,SAAA;MAAAyN,cAAA,WAAAmC,mCAAAvT,EAAA,EAAAC,GAAA,EAAAqR,QAAA;QAAA,IAAAtR,EAAA;UA74BjB5C,EAAE,CAAAmU,cAAA,CAAAD,QAAA,EAk5B7BW,iBAAiB;UAl5BU7U,EAAE,CAAAmU,cAAA,CAAAD,QAAA,EAk5BqDkB,UAAU;QAAA;QAAA,IAAAxS,EAAA;UAAA,IAAA+M,EAAA;UAl5BjE3P,EAAE,CAAA4P,cAAA,CAAAD,EAAA,GAAF3P,EAAE,CAAA6P,WAAA,QAAAhN,GAAA,CAAAiN,QAAA,GAAAH,EAAA,CAAAI,KAAA;UAAF/P,EAAE,CAAA4P,cAAA,CAAAD,EAAA,GAAF3P,EAAE,CAAA6P,WAAA,QAAAhN,GAAA,CAAAwP,WAAA,GAAA1C,EAAA;QAAA;MAAA;MAAAnJ,SAAA,sBA64B6F,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAA0P,iCAAAxT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA74BjG5C,EAAE,CAAA0D,WAAA,2CAAAb,GAAA,CAAA0O,iBAAA;QAAA;MAAA;MAAAd,QAAA;MAAAzJ,QAAA,GAAFhH,EAAE,CAAAiH,kBAAA,CA64BmQ,CAC7V;QACIC,OAAO,EAAEnC,oBAAoB;QAC7BoC,WAAW,EAAE8N;MACjB,CAAC,CACJ,GAl5B2FjV,EAAE,CAAAoH,0BAAA;MAAAC,kBAAA,EAAAjD,GAAA;MAAAkD,KAAA;MAAAC,IAAA;MAAAmJ,MAAA;MAAAlJ,QAAA,WAAA6O,6BAAAzT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAA0H,eAAA,CAAAvD,GAAA;UAAFnE,EAAE,CAAAuU,UAAA,IAAAvQ,kCAAA,gBAk5BiU,CAAC;UAl5BpUhE,EAAE,CAAA6D,YAAA,EAk5BqX,CAAC;UAl5BxX7D,EAAE,CAAA6D,YAAA,KAk5Bmb,CAAC;UAl5Btb7D,EAAE,CAAAuU,UAAA,IAAArQ,kDAAA,gCAk5BmhB,CAAC;QAAA;QAAA,IAAAtB,EAAA;UAl5BthB5C,EAAE,CAAAwU,UAAA,SAAA3R,GAAA,CAAA+L,WAk5BgQ,CAAC;UAl5BnQ5O,EAAE,CAAAyU,SAAA,EAk5B2d,CAAC;UAl5B9dzU,EAAE,CAAAwU,UAAA,UAAA3R,GAAA,CAAAiN,QAk5B2d,CAAC;QAAA;MAAA;MAAAc,YAAA,GAAy8H/Q,EAAE,CAAC6U,IAAI,EAA6FG,iBAAiB;MAAAF,MAAA,GAAAtQ,GAAA;MAAAsD,aAAA;MAAAC,eAAA;IAAA,EAAqI;EAAE;AACvwJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAp5BoG7H,EAAE,CAAA8H,iBAAA,CAo5BXmN,mBAAmB,EAAc,CAAC;IACjH3O,IAAI,EAAEnG,SAAS;IACf4H,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,uBAAuB;MAAEyI,QAAQ,EAAE,qBAAqB;MAAExI,IAAI,EAAE;QACvE,OAAO,EAAE,4CAA4C;QACrD,gDAAgD,EAAE,mBAAmB;QACrE,iBAAiB,EAAE;MACvB,CAAC;MAAEL,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAAEP,aAAa,EAAEtH,iBAAiB,CAAC8H,IAAI;MAAEC,SAAS,EAAE,CAClG;QACIlB,OAAO,EAAEnC,oBAAoB;QAC7BoC,WAAW,EAAE8N;MACjB,CAAC,CACJ;MAAEzN,QAAQ,EAAE,8WAA8W;MAAEmN,MAAM,EAAE,CAAC,s1HAAs1H;IAAE,CAAC;EAC3uI,CAAC,CAAC,QAAkB;IAAEtC,WAAW,EAAE,CAAC;MAC5B/L,IAAI,EAAEzF,eAAe;MACrBkH,IAAI,EAAE,CAACqN,UAAU,EAAE;QACX;QACA;QACAR,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAE9E,QAAQ,EAAE,CAAC;MACXxJ,IAAI,EAAExF,YAAY;MAClBiH,IAAI,EAAE,CAAC8M,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyB,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAAC3Q,IAAI,YAAA4Q,yBAAA1Q,CAAA;MAAA,YAAAA,CAAA,IAAwFyQ,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBA96B8ExW,EAAE,CAAAyW,gBAAA;MAAAnQ,IAAA,EA86BSgQ;IAAgB,EAY9F;EAAE;EAC/B;IAAS,IAAI,CAACI,IAAI,kBA37B8E1W,EAAE,CAAA2W,gBAAA;MAAAC,OAAA,GA27BqC7W,YAAY,EAAEiB,eAAe,EAAEpB,mBAAmB,EAAEA,mBAAmB,EACtMoB,eAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAA6G,SAAA,oBAAAA,SAAA,KA97BoG7H,EAAE,CAAA8H,iBAAA,CA87BXwO,gBAAgB,EAAc,CAAC;IAC9GhQ,IAAI,EAAEvF,QAAQ;IACdgH,IAAI,EAAE,CAAC;MACC6O,OAAO,EAAE,CAAC7W,YAAY,EAAEiB,eAAe,EAAEpB,mBAAmB,CAAC;MAC7DiX,OAAO,EAAE,CACLjX,mBAAmB,EACnBoB,eAAe,EACfsH,SAAS,EACTtC,kBAAkB,EAClBhB,gBAAgB,EAChBoQ,UAAU,EACVH,mBAAmB,EACnBJ,iBAAiB,CACpB;MACDiC,YAAY,EAAE,CACVxO,SAAS,EACTtC,kBAAkB,EAClBhB,gBAAgB,EAChBoQ,UAAU,EACVH,mBAAmB,EACnBJ,iBAAiB;IAEzB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASlQ,2BAA2B,EAAEG,mCAAmC,EAAEwD,SAAS,EAAEtC,kBAAkB,EAAEhB,gBAAgB,EAAEoQ,UAAU,EAAEH,mBAAmB,EAAEJ,iBAAiB,EAAEyB,gBAAgB,EAAEhS,mBAAmB,EAAEE,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}