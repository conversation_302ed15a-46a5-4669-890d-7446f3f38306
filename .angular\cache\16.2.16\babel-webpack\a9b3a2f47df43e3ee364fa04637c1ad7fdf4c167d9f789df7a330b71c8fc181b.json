{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { FormsModule } from \"@angular/forms\"; // For ngModel in radio buttons\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\";\nimport { MatRadioModule } from \"@angular/material/radio\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { QuizComponent } from \"./quiz.component\";\nexport let QuizModule = class QuizModule {};\nQuizModule = __decorate([NgModule({\n  declarations: [QuizComponent],\n  imports: [CommonModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatProgressBarModule, MatRadioModule, MatProgressSpinnerModule, RouterModule.forChild([{\n    path: \":id\",\n    component: QuizComponent\n  }])]\n})], QuizModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressBarModule", "MatRadioModule", "MatProgressSpinnerModule", "QuizComponent", "QuizModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component"], "sources": ["C:\\e-learning\\src\\app\\features\\quiz\\quiz.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // For ngModel in radio buttons\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\nimport { MatRadioModule } from \"@angular/material/radio\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { QuizComponent } from \"./quiz.component\"\n\n@NgModule({\n  declarations: [QuizComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressBarModule,\n    MatRadioModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([{ path: \":id\", component: QuizComponent }]),\n  ],\n})\nexport class QuizModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB,EAAC;AAE7C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,aAAa,QAAQ,kBAAkB;AAgBzC,WAAMC,UAAU,GAAhB,MAAMA,UAAU,GAAG;AAAbA,UAAU,GAAAC,UAAA,EAdtBZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CAACH,aAAa,CAAC;EAC7BI,OAAO,EAAE,CACPb,YAAY,EACZE,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,wBAAwB,EACxBP,YAAY,CAACa,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,KAAK;IAAEC,SAAS,EAAEP;EAAa,CAAE,CAAC,CAAC;CAErE,CAAC,C,EACWC,UAAU,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}