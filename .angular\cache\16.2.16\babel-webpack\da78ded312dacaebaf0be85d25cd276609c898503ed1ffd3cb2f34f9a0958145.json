{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { FormsModule } from \"@angular/forms\"; // Pour ngModel dans les filtres\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatSelectModule } from \"@angular/material/select\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\";\nimport { CourseListComponent } from \"./course-list/course-list.component\";\nimport { CourseDetailComponent } from \"./course-detail/course-detail.component\";\nimport { CourseCreateEditComponent } from \"./course-create-edit/course-create-edit.component\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class CoursesModule {\n  static {\n    this.ɵfac = function CoursesModule_Factory(t) {\n      return new (t || CoursesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CoursesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatChipsModule, MatProgressBarModule, RouterModule.forChild([{\n        path: \"\",\n        component: CourseListComponent\n      }, {\n        path: \"create\",\n        component: CourseCreateEditComponent\n      }, {\n        path: \"edit/:id\",\n        component: CourseCreateEditComponent\n      }, {\n        path: \":id\",\n        component: CourseDetailComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoursesModule, {\n    declarations: [CourseListComponent, CourseDetailComponent, CourseCreateEditComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatChipsModule, MatProgressBarModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatChipsModule", "MatProgressBarModule", "CourseListComponent", "CourseDetailComponent", "CourseCreateEditComponent", "ReactiveFormsModule", "CoursesModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\courses\\courses.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // Pour ngModel dans les filtres\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\n\nimport { CourseListComponent } from \"./course-list/course-list.component\"\nimport { CourseDetailComponent } from \"./course-detail/course-detail.component\"\nimport { CourseCreateEditComponent } from \"./course-create-edit/course-create-edit.component\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\n\n@NgModule({\n  declarations: [CourseListComponent, CourseDetailComponent, CourseCreateEditComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    RouterModule.forChild([\n      { path: \"\", component: CourseListComponent },\n      { path: \"create\", component: CourseCreateEditComponent },\n      { path: \"edit/:id\", component: CourseCreateEditComponent },\n      { path: \":id\", component: CourseDetailComponent },\n    ]),\n  ],\n})\nexport class CoursesModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB,EAAC;AAE7C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AAErE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,mBAAmB,QAAQ,gBAAgB;;;AAwBpD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAnBtBf,YAAY,EACZE,WAAW,EACXY,mBAAmB,EACnBX,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,oBAAoB,EACpBT,YAAY,CAACe,QAAQ,CAAC,CACpB;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEP;MAAmB,CAAE,EAC5C;QAAEM,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAEL;MAAyB,CAAE,EACxD;QAAEI,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEL;MAAyB,CAAE,EAC1D;QAAEI,IAAI,EAAE,KAAK;QAAEC,SAAS,EAAEN;MAAqB,CAAE,CAClD,CAAC;IAAA;EAAA;;;2EAGOG,aAAa;IAAAI,YAAA,GArBTR,mBAAmB,EAAEC,qBAAqB,EAAEC,yBAAyB;IAAAO,OAAA,GAElFpB,YAAY,EACZE,WAAW,EACXY,mBAAmB,EACnBX,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,oBAAoB,EAAAW,EAAA,CAAApB,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}