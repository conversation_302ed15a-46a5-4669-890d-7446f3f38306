{"version": 3, "file": "src_app_features_courses_courses_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAC8E;;;;;;;;;;;;;;;;;IAoBhEC,4DAAA,gBAAiE;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAY;;;;;IAMhGA,4DAAA,gBAAuE;IAAAA,oDAAA,iCAA0B;IAAAA,0DAAA,EAAY;;;;;IAO3GA,4DAAA,gBAAiE;IAAAA,oDAAA,gCAAoB;IAAAA,0DAAA,EAAY;;;;;IACjGA,4DAAA,gBAA4D;IAAAA,oDAAA,4CAA2B;IAAAA,0DAAA,EAAY;;;;;IAUnGA,4DAAA,gBAAkE;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAY;;;;;IASpGA,4DAAA,gBAAgE;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAY;;;;;IAC9FA,4DAAA,gBAA2D;IAAAA,oDAAA,qCAAyB;IAAAA,0DAAA,EAAY;;;;;IAJlGA,4DAAA,wBAAqG;IACxFA,oDAAA,oBAAQ;IAAAA,0DAAA,EAAY;IAC/BA,uDAAA,gBAAqD;IACrDA,wDAAA,IAAAM,gEAAA,uBAA8F;IAC9FN,wDAAA,IAAAO,gEAAA,uBAAgG;IAClGP,0DAAA,EAAiB;;;;;;IAFHA,uDAAA,GAAkD;IAAlDA,wDAAA,UAAAU,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,QAAA,aAAkD;IAClDd,uDAAA,GAA6C;IAA7CA,wDAAA,UAAAe,OAAA,GAAAJ,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAD,QAAA,QAA6C;;;;;IAwBnDd,4DAAA,yBAA0H;IAC7GA,oDAAA,kCAAiB;IAAAA,0DAAA,EAAY;IACxCA,uDAAA,eAAsD;IACxDA,0DAAA,EAAiB;;;;;IAGjBA,qEAAA,GAAmE;IACjEA,4DAAA,yBAAoE;IACvDA,oDAAA,iCAAqB;IAAAA,0DAAA,EAAY;IAC5CA,uDAAA,gBAA8D;IAChEA,0DAAA,EAAiB;IAEnBA,mEAAA,EAAe;;;;;IAGfA,4DAAA,yBAAwH;IAC3GA,oDAAA,oBAAa;IAAAA,0DAAA,EAAY;IACpCA,uDAAA,mBAAsE;IACxEA,0DAAA,EAAiB;;;;;;IAlCnBA,4DAAA,cAAuG;IAExFA,oDAAA,sBAAe;IAAAA,0DAAA,EAAY;IACtCA,4DAAA,qBAAqF;IAA3CA,wDAAA,6BAAAmB,gFAAA;MAAA,MAAAC,WAAA,GAAApB,2DAAA,CAAAsB,IAAA;MAAA,MAAAC,KAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,OAAA,GAAAzB,2DAAA;MAAA,OAAmBA,yDAAA,CAAAyB,OAAA,CAAAG,mBAAA,CAAAL,KAAA,CAAsB;IAAA,EAAC;IAClFvB,4DAAA,qBAA0B;IAAAA,oDAAA,iBAAK;IAAAA,0DAAA,EAAa;IAC5CA,4DAAA,qBAAyB;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAa;IAC1CA,4DAAA,qBAA2B;IAAAA,oDAAA,wBAAM;IAAAA,0DAAA,EAAa;IAIlDA,4DAAA,0BAAiE;IACpDA,oDAAA,wBAAgB;IAAAA,0DAAA,EAAY;IACvCA,uDAAA,gBAAwC;IAC1CA,0DAAA,EAAiB;IAGjBA,wDAAA,KAAA6B,2DAAA,6BAGiB;IAGjB7B,wDAAA,KAAA8B,yDAAA,0BAMe;IAGf9B,wDAAA,KAAA+B,2DAAA,6BAGiB;IAEjB/B,4DAAA,kBAAgE;IAA3BA,wDAAA,mBAAAgC,mEAAA;MAAA,MAAAZ,WAAA,GAAApB,2DAAA,CAAAsB,IAAA;MAAA,MAAAC,KAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAS,OAAA,GAAAjC,2DAAA;MAAA,OAASA,yDAAA,CAAAiC,OAAA,CAAAC,aAAA,CAAAX,KAAA,CAAgB;IAAA,EAAC;IAC7DvB,4DAAA,gBAAU;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;;;;;;;;IArC+BA,wDAAA,kBAAAuB,KAAA,CAAmB;IAgBVvB,uDAAA,IAAmD;IAAnDA,wDAAA,WAAAe,OAAA,GAAAoB,WAAA,CAAAtB,GAAA,kCAAAE,OAAA,CAAAqB,KAAA,cAAmD;IAMzGpC,uDAAA,GAAkD;IAAlDA,wDAAA,WAAAqC,OAAA,GAAAF,WAAA,CAAAtB,GAAA,kCAAAwB,OAAA,CAAAD,KAAA,aAAkD;IASCpC,uDAAA,GAAoD;IAApDA,wDAAA,WAAAsC,OAAA,GAAAH,WAAA,CAAAtB,GAAA,kCAAAyB,OAAA,CAAAF,KAAA,eAAoD;;;;;IAkB1HpC,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,GAAqD;IAAAA,0DAAA,EAAO;;;;IAA5DA,uDAAA,GAAqD;IAArDA,+DAAA,CAAAwC,MAAA,CAAAC,UAAA,gDAAqD;;;AA+IxF,MAAOC,yBAAyB;EAMpCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAP,UAAU,GAAG,KAAK;IAClB,KAAAQ,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAG,KAAK;EAQd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,MAAMC,EAAE,GAAGD,MAAM,CAAC1C,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAI2C,EAAE,EAAE;QACN,IAAI,CAACP,QAAQ,GAAGQ,MAAM,CAACD,EAAE,CAAC;QAC1B,IAAI,CAACf,UAAU,GAAG,IAAI;QACtB,IAAI,CAACiB,UAAU,CAAC,IAAI,CAACT,QAAQ,CAAC;;IAElC,CAAC,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACxC,UAAU,GAAG,IAAI,CAACgC,EAAE,CAACe,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAE7D,sDAAU,CAAC8D,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,EAAE/D,sDAAU,CAAC8D,QAAQ,CAAC;MACtCE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAChE,sDAAU,CAAC8D,QAAQ,EAAE9D,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvDC,MAAM,EAAE,CAAC,EAAE,EAAElE,sDAAU,CAAC8D,QAAQ,CAAC;MACjCK,UAAU,EAAE,CAAC,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC;QAAE/B,KAAK,EAAE,IAAI;QAAEgC,QAAQ,EAAE;MAAK,CAAE,EAAE,CAACrE,sDAAU,CAAC8D,QAAQ,EAAE9D,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClFK,QAAQ,EAAE,IAAI,CAACzB,EAAE,CAAC0B,KAAK,CAAC,EAAE;KAC3B,CAAC;IAEF;IACA,IAAI,IAAI,CAAC1D,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEuB,KAAK,EAAE;MAC5C,IAAI,CAACxB,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE0D,OAAO,EAAE;;EAE1C;EAEAb,UAAUA,CAACF,EAAU;IACnB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB;IACA,MAAMsB,UAAU,GAAW;MACzBhB,EAAE,EAAEA,EAAE;MACNI,KAAK,EAAE,2BAA2B;MAClCE,WAAW,EAAE,kFAAkF;MAC/FK,IAAI,EAAE,KAAK;MACXJ,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,UAAU;MAClBQ,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAElB,EAAE,EAAE,CAAC;QAAEmB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,yBAAyB;QAAEC,IAAI,EAAE;MAAW,CAAE;MACxGT,QAAQ,EAAE,CACR;QACEb,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,sBAAsB;QAC7BmB,WAAW,EAAE,OAAO;QACpBhB,KAAK,EAAE,EAAE;QACTiB,OAAO,EAAExB,EAAE;QACXyB,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,uBAAuB;QAC9BmB,WAAW,EAAE,MAAM;QACnBK,aAAa,EAAE,EAAE;QACjBJ,OAAO,EAAExB,EAAE;QACXyB,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDE,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTpB,UAAU,EAAE;KACb;IACD,IAAI,CAACtD,UAAU,CAAC2E,UAAU,CAACf,UAAU,CAAC;IACtCA,UAAU,CAACH,QAAQ,EAAEmB,OAAO,CAAEC,OAAO,IAAI;MACvC,IAAI,CAACC,UAAU,CAACD,OAAO,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAACE,gBAAgB,EAAE;IACvB,IAAI,CAACzC,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;;;EAkBF;;EAEA,IAAImB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACzD,UAAU,CAACC,GAAG,CAAC,UAAU,CAAc;EACrD;EAEA6E,UAAUA,CAACD,OAAiB;IAC1B,IAAIG,YAAuB;IAC3B,IAAIH,OAAO,EAAE;MACX,IAAIA,OAAO,CAACV,WAAW,KAAK,OAAO,EAAE;QACnCa,YAAY,GAAG,IAAI,CAAChD,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAACiC,OAAO,CAACjC,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC6B,OAAO,CAAC7B,KAAK,EAAE7D,sDAAU,CAAC8D,QAAQ,CAAC;UAC3CkB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAEhF,sDAAU,CAAC8D,QAAQ,CAAC;UACvDE,KAAK,EAAE,CAAE0B,OAAiB,CAAC1B,KAAK,EAAE,CAAChE,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACtDgB,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM,IAAIS,OAAO,CAACV,WAAW,KAAK,MAAM,EAAE;QACzCa,YAAY,GAAG,IAAI,CAAChD,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAACiC,OAAO,CAACjC,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC6B,OAAO,CAAC7B,KAAK,EAAE7D,sDAAU,CAAC8D,QAAQ,CAAC;UAC3CkB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAEhF,sDAAU,CAAC8D,QAAQ,CAAC;UACvDuB,aAAa,EAAE,CAAEK,OAAgB,CAACL,aAAa,EAAE,CAACrF,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,EAAEjE,sDAAU,CAAC8F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UAC1Fb,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM,IAAIS,OAAO,CAACV,WAAW,KAAK,QAAQ,EAAE;QAC3Ca,YAAY,GAAG,IAAI,CAAChD,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAACiC,OAAO,CAACjC,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC6B,OAAO,CAAC7B,KAAK,EAAE7D,sDAAU,CAAC8D,QAAQ,CAAC;UAC3CkB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAEhF,sDAAU,CAAC8D,QAAQ,CAAC;UACvDiC,YAAY,EAAE,CAAEL,OAAkB,CAACK,YAAY,CAAC;UAChDd,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM;QACLY,YAAY,GAAG,IAAI,CAAChD,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAACiC,OAAO,CAACjC,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC6B,OAAO,CAAC7B,KAAK,EAAE7D,sDAAU,CAAC8D,QAAQ,CAAC;UAC3CkB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAEhF,sDAAU,CAAC8D,QAAQ,CAAC;UACvDmB,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;;KAEL,MAAM;MACLY,YAAY,GAAG,IAAI,CAAChD,EAAE,CAACe,KAAK,CAAC;QAC3BH,EAAE,EAAE,CAAC,CAAC,CAAC;QACPI,KAAK,EAAE,CAAC,EAAE,EAAE7D,sDAAU,CAAC8D,QAAQ,CAAC;QAChCkB,WAAW,EAAE,CAAC,OAAO,EAAEhF,sDAAU,CAAC8D,QAAQ,CAAC;QAC3CE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAChE,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClCoB,aAAa,EAAE,CAAC,IAAI,EAAE,CAACrF,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,EAAEjE,sDAAU,CAAC8F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/DC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBd,OAAO,EAAE,CAAC,IAAI,CAAC/B,QAAQ;OACxB,CAAC;;IAEJ,IAAI,CAACoB,QAAQ,CAAC0B,IAAI,CAACH,YAAY,CAAC;EAClC;EAEA1D,aAAaA,CAACV,KAAa;IACzB,IAAI,CAAC6C,QAAQ,CAAC2B,QAAQ,CAACxE,KAAK,CAAC;EAC/B;EAEAI,mBAAmBA,CAACJ,KAAa;IAC/B,MAAMoE,YAAY,GAAG,IAAI,CAACvB,QAAQ,CAAC4B,EAAE,CAACzE,KAAK,CAAc;IACzD,MAAM0E,IAAI,GAAGN,YAAY,CAAC/E,GAAG,CAAC,aAAa,CAAC,EAAEuB,KAAK;IAEnD;IACAwD,YAAY,CAAC/E,GAAG,CAAC,OAAO,CAAC,EAAEsF,eAAe,EAAE;IAC5CP,YAAY,CAAC/E,GAAG,CAAC,OAAO,CAAC,EAAEuF,sBAAsB,EAAE;IACnDR,YAAY,CAAC/E,GAAG,CAAC,eAAe,CAAC,EAAEsF,eAAe,EAAE;IACpDP,YAAY,CAAC/E,GAAG,CAAC,eAAe,CAAC,EAAEuF,sBAAsB,EAAE;IAC3DR,YAAY,CAAC/E,GAAG,CAAC,cAAc,CAAC,EAAEsF,eAAe,EAAE;IACnDP,YAAY,CAAC/E,GAAG,CAAC,cAAc,CAAC,EAAEuF,sBAAsB,EAAE;IAE1D,IAAIF,IAAI,KAAK,OAAO,EAAE;MACpBN,YAAY,CAAC/E,GAAG,CAAC,OAAO,CAAC,EAAEwF,aAAa,CAAC,CAACtG,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9D,MAAM,IAAIkC,IAAI,KAAK,MAAM,EAAE;MAC1BN,YAAY,CAAC/E,GAAG,CAAC,eAAe,CAAC,EAAEwF,aAAa,CAAC,CAACtG,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,EAAEjE,sDAAU,CAAC8F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;IAE5F;EACF;;EAEAF,gBAAgBA,CAAA;IACd,MAAMW,iBAAiB,GAAG,IAAI,CAAC1F,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;IAC3D,MAAM0F,WAAW,GAAG,IAAI,CAAC3F,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC;IAE/C,IAAIyF,iBAAiB,EAAElE,KAAK,EAAE;MAC5BmE,WAAW,EAAEhC,OAAO,EAAE;MACtBgC,WAAW,EAAEC,QAAQ,CAAC,CAAC,CAAC;KACzB,MAAM;MACLD,WAAW,EAAEE,MAAM,EAAE;MACrBF,WAAW,EAAEC,QAAQ,CAAC,IAAI,CAAC,EAAC;;;IAE9BD,WAAW,EAAEH,sBAAsB,EAAE;EACvC;EAEAM,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC9F,UAAU,CAAC+F,KAAK,EAAE;MACzB,IAAI,CAACzD,SAAS,GAAG,IAAI;MACrB,MAAM0D,UAAU,GAAG,IAAI,CAAChG,UAAU,CAACwB,KAAK;MACxC;MACA,IAAIwE,UAAU,CAAC1C,UAAU,EAAE;QACzB0C,UAAU,CAACzC,IAAI,GAAG,CAAC;;MAGrB,IAAI,IAAI,CAAC1B,UAAU,IAAI,IAAI,CAACQ,QAAQ,EAAE;QACpC;QACA,IAAI,CAACF,aAAa,CAAC8D,aAAa,CAAC,IAAI,CAAC5D,QAAQ,EAAE2D,UAAU,CAAC,CAACtD,SAAS,CAAC;UACpEwD,IAAI,EAAGC,GAAG,IAAI;YACZ,IAAI,CAAC/D,QAAQ,CAACgE,IAAI,CAAC,gCAAgC,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAClF,IAAI,CAAC/D,SAAS,GAAG,KAAK;YACtB,IAAI,CAACJ,MAAM,CAACoE,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACjE,QAAQ,CAAC,CAAC;UACnD,CAAC;UACDkE,KAAK,EAAGC,GAAG,IAAI;YACb,IAAI,CAACpE,QAAQ,CAACgE,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAC3FI,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;YAClB,IAAI,CAAClE,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;OACH,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACF,QAAQ,CAACgE,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACrF,IAAI,CAAC/D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACoE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;;;;;;;;;;;;;;;KAeH,MAAM;MACL,IAAI,CAAClE,QAAQ,CAACgE,IAAI,CAAC,0CAA0C,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC5F,IAAI,CAACrG,UAAU,CAAC0G,gBAAgB,EAAE,EAAC;;EAEvC;;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACzE,MAAM,CAACoE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;;;uBA9PWxE,yBAAyB,EAAA1C,+DAAA,CAAAyH,uDAAA,GAAAzH,+DAAA,CAAA2H,2DAAA,GAAA3H,+DAAA,CAAA2H,mDAAA,GAAA3H,+DAAA,CAAA8H,wEAAA,GAAA9H,+DAAA,CAAAgI,oEAAA;IAAA;EAAA;;;YAAzBtF,yBAAyB;MAAAwF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtPlCxI,4DAAA,aAAmC;UAGbA,oDAAA,GAAiE;UAAAA,0DAAA,EAAiB;UAEpGA,4DAAA,uBAAkB;UACeA,wDAAA,sBAAA0I,4DAAA;YAAA,OAAYD,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UAEpD1G,4DAAA,wBAAwD;UAC3CA,oDAAA,qBAAc;UAAAA,0DAAA,EAAY;UACrCA,uDAAA,gBAAwC;UACxCA,wDAAA,KAAA2I,+CAAA,uBAAgG;UAClG3I,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,mBAAW;UAAAA,0DAAA,EAAY;UAClCA,uDAAA,mBAAqE;UACrEA,wDAAA,KAAA4I,+CAAA,uBAA6G;UAC/G5I,0DAAA,EAAiB;UAEjBA,4DAAA,cAAwB;UAETA,oDAAA,4BAAe;UAAAA,0DAAA,EAAY;UACtCA,uDAAA,gBAAsD;UACtDA,wDAAA,KAAA6I,+CAAA,uBAAiG;UACjG7I,wDAAA,KAAA8I,+CAAA,uBAAmG;UACrG9I,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,cAAM;UAAAA,0DAAA,EAAY;UAC7BA,4DAAA,sBAAqC;UACNA,oDAAA,qBAAQ;UAAAA,0DAAA,EAAa;UAClDA,4DAAA,sBAAkC;UAAAA,oDAAA,0BAAa;UAAAA,0DAAA,EAAa;UAC5DA,4DAAA,sBAA2B;UAAAA,oDAAA,mBAAM;UAAAA,0DAAA,EAAa;UAEhDA,wDAAA,KAAA+I,+CAAA,uBAAkG;UACpG/I,0DAAA,EAAiB;UAGnBA,4DAAA,wBAAyE;UAA9BA,wDAAA,oBAAAgJ,mEAAA;YAAA,OAAUP,GAAA,CAAA9C,gBAAA,EAAkB;UAAA,EAAC;UAAC3F,oDAAA,qBAAa;UAAAA,0DAAA,EAAe;UAErGA,wDAAA,KAAAiJ,oDAAA,6BAKiB;UAGjBjJ,4DAAA,oBAAgC;UACdA,oDAAA,yBAAiB;UAAAA,0DAAA,EAAiB;UAClDA,4DAAA,wBAAkB;UAEdA,wDAAA,KAAAkJ,yCAAA,mBAuCM;UACRlJ,0DAAA,EAAM;UACNA,4DAAA,kBAAgE;UAAvBA,wDAAA,mBAAAmJ,4DAAA;YAAA,OAASV,GAAA,CAAA/C,UAAA,EAAY;UAAA,EAAC;UAC7D1F,4DAAA,gBAAU;UAAAA,oDAAA,WAAG;UAAAA,0DAAA,EAAW;UAACA,oDAAA,4BAC3B;UAAAA,0DAAA,EAAS;UAIbA,4DAAA,eAA0B;UAEtBA,wDAAA,KAAAoJ,iDAAA,0BAA2D;UAC3DpJ,wDAAA,KAAAqJ,0CAAA,kBAAqF;UACvFrJ,0DAAA,EAAS;UACTA,4DAAA,kBAA2E;UAArBA,wDAAA,mBAAAsJ,4DAAA;YAAA,OAASb,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC;UAACvH,oDAAA,eAAO;UAAAA,0DAAA,EAAS;;;;;;;;;UAtG/EA,uDAAA,GAAiE;UAAjEA,+DAAA,CAAAyI,GAAA,CAAAhG,UAAA,uDAAiE;UAG3EzC,uDAAA,GAAwB;UAAxBA,wDAAA,cAAAyI,GAAA,CAAA7H,UAAA,CAAwB;UAKdZ,uDAAA,GAAmD;UAAnDA,wDAAA,UAAAqC,OAAA,GAAAoG,GAAA,CAAA7H,UAAA,CAAAC,GAAA,4BAAAwB,OAAA,CAAAvB,QAAA,aAAmD;UAMnDd,uDAAA,GAAyD;UAAzDA,wDAAA,UAAAsC,OAAA,GAAAmG,GAAA,CAAA7H,UAAA,CAAAC,GAAA,kCAAAyB,OAAA,CAAAxB,QAAA,aAAyD;UAOvDd,uDAAA,GAAmD;UAAnDA,wDAAA,UAAAuJ,OAAA,GAAAd,GAAA,CAAA7H,UAAA,CAAAC,GAAA,4BAAA0I,OAAA,CAAAzI,QAAA,aAAmD;UACnDd,uDAAA,GAA8C;UAA9CA,wDAAA,UAAAwJ,OAAA,GAAAf,GAAA,CAAA7H,UAAA,CAAAC,GAAA,4BAAA2I,OAAA,CAAA1I,QAAA,QAA8C;UAU9Cd,uDAAA,IAAoD;UAApDA,wDAAA,UAAAyJ,OAAA,GAAAhB,GAAA,CAAA7H,UAAA,CAAAC,GAAA,6BAAA4I,OAAA,CAAA3I,QAAA,aAAoD;UAMXd,uDAAA,GAA0C;UAA1CA,wDAAA,YAAA0J,OAAA,GAAAjB,GAAA,CAAA7H,UAAA,CAAAC,GAAA,iCAAA6I,OAAA,CAAAtH,KAAA,EAA0C;UAYpEpC,uDAAA,GAAsB;UAAtBA,wDAAA,YAAAyI,GAAA,CAAApE,QAAA,CAAAsF,QAAA,CAAsB;UAgDK3J,uDAAA,GAA4C;UAA5CA,wDAAA,aAAAyI,GAAA,CAAA7H,UAAA,CAAAgJ,OAAA,IAAAnB,GAAA,CAAAvF,SAAA,CAA4C;UACtElD,uDAAA,GAAe;UAAfA,wDAAA,SAAAyI,GAAA,CAAAvF,SAAA,CAAe;UACpClD,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAyI,GAAA,CAAAvF,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IChF3BlD,4DAAA,cAAuD;IAE7CA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAO;IACjCA,4DAAA,WAAM;IAAAA,oDAAA,GAAyB;IAAAA,0DAAA,EAAO;IAExCA,uDAAA,2BAAqF;IACvFA,0DAAA,EAAM;;;;IAHIA,uDAAA,GAAyB;IAAzBA,gEAAA,KAAA8J,MAAA,CAAAC,MAAA,CAAAC,WAAA,MAAyB;IAEIhK,uDAAA,GAA4B;IAA5BA,wDAAA,UAAA8J,MAAA,CAAAC,MAAA,CAAAC,WAAA,CAA4B;;;;;IAoCvDhK,4DAAA,eAAsC;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAW;;;;;IAC7DA,4DAAA,eAA8D;IAAAA,oDAAA,GAAyC;IAAAA,0DAAA,EAAW;;;;;IAApDA,uDAAA,GAAyC;IAAzCA,+DAAA,CAAAiK,OAAA,CAAAC,cAAA,CAAAC,UAAA,CAAApF,WAAA,EAAyC;;;;;IACvG/E,4DAAA,eAAuC;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAW;;;;;IAKtDA,4DAAA,eAAqD;IAAAA,oDAAA,GAA2B;IAAAA,0DAAA,EAAO;;;;IAAlCA,uDAAA,GAA2B;IAA3BA,gEAAA,KAAAmK,UAAA,CAAApG,KAAA,aAA2B;;;;;;IAElF/D,4DAAA,iBAEsE;IAA9DA,wDAAA,mBAAAoK,wEAAA;MAAApK,2DAAA,CAAAqK,IAAA;MAAA,MAAAF,UAAA,GAAAnK,2DAAA,GAAAsK,SAAA;MAAA,MAAAC,OAAA,GAAAvK,2DAAA;MAAA,OAASA,yDAAA,CAAAuK,OAAA,CAAAC,kBAAA,CAAAL,UAAA,CAAA3G,EAAA,EAAA2G,UAAA,CAAApF,WAAA,CAAmD;IAAA,EAAC;IACnE/E,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IADPA,uDAAA,GACF;IADEA,gEAAA,MAAAmK,UAAA,CAAAlF,WAAA,+BACF;;;;;;;;;;;;IApBFjF,4DAAA,cAAiF;IAO7EA,wDAAA,IAAAyK,gDAAA,uBAA6D;IAC7DzK,wDAAA,IAAA0K,gDAAA,uBAAkH;IAClH1K,wDAAA,IAAA2K,gDAAA,uBAAsD;IACxD3K,0DAAA,EAAM;IACNA,4DAAA,cAA6B;IACvBA,oDAAA,GAAmB;IAAAA,0DAAA,EAAK;IAC5BA,4DAAA,YAA+B;IAAAA,oDAAA,GAAyB;IAAAA,0DAAA,EAAI;IAC5DA,wDAAA,KAAA4K,6CAAA,mBAAuF;IACzF5K,0DAAA,EAAM;IACNA,wDAAA,KAAA6K,+CAAA,qBAIS;IACX7K,0DAAA,EAAM;;;;;IAnBCA,uDAAA,GAIE;IAJFA,wDAAA,YAAAA,6DAAA,IAAA+K,GAAA,EAAAZ,UAAA,CAAAlF,WAAA,EAAAkF,UAAA,CAAAjF,WAAA,KAAAiF,UAAA,CAAAlF,WAAA,GAAAkF,UAAA,CAAAjF,WAAA,EAIE;IACMlF,uDAAA,GAAyB;IAAzBA,wDAAA,SAAAmK,UAAA,CAAAlF,WAAA,CAAyB;IACzBjF,uDAAA,GAAiD;IAAjDA,wDAAA,UAAAmK,UAAA,CAAAlF,WAAA,IAAAkF,UAAA,CAAAjF,WAAA,CAAiD;IACjDlF,uDAAA,GAA0B;IAA1BA,wDAAA,UAAAmK,UAAA,CAAAjF,WAAA,CAA0B;IAGjClF,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAmK,UAAA,CAAAvG,KAAA,CAAmB;IACQ5D,uDAAA,GAAyB;IAAzBA,+DAAA,CAAAmK,UAAA,CAAArG,WAAA,CAAyB;IACjD9D,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAmK,UAAA,CAAApG,KAAA,CAAmB;IAGnB/D,uDAAA,GAA6C;IAA7CA,wDAAA,SAAAgL,MAAA,CAAAjB,MAAA,CAAAkB,SAAA,IAAAd,UAAA,CAAAjF,WAAA,CAA6C;;;;;;;;IAiC9DlF,4DAAA,cAAsD;IAC1BA,oDAAA,GAAwD;;IAAAA,0DAAA,EAAO;;;;IAA/DA,uDAAA,GAAwD;IAAxDA,+DAAA,CAAAA,yDAAA,OAAAA,6DAAA,IAAAoL,GAAA,EAAAC,MAAA,CAAAtB,MAAA,CAAA5F,IAAA,GAAwD;;;;;IAEpFnE,4DAAA,cAAqD;IAEfA,oDAAA,oBAAa;IAAAA,0DAAA,EAAW;;;;;;IAK5DA,4DAAA,iBAE4C;IAApCA,wDAAA,mBAAAsL,iEAAA;MAAAtL,2DAAA,CAAAuL,IAAA;MAAA,MAAAC,OAAA,GAAAxL,2DAAA;MAAA,OAASA,yDAAA,CAAAwL,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAzB,MAAA,CAAAvG,EAAA,CAAyB;IAAA,EAAC;IACzCxD,oDAAA,2BACF;IAAAA,0DAAA,EAAS;;;;;;;;IACTA,4DAAA,iBAE+C;IACnCA,oDAAA,kBAAW;IAAAA,0DAAA,EAAW;IAChCA,oDAAA,yBACF;IAAAA,0DAAA,EAAS;;;;IAHDA,wDAAA,eAAAA,6DAAA,IAAA0L,GAAA,EAAA/K,MAAA,CAAAoJ,MAAA,CAAAvG,EAAA,EAAsC;;;;;;IAI9CxD,4DAAA,iBAEuC;IAA/BA,wDAAA,mBAAA2L,iEAAA;MAAA3L,2DAAA,CAAA4L,IAAA;MAAA,MAAAC,OAAA,GAAA7L,2DAAA;MAAA,OAAAA,yDAAA,CAAA6L,OAAA,CAAAC,gBAAA,GAA4B,CAAC;IAAA,EAAC;IACpC9L,oDAAA,2BACF;IAAAA,0DAAA,EAAS;;;;;IACTA,4DAAA,2BAAkE;IAC1BA,oDAAA,wBAAY;IAAAA,0DAAA,EAAW;;;AA4YzE,MAAO+L,qBAAqB;EAMhCpJ,YACUE,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BiJ,WAAwB,EACxBhJ,QAAqB;IAJrB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAiJ,WAAW,GAAXA,WAAW;IACX,KAAAhJ,QAAQ,GAARA,QAAQ;IAPlB,KAAA8I,gBAAgB,GAAG,CAAC;EAQjB;EAEH3I,QAAQA,CAAA;IACN,IAAI,CAACN,KAAK,CAACQ,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,IAAI,CAACN,QAAQ,GAAGQ,MAAM,CAACF,MAAM,CAAC1C,GAAG,CAAC,IAAI,CAAC,CAAC;MACxC,IAAI,CAACoL,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACD,WAAW,CAACE,YAAY,CAAC5I,SAAS,CAAE6I,IAAI,IAAI;MAC/C,IAAI,CAACC,WAAW,GAAGD,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAF,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAClC,MAAM,GAAG;MACZvG,EAAE,EAAE,IAAI,CAACP,QAAQ;MACjBW,KAAK,EAAE,oBAAoB;MAC3BE,WAAW,EACT,+MAA+M;MACjNK,IAAI,EAAE,KAAK;MACXJ,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,UAAU;MAClBQ,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QACTlB,EAAE,EAAE,CAAC;QACLmB,GAAG,EAAE,QAAQ;QACbC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,WAAW;QACjBuH,GAAG,EAAE;OACN;MACDhI,QAAQ,EAAE,CACR;QACEb,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,sBAAsB;QAC7BE,WAAW,EAAE,yCAAyC;QACtDiB,WAAW,EAAE,OAAO;QACpBhB,KAAK,EAAE,EAAE;QACTkB,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE,CAAC;QACRH,OAAO,EAAE,IAAI,CAAC/B;OACf,EACD;QACEO,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,qBAAqB;QAC5BE,WAAW,EAAE,6CAA6C;QAC1DiB,WAAW,EAAE,OAAO;QACpBhB,KAAK,EAAE,EAAE;QACTkB,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE,CAAC;QACRH,OAAO,EAAE,IAAI,CAAC/B;OACf,EACD;QACEO,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,uBAAuB;QAC9BE,WAAW,EAAE,wCAAwC;QACrDiB,WAAW,EAAE,MAAM;QACnBE,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE,CAAC;QACRH,OAAO,EAAE,IAAI,CAAC/B;OACf,EACD;QACEO,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,gBAAgB;QACvBE,WAAW,EAAE,gCAAgC;QAC7CiB,WAAW,EAAE,OAAO;QACpBhB,KAAK,EAAE,EAAE;QACTkB,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClBC,KAAK,EAAE,CAAC;QACRH,OAAO,EAAE,IAAI,CAAC/B;OACf,EACD;QACEO,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,oBAAoB;QAC3BE,WAAW,EAAE,uBAAuB;QACpCiB,WAAW,EAAE,QAAQ;QACrBE,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClBC,KAAK,EAAE,CAAC;QACRH,OAAO,EAAE,IAAI,CAAC/B;OACf,CACF;MACDoC,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTpB,UAAU,EAAE,KAAK;MACjB+G,SAAS,EAAE,IAAI;MACfjB,WAAW,EAAE,EAAE,CAAE;KAClB;IAED;IACA;;;;;;;;;;;;;;;EAeF;;EAEAsC,aAAaA,CAACrI,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,cAAc;MACvB,KAAK,eAAe;QAClB,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,YAAY;MACrB;QACE,OAAO,aAAa;;EAE1B;EAEAiG,cAAcA,CAAChE,IAAY;IACzB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,aAAa;MACtB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,QAAQ;QACX,OAAO,aAAa;MACtB;QACE,OAAO,MAAM;;EAEnB;EAEAsE,kBAAkBA,CAAC+B,SAAiB,EAAExH,WAAmB;IACvD,IAAI,CAAC,IAAI,CAACgF,MAAM,CAACkB,SAAS,EAAE;MAC1B,IAAI,CAACjI,QAAQ,CAACgE,IAAI,CAAC,oDAAoD,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACtG;;IAGF,MAAMxB,OAAO,GAAG,IAAI,CAACsE,MAAM,CAAC1F,QAAQ,EAAEmI,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjJ,EAAE,KAAK+I,SAAS,CAAC;IACrE,IAAI,CAAC9G,OAAO,EAAEP,WAAW,EAAE;MACzB,IAAI,CAAClC,QAAQ,CAACgE,IAAI,CAAC,uCAAuC,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzF;;IAGF,QAAQlC,WAAW;MACjB,KAAK,MAAM;QACT,IAAI,CAACjC,MAAM,CAACoE,QAAQ,CAAC,CAAC,OAAO,EAAEqF,SAAS,CAAC,CAAC;QAC1C;MACF,KAAK,OAAO;QACV,IAAI,CAACzJ,MAAM,CAACoE,QAAQ,CAAC,CAAC,QAAQ,EAAEqF,SAAS,CAAC,CAAC,EAAC;QAC5C;MACF,KAAK,QAAQ;QACX,IAAI,CAACzJ,MAAM,CAACoE,QAAQ,CAAC,CAAC,SAAS,EAAEqF,SAAS,CAAC,CAAC,EAAC;QAC7C;MACF;QACE,IAAI,CAACvJ,QAAQ,CAACgE,IAAI,CAAC,qCAAqC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;EAE7F;EAEAwE,cAAcA,CAACxI,QAAgB;IAC7B,IAAI,CAAC,IAAI,CAACmJ,WAAW,EAAE;MACrB,IAAI,CAACpJ,QAAQ,CAACgE,IAAI,CAAC,6CAA6C,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC/F,IAAI,CAACnE,MAAM,CAACoE,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAClE,QAAQ,CAACgE,IAAI,CAAC,iDAAiD,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IACnG,IAAI,CAAC8C,MAAM,CAACkB,SAAS,GAAG,IAAI,EAAC;IAC7B,IAAI,CAAClB,MAAM,CAACC,WAAW,GAAG,CAAC;EAC7B;;;uBAvMW+B,qBAAqB,EAAA/L,+DAAA,CAAAyH,2DAAA,GAAAzH,+DAAA,CAAAyH,mDAAA,GAAAzH,+DAAA,CAAA2H,wEAAA,GAAA3H,+DAAA,CAAA8H,oEAAA,GAAA9H,+DAAA,CAAAgI,oEAAA;IAAA;EAAA;;;YAArB+D,qBAAqB;MAAA7D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqE,+BAAAnE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjhB9BxI,4DAAA,aAAqC;UAKzBA,oDAAA,GAAkB;UAAAA,0DAAA,EAAK;UAC3BA,4DAAA,WAAuB;UAAAA,oDAAA,GAAwB;UAAAA,0DAAA,EAAI;UAEnDA,4DAAA,aAAyB;UAE0CA,oDAAA,IAAmB;UAAAA,0DAAA,EAAW;UAE/FA,4DAAA,cAAuB;UACOA,oDAAA,YAAI;UAAAA,0DAAA,EAAW;UAC3CA,4DAAA,YAAM;UAAAA,oDAAA,IAA0D;UAAAA,0DAAA,EAAO;UAEzEA,4DAAA,cAAuB;UACXA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAW;UAC7BA,4DAAA,YAAM;UAAAA,oDAAA,IAA0B;UAAAA,0DAAA,EAAO;UAI3CA,wDAAA,KAAA4M,qCAAA,iBAMM;UACR5M,0DAAA,EAAM;UAENA,4DAAA,wBAA4H;UAAlDA,wDAAA,iCAAA6M,6EAAAC,MAAA;YAAA,OAAArE,GAAA,CAAAqD,gBAAA,GAAAgB,MAAA;UAAA,EAAiD;UACzH9M,4DAAA,mBAAwB;UAEJA,oDAAA,4BAAoB;UAAAA,0DAAA,EAAiB;UACrDA,4DAAA,wBAAkB;UACYA,oDAAA,IAAwB;UAAAA,0DAAA,EAAI;UAExDA,4DAAA,eAA+B;UACzBA,oDAAA,gCAAwB;UAAAA,0DAAA,EAAK;UACjCA,4DAAA,UAAI;UACYA,oDAAA,oBAAY;UAAAA,0DAAA,EAAW;UAACA,oDAAA,2CAAkC;UAAAA,0DAAA,EAAK;UAC7EA,4DAAA,UAAI;UAAUA,oDAAA,oBAAY;UAAAA,0DAAA,EAAW;UAACA,oDAAA,mDAAqC;UAAAA,0DAAA,EAAK;UAChFA,4DAAA,UAAI;UAAUA,oDAAA,oBAAY;UAAAA,0DAAA,EAAW;UAACA,oDAAA,8CAAgC;UAAAA,0DAAA,EAAK;UAC3EA,4DAAA,UAAI;UAAUA,oDAAA,oBAAY;UAAAA,0DAAA,EAAW;UAACA,oDAAA,+CAAiC;UAAAA,0DAAA,EAAK;UAOtFA,4DAAA,mBAAyB;UAELA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAiB;UACjDA,4DAAA,wBAAkB;UAEdA,wDAAA,KAAA+M,qCAAA,oBAqBM;UACR/M,0DAAA,EAAM;UAKZA,4DAAA,mBAA2B;UAEPA,oDAAA,kCAAqB;UAAAA,0DAAA,EAAiB;UACtDA,4DAAA,wBAAkB;UAGNA,oDAAA,IAAmE;UAAAA,0DAAA,EAAO;UAElFA,4DAAA,WAAK;UACCA,oDAAA,IAA0D;UAAAA,0DAAA,EAAK;UACnEA,4DAAA,aAA0B;UAAAA,oDAAA,qLAA0I;UAAAA,0DAAA,EAAI;UAUtLA,4DAAA,eAAqB;UAGfA,wDAAA,KAAAgN,qCAAA,kBAEM;UACNhN,wDAAA,KAAAiN,qCAAA,kBAIM;UAENjN,4DAAA,eAA4B;UAC1BA,wDAAA,KAAAkN,wCAAA,qBAIS;UACTlN,wDAAA,KAAAmN,wCAAA,qBAKS;UACTnN,wDAAA,KAAAoN,wCAAA,qBAIS;UACTpN,wDAAA,KAAAqN,kDAAA,+BAEmB;UACrBrN,0DAAA,EAAM;UAENA,4DAAA,eAA4B;UAElBA,oDAAA,0BAAa;UAAAA,0DAAA,EAAO;UAC1BA,4DAAA,YAAM;UAAAA,oDAAA,IAA0B;UAAAA,0DAAA,EAAO;UAEzCA,4DAAA,eAA0B;UAClBA,oDAAA,eAAO;UAAAA,0DAAA,EAAO;UACpBA,4DAAA,YAAM;UAAAA,oDAAA,IAAmB;UAAAA,0DAAA,EAAO;UAElCA,4DAAA,eAA0B;UAClBA,oDAAA,uBAAU;UAAAA,0DAAA,EAAO;UACvBA,4DAAA,YAAM;UAAAA,oDAAA,IAA4B;UAAAA,0DAAA,EAAO;UAE3CA,4DAAA,eAA0B;UAClBA,oDAAA,cAAK;UAAAA,0DAAA,EAAO;UAClBA,4DAAA,gBAA4B;UACEA,oDAAA,aAAI;UAAAA,0DAAA,EAAW;UAC3CA,4DAAA,aAAM;UAAAA,oDAAA,KAAiB;UAAAA,0DAAA,EAAO;;;UArJlCA,uDAAA,GAAkB;UAAlBA,+DAAA,CAAAyI,GAAA,CAAAsB,MAAA,CAAAnG,KAAA,CAAkB;UACC5D,uDAAA,GAAwB;UAAxBA,+DAAA,CAAAyI,GAAA,CAAAsB,MAAA,CAAAjG,WAAA,CAAwB;UAIjC9D,uDAAA,GAAoD;UAApDA,wDAAA,CAAAyI,GAAA,CAAA6D,aAAA,CAAA7D,GAAA,CAAAsB,MAAA,CAAA9F,MAAA,qBAAoD;UAACjE,uDAAA,GAAmB;UAAnBA,+DAAA,CAAAyI,GAAA,CAAAsB,MAAA,CAAA9F,MAAA,CAAmB;UAI5EjE,uDAAA,GAA0D;UAA1DA,gEAAA,KAAAyI,GAAA,CAAAsB,MAAA,CAAAzE,IAAA,QAAAmD,GAAA,CAAAsB,MAAA,CAAA1E,eAAA,qBAA0D;UAI1DrF,uDAAA,GAA0B;UAA1BA,gEAAA,KAAAyI,GAAA,CAAAsB,MAAA,CAAAhG,KAAA,aAA0B;UAIL/D,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAyI,GAAA,CAAAsB,MAAA,CAAAkB,SAAA,CAAsB;UAShBjL,uDAAA,GAAkC;UAAlCA,wDAAA,kBAAAyI,GAAA,CAAAqD,gBAAA,CAAkC;UAKrC9L,uDAAA,GAAwB;UAAxBA,+DAAA,CAAAyI,GAAA,CAAAsB,MAAA,CAAAjG,WAAA,CAAwB;UAoBzB9D,uDAAA,IAAoB;UAApBA,wDAAA,YAAAyI,GAAA,CAAAsB,MAAA,CAAA1F,QAAA,CAAoB;UAiCrCrE,uDAAA,GAAmE;UAAnEA,gEAAA,KAAAyI,GAAA,CAAAsB,MAAA,CAAArF,SAAA,kBAAA+D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,CAAAE,MAAA,kBAAA6D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,CAAAE,MAAA,SAAA6D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,kBAAA+D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,CAAAC,GAAA,kBAAA8D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,CAAAC,GAAA,QAAmE;UAGrE3E,uDAAA,GAA0D;UAA1DA,gEAAA,KAAAyI,GAAA,CAAAsB,MAAA,CAAArF,SAAA,kBAAA+D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,CAAAE,MAAA,OAAA6D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,kBAAA+D,GAAA,CAAAsB,MAAA,CAAArF,SAAA,CAAAC,GAAA,KAA0D;UAc1C3E,uDAAA,GAAwB;UAAxBA,wDAAA,UAAAyI,GAAA,CAAAsB,MAAA,CAAA7F,UAAA,CAAwB;UAGxBlE,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAyI,GAAA,CAAAsB,MAAA,CAAA7F,UAAA,CAAuB;UAQxClE,uDAAA,GAA4C;UAA5CA,wDAAA,UAAAyI,GAAA,CAAAsB,MAAA,CAAAkB,SAAA,IAAAxC,GAAA,CAAAsB,MAAA,CAAA7F,UAAA,CAA4C;UAK5ClE,uDAAA,GAA6C;UAA7CA,wDAAA,UAAAyI,GAAA,CAAAsB,MAAA,CAAAkB,SAAA,KAAAxC,GAAA,CAAAsB,MAAA,CAAA7F,UAAA,CAA6C;UAM7ClE,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAyI,GAAA,CAAAsB,MAAA,CAAAkB,SAAA,CAAsB;UAIZjL,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAyI,GAAA,CAAAsB,MAAA,CAAAkB,SAAA,CAAsB;UAQjCjL,uDAAA,GAA0B;UAA1BA,gEAAA,KAAAyI,GAAA,CAAAsB,MAAA,CAAAhG,KAAA,aAA0B;UAI1B/D,uDAAA,GAAmB;UAAnBA,+DAAA,CAAAyI,GAAA,CAAAsB,MAAA,CAAA9F,MAAA,CAAmB;UAInBjE,uDAAA,GAA4B;UAA5BA,+DAAA,CAAAyI,GAAA,CAAAsB,MAAA,CAAA1E,eAAA,CAA4B;UAM1BrF,uDAAA,GAAiB;UAAjBA,+DAAA,CAAAyI,GAAA,CAAAsB,MAAA,CAAAzE,IAAA,CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICnH7BtF,4DAAA,eAA+C;IAAAA,oDAAA,GAAwD;;IAAAA,0DAAA,EAAO;;;;IAA/DA,uDAAA,GAAwD;IAAxDA,+DAAA,CAAAA,yDAAA,OAAAA,6DAAA,IAAA+K,GAAA,EAAAyC,SAAA,CAAArJ,IAAA,GAAwD;;;;;IACvGnE,4DAAA,mBAAsD;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAW;;;;;IA+BtEA,4DAAA,eAAqD;IAAAA,oDAAA,GAAyB;IAAAA,0DAAA,EAAO;;;;IAAhCA,uDAAA,GAAyB;IAAzBA,gEAAA,MAAAyN,WAAA,CAAA1J,KAAA,UAAyB;;;;;IAHhF/D,4DAAA,cAA8E;IAClEA,oDAAA,GAAyC;IAAAA,0DAAA,EAAW;IAC9DA,4DAAA,WAAM;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAO;IAChCA,wDAAA,IAAA0N,4DAAA,mBAAqF;IACvF1N,0DAAA,EAAM;;;;;IAHMA,uDAAA,GAAyC;IAAzCA,+DAAA,CAAA2N,MAAA,CAAAzD,cAAA,CAAAuD,WAAA,CAAA1I,WAAA,EAAyC;IAC7C/E,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAyN,WAAA,CAAA7J,KAAA,CAAmB;IAClB5D,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAyN,WAAA,CAAA1J,KAAA,CAAmB;;;;;IAE5B/D,4DAAA,YAA8E;IAAAA,oDAAA,GAAiD;IAAAA,0DAAA,EAAI;;;;IAArDA,uDAAA,GAAiD;IAAjDA,gEAAA,MAAAwN,SAAA,CAAAnJ,QAAA,CAAAuJ,MAAA,yBAAiD;;;;;IAO/H5N,4DAAA,iBAAmE;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAS;;;;;;;;IACrFA,4DAAA,iBAA4G;IAChGA,oDAAA,kBAAW;IAAAA,0DAAA,EAAW;IAChCA,oDAAA,gBACF;IAAAA,0DAAA,EAAS;;;;IAH4DA,wDAAA,eAAAA,6DAAA,IAAAoL,GAAA,EAAAoC,SAAA,CAAAhK,EAAA,EAAsC;;;;;;;;IAhDjHxD,4DAAA,mBAAqE;IAIEA,oDAAA,GAAmB;IAAAA,0DAAA,EAAW;IAE/FA,wDAAA,IAAA6N,qDAAA,mBAA8G;IAC9G7N,wDAAA,IAAA8N,yDAAA,uBAAwE;IAC1E9N,0DAAA,EAAM;IACNA,4DAAA,qBAAgB;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAiB;IACnDA,4DAAA,6BAAuC;IAAAA,oDAAA,IAAwB;IAAAA,0DAAA,EAAoB;IAGrFA,4DAAA,wBAAkB;IAGFA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,YAAM;IAAAA,oDAAA,IAA0D;IAAAA,0DAAA,EAAO;IAEzEA,4DAAA,eAAuB;IACXA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAW;IAC7BA,4DAAA,YAAM;IAAAA,oDAAA,IAAsB;IAAAA,0DAAA,EAAO;IAErCA,4DAAA,eAAuB;IACXA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAAsC;IAAAA,0DAAA,EAAO;IAErDA,4DAAA,eAAuB;IACOA,oDAAA,YAAI;IAAAA,0DAAA,EAAW;IAC3CA,4DAAA,YAAM;IAAAA,oDAAA,IAAiB;IAAAA,0DAAA,EAAO;IAIlCA,4DAAA,eAA6B;IACvBA,oDAAA,yBAAiB;IAAAA,0DAAA,EAAK;IAC1BA,wDAAA,KAAA+N,qDAAA,kBAIM;;IACN/N,wDAAA,KAAAgO,mDAAA,gBAAmI;IACrIhO,0DAAA,EAAM;IAENA,4DAAA,eAA0B;IAEtBA,oDAAA,2BACF;IAAAA,0DAAA,EAAS;IACTA,wDAAA,KAAAiO,wDAAA,qBAAqF;IACrFjO,wDAAA,KAAAkO,wDAAA,qBAGS;IACXlO,0DAAA,EAAM;;;;;IAhDQA,uDAAA,GAAoD;IAApDA,wDAAA,CAAAmO,MAAA,CAAA7B,aAAA,CAAAkB,SAAA,CAAAvJ,MAAA,qBAAoD;IAACjE,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAwN,SAAA,CAAAvJ,MAAA,CAAmB;IAE/DjE,uDAAA,GAAwB;IAAxBA,wDAAA,UAAAwN,SAAA,CAAAtJ,UAAA,CAAwB;IAClClE,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAwN,SAAA,CAAAtJ,UAAA,CAAuB;IAEpBlE,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAwN,SAAA,CAAA5J,KAAA,CAAkB;IACK5D,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAwN,SAAA,CAAA1J,WAAA,CAAwB;IAOrD9D,uDAAA,GAA0D;IAA1DA,gEAAA,KAAAwN,SAAA,CAAA9I,SAAA,kBAAA8I,SAAA,CAAA9I,SAAA,CAAAE,MAAA,OAAA4I,SAAA,CAAA9I,SAAA,kBAAA8I,SAAA,CAAA9I,SAAA,CAAAC,GAAA,KAA0D;IAI1D3E,uDAAA,GAAsB;IAAtBA,gEAAA,KAAAwN,SAAA,CAAAzJ,KAAA,SAAsB;IAItB/D,uDAAA,GAAsC;IAAtCA,gEAAA,KAAAwN,SAAA,CAAAnI,eAAA,oBAAsC;IAItCrF,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAwN,SAAA,CAAAlI,IAAA,CAAiB;IAMAtF,uDAAA,GAA8B;IAA9BA,wDAAA,YAAAA,yDAAA,SAAAwN,SAAA,CAAAnJ,QAAA,QAA8B;IAKnDrE,uDAAA,GAAmD;IAAnDA,wDAAA,SAAAwN,SAAA,CAAAnJ,QAAA,IAAAmJ,SAAA,CAAAnJ,QAAA,CAAAuJ,MAAA,KAAmD;IAIZ5N,uDAAA,GAAsC;IAAtCA,wDAAA,eAAAA,6DAAA,KAAA0L,GAAA,EAAA8B,SAAA,CAAAhK,EAAA,EAAsC;IAGvCxD,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAwN,SAAA,CAAAtJ,UAAA,CAAuB;IACtBlE,uDAAA,GAAwB;IAAxBA,wDAAA,UAAAwN,SAAA,CAAAtJ,UAAA,CAAwB;;;;;IAjD3ElE,4DAAA,cAA6E;IAC3EA,wDAAA,IAAAqO,8CAAA,yBAsDW;IACbrO,0DAAA,EAAM;;;;IAvDyBA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAA8J,MAAA,CAAAwE,eAAA,CAAkB;;;;;IA0D/CtO,4DAAA,cAAwB;IACZA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAC7BA,4DAAA,SAAI;IAAAA,oDAAA,8BAAkB;IAAAA,0DAAA,EAAK;IAC3BA,4DAAA,QAAG;IAAAA,oDAAA,0DAA8C;IAAAA,0DAAA,EAAI;;;AAsOzD,MAAOuO,mBAAmB;EAO9B5L,YACUI,aAA4B,EAC5BC,QAAqB,EACrBF,MAAc;IAFd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAF,MAAM,GAANA,MAAM;IAThB,KAAA0L,OAAO,GAAa,EAAE;IACtB,KAAAF,eAAe,GAAa,EAAE;IAC9B,KAAAG,UAAU,GAAG,EAAE;IACf,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,WAAW,GAAG,KAAK;EAMhB;EAEHxL,QAAQA,CAAA;IACN,IAAI,CAACyL,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT;IACA,IAAI,CAACJ,OAAO,GAAG,CACb;MACEhL,EAAE,EAAE,CAAC;MACLI,KAAK,EAAE,oBAAoB;MAC3BE,WAAW,EAAE,kFAAkF;MAC/FK,IAAI,EAAE,KAAK;MACXJ,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,UAAU;MAClBQ,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAElB,EAAE,EAAE,CAAC;QAAEmB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,yBAAyB;QAAEC,IAAI,EAAE;MAAW,CAAE;MACxGT,QAAQ,EAAE,CACR;QACEb,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,sBAAsB;QAC7BmB,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,qBAAqB;QAC5BmB,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,uBAAuB;QAC9BmB,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,oBAAoB;QAC3BmB,WAAW,EAAE,QAAQ;QACrBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDE,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTpB,UAAU,EAAE;KACb,EACD;MACEV,EAAE,EAAE,CAAC;MACLI,KAAK,EAAE,mBAAmB;MAC1BE,WAAW,EAAE,mFAAmF;MAChGK,IAAI,EAAE,MAAM;MACZJ,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,QAAQ;MAChBQ,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAElB,EAAE,EAAE,CAAC;QAAEmB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,QAAQ;QAAEC,KAAK,EAAE,2BAA2B;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC5GT,QAAQ,EAAE,CACR;QACEb,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,mBAAmB;QAC1BmB,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,wBAAwB;QAC/BmB,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,yBAAyB;QAChCmB,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDE,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTpB,UAAU,EAAE;KACb,EACD;MACEV,EAAE,EAAE,CAAC;MACLI,KAAK,EAAE,qBAAqB;MAC5BE,WAAW,EAAE,oFAAoF;MACjGK,IAAI,EAAE,CAAC;MACPJ,KAAK,EAAE,EAAE;MACTE,MAAM,EAAE,UAAU;MAClBQ,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAElB,EAAE,EAAE,CAAC;QAAEmB,GAAG,EAAE,SAAS;QAAEC,MAAM,EAAE,QAAQ;QAAEC,KAAK,EAAE,4BAA4B;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9GT,QAAQ,EAAE,CACR;QACEb,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,aAAa;QACpBmB,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,aAAa;QACpBmB,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE3B,EAAE,EAAE,EAAE;QACNI,KAAK,EAAE,YAAY;QACnBmB,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDE,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTpB,UAAU,EAAE;KACb,CACF;IACD,IAAI,CAAC2K,YAAY,EAAE;IAEnB;IACA;;;;;;;;;;;;EAYF;;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACP,eAAe,GAAG,IAAI,CAACE,OAAO,CAACM,MAAM,CAAE/E,MAAM,IAAI;MACpD,MAAMgF,aAAa,GACjBhF,MAAM,CAACnG,KAAK,CAACoL,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACR,UAAU,CAACO,WAAW,EAAE,CAAC,IAClE,CAACjF,MAAM,CAACjG,WAAW,IAAI,EAAE,EAAEkL,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACR,UAAU,CAACO,WAAW,EAAE,CAAC;MAClF,MAAME,YAAY,GAAG,IAAI,CAACR,WAAW,KAAK,KAAK,IAAI3E,MAAM,CAAC9F,MAAM,KAAK,IAAI,CAACyK,WAAW;MACrF,MAAMS,YAAY,GAChB,IAAI,CAACR,WAAW,KAAK,KAAK,IACzB,IAAI,CAACA,WAAW,KAAK,MAAM,IAAI5E,MAAM,CAAC7F,UAAW,IACjD,IAAI,CAACyK,WAAW,KAAK,MAAM,IAAI,CAAC5E,MAAM,CAAC7F,UAAW;MAErD,OAAO6K,aAAa,IAAIG,YAAY,IAAIC,YAAY;IACtD,CAAC,CAAC;EACJ;EAEAjF,cAAcA,CAAChE,IAAY;IACzB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,aAAa;MACtB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,QAAQ;QACX,OAAO,aAAa;MACtB;QACE,OAAO,MAAM;;EAEnB;EAEAoG,aAAaA,CAACrI,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,cAAc;MACvB,KAAK,eAAe;QAClB,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,YAAY;MACrB;QACE,OAAO,aAAa;;EAE1B;;;uBApNWsK,mBAAmB,EAAAvO,+DAAA,CAAAyH,wEAAA,GAAAzH,+DAAA,CAAA2H,oEAAA,GAAA3H,+DAAA,CAAA8H,mDAAA;IAAA;EAAA;;;YAAnByG,mBAAmB;MAAArG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8G,6BAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtU5BxI,4DAAA,aAAmC;UAE3BA,oDAAA,0BAAmB;UAAAA,0DAAA,EAAK;UAG5BA,4DAAA,aAAyB;UAEVA,oDAAA,6BAAsB;UAAAA,0DAAA,EAAY;UAC7CA,4DAAA,eAAkE;UAAlDA,wDAAA,2BAAAqP,4DAAAvC,MAAA;YAAA,OAAArE,GAAA,CAAAgG,UAAA,GAAA3B,MAAA;UAAA,EAAwB,mBAAAwC,oDAAA;YAAA,OAAU7G,GAAA,CAAAoG,YAAA,EAAc;UAAA,EAAxB;UAAxC7O,0DAAA,EAAkE;UAClEA,4DAAA,kBAAoB;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UAGvCA,4DAAA,yBAA2D;UAC9CA,oDAAA,cAAM;UAAAA,0DAAA,EAAY;UAC7BA,4DAAA,qBAAyE;UAA7DA,wDAAA,2BAAAuP,kEAAAzC,MAAA;YAAA,OAAArE,GAAA,CAAAiG,WAAA,GAAA5B,MAAA;UAAA,EAAyB,6BAAA0C,oEAAA;YAAA,OAAoB/G,GAAA,CAAAoG,YAAA,EAAc;UAAA,EAAlC;UACnC7O,4DAAA,qBAAwB;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAa;UACrDA,4DAAA,qBAA6B;UAAAA,oDAAA,qBAAQ;UAAAA,0DAAA,EAAa;UAClDA,4DAAA,sBAAkC;UAAAA,oDAAA,0BAAa;UAAAA,0DAAA,EAAa;UAC5DA,4DAAA,sBAA2B;UAAAA,oDAAA,mBAAM;UAAAA,0DAAA,EAAa;UAIlDA,4DAAA,yBAA2D;UAC9CA,oDAAA,YAAI;UAAAA,0DAAA,EAAY;UAC3BA,4DAAA,qBAAyE;UAA7DA,wDAAA,2BAAAyP,kEAAA3C,MAAA;YAAA,OAAArE,GAAA,CAAAkG,WAAA,GAAA7B,MAAA;UAAA,EAAyB,6BAAA4C,oEAAA;YAAA,OAAoBjH,GAAA,CAAAoG,YAAA,EAAc;UAAA,EAAlC;UACnC7O,4DAAA,qBAAwB;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAa;UAClDA,4DAAA,sBAAyB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAa;UAC7CA,4DAAA,sBAAyB;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAOpDA,wDAAA,KAAA2P,mCAAA,kBAwDM;UAEN3P,wDAAA,KAAA4P,2CAAA,iCAAA5P,oEAAA,CAMc;UAChBA,0DAAA,EAAM;;;;UA3FkBA,uDAAA,GAAwB;UAAxBA,wDAAA,YAAAyI,GAAA,CAAAgG,UAAA,CAAwB;UAM5BzO,uDAAA,GAAyB;UAAzBA,wDAAA,YAAAyI,GAAA,CAAAiG,WAAA,CAAyB;UAUzB1O,uDAAA,IAAyB;UAAzBA,wDAAA,YAAAyI,GAAA,CAAAkG,WAAA,CAAyB;UAUhB3O,uDAAA,GAAkC;UAAlCA,wDAAA,SAAAyI,GAAA,CAAA6F,eAAA,CAAAV,MAAA,KAAkC,aAAAkC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1CrB;AACA;AACF,CAAC;AAE7C;AACsD;AACI;AACJ;AACE;AACS;AACP;AACF;AACa;AACQ;AACvB;AAEmB;AACM;AACc;AACzC;;;AA0B9C,MAAOe,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBArBtBd,yDAAY,EACZE,uDAAW,EACXW,+DAAmB,EACnBV,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,mEAAc,EACdC,6EAAkB,EAClBC,sEAAe,EACfC,oEAAc,EACdC,iFAAoB,EACpBC,yFAAwB,EACxBC,kEAAa,EACbX,0DAAY,CAACc,QAAQ,CAAC,CACpB;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEzC,mFAAmBA;MAAA,CAAE,EAC5C;QAAEwC,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAEtO,uGAAyBA;MAAA,CAAE,EACxD;QAAEqO,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEtO,uGAAyBA;MAAA,CAAE,EAC1D;QAAEqO,IAAI,EAAE,KAAK;QAAEC,SAAS,EAAEjF,yFAAqBA;MAAA,CAAE,CAClD,CAAC;IAAA;EAAA;;;sHAGO8E,aAAa;IAAAI,YAAA,GAvBT1C,mFAAmB,EAAExC,yFAAqB,EAAErJ,uGAAyB;IAAAwO,OAAA,GAElFnB,yDAAY,EACZE,uDAAW,EACXW,+DAAmB,EACnBV,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,mEAAc,EACdC,6EAAkB,EAClBC,sEAAe,EACfC,oEAAc,EACdC,iFAAoB,EACpBC,yFAAwB,EACxBC,kEAAa,EAAAlJ,0DAAA;EAAA;AAAA,K", "sources": ["./src/app/features/courses/course-create-edit/course-create-edit.component.ts", "./src/app/features/courses/course-detail/course-detail.component.ts", "./src/app/features/courses/course-list/course-list.component.ts", "./src/app/features/courses/courses.module.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { FormBuilder, FormGroup, Validators, FormArray } from \"@angular/forms\"\nimport { ActivatedRoute, Router } from \"@angular/router\"\nimport { CourseService } from \"../../../core/services/course.service\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Course, Contenu, Quiz, Video, Resume } from \"../../../core/models/course.model\"\n\n@Component({\n  selector: \"app-course-create-edit\",\n  template: `\n    <div class=\"course-form-container\">\n      <mat-card class=\"course-form-card\">\n        <mat-card-header>\n          <mat-card-title>{{ isEditMode ? 'Modifier le cours' : 'Créer un nouveau cours' }}</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <form [formGroup]=\"courseForm\" (ngSubmit)=\"onSubmit()\">\n            <!-- Course Details -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Titre du cours</mat-label>\n              <input matInput formControlName=\"titre\">\n              <mat-error *ngIf=\"courseForm.get('titre')?.hasError('required')\">Le titre est requis</mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Description</mat-label>\n              <textarea matInput formControlName=\"description\" rows=\"4\"></textarea>\n              <mat-error *ngIf=\"courseForm.get('description')?.hasError('required')\">La description est requise</mat-error>\n            </mat-form-field>\n\n            <div class=\"row-fields\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Durée (minutes)</mat-label>\n                <input matInput type=\"number\" formControlName=\"duree\">\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('required')\">La durée est requise</mat-error>\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('min')\">La durée doit être positive</mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Niveau</mat-label>\n                <mat-select formControlName=\"niveau\">\n                  <mat-option value=\"Débutant\">Débutant</mat-option>\n                  <mat-option value=\"Intermédiaire\">Intermédiaire</mat-option>\n                  <mat-option value=\"Avancé\">Avancé</mat-option>\n                </mat-select>\n                <mat-error *ngIf=\"courseForm.get('niveau')?.hasError('required')\">Le niveau est requis</mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-checkbox formControlName=\"estGratuit\" (change)=\"togglePriceField()\">Cours gratuit</mat-checkbox>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\" *ngIf=\"!courseForm.get('estGratuit')?.value\">\n              <mat-label>Prix (€)</mat-label>\n              <input matInput type=\"number\" formControlName=\"prix\">\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('required')\">Le prix est requis</mat-error>\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('min')\">Le prix doit être positif</mat-error>\n            </mat-form-field>\n\n            <!-- Contents Section -->\n            <mat-card class=\"contents-card\">\n              <mat-card-title>Contenus du cours</mat-card-title>\n              <mat-card-content>\n                <div formArrayName=\"contenus\" class=\"content-list\">\n                  <div *ngFor=\"let content of contenus.controls; let i = index\" [formGroupName]=\"i\" class=\"content-item\">\n                    <mat-form-field appearance=\"outline\" class=\"content-type-select\">\n                      <mat-label>Type de contenu</mat-label>\n                      <mat-select formControlName=\"typeContenu\" (selectionChange)=\"onContentTypeChange(i)\">\n                        <mat-option value=\"Video\">Vidéo</mat-option>\n                        <mat-option value=\"Quiz\">Quiz</mat-option>\n                        <mat-option value=\"Resume\">Résumé</mat-option>\n                      </mat-select>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"content-title-input\">\n                      <mat-label>Titre du contenu</mat-label>\n                      <input matInput formControlName=\"titre\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Video -->\n                    <mat-form-field appearance=\"outline\" class=\"content-duration-input\" *ngIf=\"content.get('typeContenu')?.value === 'Video'\">\n                      <mat-label>Durée vidéo (min)</mat-label>\n                      <input matInput type=\"number\" formControlName=\"duree\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Quiz -->\n                    <ng-container *ngIf=\"content.get('typeContenu')?.value === 'Quiz'\">\n                      <mat-form-field appearance=\"outline\" class=\"content-quiz-threshold\">\n                        <mat-label>Seuil de réussite (%)</mat-label>\n                        <input matInput type=\"number\" formControlName=\"seuilReussite\">\n                      </mat-form-field>\n                      <!-- Add quiz questions management here if needed -->\n                    </ng-container>\n\n                    <!-- Specific fields for Resume -->\n                    <mat-form-field appearance=\"outline\" class=\"content-resume-text\" *ngIf=\"content.get('typeContenu')?.value === 'Resume'\">\n                      <mat-label>Contenu texte</mat-label>\n                      <textarea matInput formControlName=\"contenuTexte\" rows=\"3\"></textarea>\n                    </mat-form-field>\n\n                    <button mat-icon-button color=\"warn\" (click)=\"removeContenu(i)\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n                </div>\n                <button mat-raised-button color=\"accent\" (click)=\"addContenu()\">\n                  <mat-icon>add</mat-icon> Ajouter un contenu\n                </button>\n              </mat-card-content>\n            </mat-card>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"courseForm.invalid || isLoading\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">{{ isEditMode ? 'Mettre à jour' : 'Créer le cours' }}</span>\n              </button>\n              <button mat-stroked-button color=\"warn\" type=\"button\" (click)=\"onCancel()\">Annuler</button>\n            </div>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [\n    `\n    .course-form-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n      display: flex;\n      justify-content: center;\n      align-items: flex-start;\n    }\n\n    .course-form-card {\n      max-width: 800px;\n      width: 100%;\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .course-form-card mat-card-title {\n      font-size: 1.8rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: center;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .row-fields {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n    }\n\n    mat-checkbox {\n      margin-bottom: 1.5rem;\n    }\n\n    .contents-card {\n      margin-top: 2rem;\n      padding: 1.5rem;\n      background-color: #f9f9f9;\n      border: 1px solid #eee;\n    }\n\n    .contents-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: left;\n    }\n\n    .content-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1.5rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .content-item {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);\n    }\n\n    .content-type-select {\n      flex: 1 1 180px;\n    }\n\n    .content-title-input {\n      flex: 2 1 250px;\n    }\n\n    .content-duration-input, .content-quiz-threshold {\n      flex: 1 1 150px;\n    }\n\n    .content-resume-text {\n      flex: 3 1 300px;\n    }\n\n    .content-item button {\n      flex-shrink: 0;\n    }\n\n    .form-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: flex-end;\n      margin-top: 2rem;\n    }\n\n    .form-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    mat-spinner {\n      margin-right: 0.5rem;\n    }\n\n    @media (max-width: 768px) {\n      .row-fields {\n        flex-direction: column;\n      }\n      .half-width {\n        width: 100%;\n      }\n      .content-item {\n        flex-direction: column;\n        align-items: stretch;\n      }\n      .content-item mat-form-field {\n        width: 100%;\n      }\n      .form-actions {\n        flex-direction: column;\n      }\n    }\n  `,\n  ],\n})\nexport class CourseCreateEditComponent implements OnInit {\n  courseForm!: FormGroup\n  isEditMode = false\n  courseId: number | null = null\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private courseService: CourseService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.initForm()\n    this.route.paramMap.subscribe((params) => {\n      const id = params.get(\"id\")\n      if (id) {\n        this.courseId = Number(id)\n        this.isEditMode = true\n        this.loadCourse(this.courseId)\n      }\n    })\n  }\n\n  initForm(): void {\n    this.courseForm = this.fb.group({\n      titre: [\"\", Validators.required],\n      description: [\"\", Validators.required],\n      duree: [null, [Validators.required, Validators.min(1)]],\n      niveau: [\"\", Validators.required],\n      estGratuit: [false],\n      prix: [{ value: null, disabled: false }, [Validators.required, Validators.min(0)]],\n      contenus: this.fb.array([]),\n    })\n\n    // Disable price if estGratuit is true initially\n    if (this.courseForm.get(\"estGratuit\")?.value) {\n      this.courseForm.get(\"prix\")?.disable()\n    }\n  }\n\n  loadCourse(id: number): void {\n    this.isLoading = true\n    // Mock data for demonstration\n    const mockCourse: Course = {\n      id: id,\n      titre: \"React Fundamentals (Edit)\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\", email: \"<EMAIL>\", role: \"Formateur\" },\n      contenus: [\n        {\n          id: 1,\n          titre: \"Introduction à React\",\n          typeContenu: \"Video\",\n          duree: 30,\n          coursId: id,\n          estComplete: false,\n          estDebloque: true,\n          ordre: 1,\n        },\n        {\n          id: 3,\n          titre: \"Quiz - Bases de React\",\n          typeContenu: \"Quiz\",\n          seuilReussite: 70,\n          coursId: id,\n          estComplete: false,\n          estDebloque: true,\n          ordre: 3,\n        },\n      ],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n    }\n    this.courseForm.patchValue(mockCourse)\n    mockCourse.contenus?.forEach((content) => {\n      this.addContenu(content)\n    })\n    this.togglePriceField()\n    this.isLoading = false\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(id).subscribe({\n      next: (data) => {\n        this.courseForm.patchValue(data);\n        data.contenus.forEach(content => {\n          this.addContenu(content);\n        });\n        this.togglePriceField();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  get contenus(): FormArray {\n    return this.courseForm.get(\"contenus\") as FormArray\n  }\n\n  addContenu(content?: Contenu): void {\n    let contentGroup: FormGroup\n    if (content) {\n      if (content.typeContenu === \"Video\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          duree: [(content as Video).duree, [Validators.min(1)]],\n          coursId: [content.coursId],\n        })\n      } else if (content.typeContenu === \"Quiz\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          seuilReussite: [(content as Quiz).seuilReussite, [Validators.min(0), Validators.max(100)]],\n          coursId: [content.coursId],\n        })\n      } else if (content.typeContenu === \"Resume\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          contenuTexte: [(content as Resume).contenuTexte],\n          coursId: [content.coursId],\n        })\n      } else {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          coursId: [content.coursId],\n        })\n      }\n    } else {\n      contentGroup = this.fb.group({\n        id: [0], // Temp ID for new content\n        titre: [\"\", Validators.required],\n        typeContenu: [\"Video\", Validators.required], // Default to Video\n        duree: [null, [Validators.min(1)]], // For video\n        seuilReussite: [null, [Validators.min(0), Validators.max(100)]], // For quiz\n        contenuTexte: [\"\"], // For resume\n        coursId: [this.courseId],\n      })\n    }\n    this.contenus.push(contentGroup)\n  }\n\n  removeContenu(index: number): void {\n    this.contenus.removeAt(index)\n  }\n\n  onContentTypeChange(index: number): void {\n    const contentGroup = this.contenus.at(index) as FormGroup\n    const type = contentGroup.get(\"typeContenu\")?.value\n\n    // Reset and re-apply validators based on type\n    contentGroup.get(\"duree\")?.clearValidators()\n    contentGroup.get(\"duree\")?.updateValueAndValidity()\n    contentGroup.get(\"seuilReussite\")?.clearValidators()\n    contentGroup.get(\"seuilReussite\")?.updateValueAndValidity()\n    contentGroup.get(\"contenuTexte\")?.clearValidators()\n    contentGroup.get(\"contenuTexte\")?.updateValueAndValidity()\n\n    if (type === \"Video\") {\n      contentGroup.get(\"duree\")?.setValidators([Validators.min(1)])\n    } else if (type === \"Quiz\") {\n      contentGroup.get(\"seuilReussite\")?.setValidators([Validators.min(0), Validators.max(100)])\n    }\n    // No specific validators for Resume contentText, it's optional\n  }\n\n  togglePriceField(): void {\n    const estGratuitControl = this.courseForm.get(\"estGratuit\")\n    const prixControl = this.courseForm.get(\"prix\")\n\n    if (estGratuitControl?.value) {\n      prixControl?.disable()\n      prixControl?.setValue(0)\n    } else {\n      prixControl?.enable()\n      prixControl?.setValue(null) // Clear value when re-enabling\n    }\n    prixControl?.updateValueAndValidity()\n  }\n\n  onSubmit(): void {\n    if (this.courseForm.valid) {\n      this.isLoading = true\n      const courseData = this.courseForm.value\n      // Ensure prix is 0 if estGratuit is true, even if disabled field value is not picked up\n      if (courseData.estGratuit) {\n        courseData.prix = 0\n      }\n\n      if (this.isEditMode && this.courseId) {\n        // Update existing course\n        this.courseService.modifierCours(this.courseId, courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open(\"Cours mis à jour avec succès !\", \"Fermer\", { duration: 3000 })\n            this.isLoading = false\n            this.router.navigate([\"/courses\", this.courseId])\n          },\n          error: (err) => {\n            this.snackBar.open(\"Erreur lors de la mise à jour du cours.\", \"Fermer\", { duration: 3000 })\n            console.error(err)\n            this.isLoading = false\n          },\n        })\n      } else {\n        // Create new course\n        // Note: Your backend's FormateurController has AjouterCours, but it expects FormateurId in URL.\n        // You might need a dedicated POST /api/cours endpoint or adjust this.\n        // For now, simulating success.\n        this.snackBar.open(\"Cours créé avec succès (simulé) !\", \"Fermer\", { duration: 3000 })\n        this.isLoading = false\n        this.router.navigate([\"/courses\"])\n        /*\n        this.courseService.createCourse(courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open('Cours créé avec succès !', 'Fermer', { duration: 3000 });\n            this.isLoading = false;\n            this.router.navigate(['/courses']);\n          },\n          error: (err) => {\n            this.snackBar.open('Erreur lors de la création du cours.', 'Fermer', { duration: 3000 });\n            console.error(err);\n            this.isLoading = false;\n          }\n        });\n        */\n      }\n    } else {\n      this.snackBar.open(\"Veuillez remplir tous les champs requis.\", \"Fermer\", { duration: 3000 })\n      this.courseForm.markAllAsTouched() // Show validation errors\n    }\n  }\n\n  onCancel(): void {\n    this.router.navigate([\"/courses\"])\n  }\n}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { ActivatedRoute, Router } from \"@angular/router\"\nimport { CourseService } from \"../../../core/services/course.service\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Course } from \"../../../core/models/course.model\"\nimport { AuthService } from \"../../../core/services/auth.service\"\nimport { User } from \"../../../core/models/user.model\"\n\n@Component({\n  selector: \"app-course-detail\",\n  template: `\n    <div class=\"course-detail-container\">\n      <div class=\"content-wrapper\">\n        <!-- Main Content -->\n        <div class=\"main-content\">\n          <div class=\"course-header\">\n            <h1>{{ course.titre }}</h1>\n            <p class=\"description\">{{ course.description }}</p>\n\n            <div class=\"course-meta\">\n              <mat-chip-listbox>\n                <mat-chip [class]=\"getLevelColor(course.niveau || 'Débutant')\">{{ course.niveau }}</mat-chip>\n              </mat-chip-listbox>\n              <div class=\"meta-item\">\n                <mat-icon class=\"star-icon\">star</mat-icon>\n                <span>{{ course.note }} ({{ course.nombreEtudiants }} étudiants)</span>\n              </div>\n              <div class=\"meta-item\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ course.duree }} minutes</span>\n              </div>\n            </div>\n\n            <div class=\"progress-section\" *ngIf=\"course.estAchete\">\n              <div class=\"progress-label\">\n                <span>Progression du cours</span>\n                <span>{{ course.progression }}%</span>\n              </div>\n              <mat-progress-bar mode=\"determinate\" [value]=\"course.progression\"></mat-progress-bar>\n            </div>\n          </div>\n\n          <mat-tab-group animationDuration=\"0ms\" [selectedIndex]=\"selectedTabIndex\" (selectedIndexChange)=\"selectedTabIndex = $event\">\n            <mat-tab label=\"Aperçu\">\n              <mat-card class=\"tab-card\">\n                <mat-card-title>Description du cours</mat-card-title>\n                <mat-card-content>\n                  <p class=\"description-full\">{{ course.description }}</p>\n\n                  <div class=\"learning-outcomes\">\n                    <h3>Ce que vous apprendrez :</h3>\n                    <ul>\n                      <li><mat-icon>check_circle</mat-icon> Les concepts fondamentaux de React</li>\n                      <li><mat-icon>check_circle</mat-icon> Création et utilisation de composants</li>\n                      <li><mat-icon>check_circle</mat-icon> Gestion de l'état avec les hooks</li>\n                      <li><mat-icon>check_circle</mat-icon> Bonnes pratiques de développement</li>\n                    </ul>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-tab>\n\n            <mat-tab label=\"Contenu\">\n              <mat-card class=\"tab-card\">\n                <mat-card-title>Contenu du cours</mat-card-title>\n                <mat-card-content>\n                  <div class=\"content-list\">\n                    <div *ngFor=\"let content of course.contenus; let i = index\" class=\"content-item\">\n                      <div class=\"content-icon-wrapper\" \n                           [ngClass]=\"{\n                             'completed': content.estComplete, \n                             'unlocked': content.estDebloque && !content.estComplete, \n                             'locked': !content.estDebloque\n                           }\">\n                        <mat-icon *ngIf=\"content.estComplete\">check_circle</mat-icon>\n                        <mat-icon *ngIf=\"!content.estComplete && content.estDebloque\">{{ getContentIcon(content.typeContenu) }}</mat-icon>\n                        <mat-icon *ngIf=\"!content.estDebloque\">lock</mat-icon>\n                      </div>\n                      <div class=\"content-details\">\n                        <h4>{{ content.titre }}</h4>\n                        <p class=\"content-description\">{{ content.description }}</p>\n                        <span *ngIf=\"content.duree\" class=\"content-duration\">{{ content.duree }} minutes</span>\n                      </div>\n                      <button mat-stroked-button color=\"primary\" \n                              *ngIf=\"course.estAchete && content.estDebloque\" \n                              (click)=\"handleStartContent(content.id, content.typeContenu)\">\n                        {{ content.estComplete ? 'Revoir' : 'Commencer' }}\n                      </button>\n                    </div>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-tab>\n\n            <mat-tab label=\"Formateur\">\n              <mat-card class=\"tab-card\">\n                <mat-card-title>À propos du formateur</mat-card-title>\n                <mat-card-content>\n                  <div class=\"instructor-info\">\n                    <div class=\"instructor-avatar\">\n                      <span>{{ course.formateur?.prenom?.[0] }}{{ course.formateur?.nom?.[0] }}</span>\n                    </div>\n                    <div>\n                      <h3>{{ course.formateur?.prenom }} {{ course.formateur?.nom }}</h3>\n                      <p class=\"instructor-bio\">Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs.</p>\n                    </div>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-tab>\n          </mat-tab-group>\n        </div>\n\n        <!-- Sidebar -->\n        <div class=\"sidebar\">\n          <mat-card class=\"sticky-card\">\n            <mat-card-content>\n              <div class=\"price-section\" *ngIf=\"!course.estGratuit\">\n                <span class=\"price-value\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <div class=\"price-section\" *ngIf=\"course.estGratuit\">\n                <mat-chip-listbox>\n                  <mat-chip class=\"free-chip-large\">Cours Gratuit</mat-chip>\n                </mat-chip-listbox>\n              </div>\n\n              <div class=\"action-buttons\">\n                <button mat-raised-button color=\"accent\" class=\"full-width-btn\" \n                        *ngIf=\"!course.estAchete && course.estGratuit\" \n                        (click)=\"enrollInCourse(course.id)\">\n                  Commencer le cours\n                </button>\n                <button mat-raised-button color=\"primary\" class=\"full-width-btn\" \n                        *ngIf=\"!course.estAchete && !course.estGratuit\" \n                        [routerLink]=\"['/payment', course.id]\">\n                  <mat-icon>euro_symbol</mat-icon>\n                  Acheter le cours\n                </button>\n                <button mat-raised-button color=\"primary\" class=\"full-width-btn\" \n                        *ngIf=\"course.estAchete\" \n                        (click)=\"selectedTabIndex = 1\">\n                  Continuer le cours\n                </button>\n                <mat-chip-listbox *ngIf=\"course.estAchete\" class=\"purchased-chip\">\n                  <mat-chip class=\"purchased-chip-item\">Cours acheté</mat-chip>\n                </mat-chip-listbox>\n              </div>\n\n              <div class=\"course-summary\">\n                <div class=\"summary-item\">\n                  <span>Durée totale:</span>\n                  <span>{{ course.duree }} minutes</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Niveau:</span>\n                  <span>{{ course.niveau }}</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Étudiants:</span>\n                  <span>{{ course.nombreEtudiants }}</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Note:</span>\n                  <div class=\"rating-display\">\n                    <mat-icon class=\"star-icon\">star</mat-icon>\n                    <span>{{ course.note }}</span>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .course-detail-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n    }\n\n    .content-wrapper {\n      display: grid;\n      grid-template-columns: 2fr 1fr;\n      gap: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .main-content {\n      grid-column: span 2; /* Default to full width on small screens */\n    }\n\n    @media (min-width: 960px) {\n      .main-content {\n        grid-column: span 2 / span 2;\n      }\n      .sidebar {\n        grid-column: span 1 / span 1;\n      }\n    }\n\n    .course-header {\n      margin-bottom: 2rem;\n    }\n\n    .course-header h1 {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-header .description {\n      font-size: 1.1rem;\n      color: #666;\n      margin-bottom: 1.5rem;\n    }\n\n    .course-meta {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      align-items: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .course-meta .mat-chip {\n      font-size: 0.9rem;\n      padding: 0.4rem 0.8rem;\n      height: auto;\n    }\n\n    .course-meta .mat-chip.bg-green-100 { background-color: #d4edda; color: #155724; }\n    .course-meta .mat-chip.bg-yellow-100 { background-color: #fff3cd; color: #856404; }\n    .course-meta .mat-chip.bg-red-100 { background-color: #f8d7da; color: #721c24; }\n\n    .meta-item {\n      display: flex;\n      align-items: center;\n      gap: 0.3rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .meta-item mat-icon {\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n      color: #888;\n    }\n\n    .meta-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .progress-section {\n      margin-top: 1.5rem;\n    }\n\n    .progress-label {\n      display: flex;\n      justify-content: space-between;\n      font-size: 0.9rem;\n      font-weight: 500;\n      margin-bottom: 0.5rem;\n      color: #444;\n    }\n\n    mat-progress-bar {\n      height: 8px;\n      border-radius: 4px;\n    }\n\n    .tab-card {\n      margin-top: 1.5rem;\n      padding: 1.5rem;\n    }\n\n    .tab-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .description-full {\n      font-size: 1rem;\n      line-height: 1.6;\n      color: #444;\n    }\n\n    .learning-outcomes {\n      margin-top: 2rem;\n    }\n\n    .learning-outcomes h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .learning-outcomes ul {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .learning-outcomes li {\n      display: flex;\n      align-items: center;\n      margin-bottom: 0.8rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .learning-outcomes li mat-icon {\n      color: #4caf50; /* Green */\n      margin-right: 0.8rem;\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n    }\n\n    .content-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .content-item {\n      display: flex;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #eee;\n      border-radius: 8px;\n      background-color: #fff;\n      transition: box-shadow 0.2s ease-in-out;\n    }\n\n    .content-item:hover {\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n    }\n\n    .content-icon-wrapper {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 1rem;\n      flex-shrink: 0;\n    }\n\n    .content-icon-wrapper mat-icon {\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .content-icon-wrapper.completed {\n      background-color: #e8f5e9; /* Light green */\n    }\n    .content-icon-wrapper.completed mat-icon {\n      color: #4caf50; /* Green */\n    }\n\n    .content-icon-wrapper.unlocked {\n      background-color: #e3f2fd; /* Light blue */\n    }\n    .content-icon-wrapper.unlocked mat-icon {\n      color: #2196f3; /* Blue */\n    }\n\n    .content-icon-wrapper.locked {\n      background-color: #f5f5f5; /* Light gray */\n    }\n    .content-icon-wrapper.locked mat-icon {\n      color: #9e9e9e; /* Gray */\n    }\n\n    .content-details {\n      flex-grow: 1;\n    }\n\n    .content-details h4 {\n      font-size: 1.1rem;\n      font-weight: 500;\n      margin-bottom: 0.2rem;\n      color: #333;\n    }\n\n    .content-description {\n      font-size: 0.85rem;\n      color: #777;\n      margin-bottom: 0.4rem;\n    }\n\n    .content-duration {\n      font-size: 0.8rem;\n      color: #888;\n    }\n\n    .content-item button {\n      margin-left: 1rem;\n      flex-shrink: 0;\n    }\n\n    .instructor-info {\n      display: flex;\n      align-items: flex-start;\n      gap: 1.5rem;\n      padding: 1rem 0;\n    }\n\n    .instructor-avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background-color: #e1bee7; /* Light purple */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 1.8rem;\n      font-weight: bold;\n      color: #8e24aa; /* Dark purple */\n      flex-shrink: 0;\n    }\n\n    .instructor-info h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .instructor-bio {\n      font-size: 0.95rem;\n      line-height: 1.5;\n      color: #555;\n    }\n\n    .sidebar {\n      grid-column: span 1;\n    }\n\n    .sticky-card {\n      position: sticky;\n      top: 2rem; /* Adjust as needed */\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .price-section {\n      text-align: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .price-value {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .free-chip-large {\n      background-color: #e6ffed;\n      color: #28a745;\n      font-size: 1.2rem;\n      padding: 0.8rem 1.5rem;\n      height: auto;\n    }\n\n    .action-buttons {\n      display: flex;\n      flex-direction: column;\n      gap: 0.8rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .full-width-btn {\n      width: 100%;\n      font-size: 1rem;\n      padding: 0.8rem 1rem;\n    }\n\n    .full-width-btn mat-icon {\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n      margin-right: 0.5rem;\n    }\n\n    .purchased-chip {\n      text-align: center;\n      margin-top: 1rem;\n    }\n\n    .purchased-chip-item {\n      background-color: #e8f5e9;\n      color: #4caf50;\n      font-size: 0.9rem;\n      padding: 0.4rem 0.8rem;\n      height: auto;\n    }\n\n    .course-summary {\n      margin-top: 1.5rem;\n      padding-top: 1.5rem;\n      border-top: 1px solid #eee;\n      display: flex;\n      flex-direction: column;\n      gap: 0.8rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .summary-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .rating-display {\n      display: flex;\n      align-items: center;\n      gap: 0.3rem;\n    }\n\n    .rating-display .star-icon {\n      color: #ffc107; /* Yellow */\n      font-size: 1.1rem;\n      width: 1.1rem;\n      height: 1.1rem;\n    }\n  `,\n  ],\n})\nexport class CourseDetailComponent implements OnInit {\n  courseId!: number\n  course!: Course\n  currentUser!: User | null\n  selectedTabIndex = 0\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private courseService: CourseService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.route.paramMap.subscribe((params) => {\n      this.courseId = Number(params.get(\"id\"))\n      this.loadCourseDetails()\n    })\n\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n    })\n  }\n\n  loadCourseDetails(): void {\n    // Mock data for demonstration\n    this.course = {\n      id: this.courseId,\n      titre: \"React Fundamentals\",\n      description:\n        \"Apprenez les bases de React avec des exemples pratiques et des projets concrets. Ce cours couvre tous les concepts essentiels pour débuter avec React : composants, props, state, hooks, et bien plus encore.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"<EMAIL>\",\n        role: \"Formateur\",\n        bio: \"Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs.\",\n      },\n      contenus: [\n        {\n          id: 1,\n          titre: \"Introduction à React\",\n          description: \"Découvrez React et ses concepts de base\",\n          typeContenu: \"Video\",\n          duree: 30,\n          estComplete: true,\n          estDebloque: true,\n          ordre: 1,\n          coursId: this.courseId,\n        },\n        {\n          id: 2,\n          titre: \"Components et Props\",\n          description: \"Apprenez à créer et utiliser des composants\",\n          typeContenu: \"Video\",\n          duree: 45,\n          estComplete: true,\n          estDebloque: true,\n          ordre: 2,\n          coursId: this.courseId,\n        },\n        {\n          id: 3,\n          titre: \"Quiz - Bases de React\",\n          description: \"Testez vos connaissances sur les bases\",\n          typeContenu: \"Quiz\",\n          estComplete: false,\n          estDebloque: true,\n          ordre: 3,\n          coursId: this.courseId,\n        },\n        {\n          id: 4,\n          titre: \"State et Hooks\",\n          description: \"Gérez l'état de vos composants\",\n          typeContenu: \"Video\",\n          duree: 50,\n          estComplete: false,\n          estDebloque: false,\n          ordre: 4,\n          coursId: this.courseId,\n        },\n        {\n          id: 5,\n          titre: \"Résumé du chapitre\",\n          description: \"Points clés à retenir\",\n          typeContenu: \"Resume\",\n          estComplete: false,\n          estDebloque: false,\n          ordre: 5,\n          coursId: this.courseId,\n        },\n      ],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n      estAchete: true, // Simulate if purchased\n      progression: 40, // Simulate progress\n    }\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(this.courseId).subscribe({\n      next: (data) => {\n        this.course = data;\n        // TODO: Fetch user enrollment status and progress\n        this.course.estAchete = true; // Example\n        this.course.progression = 40; // Example\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  getLevelColor(niveau: string): string {\n    switch (niveau) {\n      case \"Débutant\":\n        return \"bg-green-100\"\n      case \"Intermédiaire\":\n        return \"bg-yellow-100\"\n      case \"Avancé\":\n        return \"bg-red-100\"\n      default:\n        return \"bg-gray-100\"\n    }\n  }\n\n  getContentIcon(type: string): string {\n    switch (type) {\n      case \"Video\":\n        return \"play_circle\"\n      case \"Quiz\":\n        return \"quiz\"\n      case \"Resume\":\n        return \"description\"\n      default:\n        return \"book\"\n    }\n  }\n\n  handleStartContent(contentId: number, typeContenu: string): void {\n    if (!this.course.estAchete) {\n      this.snackBar.open(\"Veuillez acheter le cours pour accéder au contenu.\", \"Fermer\", { duration: 3000 })\n      return\n    }\n\n    const content = this.course.contenus?.find((c) => c.id === contentId)\n    if (!content?.estDebloque) {\n      this.snackBar.open(\"Ce contenu n'est pas encore débloqué.\", \"Fermer\", { duration: 3000 })\n      return\n    }\n\n    switch (typeContenu) {\n      case \"Quiz\":\n        this.router.navigate([\"/quiz\", contentId])\n        break\n      case \"Video\":\n        this.router.navigate([\"/video\", contentId]) // Assuming a video player component\n        break\n      case \"Resume\":\n        this.router.navigate([\"/resume\", contentId]) // Assuming a resume viewer component\n        break\n      default:\n        this.snackBar.open(\"Type de contenu non pris en charge.\", \"Fermer\", { duration: 3000 })\n    }\n  }\n\n  enrollInCourse(courseId: number): void {\n    if (!this.currentUser) {\n      this.snackBar.open(\"Veuillez vous connecter pour vous inscrire.\", \"Fermer\", { duration: 3000 })\n      this.router.navigate([\"/auth/login\"])\n      return\n    }\n\n    // Call the service to enroll in a free course\n    // this.courseService.enrollInCourse(courseId).subscribe({\n    //   next: (res) => {\n    //     this.snackBar.open('Inscription réussie au cours gratuit !', 'Fermer', { duration: 3000 });\n    //     this.course.estAchete = true; // Update UI\n    //     this.course.progression = 0;\n    //   },\n    //   error: (err) => {\n    //     this.snackBar.open('Erreur lors de l\\'inscription.', 'Fermer', { duration: 3000 });\n    //     console.error(err);\n    //   }\n    // });\n    this.snackBar.open(\"Inscription réussie au cours gratuit (simulé) !\", \"Fermer\", { duration: 3000 })\n    this.course.estAchete = true // Simulate UI update\n    this.course.progression = 0\n  }\n}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { CourseService } from \"../../../core/services/course.service\"\nimport { Course } from \"../../../core/models/course.model\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Router } from \"@angular/router\"\n\n@Component({\n  selector: \"app-course-list\",\n  template: `\n    <div class=\"course-list-container\">\n      <div class=\"header-section\">\n        <h1>Catalogue des Cours</h1>\n\n        <!-- Filtres -->\n        <div class=\"filters-row\">\n          <mat-form-field appearance=\"outline\" class=\"search-input\">\n            <mat-label>Rechercher un cours...</mat-label>\n            <input matInput [(ngModel)]=\"searchTerm\" (input)=\"applyFilters()\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-select\">\n            <mat-label>Niveau</mat-label>\n            <mat-select [(ngModel)]=\"filterLevel\" (selectionChange)=\"applyFilters()\">\n              <mat-option value=\"all\">Tous les niveaux</mat-option>\n              <mat-option value=\"Débutant\">Débutant</mat-option>\n              <mat-option value=\"Intermédiaire\">Intermédiaire</mat-option>\n              <mat-option value=\"Avancé\">Avancé</mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-select\">\n            <mat-label>Prix</mat-label>\n            <mat-select [(ngModel)]=\"filterPrice\" (selectionChange)=\"applyFilters()\">\n              <mat-option value=\"all\">Tous les prix</mat-option>\n              <mat-option value=\"free\">Gratuit</mat-option>\n              <mat-option value=\"paid\">Payant</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Liste des cours -->\n      <div class=\"courses-grid\" *ngIf=\"filteredCourses.length > 0; else noCourses\">\n        <mat-card *ngFor=\"let course of filteredCourses\" class=\"course-card\">\n          <mat-card-header>\n            <div class=\"card-header-top\">\n              <mat-chip-listbox>\n                <mat-chip [class]=\"getLevelColor(course.niveau || 'Débutant')\">{{ course.niveau }}</mat-chip>\n              </mat-chip-listbox>\n              <span class=\"price\" *ngIf=\"!course.estGratuit\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              <mat-chip *ngIf=\"course.estGratuit\" class=\"free-chip\">Gratuit</mat-chip>\n            </div>\n            <mat-card-title>{{ course.titre }}</mat-card-title>\n            <mat-card-subtitle class=\"description\">{{ course.description }}</mat-card-subtitle>\n          </mat-card-header>\n\n          <mat-card-content>\n            <div class=\"course-info\">\n              <div class=\"info-item\">\n                <mat-icon>person</mat-icon>\n                <span>{{ course.formateur?.prenom }} {{ course.formateur?.nom }}</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ course.duree }} min</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon>group</mat-icon>\n                <span>{{ course.nombreEtudiants }} étudiants</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon class=\"star-icon\">star</mat-icon>\n                <span>{{ course.note }}</span>\n              </div>\n            </div>\n\n            <div class=\"content-preview\">\n              <h4>Contenu du cours:</h4>\n              <div *ngFor=\"let content of course.contenus | slice:0:3\" class=\"content-item\">\n                <mat-icon>{{ getContentIcon(content.typeContenu) }}</mat-icon>\n                <span>{{ content.titre }}</span>\n                <span *ngIf=\"content.duree\" class=\"content-duration\">({{ content.duree }} min)</span>\n              </div>\n              <p *ngIf=\"course.contenus && course.contenus.length > 3\" class=\"more-content\">+{{ course.contenus.length - 3 }} autres contenus</p>\n            </div>\n\n            <div class=\"card-actions\">\n              <button mat-stroked-button color=\"primary\" [routerLink]=\"['/courses', course.id]\">\n                Voir détails\n              </button>\n              <button mat-raised-button color=\"accent\" *ngIf=\"course.estGratuit\">Commencer</button>\n              <button mat-raised-button color=\"primary\" *ngIf=\"!course.estGratuit\" [routerLink]=\"['/payment', course.id]\">\n                <mat-icon>euro_symbol</mat-icon>\n                Acheter\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #noCourses>\n        <div class=\"no-courses\">\n          <mat-icon>book_off</mat-icon>\n          <h3>Aucun cours trouvé</h3>\n          <p>Essayez de modifier vos critères de recherche.</p>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [\n    `\n    .course-list-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n    }\n\n    .header-section {\n      margin-bottom: 2rem;\n    }\n\n    .header-section h1 {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 1.5rem;\n      text-align: center;\n    }\n\n    .filters-row {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 2rem;\n    }\n\n    .search-input {\n      flex-grow: 1;\n      max-width: 400px;\n    }\n\n    .filter-select {\n      width: 200px;\n    }\n\n    .courses-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .course-card {\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      height: 100%;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\n    }\n\n    .course-card:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n    }\n\n    .card-header-top {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .mat-chip {\n      font-size: 0.8rem;\n      padding: 0.3rem 0.7rem;\n      height: auto;\n    }\n\n    .mat-chip.bg-green-100 { background-color: #d4edda; color: #155724; }\n    .mat-chip.bg-yellow-100 { background-color: #fff3cd; color: #856404; }\n    .mat-chip.bg-red-100 { background-color: #f8d7da; color: #721c24; }\n    .free-chip { background-color: #e6ffed; color: #28a745; }\n\n    .price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .course-card mat-card-title {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-card .description {\n      font-size: 0.9rem;\n      color: #666;\n      line-height: 1.4;\n      height: 3em; /* Limit to 2 lines */\n      overflow: hidden;\n      text-overflow: ellipsis;\n      display: -webkit-box;\n      -webkit-line-clamp: 2;\n      -webkit-box-orient: vertical;\n    }\n\n    .course-info {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      margin-top: 1rem;\n      font-size: 0.9rem;\n      color: #555;\n    }\n\n    .info-item {\n      display: flex;\n      align-items: center;\n      gap: 0.3rem;\n    }\n\n    .info-item mat-icon {\n      font-size: 1.1rem;\n      width: 1.1rem;\n      height: 1.1rem;\n      color: #888;\n    }\n\n    .info-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .content-preview {\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .content-preview h4 {\n      font-size: 1rem;\n      font-weight: 600;\n      margin-bottom: 0.8rem;\n      color: #444;\n    }\n\n    .content-item {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.4rem;\n    }\n\n    .content-item mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      color: #777;\n    }\n\n    .content-duration {\n      margin-left: auto;\n      font-size: 0.8rem;\n      color: #888;\n    }\n\n    .more-content {\n      font-size: 0.8rem;\n      color: #888;\n      margin-top: 0.5rem;\n    }\n\n    .card-actions {\n      display: flex;\n      gap: 0.8rem;\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .card-actions button {\n      flex: 1;\n      font-size: 0.9rem;\n      padding: 0.6rem 1rem;\n    }\n\n    .card-actions button mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.3rem;\n    }\n\n    .no-courses {\n      text-align: center;\n      padding: 4rem 0;\n      color: #777;\n    }\n\n    .no-courses mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #aaa;\n    }\n\n    .no-courses h3 {\n      font-size: 1.5rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    @media (max-width: 768px) {\n      .filters-row {\n        flex-direction: column;\n        align-items: stretch;\n      }\n      .search-input, .filter-select {\n        max-width: 100%;\n        width: 100%;\n      }\n      .courses-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `,\n  ],\n})\nexport class CourseListComponent implements OnInit {\n  courses: Course[] = []\n  filteredCourses: Course[] = []\n  searchTerm = \"\"\n  filterLevel = \"all\"\n  filterPrice = \"all\"\n\n  constructor(\n    private courseService: CourseService,\n    private snackBar: MatSnackBar,\n    private router: Router,\n  ) {}\n\n  ngOnInit(): void {\n    this.loadCourses()\n  }\n\n  loadCourses(): void {\n    // Mock data for demonstration\n    this.courses = [\n      {\n        id: 1,\n        titre: \"React Fundamentals\",\n        description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n        prix: 99.99,\n        duree: 120,\n        niveau: \"Débutant\",\n        formateurId: 1,\n        formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\", email: \"<EMAIL>\", role: \"Formateur\" },\n        contenus: [\n          {\n            id: 1,\n            titre: \"Introduction à React\",\n            typeContenu: \"Video\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 1,\n          },\n          {\n            id: 2,\n            titre: \"Components et Props\",\n            typeContenu: \"Video\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 2,\n          },\n          {\n            id: 3,\n            titre: \"Quiz - Bases de React\",\n            typeContenu: \"Quiz\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 3,\n          },\n          {\n            id: 4,\n            titre: \"Résumé du chapitre\",\n            typeContenu: \"Resume\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 4,\n          },\n        ],\n        nombreEtudiants: 245,\n        note: 4.8,\n        estGratuit: false,\n      },\n      {\n        id: 2,\n        titre: \"JavaScript Avancé\",\n        description: \"Maîtrisez les concepts avancés de JavaScript : closures, prototypes, async/await.\",\n        prix: 149.99,\n        duree: 180,\n        niveau: \"Avancé\",\n        formateurId: 2,\n        formateur: { id: 2, nom: \"Martin\", prenom: \"Sophie\", email: \"<EMAIL>\", role: \"Formateur\" },\n        contenus: [\n          {\n            id: 5,\n            titre: \"Closures et Scope\",\n            typeContenu: \"Video\",\n            coursId: 2,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 1,\n          },\n          {\n            id: 6,\n            titre: \"Prototypes et Héritage\",\n            typeContenu: \"Video\",\n            coursId: 2,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 2,\n          },\n          {\n            id: 7,\n            titre: \"Quiz - Concepts avancés\",\n            typeContenu: \"Quiz\",\n            coursId: 2,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 3,\n          },\n        ],\n        nombreEtudiants: 156,\n        note: 4.9,\n        estGratuit: false,\n      },\n      {\n        id: 3,\n        titre: \"Introduction au Web\",\n        description: \"Cours gratuit pour débuter dans le développement web avec HTML, CSS et JavaScript.\",\n        prix: 0,\n        duree: 60,\n        niveau: \"Débutant\",\n        formateurId: 3,\n        formateur: { id: 3, nom: \"Bernard\", prenom: \"Pierre\", email: \"<EMAIL>\", role: \"Formateur\" },\n        contenus: [\n          {\n            id: 8,\n            titre: \"HTML Basics\",\n            typeContenu: \"Video\",\n            coursId: 3,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 1,\n          },\n          {\n            id: 9,\n            titre: \"CSS Styling\",\n            typeContenu: \"Video\",\n            coursId: 3,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 2,\n          },\n          {\n            id: 10,\n            titre: \"Quiz final\",\n            typeContenu: \"Quiz\",\n            coursId: 3,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 3,\n          },\n        ],\n        nombreEtudiants: 892,\n        note: 4.6,\n        estGratuit: true,\n      },\n    ]\n    this.applyFilters()\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getAllCours().subscribe({\n      next: (data) => {\n        this.courses = data;\n        this.applyFilters();\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  applyFilters(): void {\n    this.filteredCourses = this.courses.filter((course) => {\n      const matchesSearch =\n        course.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        (course.description || '').toLowerCase().includes(this.searchTerm.toLowerCase())\n      const matchesLevel = this.filterLevel === \"all\" || course.niveau === this.filterLevel\n      const matchesPrice =\n        this.filterPrice === \"all\" ||\n        (this.filterPrice === \"free\" && course.estGratuit) ||\n        (this.filterPrice === \"paid\" && !course.estGratuit)\n\n      return matchesSearch && matchesLevel && matchesPrice\n    })\n  }\n\n  getContentIcon(type: string): string {\n    switch (type) {\n      case \"Video\":\n        return \"play_circle\"\n      case \"Quiz\":\n        return \"quiz\"\n      case \"Resume\":\n        return \"description\"\n      default:\n        return \"book\"\n    }\n  }\n\n  getLevelColor(niveau: string): string {\n    switch (niveau) {\n      case \"Débutant\":\n        return \"bg-green-100\"\n      case \"Intermédiaire\":\n        return \"bg-yellow-100\"\n      case \"Avancé\":\n        return \"bg-red-100\"\n      default:\n        return \"bg-gray-100\"\n    }\n  }\n}\n", "import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { FormsModule } from \"@angular/forms\" // Pour ngModel dans les filtres\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\nimport { MatTabsModule } from \"@angular/material/tabs\"\n\nimport { CourseListComponent } from \"./course-list/course-list.component\"\nimport { CourseDetailComponent } from \"./course-detail/course-detail.component\"\nimport { CourseCreateEditComponent } from \"./course-create-edit/course-create-edit.component\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\n\n@NgModule({\n  declarations: [CourseListComponent, CourseDetailComponent, CourseCreateEditComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    MatProgressSpinnerModule,\n    MatTabsModule,\n    RouterModule.forChild([\n      { path: \"\", component: CourseListComponent },\n      { path: \"create\", component: CourseCreateEditComponent },\n      { path: \"edit/:id\", component: CourseCreateEditComponent },\n      { path: \":id\", component: CourseDetailComponent },\n    ]),\n  ],\n})\nexport class CoursesModule {}\n"], "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "CourseCreateEditComponent_mat_form_field_37_mat_error_4_Template", "CourseCreateEditComponent_mat_form_field_37_mat_error_5_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r5", "courseForm", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_1_0", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵlistener", "CourseCreateEditComponent_div_43_Template_mat_select_selectionChange_4_listener", "restoredCtx", "ɵɵrestoreView", "_r17", "i_r12", "index", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "onContentTypeChange", "CourseCreateEditComponent_div_43_mat_form_field_15_Template", "CourseCreateEditComponent_div_43_ng_container_16_Template", "CourseCreateEditComponent_div_43_mat_form_field_17_Template", "CourseCreateEditComponent_div_43_Template_button_click_18_listener", "ctx_r18", "<PERSON><PERSON><PERSON><PERSON>", "content_r11", "value", "tmp_2_0", "tmp_3_0", "ɵɵtextInterpolate", "ctx_r8", "isEditMode", "CourseCreateEditComponent", "constructor", "fb", "route", "router", "courseService", "snackBar", "courseId", "isLoading", "ngOnInit", "initForm", "paramMap", "subscribe", "params", "id", "Number", "loadCourse", "group", "titre", "required", "description", "duree", "min", "niveau", "estGratuit", "prix", "disabled", "contenus", "array", "disable", "mockCourse", "formateurId", "formateur", "nom", "prenom", "email", "role", "typeContenu", "coursId", "estComplete", "estDebloque", "ordre", "<PERSON>uil<PERSON><PERSON><PERSON>", "nombreEtudiants", "note", "patchValue", "for<PERSON>ach", "content", "<PERSON><PERSON><PERSON><PERSON>", "togglePriceField", "contentGroup", "max", "contenuTexte", "push", "removeAt", "at", "type", "clearValidators", "updateValueAndValidity", "setValidators", "estGratuitControl", "prixControl", "setValue", "enable", "onSubmit", "valid", "courseData", "modifierCours", "next", "res", "open", "duration", "navigate", "error", "err", "console", "mark<PERSON>llAsTouched", "onCancel", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "CourseService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "CourseCreateEditComponent_Template", "rf", "ctx", "CourseCreateEditComponent_Template_form_ngSubmit_6_listener", "CourseCreateEditComponent_mat_error_11_Template", "CourseCreateEditComponent_mat_error_16_Template", "CourseCreateEditComponent_mat_error_22_Template", "CourseCreateEditComponent_mat_error_23_Template", "CourseCreateEditComponent_mat_error_34_Template", "CourseCreateEditComponent_Template_mat_checkbox_change_35_listener", "CourseCreateEditComponent_mat_form_field_37_Template", "CourseCreateEditComponent_div_43_Template", "CourseCreateEditComponent_Template_button_click_44_listener", "CourseCreateEditComponent_mat_spinner_50_Template", "CourseCreateEditComponent_span_51_Template", "CourseCreateEditComponent_Template_button_click_52_listener", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "controls", "invalid", "ɵɵtextInterpolate1", "ctx_r0", "course", "progression", "ctx_r11", "getContentIcon", "content_r8", "CourseDetailComponent_div_57_button_11_Template_button_click_0_listener", "_r19", "$implicit", "ctx_r17", "handleStartContent", "CourseDetailComponent_div_57_mat_icon_2_Template", "CourseDetailComponent_div_57_mat_icon_3_Template", "CourseDetailComponent_div_57_mat_icon_4_Template", "CourseDetailComponent_div_57_span_10_Template", "CourseDetailComponent_div_57_button_11_Template", "ɵɵpureFunction3", "_c0", "ctx_r1", "estAchete", "ɵɵpipeBindV", "ɵɵpureFunction1", "_c1", "ctx_r2", "CourseDetailComponent_button_78_Template_button_click_0_listener", "_r22", "ctx_r21", "enrollInCourse", "_c2", "CourseDetailComponent_button_80_Template_button_click_0_listener", "_r24", "ctx_r23", "selectedTabIndex", "CourseDetailComponent", "authService", "loadCourseDetails", "currentUser$", "user", "currentUser", "bio", "getLevelColor", "contentId", "find", "c", "AuthService", "CourseDetailComponent_Template", "CourseDetailComponent_div_22_Template", "CourseDetailComponent_Template_mat_tab_group_selectedIndexChange_23_listener", "$event", "CourseDetailComponent_div_57_Template", "CourseDetailComponent_div_75_Template", "CourseDetailComponent_div_76_Template", "CourseDetailComponent_button_78_Template", "CourseDetailComponent_button_79_Template", "CourseDetailComponent_button_80_Template", "CourseDetailComponent_mat_chip_listbox_81_Template", "ɵɵclassMap", "ɵɵtextInterpolate2", "course_r4", "content_r12", "CourseListComponent_div_33_mat_card_1_div_37_span_5_Template", "ctx_r7", "length", "CourseListComponent_div_33_mat_card_1_span_6_Template", "CourseListComponent_div_33_mat_card_1_mat_chip_7_Template", "CourseListComponent_div_33_mat_card_1_div_37_Template", "CourseListComponent_div_33_mat_card_1_p_39_Template", "CourseListComponent_div_33_mat_card_1_button_43_Template", "CourseListComponent_div_33_mat_card_1_button_44_Template", "ctx_r3", "ɵɵpipeBind3", "CourseListComponent_div_33_mat_card_1_Template", "filteredCourses", "CourseListComponent", "courses", "searchTerm", "filterLevel", "filterPrice", "loadCourses", "applyFilters", "filter", "matchesSearch", "toLowerCase", "includes", "matchesLevel", "matchesPrice", "CourseListComponent_Template", "CourseListComponent_Template_input_ngModelChange_8_listener", "CourseListComponent_Template_input_input_8_listener", "CourseListComponent_Template_mat_select_ngModelChange_14_listener", "CourseListComponent_Template_mat_select_selectionChange_14_listener", "CourseListComponent_Template_mat_select_ngModelChange_26_listener", "CourseListComponent_Template_mat_select_selectionChange_26_listener", "CourseListComponent_div_33_Template", "CourseListComponent_ng_template_34_Template", "ɵɵtemplateRefExtractor", "_r1", "CommonModule", "RouterModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatChipsModule", "MatProgressBarModule", "MatProgressSpinnerModule", "MatTabsModule", "ReactiveFormsModule", "CoursesModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}