{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/message.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/list\";\nimport * as i12 from \"@angular/material/divider\";\nimport * as i13 from \"@angular/material/chips\";\nfunction MessagesComponent_mat_list_item_19_mat_chip_listbox_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip-listbox\", 22)(1, \"mat-chip\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const conv_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(conv_r5.unreadCount);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"selected-conversation\": a0\n  };\n};\nfunction MessagesComponent_mat_list_item_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\", 15);\n    i0.ɵɵlistener(\"click\", function MessagesComponent_mat_list_item_19_Template_mat_list_item_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const conv_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.selectConversation(conv_r5.participantId));\n    });\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-chip-listbox\")(8, \"mat-chip\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 18)(11, \"span\", 19);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 20);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, MessagesComponent_mat_list_item_19_mat_chip_listbox_15_Template, 3, 1, \"mat-chip-listbox\", 21);\n    i0.ɵɵelement(16, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const conv_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r0.selectedConversationId === conv_r5.participantId));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getInitials(conv_r5.participantName));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(conv_r5.participantName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r0.getRoleChipClass(conv_r5.participantRole));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(conv_r5.participantRole);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(conv_r5.lastMessageContent);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatDate(conv_r5.lastMessageDate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conv_r5.unreadCount > 0);\n  }\n}\nfunction MessagesComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"chat_bubble_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Aucune conversation trouv\\u00E9e.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"my-message\": a0,\n    \"other-message\": a1\n  };\n};\nfunction MessagesComponent_ng_container_22_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"p\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const msg_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c1, msg_r11.expediteurId === (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.id), msg_r11.expediteurId !== (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.id)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(msg_r11.contenu);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.formatDate(msg_r11.dateEnvoi));\n  }\n}\nfunction MessagesComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-card-header\", 25)(2, \"div\", 26)(3, \"div\", 16)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\")(7, \"mat-card-title\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip-listbox\")(10, \"mat-chip\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(12, \"mat-card-content\", 27);\n    i0.ɵɵtemplate(13, MessagesComponent_ng_container_22_div_13_Template, 6, 6, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-card-actions\", 29)(15, \"mat-form-field\", 30)(16, \"textarea\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function MessagesComponent_ng_container_22_Template_textarea_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.newMessageContent = $event);\n    })(\"keydown.enter\", function MessagesComponent_ng_container_22_Template_textarea_keydown_enter_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.sendMessage($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function MessagesComponent_ng_container_22_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.sendMessage());\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"send\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getInitials((ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantName) || \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r2.getRoleChipClass((ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantRole) || \"Client\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedParticipant == null ? null : ctx_r2.selectedParticipant.participantRole, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newMessageContent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.newMessageContent.trim());\n  }\n}\nfunction MessagesComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"S\\u00E9lectionnez une conversation pour commencer\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class MessagesComponent {\n  constructor(messageService, authService, snackBar) {\n    this.messageService = messageService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.conversations = [];\n    this.filteredConversations = [];\n    this.selectedConversationId = null;\n    this.messages = [];\n    this.newMessageContent = \"\";\n    this.searchTerm = \"\";\n    this.currentUser = null;\n    this.selectedParticipant = null;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (this.currentUser) {\n        this.loadConversations();\n      }\n    });\n  }\n  loadConversations() {\n    // Mock data for demonstration\n    this.conversations = [{\n      participantId: 2,\n      participantName: \"Jean Dupont\",\n      participantRole: \"Formateur\",\n      lastMessageContent: \"Bonjour, avez-vous des questions sur le cours React ?\",\n      lastMessageDate: new Date(\"2024-01-15T10:30:00Z\"),\n      unreadCount: 2\n    }, {\n      participantId: 3,\n      participantName: \"Sophie Bernard\",\n      participantRole: \"Client\",\n      lastMessageContent: \"Merci pour votre aide !\",\n      lastMessageDate: new Date(\"2024-01-14T15:45:00Z\"),\n      unreadCount: 0\n    }, {\n      participantId: 4,\n      participantName: \"Admin System\",\n      participantRole: \"Admin\",\n      lastMessageContent: \"Votre demande a été traitée.\",\n      lastMessageDate: new Date(\"2024-01-13T11:00:00Z\"),\n      unreadCount: 0\n    }];\n    this.applySearch();\n    // Uncomment to fetch from API\n    /*\n    // This would require a backend endpoint to get conversation previews for a user\n    // For example: this.messageService.getConversations(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  applySearch() {\n    this.filteredConversations = this.conversations.filter(conv => conv.participantName.toLowerCase().includes(this.searchTerm.toLowerCase()));\n  }\n  selectConversation(participantId) {\n    this.selectedConversationId = participantId;\n    this.selectedParticipant = this.conversations.find(c => c.participantId === participantId) || null;\n    this.loadMessages(participantId);\n  }\n  loadMessages(participantId) {\n    if (!this.currentUser) return;\n    // Mock messages for the selected conversation\n    this.messages = [{\n      id: 1,\n      expediteurId: participantId,\n      destinataireId: this.currentUser.id,\n      contenu: \"Bonjour, j'espère que vous appréciez le cours React !\",\n      dateEnvoi: new Date(\"2024-01-15T09:00:00Z\"),\n      expediteur: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      },\n      destinataire: this.currentUser\n    }, {\n      id: 2,\n      expediteurId: this.currentUser.id,\n      destinataireId: participantId,\n      contenu: \"Bonjour Jean, oui c'est très intéressant ! J'ai une question sur les hooks.\",\n      dateEnvoi: new Date(\"2024-01-15T09:15:00Z\"),\n      expediteur: this.currentUser,\n      destinataire: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      }\n    }, {\n      id: 3,\n      expediteurId: participantId,\n      destinataireId: this.currentUser.id,\n      contenu: \"Parfait ! N'hésitez pas à me poser toutes vos questions. Les hooks peuvent sembler complexes au début.\",\n      dateEnvoi: new Date(\"2024-01-15T09:30:00Z\"),\n      expediteur: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      },\n      destinataire: this.currentUser\n    }, {\n      id: 4,\n      expediteurId: participantId,\n      destinataireId: this.currentUser.id,\n      contenu: \"Bonjour, avez-vous des questions sur le cours React ?\",\n      dateEnvoi: new Date(\"2024-01-15T10:30:00Z\"),\n      expediteur: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      },\n      destinataire: this.currentUser\n    }];\n    // Uncomment to fetch from API\n    /*\n    this.messageService.getMessages(this.currentUser.id, participantId).subscribe({\n      next: (data) => {\n        this.messages = data;\n        // Mark messages as read\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des messages.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  sendMessage(event) {\n    const keyboardEvent = event;\n    if (keyboardEvent && keyboardEvent.key === \"Enter\" && !keyboardEvent.shiftKey) {\n      keyboardEvent.preventDefault();\n    } else if (keyboardEvent && keyboardEvent.key !== \"Enter\") {\n      return; // Only proceed on Enter key press\n    }\n\n    if (!this.newMessageContent.trim() || !this.selectedConversationId || !this.currentUser) return;\n    const message = {\n      expediteurId: this.currentUser.id,\n      destinataireId: this.selectedConversationId,\n      contenu: this.newMessageContent,\n      dateEnvoi: new Date()\n    };\n    // Mock message sending\n    this.messages.push(message);\n    this.newMessageContent = \"\";\n    this.snackBar.open(\"Message envoyé (simulé) !\", \"Fermer\", {\n      duration: 1000\n    });\n    // Uncomment to send via API\n    /*\n    this.messageService.envoyerMessage(message).subscribe({\n      next: (res) => {\n        this.messages.push(message); // Add to local list after successful send\n        this.newMessageContent = '';\n        this.snackBar.open('Message envoyé !', 'Fermer', { duration: 1000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de l\\'envoi du message.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  getInitials(name) {\n    const parts = name.split(\" \");\n    if (parts.length >= 2) {\n      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();\n    } else if (parts.length === 1 && parts[0].length > 0) {\n      return parts[0][0].toUpperCase();\n    }\n    return \"\";\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    const now = new Date();\n    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 24 && d.getDate() === now.getDate()) {\n      return d.toLocaleTimeString(\"fr-FR\", {\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n      });\n    } else if (diffInHours < 48 && d.getDate() === now.getDate() - 1) {\n      return \"Hier\";\n    } else {\n      return d.toLocaleDateString(\"fr-FR\", {\n        day: \"2-digit\",\n        month: \"2-digit\"\n      });\n    }\n  }\n  getRoleChipClass(role) {\n    switch (role) {\n      case \"Client\":\n        return \"role-chip-Client\";\n      case \"Formateur\":\n        return \"role-chip-Formateur\";\n      case \"Admin\":\n        return \"role-chip-Admin\";\n      default:\n        return \"\";\n    }\n  }\n  static {\n    this.ɵfac = function MessagesComponent_Factory(t) {\n      return new (t || MessagesComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessagesComponent,\n      selectors: [[\"app-messages\"]],\n      decls: 25,\n      vars: 5,\n      consts: [[1, \"messages-container\"], [1, \"messages-wrapper\"], [1, \"conversation-list-card\"], [1, \"card-title-with-icon\"], [\"mat-icon-button\", \"\"], [1, \"conversation-search\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"matSuffix\", \"\"], [1, \"conversations-scroll-area\"], [3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-conversations\", 4, \"ngIf\"], [1, \"chat-area-card\"], [4, \"ngIf\", \"ngIfElse\"], [\"noConversationSelected\", \"\"], [3, \"ngClass\", \"click\"], [\"matListItemAvatar\", \"\", 1, \"avatar-placeholder\"], [\"matListItemTitle\", \"\", 1, \"conversation-title\"], [\"matListItemLine\", \"\", 1, \"conversation-last-message\"], [1, \"message-content\"], [1, \"message-date\"], [\"matListItemMeta\", \"\", 4, \"ngIf\"], [\"matListItemMeta\", \"\"], [\"color\", \"warn\", \"selected\", \"\"], [1, \"no-conversations\"], [1, \"chat-header\"], [1, \"chat-header-info\"], [1, \"messages-display-area\"], [\"class\", \"message-bubble-wrapper\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"message-input-area\"], [\"appearance\", \"outline\", 1, \"full-width\", \"message-input-field\"], [\"matInput\", \"\", \"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", 3, \"ngModel\", \"ngModelChange\", \"keydown.enter\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"message-bubble-wrapper\", 3, \"ngClass\"], [1, \"message-bubble\"], [1, \"message-timestamp\"], [1, \"no-conversation-selected\"]],\n      template: function MessagesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\")(4, \"mat-card-title\", 3)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"message\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" Messages \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 4)(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"add\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"mat-card-content\", 5)(12, \"mat-form-field\", 6)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Rechercher une conversation...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function MessagesComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function MessagesComponent_Template_input_input_15_listener() {\n            return ctx.applySearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"mat-icon\", 8);\n          i0.ɵɵtext(17, \"search\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"mat-nav-list\", 9);\n          i0.ɵɵtemplate(19, MessagesComponent_mat_list_item_19_Template, 17, 11, \"mat-list-item\", 10);\n          i0.ɵɵtemplate(20, MessagesComponent_div_20_Template, 5, 0, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"mat-card\", 12);\n          i0.ɵɵtemplate(22, MessagesComponent_ng_container_22_Template, 20, 8, \"ng-container\", 13);\n          i0.ɵɵtemplate(23, MessagesComponent_ng_template_23_Template, 5, 0, \"ng-template\", null, 14, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r3 = i0.ɵɵreference(24);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredConversations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredConversations.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedConversationId)(\"ngIfElse\", _r3);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatIconButton, i7.MatMiniFabButton, i8.MatIcon, i9.MatInput, i10.MatFormField, i10.MatLabel, i10.MatSuffix, i11.MatNavList, i11.MatListItem, i11.MatListItemAvatar, i12.MatDivider, i11.MatListItemLine, i11.MatListItemTitle, i11.MatListItemMeta, i13.MatChip, i13.MatChipListbox],\n      styles: [\".messages-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n  padding: 2rem;\\n}\\n\\n.messages-wrapper[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 2fr;\\n  gap: 1.5rem;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  height: calc(100vh - 4rem); \\n\\n}\\n\\n@media (max-width: 960px) {\\n  .messages-wrapper[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    height: auto;\\n  }\\n  .conversation-list-card[_ngcontent-%COMP%] {\\n    height: auto;\\n    min-height: 300px;\\n  }\\n  .chat-area-card[_ngcontent-%COMP%] {\\n    height: 600px; \\n\\n  }\\n}\\n.conversation-list-card[_ngcontent-%COMP%], .chat-area-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.conversation-list-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.card-title-with-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n}\\n\\n.card-title-with-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.conversation-search[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 0.5rem;\\n}\\n\\n.conversation-search[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.conversations-scroll-area[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  overflow-y: auto;\\n  padding: 0;\\n}\\n\\n.mat-list-item[_ngcontent-%COMP%] {\\n  height: auto !important;\\n  padding: 1rem 1.5rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease-in-out;\\n}\\n\\n.mat-list-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.mat-list-item.selected-conversation[_ngcontent-%COMP%] {\\n  background-color: #ede7f6; \\n\\n  border-left: 4px solid #673ab7; \\n\\n}\\n\\n.avatar-placeholder[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  background-color: #e1bee7; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.1rem;\\n  font-weight: bold;\\n  color: #8e24aa; \\n\\n  flex-shrink: 0;\\n}\\n\\n.conversation-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-weight: 500;\\n  font-size: 1rem;\\n}\\n\\n.conversation-last-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  font-size: 0.85rem;\\n  color: #777;\\n  margin-top: 0.2rem;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  margin-right: 0.5rem;\\n}\\n\\n.message-date[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.mat-chip[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.5rem;\\n  height: auto;\\n}\\n\\n.role-chip-Client[_ngcontent-%COMP%] {\\n  background-color: #e0e0e0;\\n  color: #424242;\\n}\\n\\n.role-chip-Formateur[_ngcontent-%COMP%] {\\n  background-color: #bbdefb;\\n  color: #1976d2;\\n}\\n\\n.role-chip-Admin[_ngcontent-%COMP%] {\\n  background-color: #e1bee7;\\n  color: #8e24aa;\\n}\\n\\n.no-conversations[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #999;\\n}\\n\\n.no-conversations[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  margin-bottom: 1rem;\\n}\\n\\n\\n\\n.chat-area-card[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #eee;\\n  padding: 1rem 1.5rem;\\n}\\n\\n.chat-header-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.chat-header-info[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 0.2rem;\\n}\\n\\n.messages-display-area[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  overflow-y: auto;\\n  padding: 1.5rem;\\n  background-color: #fcfcfc;\\n}\\n\\n.message-bubble-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n}\\n\\n.message-bubble-wrapper.my-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n.message-bubble-wrapper.other-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  max-width: 70%;\\n  padding: 0.8rem 1.2rem;\\n  border-radius: 18px;\\n  position: relative;\\n  word-wrap: break-word;\\n}\\n\\n.my-message[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background-color: #673ab7; \\n\\n  color: white;\\n  border-bottom-right-radius: 4px;\\n}\\n\\n.other-message[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background-color: #e0e0e0; \\n\\n  color: #333;\\n  border-bottom-left-radius: 4px;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n}\\n\\n.message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-top: 0.5rem;\\n  display: block;\\n  text-align: right;\\n  color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n.other-message[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: #777;\\n}\\n\\n.message-input-area[_ngcontent-%COMP%] {\\n  border-top: 1px solid #eee;\\n  padding: 1rem 1.5rem;\\n  display: flex;\\n  align-items: flex-end;\\n  gap: 0.8rem;\\n}\\n\\n.message-input-field[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.message-input-field[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n  max-height: 120px;\\n  overflow-y: auto;\\n}\\n\\n.message-input-area[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.no-conversation-selected[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  color: #999;\\n  text-align: center;\\n}\\n\\n.no-conversation-selected[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  margin-bottom: 1rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "conv_r5", "unreadCount", "ɵɵlistener", "MessagesComponent_mat_list_item_19_Template_mat_list_item_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "selectConversation", "participantId", "ɵɵtemplate", "MessagesComponent_mat_list_item_19_mat_chip_listbox_15_Template", "ɵɵelement", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "selectedConversationId", "getInitials", "participantName", "ɵɵclassMap", "getRoleChipClass", "participantRole", "lastMessageContent", "formatDate", "lastMessageDate", "ɵɵpureFunction2", "_c1", "msg_r11", "expediteurId", "ctx_r10", "currentUser", "id", "contenu", "dateEnvoi", "ɵɵelementContainerStart", "MessagesComponent_ng_container_22_div_13_Template", "MessagesComponent_ng_container_22_Template_textarea_ngModelChange_16_listener", "$event", "_r13", "ctx_r12", "newMessageContent", "MessagesComponent_ng_container_22_Template_textarea_keydown_enter_16_listener", "ctx_r14", "sendMessage", "MessagesComponent_ng_container_22_Template_button_click_17_listener", "ctx_r15", "ɵɵelementContainerEnd", "ctx_r2", "selectedParticipant", "ɵɵtextInterpolate1", "messages", "trim", "MessagesComponent", "constructor", "messageService", "authService", "snackBar", "conversations", "filteredConversations", "searchTerm", "ngOnInit", "currentUser$", "subscribe", "user", "loadConversations", "Date", "applySearch", "filter", "conv", "toLowerCase", "includes", "find", "c", "loadMessages", "destinataireId", "expediteur", "nom", "prenom", "email", "role", "destina<PERSON>", "event", "keyboardEvent", "key", "shift<PERSON>ey", "preventDefault", "message", "push", "open", "duration", "name", "parts", "split", "length", "toUpperCase", "date", "d", "now", "diffInHours", "getTime", "getDate", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "day", "month", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "AuthService", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "MessagesComponent_Template", "rf", "ctx", "MessagesComponent_Template_input_ngModelChange_15_listener", "MessagesComponent_Template_input_input_15_listener", "MessagesComponent_mat_list_item_19_Template", "MessagesComponent_div_20_Template", "MessagesComponent_ng_container_22_Template", "MessagesComponent_ng_template_23_Template", "ɵɵtemplateRefExtractor", "_r3"], "sources": ["C:\\e-learning\\src\\app\\features\\messages\\messages.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { MessageService } from \"../../core/services/message.service\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { Message } from \"../../core/models/message.model\"\nimport { User } from \"../../core/models/user.model\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\n\ninterface ConversationPreview {\n  participantId: number\n  participantName: string\n  participantRole: \"Client\" | \"Formateur\" | \"Admin\"\n  lastMessageContent: string\n  lastMessageDate: Date\n  unreadCount: number\n}\n\n@Component({\n  selector: \"app-messages\",\n  template: `\n    <div class=\"messages-container\">\n      <div class=\"messages-wrapper\">\n        <!-- Conversation List -->\n        <mat-card class=\"conversation-list-card\">\n          <mat-card-header>\n            <mat-card-title class=\"card-title-with-icon\">\n              <mat-icon>message</mat-icon>\n              Messages\n            </mat-card-title>\n            <button mat-icon-button><mat-icon>add</mat-icon></button>\n          </mat-card-header>\n          <mat-card-content class=\"conversation-search\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Rechercher une conversation...</mat-label>\n              <input matInput [(ngModel)]=\"searchTerm\" (input)=\"applySearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n          </mat-card-content>\n          <mat-nav-list class=\"conversations-scroll-area\">\n            <mat-list-item *ngFor=\"let conv of filteredConversations\" \n                           (click)=\"selectConversation(conv.participantId)\"\n                           [ngClass]=\"{'selected-conversation': selectedConversationId === conv.participantId}\">\n              <div matListItemAvatar class=\"avatar-placeholder\">\n                <span>{{ getInitials(conv.participantName) }}</span>\n              </div>\n              <div matListItemTitle class=\"conversation-title\">\n                <span>{{ conv.participantName }}</span>\n                <mat-chip-listbox>\n                  <mat-chip [class]=\"getRoleChipClass(conv.participantRole)\">{{ conv.participantRole }}</mat-chip>\n                </mat-chip-listbox>\n              </div>\n              <div matListItemLine class=\"conversation-last-message\">\n                <span class=\"message-content\">{{ conv.lastMessageContent }}</span>\n                <span class=\"message-date\">{{ formatDate(conv.lastMessageDate) }}</span>\n              </div>\n              <mat-chip-listbox *ngIf=\"conv.unreadCount > 0\" matListItemMeta>\n                <mat-chip color=\"warn\" selected>{{ conv.unreadCount }}</mat-chip>\n              </mat-chip-listbox>\n              <mat-divider></mat-divider>\n            </mat-list-item>\n            <div *ngIf=\"filteredConversations.length === 0\" class=\"no-conversations\">\n              <mat-icon>chat_bubble_outline</mat-icon>\n              <p>Aucune conversation trouvée.</p>\n            </div>\n          </mat-nav-list>\n        </mat-card>\n\n        <!-- Chat Area -->\n        <mat-card class=\"chat-area-card\">\n          <ng-container *ngIf=\"selectedConversationId; else noConversationSelected\">\n            <mat-card-header class=\"chat-header\">\n              <div class=\"chat-header-info\">\n                <div matListItemAvatar class=\"avatar-placeholder\">\n                  <span>{{ getInitials(selectedParticipant?.participantName || '') }}</span>\n                </div>\n                <div>\n                  <mat-card-title>{{ selectedParticipant?.participantName }}</mat-card-title>\n                  <mat-chip-listbox>\n                    <mat-chip [class]=\"getRoleChipClass(selectedParticipant?.participantRole || 'Client')\">\n                      {{ selectedParticipant?.participantRole }}\n                    </mat-chip>\n                  </mat-chip-listbox>\n                </div>\n              </div>\n            </mat-card-header>\n\n            <mat-card-content class=\"messages-display-area\">\n              <div *ngFor=\"let msg of messages\" \n                   class=\"message-bubble-wrapper\" \n                   [ngClass]=\"{'my-message': msg.expediteurId === currentUser?.id, 'other-message': msg.expediteurId !== currentUser?.id}\">\n                <div class=\"message-bubble\">\n                  <p class=\"message-content\">{{ msg.contenu }}</p>\n                  <span class=\"message-timestamp\">{{ formatDate(msg.dateEnvoi!) }}</span>\n                </div>\n              </div>\n            </mat-card-content>\n\n            <mat-card-actions class=\"message-input-area\">\n              <mat-form-field appearance=\"outline\" class=\"full-width message-input-field\">\n                <textarea matInput placeholder=\"Tapez votre message...\" \n                          [(ngModel)]=\"newMessageContent\" \n                          (keydown.enter)=\"sendMessage($event)\"\n                          rows=\"1\"></textarea>\n              </mat-form-field>\n              <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!newMessageContent.trim()\">\n                <mat-icon>send</mat-icon>\n              </button>\n            </mat-card-actions>\n          </ng-container>\n\n          <ng-template #noConversationSelected>\n            <div class=\"no-conversation-selected\">\n              <mat-icon>chat</mat-icon>\n              <p>Sélectionnez une conversation pour commencer</p>\n            </div>\n          </ng-template>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .messages-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      padding: 2rem;\n    }\n\n    .messages-wrapper {\n      display: grid;\n      grid-template-columns: 1fr 2fr;\n      gap: 1.5rem;\n      max-width: 1400px;\n      margin: 0 auto;\n      height: calc(100vh - 4rem); /* Adjust height to fit viewport */\n    }\n\n    @media (max-width: 960px) {\n      .messages-wrapper {\n        grid-template-columns: 1fr;\n        height: auto;\n      }\n      .conversation-list-card {\n        height: auto;\n        min-height: 300px;\n      }\n      .chat-area-card {\n        height: 600px; /* Fixed height for chat on small screens */\n      }\n    }\n\n    .conversation-list-card, .chat-area-card {\n      display: flex;\n      flex-direction: column;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .conversation-list-card mat-card-header {\n      padding-bottom: 0;\n    }\n\n    .card-title-with-icon {\n      display: flex;\n      align-items: center;\n      font-size: 1.4rem;\n      font-weight: 600;\n    }\n\n    .card-title-with-icon mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .conversation-search {\n      padding: 1rem 1.5rem 0.5rem;\n    }\n\n    .conversation-search .full-width {\n      width: 100%;\n    }\n\n    .conversations-scroll-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n\n    .mat-list-item {\n      height: auto !important;\n      padding: 1rem 1.5rem;\n      cursor: pointer;\n      transition: background-color 0.2s ease-in-out;\n    }\n\n    .mat-list-item:hover {\n      background-color: #f5f5f5;\n    }\n\n    .mat-list-item.selected-conversation {\n      background-color: #ede7f6; /* Light purple */\n      border-left: 4px solid #673ab7; /* Purple */\n    }\n\n    .avatar-placeholder {\n      width: 48px;\n      height: 48px;\n      border-radius: 50%;\n      background-color: #e1bee7; /* Light purple */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 1.1rem;\n      font-weight: bold;\n      color: #8e24aa; /* Dark purple */\n      flex-shrink: 0;\n    }\n\n    .conversation-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 500;\n      font-size: 1rem;\n    }\n\n    .conversation-last-message {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-size: 0.85rem;\n      color: #777;\n      margin-top: 0.2rem;\n    }\n\n    .message-content {\n      flex-grow: 1;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      margin-right: 0.5rem;\n    }\n\n    .message-date {\n      flex-shrink: 0;\n    }\n\n    .mat-chip {\n      font-size: 0.7rem;\n      padding: 0.2rem 0.5rem;\n      height: auto;\n    }\n\n    .role-chip-Client { background-color: #e0e0e0; color: #424242; }\n    .role-chip-Formateur { background-color: #bbdefb; color: #1976d2; }\n    .role-chip-Admin { background-color: #e1bee7; color: #8e24aa; }\n\n    .no-conversations {\n      text-align: center;\n      padding: 2rem;\n      color: #999;\n    }\n\n    .no-conversations mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    /* Chat Area */\n    .chat-area-card {\n      background-color: #fff;\n    }\n\n    .chat-header {\n      border-bottom: 1px solid #eee;\n      padding: 1rem 1.5rem;\n    }\n\n    .chat-header-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .chat-header-info mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .messages-display-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 1.5rem;\n      background-color: #fcfcfc;\n    }\n\n    .message-bubble-wrapper {\n      display: flex;\n      margin-bottom: 1rem;\n    }\n\n    .message-bubble-wrapper.my-message {\n      justify-content: flex-end;\n    }\n\n    .message-bubble-wrapper.other-message {\n      justify-content: flex-start;\n    }\n\n    .message-bubble {\n      max-width: 70%;\n      padding: 0.8rem 1.2rem;\n      border-radius: 18px;\n      position: relative;\n      word-wrap: break-word;\n    }\n\n    .my-message .message-bubble {\n      background-color: #673ab7; /* Purple */\n      color: white;\n      border-bottom-right-radius: 4px;\n    }\n\n    .other-message .message-bubble {\n      background-color: #e0e0e0; /* Light gray */\n      color: #333;\n      border-bottom-left-radius: 4px;\n    }\n\n    .message-bubble p {\n      margin: 0;\n      font-size: 0.95rem;\n      line-height: 1.4;\n    }\n\n    .message-timestamp {\n      font-size: 0.75rem;\n      margin-top: 0.5rem;\n      display: block;\n      text-align: right;\n      color: rgba(255, 255, 255, 0.7); /* For my messages */\n    }\n\n    .other-message .message-timestamp {\n      color: #777;\n    }\n\n    .message-input-area {\n      border-top: 1px solid #eee;\n      padding: 1rem 1.5rem;\n      display: flex;\n      align-items: flex-end;\n      gap: 0.8rem;\n    }\n\n    .message-input-field {\n      flex-grow: 1;\n    }\n\n    .message-input-field textarea {\n      min-height: 40px;\n      max-height: 120px;\n      overflow-y: auto;\n    }\n\n    .message-input-area button {\n      flex-shrink: 0;\n    }\n\n    .no-conversation-selected {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      color: #999;\n      text-align: center;\n    }\n\n    .no-conversation-selected mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n    }\n  `,\n  ],\n})\nexport class MessagesComponent implements OnInit {\n  conversations: ConversationPreview[] = []\n  filteredConversations: ConversationPreview[] = []\n  selectedConversationId: number | null = null\n  messages: Message[] = []\n  newMessageContent = \"\"\n  searchTerm = \"\"\n  currentUser: User | null = null\n  selectedParticipant: ConversationPreview | null = null\n\n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (this.currentUser) {\n        this.loadConversations()\n      }\n    })\n  }\n\n  loadConversations(): void {\n    // Mock data for demonstration\n    this.conversations = [\n      {\n        participantId: 2, // Formateur Jean Dupont\n        participantName: \"Jean Dupont\",\n        participantRole: \"Formateur\",\n        lastMessageContent: \"Bonjour, avez-vous des questions sur le cours React ?\",\n        lastMessageDate: new Date(\"2024-01-15T10:30:00Z\"),\n        unreadCount: 2,\n      },\n      {\n        participantId: 3, // Client Sophie Bernard\n        participantName: \"Sophie Bernard\",\n        participantRole: \"Client\",\n        lastMessageContent: \"Merci pour votre aide !\",\n        lastMessageDate: new Date(\"2024-01-14T15:45:00Z\"),\n        unreadCount: 0,\n      },\n      {\n        participantId: 4, // Admin Admin System\n        participantName: \"Admin System\",\n        participantRole: \"Admin\",\n        lastMessageContent: \"Votre demande a été traitée.\",\n        lastMessageDate: new Date(\"2024-01-13T11:00:00Z\"),\n        unreadCount: 0,\n      },\n    ]\n    this.applySearch()\n\n    // Uncomment to fetch from API\n    /*\n    // This would require a backend endpoint to get conversation previews for a user\n    // For example: this.messageService.getConversations(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  applySearch(): void {\n    this.filteredConversations = this.conversations.filter((conv) =>\n      conv.participantName.toLowerCase().includes(this.searchTerm.toLowerCase()),\n    )\n  }\n\n  selectConversation(participantId: number): void {\n    this.selectedConversationId = participantId\n    this.selectedParticipant = this.conversations.find((c) => c.participantId === participantId) || null\n    this.loadMessages(participantId)\n  }\n\n  loadMessages(participantId: number): void {\n    if (!this.currentUser) return\n\n    // Mock messages for the selected conversation\n    this.messages = [\n      {\n        id: 1,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu: \"Bonjour, j'espère que vous appréciez le cours React !\",\n        dateEnvoi: new Date(\"2024-01-15T09:00:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n      {\n        id: 2,\n        expediteurId: this.currentUser.id,\n        destinataireId: participantId,\n        contenu: \"Bonjour Jean, oui c'est très intéressant ! J'ai une question sur les hooks.\",\n        dateEnvoi: new Date(\"2024-01-15T09:15:00Z\"),\n        expediteur: this.currentUser,\n        destinataire: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n      },\n      {\n        id: 3,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu:\n          \"Parfait ! N'hésitez pas à me poser toutes vos questions. Les hooks peuvent sembler complexes au début.\",\n        dateEnvoi: new Date(\"2024-01-15T09:30:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n      {\n        id: 4,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu: \"Bonjour, avez-vous des questions sur le cours React ?\",\n        dateEnvoi: new Date(\"2024-01-15T10:30:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n    ]\n\n    // Uncomment to fetch from API\n    /*\n    this.messageService.getMessages(this.currentUser.id, participantId).subscribe({\n      next: (data) => {\n        this.messages = data;\n        // Mark messages as read\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des messages.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  sendMessage(event?: Event): void {\n    const keyboardEvent = event as KeyboardEvent;\n    if (keyboardEvent && keyboardEvent.key === \"Enter\" && !keyboardEvent.shiftKey) {\n      keyboardEvent.preventDefault()\n    } else if (keyboardEvent && keyboardEvent.key !== \"Enter\") {\n      return // Only proceed on Enter key press\n    }\n\n    if (!this.newMessageContent.trim() || !this.selectedConversationId || !this.currentUser) return\n\n    const message: Message = {\n      expediteurId: this.currentUser.id,\n      destinataireId: this.selectedConversationId,\n      contenu: this.newMessageContent,\n      dateEnvoi: new Date(),\n    }\n\n    // Mock message sending\n    this.messages.push(message)\n    this.newMessageContent = \"\"\n    this.snackBar.open(\"Message envoyé (simulé) !\", \"Fermer\", { duration: 1000 })\n\n    // Uncomment to send via API\n    /*\n    this.messageService.envoyerMessage(message).subscribe({\n      next: (res) => {\n        this.messages.push(message); // Add to local list after successful send\n        this.newMessageContent = '';\n        this.snackBar.open('Message envoyé !', 'Fermer', { duration: 1000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de l\\'envoi du message.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  getInitials(name: string): string {\n    const parts = name.split(\" \")\n    if (parts.length >= 2) {\n      return `${parts[0][0]}${parts[1][0]}`.toUpperCase()\n    } else if (parts.length === 1 && parts[0].length > 0) {\n      return parts[0][0].toUpperCase()\n    }\n    return \"\"\n  }\n\n  formatDate(date: Date): string {\n    const d = new Date(date)\n    const now = new Date()\n    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60)\n\n    if (diffInHours < 24 && d.getDate() === now.getDate()) {\n      return d.toLocaleTimeString(\"fr-FR\", { hour: \"2-digit\", minute: \"2-digit\" })\n    } else if (diffInHours < 48 && d.getDate() === now.getDate() - 1) {\n      return \"Hier\"\n    } else {\n      return d.toLocaleDateString(\"fr-FR\", { day: \"2-digit\", month: \"2-digit\" })\n    }\n  }\n\n  getRoleChipClass(role: string): string {\n    switch (role) {\n      case \"Client\":\n        return \"role-chip-Client\"\n      case \"Formateur\":\n        return \"role-chip-Formateur\"\n      case \"Admin\":\n        return \"role-chip-Admin\"\n      default:\n        return \"\"\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;IAsDcA,EAAA,CAAAC,cAAA,2BAA+D;IAC7BD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAAjCH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,WAAA,CAAsB;;;;;;;;;;;IAjB1DP,EAAA,CAAAC,cAAA,wBAEoG;IADrFD,EAAA,CAAAQ,UAAA,mBAAAC,2EAAA;MAAA,MAAAC,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,OAAA,GAAAI,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,kBAAA,CAAAX,OAAA,CAAAY,aAAA,CAAsC;IAAA,EAAC;IAE7DlB,EAAA,CAAAC,cAAA,cAAkD;IAC1CD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EAAA,CAAAC,cAAA,cAAiD;IACzCD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,uBAAkB;IAC2CD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAGpGH,EAAA,CAAAC,cAAA,eAAuD;IACvBD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE1EH,EAAA,CAAAmB,UAAA,KAAAC,+DAAA,+BAEmB;IACnBpB,EAAA,CAAAqB,SAAA,mBAA2B;IAC7BrB,EAAA,CAAAG,YAAA,EAAgB;;;;;IAlBDH,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,sBAAA,KAAApB,OAAA,CAAAY,aAAA,EAAoF;IAEzFlB,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAoB,MAAA,CAAAE,WAAA,CAAArB,OAAA,CAAAsB,eAAA,EAAuC;IAGvC5B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAsB,eAAA,CAA0B;IAEpB5B,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAA6B,UAAA,CAAAJ,MAAA,CAAAK,gBAAA,CAAAxB,OAAA,CAAAyB,eAAA,EAAgD;IAAC/B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAyB,eAAA,CAA0B;IAIzD/B,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAA0B,kBAAA,CAA6B;IAChChC,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAoB,MAAA,CAAAQ,UAAA,CAAA3B,OAAA,CAAA4B,eAAA,EAAsC;IAEhDlC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAsB,UAAA,SAAAhB,OAAA,CAAAC,WAAA,KAA0B;;;;;IAK/CP,EAAA,CAAAC,cAAA,cAAyE;IAC7DD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;IAyBnCH,EAAA,CAAAC,cAAA,cAE6H;IAE9FD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChDH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHtEH,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAmC,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,YAAA,MAAAC,OAAA,CAAAC,WAAA,kBAAAD,OAAA,CAAAC,WAAA,CAAAC,EAAA,GAAAJ,OAAA,CAAAC,YAAA,MAAAC,OAAA,CAAAC,WAAA,kBAAAD,OAAA,CAAAC,WAAA,CAAAC,EAAA,GAAuH;IAE7FzC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAgC,OAAA,CAAAK,OAAA,CAAiB;IACZ1C,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAkC,OAAA,CAAAN,UAAA,CAAAI,OAAA,CAAAM,SAAA,EAAgC;;;;;;IAvBxE3C,EAAA,CAAA4C,uBAAA,GAA0E;IACxE5C,EAAA,CAAAC,cAAA,0BAAqC;IAGzBD,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5EH,EAAA,CAAAC,cAAA,UAAK;IACaD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAC3EH,EAAA,CAAAC,cAAA,uBAAkB;IAEdD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAMnBH,EAAA,CAAAC,cAAA,4BAAgD;IAC9CD,EAAA,CAAAmB,UAAA,KAAA0B,iDAAA,kBAOM;IACR7C,EAAA,CAAAG,YAAA,EAAmB;IAEnBH,EAAA,CAAAC,cAAA,4BAA6C;IAG/BD,EAAA,CAAAQ,UAAA,2BAAAsC,8EAAAC,MAAA;MAAA/C,EAAA,CAAAW,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAiC,OAAA,CAAAC,iBAAA,GAAAH,MAAA;IAAA,EAA+B,2BAAAI,8EAAAJ,MAAA;MAAA/C,EAAA,CAAAW,aAAA,CAAAqC,IAAA;MAAA,MAAAI,OAAA,GAAApD,EAAA,CAAAe,aAAA;MAAA,OACdf,EAAA,CAAAgB,WAAA,CAAAoC,OAAA,CAAAC,WAAA,CAAAN,MAAA,CAAmB;IAAA,EADL;IAEtB/C,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,kBAAoG;IAA/DD,EAAA,CAAAQ,UAAA,mBAAA8C,oEAAA;MAAAtD,EAAA,CAAAW,aAAA,CAAAqC,IAAA;MAAA,MAAAO,OAAA,GAAAvD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuC,OAAA,CAAAF,WAAA,EAAa;IAAA,EAAC;IAC1DrD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAG/BH,EAAA,CAAAwD,qBAAA,EAAe;;;;IAnCDxD,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAK,iBAAA,CAAAoD,MAAA,CAAA9B,WAAA,EAAA8B,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA9B,eAAA,SAA6D;IAGnD5B,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAoD,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA9B,eAAA,CAA0C;IAE9C5B,EAAA,CAAAI,SAAA,GAA4E;IAA5EJ,EAAA,CAAA6B,UAAA,CAAA4B,MAAA,CAAA3B,gBAAA,EAAA2B,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA3B,eAAA,eAA4E;IACpF/B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2D,kBAAA,MAAAF,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAA3B,eAAA,MACF;IAOe/B,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAsB,UAAA,YAAAmC,MAAA,CAAAG,QAAA,CAAW;IAapB5D,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAsB,UAAA,YAAAmC,MAAA,CAAAP,iBAAA,CAA+B;IAIkBlD,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAsB,UAAA,cAAAmC,MAAA,CAAAP,iBAAA,CAAAW,IAAA,GAAsC;;;;;IAOrG7D,EAAA,CAAAC,cAAA,cAAsC;IAC1BD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AAuRjE,OAAM,MAAO2D,iBAAiB;EAU5BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAZlB,KAAAC,aAAa,GAA0B,EAAE;IACzC,KAAAC,qBAAqB,GAA0B,EAAE;IACjD,KAAA1C,sBAAsB,GAAkB,IAAI;IAC5C,KAAAkC,QAAQ,GAAc,EAAE;IACxB,KAAAV,iBAAiB,GAAG,EAAE;IACtB,KAAAmB,UAAU,GAAG,EAAE;IACf,KAAA7B,WAAW,GAAgB,IAAI;IAC/B,KAAAkB,mBAAmB,GAA+B,IAAI;EAMnD;EAEHY,QAAQA,CAAA;IACN,IAAI,CAACL,WAAW,CAACM,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACjC,WAAW,GAAGiC,IAAI;MACvB,IAAI,IAAI,CAACjC,WAAW,EAAE;QACpB,IAAI,CAACkC,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACP,aAAa,GAAG,CACnB;MACEjD,aAAa,EAAE,CAAC;MAChBU,eAAe,EAAE,aAAa;MAC9BG,eAAe,EAAE,WAAW;MAC5BC,kBAAkB,EAAE,uDAAuD;MAC3EE,eAAe,EAAE,IAAIyC,IAAI,CAAC,sBAAsB,CAAC;MACjDpE,WAAW,EAAE;KACd,EACD;MACEW,aAAa,EAAE,CAAC;MAChBU,eAAe,EAAE,gBAAgB;MACjCG,eAAe,EAAE,QAAQ;MACzBC,kBAAkB,EAAE,yBAAyB;MAC7CE,eAAe,EAAE,IAAIyC,IAAI,CAAC,sBAAsB,CAAC;MACjDpE,WAAW,EAAE;KACd,EACD;MACEW,aAAa,EAAE,CAAC;MAChBU,eAAe,EAAE,cAAc;MAC/BG,eAAe,EAAE,OAAO;MACxBC,kBAAkB,EAAE,8BAA8B;MAClDE,eAAe,EAAE,IAAIyC,IAAI,CAAC,sBAAsB,CAAC;MACjDpE,WAAW,EAAE;KACd,CACF;IACD,IAAI,CAACqE,WAAW,EAAE;IAElB;IACA;;;;EAIF;;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACR,qBAAqB,GAAG,IAAI,CAACD,aAAa,CAACU,MAAM,CAAEC,IAAI,IAC1DA,IAAI,CAAClD,eAAe,CAACmD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACX,UAAU,CAACU,WAAW,EAAE,CAAC,CAC3E;EACH;EAEA9D,kBAAkBA,CAACC,aAAqB;IACtC,IAAI,CAACQ,sBAAsB,GAAGR,aAAa;IAC3C,IAAI,CAACwC,mBAAmB,GAAG,IAAI,CAACS,aAAa,CAACc,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAChE,aAAa,KAAKA,aAAa,CAAC,IAAI,IAAI;IACpG,IAAI,CAACiE,YAAY,CAACjE,aAAa,CAAC;EAClC;EAEAiE,YAAYA,CAACjE,aAAqB;IAChC,IAAI,CAAC,IAAI,CAACsB,WAAW,EAAE;IAEvB;IACA,IAAI,CAACoB,QAAQ,GAAG,CACd;MACEnB,EAAE,EAAE,CAAC;MACLH,YAAY,EAAEpB,aAAa;MAC3BkE,cAAc,EAAE,IAAI,CAAC5C,WAAW,CAACC,EAAE;MACnCC,OAAO,EAAE,uDAAuD;MAChEC,SAAS,EAAE,IAAIgC,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE;QAAE5C,EAAE,EAAEvB,aAAa;QAAEoE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAAClD;KACpB,EACD;MACEC,EAAE,EAAE,CAAC;MACLH,YAAY,EAAE,IAAI,CAACE,WAAW,CAACC,EAAE;MACjC2C,cAAc,EAAElE,aAAa;MAC7BwB,OAAO,EAAE,6EAA6E;MACtFC,SAAS,EAAE,IAAIgC,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE,IAAI,CAAC7C,WAAW;MAC5BkD,YAAY,EAAE;QAAEjD,EAAE,EAAEvB,aAAa;QAAEoE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW;KAC/F,EACD;MACEhD,EAAE,EAAE,CAAC;MACLH,YAAY,EAAEpB,aAAa;MAC3BkE,cAAc,EAAE,IAAI,CAAC5C,WAAW,CAACC,EAAE;MACnCC,OAAO,EACL,wGAAwG;MAC1GC,SAAS,EAAE,IAAIgC,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE;QAAE5C,EAAE,EAAEvB,aAAa;QAAEoE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAAClD;KACpB,EACD;MACEC,EAAE,EAAE,CAAC;MACLH,YAAY,EAAEpB,aAAa;MAC3BkE,cAAc,EAAE,IAAI,CAAC5C,WAAW,CAACC,EAAE;MACnCC,OAAO,EAAE,uDAAuD;MAChEC,SAAS,EAAE,IAAIgC,IAAI,CAAC,sBAAsB,CAAC;MAC3CU,UAAU,EAAE;QAAE5C,EAAE,EAAEvB,aAAa;QAAEoE,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAAClD;KACpB,CACF;IAED;IACA;;;;;;;;;;;;EAYF;;EAEAa,WAAWA,CAACsC,KAAa;IACvB,MAAMC,aAAa,GAAGD,KAAsB;IAC5C,IAAIC,aAAa,IAAIA,aAAa,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,aAAa,CAACE,QAAQ,EAAE;MAC7EF,aAAa,CAACG,cAAc,EAAE;KAC/B,MAAM,IAAIH,aAAa,IAAIA,aAAa,CAACC,GAAG,KAAK,OAAO,EAAE;MACzD,OAAM,CAAC;;;IAGT,IAAI,CAAC,IAAI,CAAC3C,iBAAiB,CAACW,IAAI,EAAE,IAAI,CAAC,IAAI,CAACnC,sBAAsB,IAAI,CAAC,IAAI,CAACc,WAAW,EAAE;IAEzF,MAAMwD,OAAO,GAAY;MACvB1D,YAAY,EAAE,IAAI,CAACE,WAAW,CAACC,EAAE;MACjC2C,cAAc,EAAE,IAAI,CAAC1D,sBAAsB;MAC3CgB,OAAO,EAAE,IAAI,CAACQ,iBAAiB;MAC/BP,SAAS,EAAE,IAAIgC,IAAI;KACpB;IAED;IACA,IAAI,CAACf,QAAQ,CAACqC,IAAI,CAACD,OAAO,CAAC;IAC3B,IAAI,CAAC9C,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACgB,QAAQ,CAACgC,IAAI,CAAC,2BAA2B,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAE7E;IACA;;;;;;;;;;;;;EAaF;;EAEAxE,WAAWA,CAACyE,IAAY;IACtB,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;MACrB,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACG,WAAW,EAAE;KACpD,MAAM,IAAIH,KAAK,CAACE,MAAM,KAAK,CAAC,IAAIF,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;MACpD,OAAOF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACG,WAAW,EAAE;;IAElC,OAAO,EAAE;EACX;EAEAvE,UAAUA,CAACwE,IAAU;IACnB,MAAMC,CAAC,GAAG,IAAI/B,IAAI,CAAC8B,IAAI,CAAC;IACxB,MAAME,GAAG,GAAG,IAAIhC,IAAI,EAAE;IACtB,MAAMiC,WAAW,GAAG,CAACD,GAAG,CAACE,OAAO,EAAE,GAAGH,CAAC,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEpE,IAAID,WAAW,GAAG,EAAE,IAAIF,CAAC,CAACI,OAAO,EAAE,KAAKH,GAAG,CAACG,OAAO,EAAE,EAAE;MACrD,OAAOJ,CAAC,CAACK,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE,CAAC;KAC7E,MAAM,IAAIL,WAAW,GAAG,EAAE,IAAIF,CAAC,CAACI,OAAO,EAAE,KAAKH,GAAG,CAACG,OAAO,EAAE,GAAG,CAAC,EAAE;MAChE,OAAO,MAAM;KACd,MAAM;MACL,OAAOJ,CAAC,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAS,CAAE,CAAC;;EAE9E;EAEAtF,gBAAgBA,CAAC2D,IAAY;IAC3B,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,kBAAkB;MAC3B,KAAK,WAAW;QACd,OAAO,qBAAqB;MAC9B,KAAK,OAAO;QACV,OAAO,iBAAiB;MAC1B;QACE,OAAO,EAAE;;EAEf;;;uBA9MW3B,iBAAiB,EAAA9D,EAAA,CAAAqH,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvH,EAAA,CAAAqH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzH,EAAA,CAAAqH,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjB7D,iBAAiB;MAAA8D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApX1BlI,EAAA,CAAAC,cAAA,aAAgC;UAMZD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,gBAAwB;UAAUD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAElDH,EAAA,CAAAC,cAAA,2BAA8C;UAE/BD,EAAA,CAAAE,MAAA,sCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrDH,EAAA,CAAAC,cAAA,gBAAiE;UAAjDD,EAAA,CAAAQ,UAAA,2BAAA4H,2DAAArF,MAAA;YAAA,OAAAoF,GAAA,CAAA9D,UAAA,GAAAtB,MAAA;UAAA,EAAwB,mBAAAsF,mDAAA;YAAA,OAAUF,GAAA,CAAAvD,WAAA,EAAa;UAAA,EAAvB;UAAxC5E,EAAA,CAAAG,YAAA,EAAiE;UACjEH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,uBAAgD;UAC9CD,EAAA,CAAAmB,UAAA,KAAAmH,2CAAA,8BAoBgB;UAChBtI,EAAA,CAAAmB,UAAA,KAAAoH,iCAAA,kBAGM;UACRvI,EAAA,CAAAG,YAAA,EAAe;UAIjBH,EAAA,CAAAC,cAAA,oBAAiC;UAC/BD,EAAA,CAAAmB,UAAA,KAAAqH,0CAAA,4BAuCe;UAEfxI,EAAA,CAAAmB,UAAA,KAAAsH,yCAAA,iCAAAzI,EAAA,CAAA0I,sBAAA,CAKc;UAChB1I,EAAA,CAAAG,YAAA,EAAW;;;;UAlFWH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAsB,UAAA,YAAA6G,GAAA,CAAA9D,UAAA,CAAwB;UAKVrE,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAsB,UAAA,YAAA6G,GAAA,CAAA/D,qBAAA,CAAwB;UAqBlDpE,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAsB,UAAA,SAAA6G,GAAA,CAAA/D,qBAAA,CAAAmC,MAAA,OAAwC;UASjCvG,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAsB,UAAA,SAAA6G,GAAA,CAAAzG,sBAAA,CAA8B,aAAAiH,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}