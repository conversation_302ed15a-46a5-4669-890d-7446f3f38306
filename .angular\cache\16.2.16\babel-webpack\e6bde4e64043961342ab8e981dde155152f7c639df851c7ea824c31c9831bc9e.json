{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from \"@angular/core\";\nexport let DashboardComponent = class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n    this.stats = {\n      totalCourses: 12,\n      totalStudents: 245,\n      totalRevenue: 15420,\n      completedCertificates: 89,\n      pendingMessages: 5\n    };\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (!user) {\n        // Redirect to login if no user is logged in\n        this.router.navigate([\"/auth/login\"]);\n      }\n    });\n    // Mock user data if not logged in (for development)\n    if (!this.currentUser) {\n      const mockUser = {\n        id: 1,\n        email: \"<EMAIL>\",\n        firstName: \"John\",\n        lastName: \"Doe\",\n        nom: \"Doe\",\n        prenom: \"<PERSON>\",\n        role: \"Formateur\" // Change this to 'Client' or 'Admin' to test different dashboards\n      };\n      // Simulate setting user and role in local storage for initial load\n      localStorage.setItem(\"token\", \"mock-jwt-token\");\n      localStorage.setItem(\"userRole\", mockUser.role);\n      this.authService[\"currentUserSubject\"].next(mockUser); // Directly update subject for mock\n      this.currentUser = mockUser;\n    }\n  }\n  logout() {\n    this.authService.logout();\n  }\n  getRoleTitle() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Tableau de bord Étudiant\";\n      case \"Formateur\":\n        return \"Tableau de bord Formateur\";\n      case \"Admin\":\n        return \"Tableau de bord Administrateur\";\n      default:\n        return \"Tableau de bord\";\n    }\n  }\n  getStatsForRole() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return [{\n          title: \"Cours inscrits\",\n          value: this.stats.totalCourses,\n          icon: \"book\",\n          color: \"blue\"\n        }, {\n          title: \"Certificats obtenus\",\n          value: this.stats.completedCertificates,\n          icon: \"emoji_events\",\n          color: \"yellow\"\n        }, {\n          title: \"Messages\",\n          value: this.stats.pendingMessages,\n          icon: \"message\",\n          color: \"green\"\n        }];\n      case \"Formateur\":\n        return [{\n          title: \"Cours publiés\",\n          value: this.stats.totalCourses,\n          icon: \"book\",\n          color: \"blue\"\n        }, {\n          title: \"Total étudiants\",\n          value: this.stats.totalStudents,\n          icon: \"people\",\n          color: \"purple\"\n        }, {\n          title: \"Revenus (€)\",\n          value: this.stats.totalRevenue,\n          icon: \"euro_symbol\",\n          color: \"green\"\n        }, {\n          title: \"Certificats émis\",\n          value: this.stats.completedCertificates,\n          icon: \"emoji_events\",\n          color: \"yellow\"\n        }];\n      case \"Admin\":\n        return [{\n          title: \"Total cours\",\n          value: this.stats.totalCourses,\n          icon: \"book\",\n          color: \"blue\"\n        }, {\n          title: \"Total utilisateurs\",\n          value: this.stats.totalStudents,\n          icon: \"people\",\n          color: \"purple\"\n        }, {\n          title: \"Revenus plateforme (€)\",\n          value: this.stats.totalRevenue,\n          icon: \"trending_up\",\n          color: \"green\"\n        }, {\n          title: \"Certificats générés\",\n          value: this.stats.completedCertificates,\n          icon: \"emoji_events\",\n          color: \"yellow\"\n        }];\n      default:\n        return [];\n    }\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: \"app-dashboard\",\n  template: `\n    <div class=\"dashboard-container\">\n      <!-- Header -->\n      <mat-toolbar color=\"primary\" class=\"dashboard-header\">\n        <div class=\"header-content\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <span>Training Platform</span>\n          </div>\n          <div class=\"user-info\">\n            <mat-chip-set>\n              <mat-chip class=\"role-chip\">{{ currentUser?.role }}</mat-chip>\n            </mat-chip-set>\n            <button mat-button (click)=\"logout()\">\n              <mat-icon>logout</mat-icon>\n              Déconnexion\n            </button>\n          </div>\n        </div>\n      </mat-toolbar>\n\n      <!-- Main Content -->\n      <div class=\"dashboard-content\">\n        <div class=\"welcome-section\">\n          <h1>{{ getRoleTitle() }}</h1>\n          <p>Bienvenue ! Voici un aperçu de votre activité.</p>\n        </div>\n\n        <!-- Stats Grid -->\n        <div class=\"stats-grid\">\n          <mat-card *ngFor=\"let stat of getStatsForRole()\" class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-info\">\n                  <p class=\"stat-label\">{{ stat.title }}</p>\n                  <p class=\"stat-value\">{{ stat.value }}</p>\n                </div>\n                <mat-icon [class]=\"stat.color\" class=\"stat-icon\">{{ stat.icon }}</mat-icon>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Quick Actions & Recent Activity -->\n        <div class=\"dashboard-grid\">\n          <mat-card class=\"actions-card\">\n            <mat-card-header>\n              <mat-card-title>Actions rapides</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"actions-list\">\n                <ng-container *ngIf=\"currentUser?.role === 'Formateur'\">\n                  <button mat-stroked-button routerLink=\"/courses/create\" class=\"action-btn\">\n                    <mat-icon>add</mat-icon>\n                    Créer un nouveau cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/dashboard/students\" class=\"action-btn\">\n                    <mat-icon>people</mat-icon>\n                    Voir les étudiants\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Client'\">\n                  <button mat-stroked-button routerLink=\"/courses\" class=\"action-btn\">\n                    <mat-icon>book</mat-icon>\n                    Parcourir les cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/certificates\" class=\"action-btn\">\n                    <mat-icon>emoji_events</mat-icon>\n                    Voir mes certificats\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Admin'\">\n                  <button mat-stroked-button routerLink=\"/admin/users\" class=\"action-btn\">\n                    <mat-icon>admin_panel_settings</mat-icon>\n                    Gérer les utilisateurs\n                  </button>\n                  <button mat-stroked-button routerLink=\"/admin/analytics\" class=\"action-btn\">\n                    <mat-icon>analytics</mat-icon>\n                    Voir les analyses\n                  </button>\n                </ng-container>\n                \n                <button mat-stroked-button routerLink=\"/messages\" class=\"action-btn\">\n                  <mat-icon>message</mat-icon>\n                  Messages\n                </button>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"activity-card\">\n            <mat-card-header>\n              <mat-card-title>Activité récente</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"activity-list\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot green\"></div>\n                  <p>Nouvel étudiant inscrit à \"React Fundamentals\"</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot blue\"></div>\n                  <p>Certificat généré pour John Doe</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot yellow\"></div>\n                  <p>Paiement reçu : 299€</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot purple\"></div>\n                  <p>Nouveau message d'un étudiant</p>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .dashboard-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n    }\n\n    .dashboard-header {\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n    }\n\n    .header-content {\n      width: 100%;\n      max-width: 1200px;\n      margin: 0 auto;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 1.5rem;\n      font-weight: bold;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .role-chip {\n      text-transform: capitalize;\n    }\n\n    .dashboard-content {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 2rem;\n    }\n\n    .welcome-section {\n      margin-bottom: 2rem;\n    }\n\n    .welcome-section h1 {\n      font-size: 2rem;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .welcome-section p {\n      color: #666;\n      font-size: 1.1rem;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1.5rem;\n      margin-bottom: 2rem;\n    }\n\n    .stat-card {\n      padding: 1rem;\n    }\n\n    .stat-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .stat-label {\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.5rem;\n    }\n\n    .stat-value {\n      font-size: 2rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .stat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .stat-icon.blue { color: #2196f3; }\n    .stat-icon.purple { color: #9c27b0; }\n    .stat-icon.green { color: #4caf50; }\n    .stat-icon.yellow { color: #ff9800; }\n\n    .dashboard-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 2rem;\n    }\n\n    .actions-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .action-btn {\n      justify-content: flex-start;\n      padding: 1rem;\n      text-align: left;\n    }\n\n    .action-btn mat-icon {\n      margin-right: 1rem;\n    }\n\n    .activity-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .activity-item {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .activity-dot {\n      width: 8px;\n      height: 8px;\n      border-radius: 50%;\n    }\n\n    .activity-dot.green { background-color: #4caf50; }\n    .activity-dot.blue { background-color: #2196f3; }\n    .activity-dot.yellow { background-color: #ff9800; }\n    .activity-dot.purple { background-color: #9c27b0; }\n\n    .activity-item p {\n      margin: 0;\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .dashboard-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})], DashboardComponent);", "map": {"version": 3, "names": ["Component", "DashboardComponent", "constructor", "authService", "router", "currentUser", "stats", "totalCourses", "totalStudents", "totalRevenue", "completedCertificates", "pendingMessages", "ngOnInit", "currentUser$", "subscribe", "user", "navigate", "mockUser", "id", "email", "firstName", "lastName", "nom", "prenom", "role", "localStorage", "setItem", "next", "logout", "getRoleTitle", "getStatsForRole", "title", "value", "icon", "color", "__decorate", "selector", "template", "styles"], "sources": ["C:\\e-learning\\src\\app\\features\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, type OnInit } from \"@angular/core\"\nimport type { Router } from \"@angular/router\"\nimport type { AuthService } from \"../../core/services/auth.service\"\nimport type { User } from \"../../core/models/user.model\"\n\ninterface DashboardStats {\n  totalCourses: number\n  totalStudents: number\n  totalRevenue: number\n  completedCertificates: number\n  pendingMessages: number\n}\n\n@Component({\n  selector: \"app-dashboard\",\n  template: `\n    <div class=\"dashboard-container\">\n      <!-- Header -->\n      <mat-toolbar color=\"primary\" class=\"dashboard-header\">\n        <div class=\"header-content\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <span>Training Platform</span>\n          </div>\n          <div class=\"user-info\">\n            <mat-chip-set>\n              <mat-chip class=\"role-chip\">{{ currentUser?.role }}</mat-chip>\n            </mat-chip-set>\n            <button mat-button (click)=\"logout()\">\n              <mat-icon>logout</mat-icon>\n              Déconnexion\n            </button>\n          </div>\n        </div>\n      </mat-toolbar>\n\n      <!-- Main Content -->\n      <div class=\"dashboard-content\">\n        <div class=\"welcome-section\">\n          <h1>{{ getRoleTitle() }}</h1>\n          <p>Bienvenue ! Voici un aperçu de votre activité.</p>\n        </div>\n\n        <!-- Stats Grid -->\n        <div class=\"stats-grid\">\n          <mat-card *ngFor=\"let stat of getStatsForRole()\" class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-info\">\n                  <p class=\"stat-label\">{{ stat.title }}</p>\n                  <p class=\"stat-value\">{{ stat.value }}</p>\n                </div>\n                <mat-icon [class]=\"stat.color\" class=\"stat-icon\">{{ stat.icon }}</mat-icon>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Quick Actions & Recent Activity -->\n        <div class=\"dashboard-grid\">\n          <mat-card class=\"actions-card\">\n            <mat-card-header>\n              <mat-card-title>Actions rapides</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"actions-list\">\n                <ng-container *ngIf=\"currentUser?.role === 'Formateur'\">\n                  <button mat-stroked-button routerLink=\"/courses/create\" class=\"action-btn\">\n                    <mat-icon>add</mat-icon>\n                    Créer un nouveau cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/dashboard/students\" class=\"action-btn\">\n                    <mat-icon>people</mat-icon>\n                    Voir les étudiants\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Client'\">\n                  <button mat-stroked-button routerLink=\"/courses\" class=\"action-btn\">\n                    <mat-icon>book</mat-icon>\n                    Parcourir les cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/certificates\" class=\"action-btn\">\n                    <mat-icon>emoji_events</mat-icon>\n                    Voir mes certificats\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Admin'\">\n                  <button mat-stroked-button routerLink=\"/admin/users\" class=\"action-btn\">\n                    <mat-icon>admin_panel_settings</mat-icon>\n                    Gérer les utilisateurs\n                  </button>\n                  <button mat-stroked-button routerLink=\"/admin/analytics\" class=\"action-btn\">\n                    <mat-icon>analytics</mat-icon>\n                    Voir les analyses\n                  </button>\n                </ng-container>\n                \n                <button mat-stroked-button routerLink=\"/messages\" class=\"action-btn\">\n                  <mat-icon>message</mat-icon>\n                  Messages\n                </button>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"activity-card\">\n            <mat-card-header>\n              <mat-card-title>Activité récente</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"activity-list\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot green\"></div>\n                  <p>Nouvel étudiant inscrit à \"React Fundamentals\"</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot blue\"></div>\n                  <p>Certificat généré pour John Doe</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot yellow\"></div>\n                  <p>Paiement reçu : 299€</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot purple\"></div>\n                  <p>Nouveau message d'un étudiant</p>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .dashboard-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n    }\n\n    .dashboard-header {\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n    }\n\n    .header-content {\n      width: 100%;\n      max-width: 1200px;\n      margin: 0 auto;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 1.5rem;\n      font-weight: bold;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .role-chip {\n      text-transform: capitalize;\n    }\n\n    .dashboard-content {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 2rem;\n    }\n\n    .welcome-section {\n      margin-bottom: 2rem;\n    }\n\n    .welcome-section h1 {\n      font-size: 2rem;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .welcome-section p {\n      color: #666;\n      font-size: 1.1rem;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1.5rem;\n      margin-bottom: 2rem;\n    }\n\n    .stat-card {\n      padding: 1rem;\n    }\n\n    .stat-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .stat-label {\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.5rem;\n    }\n\n    .stat-value {\n      font-size: 2rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .stat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .stat-icon.blue { color: #2196f3; }\n    .stat-icon.purple { color: #9c27b0; }\n    .stat-icon.green { color: #4caf50; }\n    .stat-icon.yellow { color: #ff9800; }\n\n    .dashboard-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 2rem;\n    }\n\n    .actions-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .action-btn {\n      justify-content: flex-start;\n      padding: 1rem;\n      text-align: left;\n    }\n\n    .action-btn mat-icon {\n      margin-right: 1rem;\n    }\n\n    .activity-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .activity-item {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .activity-dot {\n      width: 8px;\n      height: 8px;\n      border-radius: 50%;\n    }\n\n    .activity-dot.green { background-color: #4caf50; }\n    .activity-dot.blue { background-color: #2196f3; }\n    .activity-dot.yellow { background-color: #ff9800; }\n    .activity-dot.purple { background-color: #9c27b0; }\n\n    .activity-item p {\n      margin: 0;\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .dashboard-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `,\n  ],\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: User | null = null\n  stats: DashboardStats = {\n    totalCourses: 12,\n    totalStudents: 245,\n    totalRevenue: 15420,\n    completedCertificates: 89,\n    pendingMessages: 5,\n  }\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (!user) {\n        // Redirect to login if no user is logged in\n        this.router.navigate([\"/auth/login\"])\n      }\n    })\n\n    // Mock user data if not logged in (for development)\n    if (!this.currentUser) {\n      const mockUser: User = {\n        id: 1,\n        email: \"<EMAIL>\",\n        firstName: \"John\",\n        lastName: \"Doe\",\n        nom: \"Doe\",\n        prenom: \"John\",\n        role: \"Formateur\", // Change this to 'Client' or 'Admin' to test different dashboards\n      }\n      // Simulate setting user and role in local storage for initial load\n      localStorage.setItem(\"token\", \"mock-jwt-token\")\n      localStorage.setItem(\"userRole\", mockUser.role)\n      this.authService[\"currentUserSubject\"].next(mockUser) // Directly update subject for mock\n      this.currentUser = mockUser\n    }\n  }\n\n  logout(): void {\n    this.authService.logout()\n  }\n\n  getRoleTitle(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Tableau de bord Étudiant\"\n      case \"Formateur\":\n        return \"Tableau de bord Formateur\"\n      case \"Admin\":\n        return \"Tableau de bord Administrateur\"\n      default:\n        return \"Tableau de bord\"\n    }\n  }\n\n  getStatsForRole() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return [\n          { title: \"Cours inscrits\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          {\n            title: \"Certificats obtenus\",\n            value: this.stats.completedCertificates,\n            icon: \"emoji_events\",\n            color: \"yellow\",\n          },\n          { title: \"Messages\", value: this.stats.pendingMessages, icon: \"message\", color: \"green\" },\n        ]\n      case \"Formateur\":\n        return [\n          { title: \"Cours publiés\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          { title: \"Total étudiants\", value: this.stats.totalStudents, icon: \"people\", color: \"purple\" },\n          { title: \"Revenus (€)\", value: this.stats.totalRevenue, icon: \"euro_symbol\", color: \"green\" },\n          { title: \"Certificats émis\", value: this.stats.completedCertificates, icon: \"emoji_events\", color: \"yellow\" },\n        ]\n      case \"Admin\":\n        return [\n          { title: \"Total cours\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          { title: \"Total utilisateurs\", value: this.stats.totalStudents, icon: \"people\", color: \"purple\" },\n          { title: \"Revenus plateforme (€)\", value: this.stats.totalRevenue, icon: \"trending_up\", color: \"green\" },\n          {\n            title: \"Certificats générés\",\n            value: this.stats.completedCertificates,\n            icon: \"emoji_events\",\n            color: \"yellow\",\n          },\n        ]\n      default:\n        return []\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAqB,eAAe;AA2S/C,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAU7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAXhB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,KAAK,GAAmB;MACtBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE,KAAK;MACnBC,qBAAqB,EAAE,EAAE;MACzBC,eAAe,EAAE;KAClB;EAKE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,CAACU,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACV,WAAW,GAAGU,IAAI;MACvB,IAAI,CAACA,IAAI,EAAE;QACT;QACA,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;IAEzC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACrB,MAAMY,QAAQ,GAAS;QACrBC,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,kBAAkB;QACzBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,WAAW,CAAE;OACpB;MACD;MACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC;MAC/CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAET,QAAQ,CAACO,IAAI,CAAC;MAC/C,IAAI,CAACrB,WAAW,CAAC,oBAAoB,CAAC,CAACwB,IAAI,CAACV,QAAQ,CAAC,EAAC;MACtD,IAAI,CAACZ,WAAW,GAAGY,QAAQ;;EAE/B;EAEAW,MAAMA,CAAA;IACJ,IAAI,CAACzB,WAAW,CAACyB,MAAM,EAAE;EAC3B;EAEAC,YAAYA,CAAA;IACV,QAAQ,IAAI,CAACxB,WAAW,EAAEmB,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,0BAA0B;MACnC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,OAAO;QACV,OAAO,gCAAgC;MACzC;QACE,OAAO,iBAAiB;;EAE9B;EAEAM,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACzB,WAAW,EAAEmB,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,CACL;UAAEO,KAAK,EAAE,gBAAgB;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACC,YAAY;UAAE0B,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAM,CAAE,EACxF;UACEH,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACI,qBAAqB;UACvCuB,IAAI,EAAE,cAAc;UACpBC,KAAK,EAAE;SACR,EACD;UAAEH,KAAK,EAAE,UAAU;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACK,eAAe;UAAEsB,IAAI,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAO,CAAE,CAC1F;MACH,KAAK,WAAW;QACd,OAAO,CACL;UAAEH,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACC,YAAY;UAAE0B,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAM,CAAE,EACvF;UAAEH,KAAK,EAAE,iBAAiB;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACE,aAAa;UAAEyB,IAAI,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAE,EAC9F;UAAEH,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACG,YAAY;UAAEwB,IAAI,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAO,CAAE,EAC7F;UAAEH,KAAK,EAAE,kBAAkB;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACI,qBAAqB;UAAEuB,IAAI,EAAE,cAAc;UAAEC,KAAK,EAAE;QAAQ,CAAE,CAC9G;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEH,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACC,YAAY;UAAE0B,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAM,CAAE,EACrF;UAAEH,KAAK,EAAE,oBAAoB;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACE,aAAa;UAAEyB,IAAI,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAE,EACjG;UAAEH,KAAK,EAAE,wBAAwB;UAAEC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACG,YAAY;UAAEwB,IAAI,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAO,CAAE,EACxG;UACEH,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACI,qBAAqB;UACvCuB,IAAI,EAAE,cAAc;UACpBC,KAAK,EAAE;SACR,CACF;MACH;QACE,OAAO,EAAE;;EAEf;CACD;AAhGYjC,kBAAkB,GAAAkC,UAAA,EA9R9BnC,SAAS,CAAC;EACToC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwHT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+JD;CAEF,CAAC,C,EACWrC,kBAAkB,CAgG9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}