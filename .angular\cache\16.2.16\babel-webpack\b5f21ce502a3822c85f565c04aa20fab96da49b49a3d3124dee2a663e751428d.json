{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = \"elearning-platform\";\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"app-container\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLGlCQUFBO0VBQ0EsNkRBQUE7QUFBTiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5hcHAtY29udGFpbmVyIHtcbiAgICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\e-learning\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from \"@angular/core\"\n\n@Component({\n  selector: \"app-root\",\n  template: `\n    <div class=\"app-container\">\n      <router-outlet></router-outlet>\n    </div>\n  `,\n  styles: [\n    `\n    .app-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n  `,\n  ],\n})\nexport class AppComponent {\n  title = \"elearning-platform\"\n}\n"], "mappings": ";;AAkBA,OAAM,MAAOA,YAAY;EAhBzBC,YAAA;IAiBE,KAAAC,KAAK,GAAG,oBAAoB;;;;uBADjBF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAbrBE,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAAE,SAAA,oBAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}