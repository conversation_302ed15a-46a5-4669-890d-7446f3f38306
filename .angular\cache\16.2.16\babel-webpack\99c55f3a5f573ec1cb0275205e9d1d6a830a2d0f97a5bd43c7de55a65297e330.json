{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from \"@angular/core\";\nexport let CourseListComponent = class CourseListComponent {\n  constructor(courseService, snackBar, router) {\n    this.courseService = courseService;\n    this.snackBar = snackBar;\n    this.router = router;\n    this.courses = [];\n    this.filteredCourses = [];\n    this.searchTerm = \"\";\n    this.filterLevel = \"all\";\n    this.filterPrice = \"all\";\n  }\n  ngOnInit() {\n    this.loadCourses();\n  }\n  loadCourses() {\n    // Mock data for demonstration\n    this.courses = [{\n      id: 1,\n      titre: \"React Fundamentals\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\"\n      },\n      contenus: [{\n        id: 1,\n        titre: \"Introduction à React\",\n        typeContenu: \"Video\",\n        coursId: 1,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 1\n      }, {\n        id: 2,\n        titre: \"Components et Props\",\n        typeContenu: \"Video\",\n        coursId: 1,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 2\n      }, {\n        id: 3,\n        titre: \"Quiz - Bases de React\",\n        typeContenu: \"Quiz\",\n        coursId: 1,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 3\n      }, {\n        id: 4,\n        titre: \"Résumé du chapitre\",\n        typeContenu: \"Resume\",\n        coursId: 1,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 4\n      }],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false\n    }, {\n      id: 2,\n      titre: \"JavaScript Avancé\",\n      description: \"Maîtrisez les concepts avancés de JavaScript : closures, prototypes, async/await.\",\n      prix: 149.99,\n      duree: 180,\n      niveau: \"Avancé\",\n      formateurId: 2,\n      formateur: {\n        id: 2,\n        nom: \"Martin\",\n        prenom: \"Sophie\"\n      },\n      contenus: [{\n        id: 5,\n        titre: \"Closures et Scope\",\n        typeContenu: \"Video\",\n        coursId: 2,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 1\n      }, {\n        id: 6,\n        titre: \"Prototypes et Héritage\",\n        typeContenu: \"Video\",\n        coursId: 2,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 2\n      }, {\n        id: 7,\n        titre: \"Quiz - Concepts avancés\",\n        typeContenu: \"Quiz\",\n        coursId: 2,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 3\n      }],\n      nombreEtudiants: 156,\n      note: 4.9,\n      estGratuit: false\n    }, {\n      id: 3,\n      titre: \"Introduction au Web\",\n      description: \"Cours gratuit pour débuter dans le développement web avec HTML, CSS et JavaScript.\",\n      prix: 0,\n      duree: 60,\n      niveau: \"Débutant\",\n      formateurId: 3,\n      formateur: {\n        id: 3,\n        nom: \"Bernard\",\n        prenom: \"Pierre\"\n      },\n      contenus: [{\n        id: 8,\n        titre: \"HTML Basics\",\n        typeContenu: \"Video\",\n        coursId: 3,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 1\n      }, {\n        id: 9,\n        titre: \"CSS Styling\",\n        typeContenu: \"Video\",\n        coursId: 3,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 2\n      }, {\n        id: 10,\n        titre: \"Quiz final\",\n        typeContenu: \"Quiz\",\n        coursId: 3,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 3\n      }],\n      nombreEtudiants: 892,\n      note: 4.6,\n      estGratuit: true\n    }];\n    this.applyFilters();\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getAllCours().subscribe({\n      next: (data) => {\n        this.courses = data;\n        this.applyFilters();\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  applyFilters() {\n    this.filteredCourses = this.courses.filter(course => {\n      const matchesSearch = course.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) || course.description.toLowerCase().includes(this.searchTerm.toLowerCase());\n      const matchesLevel = this.filterLevel === \"all\" || course.niveau === this.filterLevel;\n      const matchesPrice = this.filterPrice === \"all\" || this.filterPrice === \"free\" && course.estGratuit || this.filterPrice === \"paid\" && !course.estGratuit;\n      return matchesSearch && matchesLevel && matchesPrice;\n    });\n  }\n  getContentIcon(type) {\n    switch (type) {\n      case \"Video\":\n        return \"play_circle\";\n      case \"Quiz\":\n        return \"quiz\";\n      case \"Resume\":\n        return \"description\";\n      default:\n        return \"book\";\n    }\n  }\n  getLevelColor(niveau) {\n    switch (niveau) {\n      case \"Débutant\":\n        return \"bg-green-100\";\n      case \"Intermédiaire\":\n        return \"bg-yellow-100\";\n      case \"Avancé\":\n        return \"bg-red-100\";\n      default:\n        return \"bg-gray-100\";\n    }\n  }\n};\nCourseListComponent = __decorate([Component({\n  selector: \"app-course-list\",\n  template: `\n    <div class=\"course-list-container\">\n      <div class=\"header-section\">\n        <h1>Catalogue des Cours</h1>\n\n        <!-- Filtres -->\n        <div class=\"filters-row\">\n          <mat-form-field appearance=\"outline\" class=\"search-input\">\n            <mat-label>Rechercher un cours...</mat-label>\n            <input matInput [(ngModel)]=\"searchTerm\" (input)=\"applyFilters()\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-select\">\n            <mat-label>Niveau</mat-label>\n            <mat-select [(ngModel)]=\"filterLevel\" (selectionChange)=\"applyFilters()\">\n              <mat-option value=\"all\">Tous les niveaux</mat-option>\n              <mat-option value=\"Débutant\">Débutant</mat-option>\n              <mat-option value=\"Intermédiaire\">Intermédiaire</mat-option>\n              <mat-option value=\"Avancé\">Avancé</mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-select\">\n            <mat-label>Prix</mat-label>\n            <mat-select [(ngModel)]=\"filterPrice\" (selectionChange)=\"applyFilters()\">\n              <mat-option value=\"all\">Tous les prix</mat-option>\n              <mat-option value=\"free\">Gratuit</mat-option>\n              <mat-option value=\"paid\">Payant</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Liste des cours -->\n      <div class=\"courses-grid\" *ngIf=\"filteredCourses.length > 0; else noCourses\">\n        <mat-card *ngFor=\"let course of filteredCourses\" class=\"course-card\">\n          <mat-card-header>\n            <div class=\"card-header-top\">\n              <mat-chip-listbox>\n                <mat-chip [class]=\"getLevelColor(course.niveau)\">{{ course.niveau }}</mat-chip>\n              </mat-chip-listbox>\n              <span class=\"price\" *ngIf=\"!course.estGratuit\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              <mat-chip *ngIf=\"course.estGratuit\" class=\"free-chip\">Gratuit</mat-chip>\n            </div>\n            <mat-card-title>{{ course.titre }}</mat-card-title>\n            <mat-card-subtitle class=\"description\">{{ course.description }}</mat-card-subtitle>\n          </mat-card-header>\n\n          <mat-card-content>\n            <div class=\"course-info\">\n              <div class=\"info-item\">\n                <mat-icon>person</mat-icon>\n                <span>{{ course.formateur.prenom }} {{ course.formateur.nom }}</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ course.duree }} min</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon>group</mat-icon>\n                <span>{{ course.nombreEtudiants }} étudiants</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon class=\"star-icon\">star</mat-icon>\n                <span>{{ course.note }}</span>\n              </div>\n            </div>\n\n            <div class=\"content-preview\">\n              <h4>Contenu du cours:</h4>\n              <div *ngFor=\"let content of course.contenus | slice:0:3\" class=\"content-item\">\n                <mat-icon>{{ getContentIcon(content.typeContenu) }}</mat-icon>\n                <span>{{ content.titre }}</span>\n                <span *ngIf=\"content.duree\" class=\"content-duration\">({{ content.duree }} min)</span>\n              </div>\n              <p *ngIf=\"course.contenus.length > 3\" class=\"more-content\">+{{ course.contenus.length - 3 }} autres contenus</p>\n            </div>\n\n            <div class=\"card-actions\">\n              <button mat-stroked-button color=\"primary\" [routerLink]=\"['/courses', course.id]\">\n                Voir détails\n              </button>\n              <button mat-raised-button color=\"accent\" *ngIf=\"course.estGratuit\">Commencer</button>\n              <button mat-raised-button color=\"primary\" *ngIf=\"!course.estGratuit\" [routerLink]=\"['/payment', course.id]\">\n                <mat-icon>euro_symbol</mat-icon>\n                Acheter\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #noCourses>\n        <div class=\"no-courses\">\n          <mat-icon>book_off</mat-icon>\n          <h3>Aucun cours trouvé</h3>\n          <p>Essayez de modifier vos critères de recherche.</p>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [`\n    .course-list-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n    }\n\n    .header-section {\n      margin-bottom: 2rem;\n    }\n\n    .header-section h1 {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 1.5rem;\n      text-align: center;\n    }\n\n    .filters-row {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 2rem;\n    }\n\n    .search-input {\n      flex-grow: 1;\n      max-width: 400px;\n    }\n\n    .filter-select {\n      width: 200px;\n    }\n\n    .courses-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .course-card {\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      height: 100%;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\n    }\n\n    .course-card:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n    }\n\n    .card-header-top {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .mat-chip {\n      font-size: 0.8rem;\n      padding: 0.3rem 0.7rem;\n      height: auto;\n    }\n\n    .mat-chip.bg-green-100 { background-color: #d4edda; color: #155724; }\n    .mat-chip.bg-yellow-100 { background-color: #fff3cd; color: #856404; }\n    .mat-chip.bg-red-100 { background-color: #f8d7da; color: #721c24; }\n    .free-chip { background-color: #e6ffed; color: #28a745; }\n\n    .price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .course-card mat-card-title {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-card .description {\n      font-size: 0.9rem;\n      color: #666;\n      line-height: 1.4;\n      height: 3em; /* Limit to 2 lines */\n      overflow: hidden;\n      text-overflow: ellipsis;\n      display: -webkit-box;\n      -webkit-line-clamp: 2;\n      -webkit-box-orient: vertical;\n    }\n\n    .course-info {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      margin-top: 1rem;\n      font-size: 0.9rem;\n      color: #555;\n    }\n\n    .info-item {\n      display: flex;\n      align-items: center;\n      gap: 0.3rem;\n    }\n\n    .info-item mat-icon {\n      font-size: 1.1rem;\n      width: 1.1rem;\n      height: 1.1rem;\n      color: #888;\n    }\n\n    .info-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .content-preview {\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .content-preview h4 {\n      font-size: 1rem;\n      font-weight: 600;\n      margin-bottom: 0.8rem;\n      color: #444;\n    }\n\n    .content-item {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.4rem;\n    }\n\n    .content-item mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      color: #777;\n    }\n\n    .content-duration {\n      margin-left: auto;\n      font-size: 0.8rem;\n      color: #888;\n    }\n\n    .more-content {\n      font-size: 0.8rem;\n      color: #888;\n      margin-top: 0.5rem;\n    }\n\n    .card-actions {\n      display: flex;\n      gap: 0.8rem;\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .card-actions button {\n      flex: 1;\n      font-size: 0.9rem;\n      padding: 0.6rem 1rem;\n    }\n\n    .card-actions button mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.3rem;\n    }\n\n    .no-courses {\n      text-align: center;\n      padding: 4rem 0;\n      color: #777;\n    }\n\n    .no-courses mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #aaa;\n    }\n\n    .no-courses h3 {\n      font-size: 1.5rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    @media (max-width: 768px) {\n      .filters-row {\n        flex-direction: column;\n        align-items: stretch;\n      }\n      .search-input, .filter-select {\n        max-width: 100%;\n        width: 100%;\n      }\n      .courses-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})], CourseListComponent);", "map": {"version": 3, "names": ["Component", "CourseListComponent", "constructor", "courseService", "snackBar", "router", "courses", "filteredCourses", "searchTerm", "filterLevel", "filterPrice", "ngOnInit", "loadCourses", "id", "titre", "description", "prix", "duree", "niveau", "formateurId", "formateur", "nom", "prenom", "contenus", "typeContenu", "coursId", "estComplete", "estDebloque", "ordre", "nombreEtudiants", "note", "estGratuit", "applyFilters", "filter", "course", "matchesSearch", "toLowerCase", "includes", "matchesLevel", "matchesPrice", "getContentIcon", "type", "getLevelColor", "__decorate", "selector", "template", "styles"], "sources": ["C:\\e-learning\\src\\app\\features\\courses\\course-list\\course-list.component.ts"], "sourcesContent": ["import { Component, type OnInit } from \"@angular/core\"\nimport type { CourseService } from \"../../../core/services/course.service\"\nimport type { Course } from \"../../../core/models/course.model\"\nimport type { MatSnackBar } from \"@angular/material/snack-bar\"\nimport type { Router } from \"@angular/router\"\n\n@Component({\n  selector: \"app-course-list\",\n  template: `\n    <div class=\"course-list-container\">\n      <div class=\"header-section\">\n        <h1>Catalogue des Cours</h1>\n\n        <!-- Filtres -->\n        <div class=\"filters-row\">\n          <mat-form-field appearance=\"outline\" class=\"search-input\">\n            <mat-label>Rechercher un cours...</mat-label>\n            <input matInput [(ngModel)]=\"searchTerm\" (input)=\"applyFilters()\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-select\">\n            <mat-label>Niveau</mat-label>\n            <mat-select [(ngModel)]=\"filterLevel\" (selectionChange)=\"applyFilters()\">\n              <mat-option value=\"all\">Tous les niveaux</mat-option>\n              <mat-option value=\"Débutant\">Débutant</mat-option>\n              <mat-option value=\"Intermédiaire\">Intermédiaire</mat-option>\n              <mat-option value=\"Avancé\">Avancé</mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"filter-select\">\n            <mat-label>Prix</mat-label>\n            <mat-select [(ngModel)]=\"filterPrice\" (selectionChange)=\"applyFilters()\">\n              <mat-option value=\"all\">Tous les prix</mat-option>\n              <mat-option value=\"free\">Gratuit</mat-option>\n              <mat-option value=\"paid\">Payant</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Liste des cours -->\n      <div class=\"courses-grid\" *ngIf=\"filteredCourses.length > 0; else noCourses\">\n        <mat-card *ngFor=\"let course of filteredCourses\" class=\"course-card\">\n          <mat-card-header>\n            <div class=\"card-header-top\">\n              <mat-chip-listbox>\n                <mat-chip [class]=\"getLevelColor(course.niveau)\">{{ course.niveau }}</mat-chip>\n              </mat-chip-listbox>\n              <span class=\"price\" *ngIf=\"!course.estGratuit\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              <mat-chip *ngIf=\"course.estGratuit\" class=\"free-chip\">Gratuit</mat-chip>\n            </div>\n            <mat-card-title>{{ course.titre }}</mat-card-title>\n            <mat-card-subtitle class=\"description\">{{ course.description }}</mat-card-subtitle>\n          </mat-card-header>\n\n          <mat-card-content>\n            <div class=\"course-info\">\n              <div class=\"info-item\">\n                <mat-icon>person</mat-icon>\n                <span>{{ course.formateur.prenom }} {{ course.formateur.nom }}</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ course.duree }} min</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon>group</mat-icon>\n                <span>{{ course.nombreEtudiants }} étudiants</span>\n              </div>\n              <div class=\"info-item\">\n                <mat-icon class=\"star-icon\">star</mat-icon>\n                <span>{{ course.note }}</span>\n              </div>\n            </div>\n\n            <div class=\"content-preview\">\n              <h4>Contenu du cours:</h4>\n              <div *ngFor=\"let content of course.contenus | slice:0:3\" class=\"content-item\">\n                <mat-icon>{{ getContentIcon(content.typeContenu) }}</mat-icon>\n                <span>{{ content.titre }}</span>\n                <span *ngIf=\"content.duree\" class=\"content-duration\">({{ content.duree }} min)</span>\n              </div>\n              <p *ngIf=\"course.contenus.length > 3\" class=\"more-content\">+{{ course.contenus.length - 3 }} autres contenus</p>\n            </div>\n\n            <div class=\"card-actions\">\n              <button mat-stroked-button color=\"primary\" [routerLink]=\"['/courses', course.id]\">\n                Voir détails\n              </button>\n              <button mat-raised-button color=\"accent\" *ngIf=\"course.estGratuit\">Commencer</button>\n              <button mat-raised-button color=\"primary\" *ngIf=\"!course.estGratuit\" [routerLink]=\"['/payment', course.id]\">\n                <mat-icon>euro_symbol</mat-icon>\n                Acheter\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #noCourses>\n        <div class=\"no-courses\">\n          <mat-icon>book_off</mat-icon>\n          <h3>Aucun cours trouvé</h3>\n          <p>Essayez de modifier vos critères de recherche.</p>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [\n    `\n    .course-list-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n    }\n\n    .header-section {\n      margin-bottom: 2rem;\n    }\n\n    .header-section h1 {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 1.5rem;\n      text-align: center;\n    }\n\n    .filters-row {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 2rem;\n    }\n\n    .search-input {\n      flex-grow: 1;\n      max-width: 400px;\n    }\n\n    .filter-select {\n      width: 200px;\n    }\n\n    .courses-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .course-card {\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      height: 100%;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\n    }\n\n    .course-card:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n    }\n\n    .card-header-top {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .mat-chip {\n      font-size: 0.8rem;\n      padding: 0.3rem 0.7rem;\n      height: auto;\n    }\n\n    .mat-chip.bg-green-100 { background-color: #d4edda; color: #155724; }\n    .mat-chip.bg-yellow-100 { background-color: #fff3cd; color: #856404; }\n    .mat-chip.bg-red-100 { background-color: #f8d7da; color: #721c24; }\n    .free-chip { background-color: #e6ffed; color: #28a745; }\n\n    .price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .course-card mat-card-title {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-card .description {\n      font-size: 0.9rem;\n      color: #666;\n      line-height: 1.4;\n      height: 3em; /* Limit to 2 lines */\n      overflow: hidden;\n      text-overflow: ellipsis;\n      display: -webkit-box;\n      -webkit-line-clamp: 2;\n      -webkit-box-orient: vertical;\n    }\n\n    .course-info {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      margin-top: 1rem;\n      font-size: 0.9rem;\n      color: #555;\n    }\n\n    .info-item {\n      display: flex;\n      align-items: center;\n      gap: 0.3rem;\n    }\n\n    .info-item mat-icon {\n      font-size: 1.1rem;\n      width: 1.1rem;\n      height: 1.1rem;\n      color: #888;\n    }\n\n    .info-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .content-preview {\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .content-preview h4 {\n      font-size: 1rem;\n      font-weight: 600;\n      margin-bottom: 0.8rem;\n      color: #444;\n    }\n\n    .content-item {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.4rem;\n    }\n\n    .content-item mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      color: #777;\n    }\n\n    .content-duration {\n      margin-left: auto;\n      font-size: 0.8rem;\n      color: #888;\n    }\n\n    .more-content {\n      font-size: 0.8rem;\n      color: #888;\n      margin-top: 0.5rem;\n    }\n\n    .card-actions {\n      display: flex;\n      gap: 0.8rem;\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .card-actions button {\n      flex: 1;\n      font-size: 0.9rem;\n      padding: 0.6rem 1rem;\n    }\n\n    .card-actions button mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.3rem;\n    }\n\n    .no-courses {\n      text-align: center;\n      padding: 4rem 0;\n      color: #777;\n    }\n\n    .no-courses mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #aaa;\n    }\n\n    .no-courses h3 {\n      font-size: 1.5rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    @media (max-width: 768px) {\n      .filters-row {\n        flex-direction: column;\n        align-items: stretch;\n      }\n      .search-input, .filter-select {\n        max-width: 100%;\n        width: 100%;\n      }\n      .courses-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `,\n  ],\n})\nexport class CourseListComponent implements OnInit {\n  courses: Course[] = []\n  filteredCourses: Course[] = []\n  searchTerm = \"\"\n  filterLevel = \"all\"\n  filterPrice = \"all\"\n\n  constructor(\n    private courseService: CourseService,\n    private snackBar: MatSnackBar,\n    private router: Router,\n  ) {}\n\n  ngOnInit(): void {\n    this.loadCourses()\n  }\n\n  loadCourses(): void {\n    // Mock data for demonstration\n    this.courses = [\n      {\n        id: 1,\n        titre: \"React Fundamentals\",\n        description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n        prix: 99.99,\n        duree: 120,\n        niveau: \"Débutant\",\n        formateurId: 1,\n        formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\" },\n        contenus: [\n          {\n            id: 1,\n            titre: \"Introduction à React\",\n            typeContenu: \"Video\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 1,\n          },\n          {\n            id: 2,\n            titre: \"Components et Props\",\n            typeContenu: \"Video\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 2,\n          },\n          {\n            id: 3,\n            titre: \"Quiz - Bases de React\",\n            typeContenu: \"Quiz\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 3,\n          },\n          {\n            id: 4,\n            titre: \"Résumé du chapitre\",\n            typeContenu: \"Resume\",\n            coursId: 1,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 4,\n          },\n        ],\n        nombreEtudiants: 245,\n        note: 4.8,\n        estGratuit: false,\n      },\n      {\n        id: 2,\n        titre: \"JavaScript Avancé\",\n        description: \"Maîtrisez les concepts avancés de JavaScript : closures, prototypes, async/await.\",\n        prix: 149.99,\n        duree: 180,\n        niveau: \"Avancé\",\n        formateurId: 2,\n        formateur: { id: 2, nom: \"Martin\", prenom: \"Sophie\" },\n        contenus: [\n          {\n            id: 5,\n            titre: \"Closures et Scope\",\n            typeContenu: \"Video\",\n            coursId: 2,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 1,\n          },\n          {\n            id: 6,\n            titre: \"Prototypes et Héritage\",\n            typeContenu: \"Video\",\n            coursId: 2,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 2,\n          },\n          {\n            id: 7,\n            titre: \"Quiz - Concepts avancés\",\n            typeContenu: \"Quiz\",\n            coursId: 2,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 3,\n          },\n        ],\n        nombreEtudiants: 156,\n        note: 4.9,\n        estGratuit: false,\n      },\n      {\n        id: 3,\n        titre: \"Introduction au Web\",\n        description: \"Cours gratuit pour débuter dans le développement web avec HTML, CSS et JavaScript.\",\n        prix: 0,\n        duree: 60,\n        niveau: \"Débutant\",\n        formateurId: 3,\n        formateur: { id: 3, nom: \"Bernard\", prenom: \"Pierre\" },\n        contenus: [\n          {\n            id: 8,\n            titre: \"HTML Basics\",\n            typeContenu: \"Video\",\n            coursId: 3,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 1,\n          },\n          {\n            id: 9,\n            titre: \"CSS Styling\",\n            typeContenu: \"Video\",\n            coursId: 3,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 2,\n          },\n          {\n            id: 10,\n            titre: \"Quiz final\",\n            typeContenu: \"Quiz\",\n            coursId: 3,\n            estComplete: false,\n            estDebloque: true,\n            ordre: 3,\n          },\n        ],\n        nombreEtudiants: 892,\n        note: 4.6,\n        estGratuit: true,\n      },\n    ]\n    this.applyFilters()\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getAllCours().subscribe({\n      next: (data) => {\n        this.courses = data;\n        this.applyFilters();\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  applyFilters(): void {\n    this.filteredCourses = this.courses.filter((course) => {\n      const matchesSearch =\n        course.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        course.description.toLowerCase().includes(this.searchTerm.toLowerCase())\n      const matchesLevel = this.filterLevel === \"all\" || course.niveau === this.filterLevel\n      const matchesPrice =\n        this.filterPrice === \"all\" ||\n        (this.filterPrice === \"free\" && course.estGratuit) ||\n        (this.filterPrice === \"paid\" && !course.estGratuit)\n\n      return matchesSearch && matchesLevel && matchesPrice\n    })\n  }\n\n  getContentIcon(type: string): string {\n    switch (type) {\n      case \"Video\":\n        return \"play_circle\"\n      case \"Quiz\":\n        return \"quiz\"\n      case \"Resume\":\n        return \"description\"\n      default:\n        return \"book\"\n    }\n  }\n\n  getLevelColor(niveau: string): string {\n    switch (niveau) {\n      case \"Débutant\":\n        return \"bg-green-100\"\n      case \"Intermédiaire\":\n        return \"bg-yellow-100\"\n      case \"Avancé\":\n        return \"bg-red-100\"\n      default:\n        return \"bg-gray-100\"\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAqB,eAAe;AA+U/C,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAO9BC,YACUC,aAA4B,EAC5BC,QAAqB,EACrBC,MAAc;IAFd,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,WAAW,GAAG,KAAK;EAMhB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT;IACA,IAAI,CAACN,OAAO,GAAG,CACb;MACEO,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,kFAAkF;MAC/FC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,UAAU;MAClBC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAEP,EAAE,EAAE,CAAC;QAAEQ,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAM,CAAE;MACnDC,QAAQ,EAAE,CACR;QACEV,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,sBAAsB;QAC7BU,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEf,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,qBAAqB;QAC5BU,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEf,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,uBAAuB;QAC9BU,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEf,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,oBAAoB;QAC3BU,WAAW,EAAE,QAAQ;QACrBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDC,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTC,UAAU,EAAE;KACb,EACD;MACElB,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,mFAAmF;MAChGC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAEP,EAAE,EAAE,CAAC;QAAEQ,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MACrDC,QAAQ,EAAE,CACR;QACEV,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,mBAAmB;QAC1BU,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEf,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,wBAAwB;QAC/BU,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEf,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,yBAAyB;QAChCU,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDC,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTC,UAAU,EAAE;KACb,EACD;MACElB,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,oFAAoF;MACjGC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,UAAU;MAClBC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAEP,EAAE,EAAE,CAAC;QAAEQ,GAAG,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAQ,CAAE;MACtDC,QAAQ,EAAE,CACR;QACEV,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,aAAa;QACpBU,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEf,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,aAAa;QACpBU,WAAW,EAAE,OAAO;QACpBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEf,EAAE,EAAE,EAAE;QACNC,KAAK,EAAE,YAAY;QACnBU,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDC,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTC,UAAU,EAAE;KACb,CACF;IACD,IAAI,CAACC,YAAY,EAAE;IAEnB;IACA;;;;;;;;;;;;EAYF;;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACzB,eAAe,GAAG,IAAI,CAACD,OAAO,CAAC2B,MAAM,CAAEC,MAAM,IAAI;MACpD,MAAMC,aAAa,GACjBD,MAAM,CAACpB,KAAK,CAACsB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC7B,UAAU,CAAC4B,WAAW,EAAE,CAAC,IAClEF,MAAM,CAACnB,WAAW,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC7B,UAAU,CAAC4B,WAAW,EAAE,CAAC;MAC1E,MAAME,YAAY,GAAG,IAAI,CAAC7B,WAAW,KAAK,KAAK,IAAIyB,MAAM,CAAChB,MAAM,KAAK,IAAI,CAACT,WAAW;MACrF,MAAM8B,YAAY,GAChB,IAAI,CAAC7B,WAAW,KAAK,KAAK,IACzB,IAAI,CAACA,WAAW,KAAK,MAAM,IAAIwB,MAAM,CAACH,UAAW,IACjD,IAAI,CAACrB,WAAW,KAAK,MAAM,IAAI,CAACwB,MAAM,CAACH,UAAW;MAErD,OAAOI,aAAa,IAAIG,YAAY,IAAIC,YAAY;IACtD,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAACC,IAAY;IACzB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,aAAa;MACtB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,QAAQ;QACX,OAAO,aAAa;MACtB;QACE,OAAO,MAAM;;EAEnB;EAEAC,aAAaA,CAACxB,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,cAAc;MACvB,KAAK,eAAe;QAClB,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,YAAY;MACrB;QACE,OAAO,aAAa;;EAE1B;CACD;AArNYjB,mBAAmB,GAAA0C,UAAA,EAzU/B3C,SAAS,CAAC;EACT4C,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqGT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6ND;CAEF,CAAC,C,EACW7C,mBAAmB,CAqN/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}