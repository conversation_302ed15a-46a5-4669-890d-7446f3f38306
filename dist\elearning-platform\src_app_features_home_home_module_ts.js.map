{"version": 3, "file": "src_app_features_home_home_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA6JM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxJtBE,4DAAA,aAA4B;UAKQA,oDAAA,aAAM;UAAAA,0DAAA,EAAW;UAC7CA,4DAAA,SAAI;UAAAA,oDAAA,wBAAiB;UAAAA,0DAAA,EAAK;UAE5BA,4DAAA,WAAoB;UAAAA,oDAAA,2EAAoE;UAAAA,0DAAA,EAAI;UAG5FA,4DAAA,cAA4B;UAExBA,oDAAA,mCACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,iBAAsE;UACpEA,oDAAA,iCACF;UAAAA,0DAAA,EAAS;UAKbA,4DAAA,cAA2B;UAGgBA,oDAAA,YAAI;UAAAA,0DAAA,EAAW;UACpDA,4DAAA,UAAI;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAK;UACvBA,4DAAA,SAAG;UAAAA,oDAAA,kDAAqC;UAAAA,0DAAA,EAAI;UAIhDA,4DAAA,oBAA+B;UAEWA,oDAAA,oBAAY;UAAAA,0DAAA,EAAW;UAC7DA,4DAAA,UAAI;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAK;UACpBA,4DAAA,SAAG;UAAAA,oDAAA,kDAAqC;UAAAA,0DAAA,EAAI;UAIhDA,4DAAA,oBAA+B;UAEWA,oDAAA,eAAO;UAAAA,0DAAA,EAAW;UACxDA,4DAAA,UAAI;UAAAA,oDAAA,qCAAmB;UAAAA,0DAAA,EAAK;UAC5BA,4DAAA,SAAG;UAAAA,oDAAA,kDAA2B;UAAAA,0DAAA,EAAI;UAItCA,4DAAA,oBAA+B;UAESA,oDAAA,eAAO;UAAAA,0DAAA,EAAW;UACtDA,4DAAA,UAAI;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAK;UACnBA,4DAAA,SAAG;UAAAA,oDAAA,6BAAqB;UAAAA,0DAAA,EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvDI;AACA;AACY;AACJ;AACA;AAEN;;;AAY1C,MAAOS,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAPnBL,yDAAY,EACZE,qEAAe,EACfC,iEAAa,EACbC,iEAAa,EACbH,yDAAY,CAACK,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAErB,0DAAaA;MAAA,CAAE,CAAC,CAAC;IAAA;EAAA;;;sHAGtDkB,UAAU;IAAAI,YAAA,GATNtB,0DAAa;IAAAuB,OAAA,GAE1BV,yDAAY,EACZE,qEAAe,EACfC,iEAAa,EACbC,iEAAa,EAAAO,yDAAA;EAAA;AAAA,K", "sources": ["./src/app/features/home/<USER>", "./src/app/features/home/<USER>"], "sourcesContent": ["import { Component } from \"@angular/core\"\n\n@Component({\n  selector: \"app-home\",\n  template: `\n    <div class=\"home-container\">\n      <div class=\"container\">\n        <!-- Header -->\n        <div class=\"header\">\n          <div class=\"logo\">\n            <mat-icon class=\"logo-icon\">school</mat-icon>\n            <h1>Training Platform</h1>\n          </div>\n          <p class=\"subtitle\">Plateforme de formation en ligne connectant formateurs et apprenants</p>\n\n          <!-- Action Buttons -->\n          <div class=\"action-buttons\">\n            <button mat-raised-button color=\"accent\" routerLink=\"/auth/login\">\n              🔑 Se connecter\n            </button>\n            <button mat-raised-button color=\"primary\" routerLink=\"/auth/register\">\n              📝 S'inscrire\n            </button>\n          </div>\n        </div>\n\n        <!-- Features Grid -->\n        <div class=\"features-grid\">\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon green\">book</mat-icon>\n              <h3>Cours en ligne</h3>\n              <p><PERSON><PERSON><PERSON> et suivez des cours interactifs</p>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon yellow\">emoji_events</mat-icon>\n              <h3>Certificats</h3>\n              <p>Obtenez des certificats personnalisés</p>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon orange\">payment</mat-icon>\n              <h3>Paiements sécurisés</h3>\n              <p>Système de paiement intégré</p>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon blue\">message</mat-icon>\n              <h3>Messagerie</h3>\n              <p>Communication directe</p>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .home-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .header {\n      text-align: center;\n      margin-bottom: 4rem;\n      color: white;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 1rem;\n    }\n\n    .logo-icon {\n      font-size: 3rem;\n      margin-right: 1rem;\n    }\n\n    .logo h1 {\n      font-size: 3rem;\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .subtitle {\n      font-size: 1.25rem;\n      margin-bottom: 2rem;\n      opacity: 0.9;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 4rem;\n    }\n\n    .action-buttons button {\n      padding: 0.75rem 2rem;\n      font-size: 1.1rem;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n      gap: 2rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .feature-card {\n      background: rgba(255, 255, 255, 0.1);\n      backdrop-filter: blur(10px);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      color: white;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .feature-icon {\n      font-size: 4rem;\n      margin-bottom: 1rem;\n    }\n\n    .feature-icon.green { color: #4caf50; }\n    .feature-icon.yellow { color: #ffc107; }\n    .feature-icon.orange { color: #ff9800; }\n    .feature-icon.blue { color: #2196f3; }\n\n    .feature-card h3 {\n      font-size: 1.5rem;\n      margin-bottom: 1rem;\n      font-weight: 600;\n    }\n\n    .feature-card p {\n      opacity: 0.8;\n    }\n  `,\n  ],\n})\nexport class HomeComponent {}\n", "import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatIconModule } from \"@angular/material/icon\"\n\nimport { HomeComponent } from \"./home.component\"\n\n@NgModule({\n  declarations: [HomeComponent],\n  imports: [\n    CommonModule,\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    RouterModule.forChild([{ path: \"\", component: HomeComponent }]),\n  ],\n})\nexport class HomeModule {}\n"], "names": ["HomeComponent", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "CommonModule", "RouterModule", "MatButtonModule", "MatCardModule", "MatIconModule", "HomeModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}