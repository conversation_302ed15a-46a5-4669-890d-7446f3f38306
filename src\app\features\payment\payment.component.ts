import { Component, OnInit } from "@angular/core"
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms"
import { ActivatedRoute, Router } from "@angular/router"
import { CourseService } from "../../core/services/course.service"
import { PaymentService } from "../../core/services/payment.service"
import { MatSnackBar } from "@angular/material/snack-bar"
import { Course } from "../../core/models/course.model"
import { AuthService } from "../../core/services/auth.service"
import { User } from "../../core/models/user.model"

@Component({
  selector: "app-payment",
  template: `
    <div class="payment-container">
      <div class="content-wrapper">
        <!-- Payment Form -->
        <div class="payment-form-section">
          <mat-card class="payment-card">
            <mat-card-header>
              <mat-card-title class="card-title-with-icon">
                <mat-icon>credit_card</mat-icon>
                Informations de paiement
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <form [formGroup]="paymentForm" (ngSubmit)="onSubmit()">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Adresse e-mail</mat-label>
                  <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                  <mat-error *ngIf="paymentForm.get('email')?.hasError('required')">L'email est requis</mat-error>
                  <mat-error *ngIf="paymentForm.get('email')?.hasError('email')">Format d'email invalide</mat-error>
                </mat-form-field>

                <mat-divider class="form-divider"></mat-divider>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Numéro de carte</mat-label>
                  <input matInput formControlName="cardNumber" placeholder="1234 5678 9012 3456"
                         (input)="formatCardNumber($event)">
                  <mat-error *ngIf="paymentForm.get('cardNumber')?.hasError('required')">Le numéro de carte est requis</mat-error>
                  <mat-error *ngIf="paymentForm.get('cardNumber')?.hasError('pattern')">Numéro de carte invalide</mat-error>
                </mat-form-field>

                <div class="row-fields">
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Date d'expiration</mat-label>
                    <input matInput formControlName="expiryDate" placeholder="MM/AA"
                           (input)="formatExpiryDate($event)">
                    <mat-error *ngIf="paymentForm.get('expiryDate')?.hasError('required')">Date requise</mat-error>
                    <mat-error *ngIf="paymentForm.get('expiryDate')?.hasError('pattern')">Format MM/AA invalide</mat-error>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>CVV</mat-label>
                    <input matInput formControlName="cvv" placeholder="123"
                           (input)="formatCvv($event)">
                    <mat-error *ngIf="paymentForm.get('cvv')?.hasError('required')">CVV requis</mat-error>
                    <mat-error *ngIf="paymentForm.get('cvv')?.hasError('pattern')">CVV invalide</mat-error>
                  </mat-form-field>
                </div>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Nom sur la carte</mat-label>
                  <input matInput formControlName="cardName" placeholder="Jean Dupont">
                  <mat-error *ngIf="paymentForm.get('cardName')?.hasError('required')">Le nom est requis</mat-error>
                </mat-form-field>

                <div class="secure-payment-info">
                  <mat-icon>lock</mat-icon>
                  <span class="secure-text">Paiement sécurisé</span>
                  <p class="secure-description">Vos informations sont protégées par un cryptage SSL 256 bits.</p>
                </div>

                <button mat-raised-button color="primary" type="submit" 
                        [disabled]="paymentForm.invalid || isProcessing" class="full-width-btn submit-btn">
                  <mat-spinner diameter="20" *ngIf="isProcessing"></mat-spinner>
                  <span *ngIf="!isProcessing">
                    <mat-icon>euro_symbol</mat-icon>
                    Payer {{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}
                  </span>
                </button>
              </form>
            </mat-card-content>
          </mat-card>
        </div>

        <!-- Order Summary & Details -->
        <div class="summary-section">
          <mat-card class="summary-card">
            <mat-card-header>
              <mat-card-title>Résumé de la commande</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="course-summary-item">
                <div class="course-image-placeholder">
                  <mat-icon>credit_card</mat-icon>
                </div>
                <div class="course-details">
                  <h3>{{ course.titre }}</h3>
                  <p class="course-instructor">Par {{ course.formateur.prenom }} {{ course.formateur.nom }}</p>
                  <div class="course-meta-summary">
                    <div class="meta-item">
                      <mat-icon>schedule</mat-icon>
                      <span>{{ course.duree }} min</span>
                    </div>
                    <div class="meta-item">
                      <mat-icon>group</mat-icon>
                      <span>{{ course.nombreEtudiants }} étudiants</span>
                    </div>
                    <div class="meta-item">
                      <mat-icon class="star-icon">star</mat-icon>
                      <span>{{ course.note }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <mat-divider class="summary-divider"></mat-divider>

              <div class="price-breakdown">
                <div class="price-item">
                  <span>Prix du cours</span>
                  <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>
                </div>
                <div class="price-item sub-item">
                  <span>TVA incluse</span>
                  <span>0€</span>
                </div>
              </div>

              <mat-divider class="summary-divider"></mat-divider>

              <div class="total-price">
                <span>Total</span>
                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Earnings Distribution -->
          <mat-card class="earnings-card">
            <mat-card-header>
              <mat-card-title>Répartition des gains</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="earnings-item">
                <span>Formateur (70%)</span>
                <span class="trainer-earnings">{{ trainerEarnings | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>
              </div>
              <div class="earnings-item">
                <span>Plateforme (30%)</span>
                <span class="platform-fee">{{ platformFee | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>
              </div>
              <mat-divider class="summary-divider"></mat-divider>
              <div class="earnings-item total-earnings">
                <span>Total</span>
                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Guarantee -->
          <mat-card class="guarantee-card">
            <mat-card-content>
              <div class="guarantee-header">
                <mat-icon>check_circle</mat-icon>
                <span>Garantie 30 jours</span>
              </div>
              <p class="guarantee-text">
                Si vous n'êtes pas satisfait du cours, nous vous remboursons intégralement sous 30 jours.
              </p>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>

    <div class="payment-success-overlay" *ngIf="paymentSuccess">
      <mat-card class="success-card">
        <mat-card-content>
          <div class="success-icon-wrapper">
            <mat-icon>check_circle</mat-icon>
          </div>
          <h2>Paiement réussi !</h2>
          <p>Votre achat a été traité avec succès. Vous allez être redirigé vers le cours.</p>
          <div class="purchased-course-info">
            <p class="course-title">{{ course.titre }}</p>
            <p class="course-price">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [
    `
    .payment-container {
      min-height: 100vh;
      background-color: #f5f5f5;
      padding: 2rem;
      display: flex;
      justify-content: center;
      align-items: flex-start;
    }

    .content-wrapper {
      display: grid;
      grid-template-columns: 1.5fr 1fr;
      gap: 2rem;
      max-width: 1200px;
      width: 100%;
    }

    @media (max-width: 960px) {
      .content-wrapper {
        grid-template-columns: 1fr;
      }
    }

    .payment-card, .summary-card, .earnings-card, .guarantee-card {
      padding: 1.5rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .card-title-with-icon {
      display: flex;
      align-items: center;
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .card-title-with-icon mat-icon {
      margin-right: 0.5rem;
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    .full-width {
      width: 100%;
      margin-bottom: 1rem;
    }

    .row-fields {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .half-width {
      flex: 1;
    }

    .form-divider {
      margin: 1.5rem 0;
    }

    .secure-payment-info {
      background-color: #e3f2fd; /* Light blue */
      border: 1px solid #bbdefb; /* Lighter blue */
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      color: #1565c0; /* Darker blue */
    }

    .secure-payment-info mat-icon {
      margin-right: 0.5rem;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    .secure-text {
      font-weight: 500;
      font-size: 0.95rem;
    }

    .secure-description {
      font-size: 0.85rem;
      margin-top: 0.5rem;
      line-height: 1.4;
    }

    .full-width-btn {
      width: 100%;
      padding: 0.8rem 1rem;
      font-size: 1.1rem;
      height: 48px;
    }

    .full-width-btn mat-icon {
      margin-right: 0.5rem;
    }

    .submit-btn mat-spinner {
      margin-right: 0.5rem;
    }

    .summary-section {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .course-summary-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .course-image-placeholder {
      width: 64px;
      height: 64px;
      background-color: #e1bee7; /* Light purple */
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .course-image-placeholder mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: #8e24aa; /* Dark purple */
    }

    .course-details h3 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.2rem;
    }

    .course-instructor {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 0.5rem;
    }

    .course-meta-summary {
      display: flex;
      flex-wrap: wrap;
      gap: 0.8rem;
      font-size: 0.85rem;
      color: #777;
    }

    .course-meta-summary .meta-item {
      display: flex;
      align-items: center;
      gap: 0.2rem;
    }

    .course-meta-summary .meta-item mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: #999;
    }

    .course-meta-summary .meta-item .star-icon {
      color: #ffc107; /* Yellow */
    }

    .summary-divider {
      margin: 1rem 0;
    }

    .price-breakdown, .earnings-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.5rem;
      font-size: 0.95rem;
      color: #444;
    }

    .price-breakdown .sub-item {
      font-size: 0.85rem;
      color: #777;
    }

    .total-price {
      display: flex;
      justify-content: space-between;
      font-size: 1.3rem;
      font-weight: bold;
      margin-top: 1rem;
    }

    .trainer-earnings {
      color: #388e3c; /* Green */
      font-weight: 500;
    }

    .platform-fee {
      color: #673ab7; /* Purple */
      font-weight: 500;
    }

    .total-earnings {
      font-size: 1.1rem;
      font-weight: bold;
    }

    .guarantee-card {
      background-color: #e8f5e9; /* Light green */
      border: 1px solid #c8e6c9; /* Lighter green */
      color: #388e3c; /* Dark green */
    }

    .guarantee-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
    }

    .guarantee-header mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    .guarantee-text {
      font-size: 0.85rem;
      line-height: 1.4;
    }

    /* Payment Success Overlay */
    .payment-success-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .success-card {
      text-align: center;
      padding: 2rem;
      max-width: 400px;
      width: 100%;
    }

    .success-icon-wrapper {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #e8f5e9;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1.5rem;
    }

    .success-icon-wrapper mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #4caf50;
    }

    .success-card h2 {
      font-size: 1.8rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .success-card p {
      font-size: 1rem;
      color: #666;
      margin-bottom: 1.5rem;
    }

    .purchased-course-info {
      background-color: #f5f5f5;
      border-radius: 8px;
      padding: 1rem;
    }

    .purchased-course-info .course-title {
      font-weight: 500;
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .purchased-course-info .course-price {
      font-size: 1.5rem;
      font-weight: bold;
      color: #4caf50; /* Green */
    }
  `,
  ],
})
export class PaymentComponent implements OnInit {
  courseId!: number
  course!: Course
  paymentForm!: FormGroup
  isProcessing = false
  paymentSuccess = false
  currentUser!: User | null

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private courseService: CourseService,
    private paymentService: PaymentService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user
    })

    this.route.paramMap.subscribe((params) => {
      this.courseId = Number(params.get("courseId"))
      this.loadCourseDetails()
    })

    this.initPaymentForm()
  }

  initPaymentForm(): void {
    this.paymentForm = this.fb.group({
      email: [this.currentUser?.email || "", [Validators.required, Validators.email]],
      cardNumber: ["", [Validators.required, Validators.pattern(/^\d{4}\s\d{4}\s\d{4}\s\d{4}$/)]],
      expiryDate: ["", [Validators.required, Validators.pattern(/^(0[1-9]|1[0-2])\/\d{2}$/)]],
      cvv: ["", [Validators.required, Validators.pattern(/^\d{3}$/)]],
      cardName: ["", Validators.required],
    })
  }

  loadCourseDetails(): void {
    // Mock data for demonstration
    this.course = {
      id: this.courseId,
      titre: "React Fundamentals",
      description: "Apprenez les bases de React avec des exemples pratiques et des projets concrets.",
      prix: 99.99,
      duree: 120,
      niveau: "Débutant",
      formateurId: 1,
      formateur: { id: 1, nom: "Dupont", prenom: "Jean", email: "<EMAIL>", role: "Formateur" },
      contenus: [],
      nombreEtudiants: 245,
      note: 4.8,
      estGratuit: false,
    }

    // Uncomment to fetch from API
    /*
    this.courseService.getCours(this.courseId).subscribe({
      next: (data) => {
        this.course = data;
        this.paymentForm.patchValue({ email: this.currentUser?.email || '' });
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.router.navigate(['/courses']);
      }
    });
    */
  }

  formatCardNumber(event: Event): void {
    const input = event.target as HTMLInputElement
    let value = input.value.replace(/\s/g, "")
    if (value.length > 0) {
      value = value.match(/.{1,4}/g)?.join(" ") || ""
    }
    this.paymentForm.get("cardNumber")?.setValue(value, { emitEvent: false })
  }

  formatExpiryDate(event: Event): void {
    const input = event.target as HTMLInputElement
    let value = input.value.replace(/\D/g, "")
    if (value.length > 2) {
      value = value.substring(0, 2) + "/" + value.substring(2, 4)
    }
    this.paymentForm.get("expiryDate")?.setValue(value, { emitEvent: false })
  }

  formatCvv(event: Event): void {
    const input = event.target as HTMLInputElement
    const value = input.value.replace(/\D/g, "").substring(0, 3)
    this.paymentForm.get("cvv")?.setValue(value, { emitEvent: false })
  }

  onSubmit(): void {
    if (this.paymentForm.valid && this.currentUser && this.course) {
      this.isProcessing = true
      const paiementData = {
        clientId: this.currentUser.id,
        coursId: this.course.id,
        montant: this.course.prix,
      }

      // Mock payment processing
      setTimeout(() => {
        this.isProcessing = false
        this.paymentSuccess = true
        this.snackBar.open("Paiement réussi !", "Fermer", { duration: 3000 })
        setTimeout(() => {
          this.router.navigate(["/courses", this.course.id])
        }, 3000)
      }, 2000)

      // Uncomment to use API
      /*
      this.paymentService.effectuerPaiement(paiementData).subscribe({
        next: (response) => {
          this.isProcessing = false;
          this.paymentSuccess = true;
          this.snackBar.open('Paiement réussi !', 'Fermer', { duration: 3000 });
          setTimeout(() => {
            this.router.navigate(['/courses', this.course.id]);
          }, 3000);
        },
        error: (err) => {
          this.isProcessing = false;
          this.snackBar.open('Erreur lors du paiement. Veuillez réessayer.', 'Fermer', { duration: 5000 });
          console.error(err);
        }
      });
      */
    } else {
      this.snackBar.open("Veuillez remplir correctement toutes les informations de paiement.", "Fermer", {
        duration: 5000,
      })
      this.paymentForm.markAllAsTouched()
    }
  }

  get trainerEarnings(): number {
    return this.course ? this.course.prix * 0.7 : 0
  }

  get platformFee(): number {
    return this.course ? this.course.prix * 0.3 : 0
  }
}
