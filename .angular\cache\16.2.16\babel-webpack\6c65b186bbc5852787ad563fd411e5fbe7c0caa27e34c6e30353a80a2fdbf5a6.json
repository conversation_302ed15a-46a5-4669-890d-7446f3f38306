{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/services/course.service\";\nimport * as i3 from \"../../../core/services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/progress-bar\";\nimport * as i11 from \"@angular/material/tabs\";\nfunction CourseDetailComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"span\");\n    i0.ɵɵtext(3, \"Progression du cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"mat-progress-bar\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.course.progression, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r0.course.progression);\n  }\n}\nfunction CourseDetailComponent_div_57_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseDetailComponent_div_57_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.getContentIcon(content_r8.typeContenu));\n  }\n}\nfunction CourseDetailComponent_div_57_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"lock\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseDetailComponent_div_57_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", content_r8.duree, \" minutes\");\n  }\n}\nfunction CourseDetailComponent_div_57_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function CourseDetailComponent_div_57_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const content_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.handleStartContent(content_r8.id, content_r8.typeContenu));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", content_r8.estComplete ? \"Revoir\" : \"Commencer\", \" \");\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"completed\": a0,\n    \"unlocked\": a1,\n    \"locked\": a2\n  };\n};\nfunction CourseDetailComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵtemplate(2, CourseDetailComponent_div_57_mat_icon_2_Template, 2, 0, \"mat-icon\", 37);\n    i0.ɵɵtemplate(3, CourseDetailComponent_div_57_mat_icon_3_Template, 2, 1, \"mat-icon\", 37);\n    i0.ɵɵtemplate(4, CourseDetailComponent_div_57_mat_icon_4_Template, 2, 0, \"mat-icon\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 38)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 39);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, CourseDetailComponent_div_57_span_10_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, CourseDetailComponent_div_57_button_11_Template, 2, 1, \"button\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c0, content_r8.estComplete, content_r8.estDebloque && !content_r8.estComplete, !content_r8.estDebloque));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", content_r8.estComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !content_r8.estComplete && content_r8.estDebloque);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !content_r8.estDebloque);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(content_r8.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(content_r8.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", content_r8.duree);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.course.estAchete && content_r8.estDebloque);\n  }\n}\nconst _c1 = function (a0) {\n  return [a0, \"EUR\", \"symbol\", \"1.2-2\", \"fr\"];\n};\nfunction CourseDetailComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"span\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBindV(3, 1, i0.ɵɵpureFunction1(7, _c1, ctx_r2.course.prix)));\n  }\n}\nfunction CourseDetailComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"mat-chip-listbox\")(2, \"mat-chip\", 46);\n    i0.ɵɵtext(3, \"Cours Gratuit\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CourseDetailComponent_button_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function CourseDetailComponent_button_78_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.enrollInCourse(ctx_r21.course.id));\n    });\n    i0.ɵɵtext(1, \" Commencer le cours \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = function (a1) {\n  return [\"/payment\", a1];\n};\nfunction CourseDetailComponent_button_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 48)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"euro_symbol\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Acheter le cours \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c2, ctx_r5.course.id));\n  }\n}\nfunction CourseDetailComponent_button_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function CourseDetailComponent_button_80_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.selectedTabIndex = 1);\n    });\n    i0.ɵɵtext(1, \" Continuer le cours \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseDetailComponent_mat_chip_listbox_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip-listbox\", 50)(1, \"mat-chip\", 51);\n    i0.ɵɵtext(2, \"Cours achet\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CourseDetailComponent {\n  constructor(route, router, courseService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.courseService = courseService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.selectedTabIndex = 0;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      this.courseId = Number(params.get(\"id\"));\n      this.loadCourseDetails();\n    });\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  loadCourseDetails() {\n    // Mock data for demonstration\n    this.course = {\n      id: this.courseId,\n      titre: \"React Fundamentals\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets. Ce cours couvre tous les concepts essentiels pour débuter avec React : composants, props, state, hooks, et bien plus encore.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"<EMAIL>\",\n        role: \"Formateur\",\n        bio: \"Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs.\"\n      },\n      contenus: [{\n        id: 1,\n        titre: \"Introduction à React\",\n        description: \"Découvrez React et ses concepts de base\",\n        typeContenu: \"Video\",\n        duree: 30,\n        estComplete: true,\n        estDebloque: true,\n        ordre: 1,\n        coursId: this.courseId\n      }, {\n        id: 2,\n        titre: \"Components et Props\",\n        description: \"Apprenez à créer et utiliser des composants\",\n        typeContenu: \"Video\",\n        duree: 45,\n        estComplete: true,\n        estDebloque: true,\n        ordre: 2,\n        coursId: this.courseId\n      }, {\n        id: 3,\n        titre: \"Quiz - Bases de React\",\n        description: \"Testez vos connaissances sur les bases\",\n        typeContenu: \"Quiz\",\n        estComplete: false,\n        estDebloque: true,\n        ordre: 3,\n        coursId: this.courseId\n      }, {\n        id: 4,\n        titre: \"State et Hooks\",\n        description: \"Gérez l'état de vos composants\",\n        typeContenu: \"Video\",\n        duree: 50,\n        estComplete: false,\n        estDebloque: false,\n        ordre: 4,\n        coursId: this.courseId\n      }, {\n        id: 5,\n        titre: \"Résumé du chapitre\",\n        description: \"Points clés à retenir\",\n        typeContenu: \"Resume\",\n        estComplete: false,\n        estDebloque: false,\n        ordre: 5,\n        coursId: this.courseId\n      }],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n      estAchete: true,\n      progression: 40 // Simulate progress\n    };\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(this.courseId).subscribe({\n      next: (data) => {\n        this.course = data;\n        // TODO: Fetch user enrollment status and progress\n        this.course.estAchete = true; // Example\n        this.course.progression = 40; // Example\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  getLevelColor(niveau) {\n    switch (niveau) {\n      case \"Débutant\":\n        return \"bg-green-100\";\n      case \"Intermédiaire\":\n        return \"bg-yellow-100\";\n      case \"Avancé\":\n        return \"bg-red-100\";\n      default:\n        return \"bg-gray-100\";\n    }\n  }\n  getContentIcon(type) {\n    switch (type) {\n      case \"Video\":\n        return \"play_circle\";\n      case \"Quiz\":\n        return \"quiz\";\n      case \"Resume\":\n        return \"description\";\n      default:\n        return \"book\";\n    }\n  }\n  handleStartContent(contentId, typeContenu) {\n    if (!this.course.estAchete) {\n      this.snackBar.open(\"Veuillez acheter le cours pour accéder au contenu.\", \"Fermer\", {\n        duration: 3000\n      });\n      return;\n    }\n    const content = this.course.contenus.find(c => c.id === contentId);\n    if (!content?.estDebloque) {\n      this.snackBar.open(\"Ce contenu n'est pas encore débloqué.\", \"Fermer\", {\n        duration: 3000\n      });\n      return;\n    }\n    switch (typeContenu) {\n      case \"Quiz\":\n        this.router.navigate([\"/quiz\", contentId]);\n        break;\n      case \"Video\":\n        this.router.navigate([\"/video\", contentId]); // Assuming a video player component\n        break;\n      case \"Resume\":\n        this.router.navigate([\"/resume\", contentId]); // Assuming a resume viewer component\n        break;\n      default:\n        this.snackBar.open(\"Type de contenu non pris en charge.\", \"Fermer\", {\n          duration: 3000\n        });\n    }\n  }\n  enrollInCourse(courseId) {\n    if (!this.currentUser) {\n      this.snackBar.open(\"Veuillez vous connecter pour vous inscrire.\", \"Fermer\", {\n        duration: 3000\n      });\n      this.router.navigate([\"/auth/login\"]);\n      return;\n    }\n    // Call the service to enroll in a free course\n    // this.courseService.enrollInCourse(courseId).subscribe({\n    //   next: (res) => {\n    //     this.snackBar.open('Inscription réussie au cours gratuit !', 'Fermer', { duration: 3000 });\n    //     this.course.estAchete = true; // Update UI\n    //     this.course.progression = 0;\n    //   },\n    //   error: (err) => {\n    //     this.snackBar.open('Erreur lors de l\\'inscription.', 'Fermer', { duration: 3000 });\n    //     console.error(err);\n    //   }\n    // });\n    this.snackBar.open(\"Inscription réussie au cours gratuit (simulé) !\", \"Fermer\", {\n      duration: 3000\n    });\n    this.course.estAchete = true; // Simulate UI update\n    this.course.progression = 0;\n  }\n  static {\n    this.ɵfac = function CourseDetailComponent_Factory(t) {\n      return new (t || CourseDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CourseService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CourseDetailComponent,\n      selectors: [[\"app-course-detail\"]],\n      decls: 106,\n      vars: 26,\n      consts: [[1, \"course-detail-container\"], [1, \"content-wrapper\"], [1, \"main-content\"], [1, \"course-header\"], [1, \"description\"], [1, \"course-meta\"], [1, \"meta-item\"], [1, \"star-icon\"], [\"class\", \"progress-section\", 4, \"ngIf\"], [\"animationDuration\", \"0ms\", 3, \"selectedIndex\", \"selectedIndexChange\"], [\"label\", \"Aper\\u00E7u\"], [1, \"tab-card\"], [1, \"description-full\"], [1, \"learning-outcomes\"], [\"label\", \"Contenu\"], [1, \"content-list\"], [\"class\", \"content-item\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Formateur\"], [1, \"instructor-info\"], [1, \"instructor-avatar\"], [1, \"instructor-bio\"], [1, \"sidebar\"], [1, \"sticky-card\"], [\"class\", \"price-section\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"class\", \"full-width-btn\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"full-width-btn\", 3, \"routerLink\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"full-width-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"purchased-chip\", 4, \"ngIf\"], [1, \"course-summary\"], [1, \"summary-item\"], [1, \"rating-display\"], [1, \"progress-section\"], [1, \"progress-label\"], [\"mode\", \"determinate\", 3, \"value\"], [1, \"content-item\"], [1, \"content-icon-wrapper\", 3, \"ngClass\"], [4, \"ngIf\"], [1, \"content-details\"], [1, \"content-description\"], [\"class\", \"content-duration\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [1, \"content-duration\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"price-section\"], [1, \"price-value\"], [1, \"free-chip-large\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 1, \"full-width-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full-width-btn\", 3, \"routerLink\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full-width-btn\", 3, \"click\"], [1, \"purchased-chip\"], [1, \"purchased-chip-item\"]],\n      template: function CourseDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 4);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"mat-chip-listbox\")(10, \"mat-chip\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 6)(13, \"mat-icon\", 7);\n          i0.ɵɵtext(14, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\");\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(22, CourseDetailComponent_div_22_Template, 7, 2, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"mat-tab-group\", 9);\n          i0.ɵɵlistener(\"selectedIndexChange\", function CourseDetailComponent_Template_mat_tab_group_selectedIndexChange_23_listener($event) {\n            return ctx.selectedTabIndex = $event;\n          });\n          i0.ɵɵelementStart(24, \"mat-tab\", 10)(25, \"mat-card\", 11)(26, \"mat-card-title\");\n          i0.ɵɵtext(27, \"Description du cours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-card-content\")(29, \"p\", 12);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"h3\");\n          i0.ɵɵtext(33, \"Ce que vous apprendrez :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"ul\")(35, \"li\")(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \" Les concepts fondamentaux de React\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\")(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Cr\\u00E9ation et utilisation de composants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"li\")(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Gestion de l'\\u00E9tat avec les hooks\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"li\")(48, \"mat-icon\");\n          i0.ɵɵtext(49, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" Bonnes pratiques de d\\u00E9veloppement\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(51, \"mat-tab\", 14)(52, \"mat-card\", 11)(53, \"mat-card-title\");\n          i0.ɵɵtext(54, \"Contenu du cours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"mat-card-content\")(56, \"div\", 15);\n          i0.ɵɵtemplate(57, CourseDetailComponent_div_57_Template, 12, 12, \"div\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(58, \"mat-tab\", 17)(59, \"mat-card\", 11)(60, \"mat-card-title\");\n          i0.ɵɵtext(61, \"\\u00C0 propos du formateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"mat-card-content\")(63, \"div\", 18)(64, \"div\", 19)(65, \"span\");\n          i0.ɵɵtext(66);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\")(68, \"h3\");\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"p\", 20);\n          i0.ɵɵtext(71, \"D\\u00E9veloppeur Full-Stack avec 8 ans d'exp\\u00E9rience. Sp\\u00E9cialis\\u00E9 en React et Node.js. Formateur passionn\\u00E9 ayant form\\u00E9 plus de 1000 d\\u00E9veloppeurs.\");\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵelementStart(72, \"div\", 21)(73, \"mat-card\", 22)(74, \"mat-card-content\");\n          i0.ɵɵtemplate(75, CourseDetailComponent_div_75_Template, 4, 9, \"div\", 23);\n          i0.ɵɵtemplate(76, CourseDetailComponent_div_76_Template, 4, 0, \"div\", 23);\n          i0.ɵɵelementStart(77, \"div\", 24);\n          i0.ɵɵtemplate(78, CourseDetailComponent_button_78_Template, 2, 0, \"button\", 25);\n          i0.ɵɵtemplate(79, CourseDetailComponent_button_79_Template, 4, 3, \"button\", 26);\n          i0.ɵɵtemplate(80, CourseDetailComponent_button_80_Template, 2, 0, \"button\", 27);\n          i0.ɵɵtemplate(81, CourseDetailComponent_mat_chip_listbox_81_Template, 3, 0, \"mat-chip-listbox\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 29)(83, \"div\", 30)(84, \"span\");\n          i0.ɵɵtext(85, \"Dur\\u00E9e totale:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"span\");\n          i0.ɵɵtext(87);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 30)(89, \"span\");\n          i0.ɵɵtext(90, \"Niveau:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"span\");\n          i0.ɵɵtext(92);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"div\", 30)(94, \"span\");\n          i0.ɵɵtext(95, \"\\u00C9tudiants:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"span\");\n          i0.ɵɵtext(97);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 30)(99, \"span\");\n          i0.ɵɵtext(100, \"Note:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"div\", 31)(102, \"mat-icon\", 7);\n          i0.ɵɵtext(103, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"span\");\n          i0.ɵɵtext(105);\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.course.titre);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.course.description);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.getLevelColor(ctx.course.niveau));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.course.niveau);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate2(\"\", ctx.course.note, \" (\", ctx.course.nombreEtudiants, \" \\u00E9tudiants)\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", ctx.course.duree, \" minutes\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.course.estAchete);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"selectedIndex\", ctx.selectedTabIndex);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.course.description);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.course.contenus);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate2(\"\", ctx.course.formateur.prenom[0], \"\", ctx.course.formateur.nom[0], \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"\", ctx.course.formateur.prenom, \" \", ctx.course.formateur.nom, \"\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", !ctx.course.estGratuit);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.course.estGratuit);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.course.estAchete && ctx.course.estGratuit);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.course.estAchete && !ctx.course.estGratuit);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.course.estAchete);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.course.estAchete);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"\", ctx.course.duree, \" minutes\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.course.niveau);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.course.nombreEtudiants);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.course.note);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.MatCard, i6.MatCardContent, i6.MatCardTitle, i7.MatButton, i8.MatIcon, i9.MatChip, i9.MatChipListbox, i10.MatProgressBar, i11.MatTab, i11.MatTabGroup, i1.RouterLink, i5.CurrencyPipe],\n      styles: [\".course-detail-container[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 2rem;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  grid-column: span 2; \\n\\n}\\n\\n@media (min-width: 960px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    grid-column: span 2/span 2;\\n  }\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-column: span 1/span 1;\\n  }\\n}\\n.course-header[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.course-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: bold;\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.course-header[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.course-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.course-meta[_ngcontent-%COMP%]   .mat-chip[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  padding: 0.4rem 0.8rem;\\n  height: auto;\\n}\\n\\n.course-meta[_ngcontent-%COMP%]   .mat-chip.bg-green-100[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n\\n.course-meta[_ngcontent-%COMP%]   .mat-chip.bg-yellow-100[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n}\\n\\n.course-meta[_ngcontent-%COMP%]   .mat-chip.bg-red-100[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  font-size: 0.95rem;\\n  color: #555;\\n}\\n\\n.meta-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  color: #888;\\n}\\n\\n.meta-item[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%] {\\n  color: #ffc107; \\n\\n}\\n\\n.progress-section[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  margin-bottom: 0.5rem;\\n  color: #444;\\n}\\n\\nmat-progress-bar[_ngcontent-%COMP%] {\\n  height: 8px;\\n  border-radius: 4px;\\n}\\n\\n.tab-card[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  padding: 1.5rem;\\n}\\n\\n.tab-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n}\\n\\n.description-full[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.6;\\n  color: #444;\\n}\\n\\n.learning-outcomes[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.learning-outcomes[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n  color: #333;\\n}\\n\\n.learning-outcomes[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.learning-outcomes[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 0.8rem;\\n  font-size: 0.95rem;\\n  color: #555;\\n}\\n\\n.learning-outcomes[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50; \\n\\n  margin-right: 0.8rem;\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n\\n.content-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.content-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem;\\n  border: 1px solid #eee;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  transition: box-shadow 0.2s ease-in-out;\\n}\\n\\n.content-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.content-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 1rem;\\n  flex-shrink: 0;\\n}\\n\\n.content-icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.content-icon-wrapper.completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9; \\n\\n}\\n\\n.content-icon-wrapper.completed[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50; \\n\\n}\\n\\n.content-icon-wrapper.unlocked[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd; \\n\\n}\\n\\n.content-icon-wrapper.unlocked[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2196f3; \\n\\n}\\n\\n.content-icon-wrapper.locked[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5; \\n\\n}\\n\\n.content-icon-wrapper.locked[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #9e9e9e; \\n\\n}\\n\\n.content-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.content-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n  margin-bottom: 0.2rem;\\n  color: #333;\\n}\\n\\n.content-description[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #777;\\n  margin-bottom: 0.4rem;\\n}\\n\\n.content-duration[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #888;\\n}\\n\\n.content-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n  flex-shrink: 0;\\n}\\n\\n.instructor-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1.5rem;\\n  padding: 1rem 0;\\n}\\n\\n.instructor-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background-color: #e1bee7; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.8rem;\\n  font-weight: bold;\\n  color: #8e24aa; \\n\\n  flex-shrink: 0;\\n}\\n\\n.instructor-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  color: #333;\\n}\\n\\n.instructor-bio[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  line-height: 1.5;\\n  color: #555;\\n}\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  grid-column: span 1;\\n}\\n\\n.sticky-card[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 2rem; \\n\\n  padding: 1.5rem;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.price-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.price-value[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: bold;\\n  color: #673ab7; \\n\\n}\\n\\n.free-chip-large[_ngcontent-%COMP%] {\\n  background-color: #e6ffed;\\n  color: #28a745;\\n  font-size: 1.2rem;\\n  padding: 0.8rem 1.5rem;\\n  height: auto;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.8rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.full-width-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  font-size: 1rem;\\n  padding: 0.8rem 1rem;\\n}\\n\\n.full-width-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  margin-right: 0.5rem;\\n}\\n\\n.purchased-chip[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n}\\n\\n.purchased-chip-item[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #4caf50;\\n  font-size: 0.9rem;\\n  padding: 0.4rem 0.8rem;\\n  height: auto;\\n}\\n\\n.course-summary[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #eee;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.8rem;\\n  font-size: 0.95rem;\\n  color: #555;\\n}\\n\\n.summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.rating-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n}\\n\\n.rating-display[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%] {\\n  color: #ffc107; \\n\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "course", "progression", "ɵɵproperty", "ɵɵtextInterpolate", "ctx_r11", "getContentIcon", "content_r8", "typeContenu", "duree", "ɵɵlistener", "CourseDetailComponent_div_57_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r19", "ɵɵnextContext", "$implicit", "ctx_r17", "ɵɵresetView", "handleStartContent", "id", "estComplete", "ɵɵtemplate", "CourseDetailComponent_div_57_mat_icon_2_Template", "CourseDetailComponent_div_57_mat_icon_3_Template", "CourseDetailComponent_div_57_mat_icon_4_Template", "CourseDetailComponent_div_57_span_10_Template", "CourseDetailComponent_div_57_button_11_Template", "ɵɵpureFunction3", "_c0", "estDebloque", "titre", "description", "ctx_r1", "estAchete", "ɵɵpipeBindV", "ɵɵpureFunction1", "_c1", "ctx_r2", "prix", "CourseDetailComponent_button_78_Template_button_click_0_listener", "_r22", "ctx_r21", "enrollInCourse", "_c2", "ctx_r5", "CourseDetailComponent_button_80_Template_button_click_0_listener", "_r24", "ctx_r23", "selectedTabIndex", "CourseDetailComponent", "constructor", "route", "router", "courseService", "authService", "snackBar", "ngOnInit", "paramMap", "subscribe", "params", "courseId", "Number", "get", "loadCourseDetails", "currentUser$", "user", "currentUser", "niveau", "formateurId", "formateur", "nom", "prenom", "email", "role", "bio", "contenus", "ordre", "coursId", "nombreEtudiants", "note", "estGratuit", "getLevelColor", "type", "contentId", "open", "duration", "content", "find", "c", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "CourseService", "i3", "AuthService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "CourseDetailComponent_Template", "rf", "ctx", "CourseDetailComponent_div_22_Template", "CourseDetailComponent_Template_mat_tab_group_selectedIndexChange_23_listener", "$event", "CourseDetailComponent_div_57_Template", "CourseDetailComponent_div_75_Template", "CourseDetailComponent_div_76_Template", "CourseDetailComponent_button_78_Template", "CourseDetailComponent_button_79_Template", "CourseDetailComponent_button_80_Template", "CourseDetailComponent_mat_chip_listbox_81_Template", "ɵɵclassMap", "ɵɵtextInterpolate2"], "sources": ["C:\\e-learning\\src\\app\\features\\courses\\course-detail\\course-detail.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { ActivatedRoute, Router } from \"@angular/router\"\nimport { CourseService } from \"../../../core/services/course.service\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Course } from \"../../../core/models/course.model\"\nimport { AuthService } from \"../../../core/services/auth.service\"\nimport { User } from \"../../../core/models/user.model\"\n\n@Component({\n  selector: \"app-course-detail\",\n  template: `\n    <div class=\"course-detail-container\">\n      <div class=\"content-wrapper\">\n        <!-- Main Content -->\n        <div class=\"main-content\">\n          <div class=\"course-header\">\n            <h1>{{ course.titre }}</h1>\n            <p class=\"description\">{{ course.description }}</p>\n\n            <div class=\"course-meta\">\n              <mat-chip-listbox>\n                <mat-chip [class]=\"getLevelColor(course.niveau)\">{{ course.niveau }}</mat-chip>\n              </mat-chip-listbox>\n              <div class=\"meta-item\">\n                <mat-icon class=\"star-icon\">star</mat-icon>\n                <span>{{ course.note }} ({{ course.nombreEtudiants }} étudiants)</span>\n              </div>\n              <div class=\"meta-item\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ course.duree }} minutes</span>\n              </div>\n            </div>\n\n            <div class=\"progress-section\" *ngIf=\"course.estAchete\">\n              <div class=\"progress-label\">\n                <span>Progression du cours</span>\n                <span>{{ course.progression }}%</span>\n              </div>\n              <mat-progress-bar mode=\"determinate\" [value]=\"course.progression\"></mat-progress-bar>\n            </div>\n          </div>\n\n          <mat-tab-group animationDuration=\"0ms\" [(selectedIndex)]=\"selectedTabIndex\">\n            <mat-tab label=\"Aperçu\">\n              <mat-card class=\"tab-card\">\n                <mat-card-title>Description du cours</mat-card-title>\n                <mat-card-content>\n                  <p class=\"description-full\">{{ course.description }}</p>\n\n                  <div class=\"learning-outcomes\">\n                    <h3>Ce que vous apprendrez :</h3>\n                    <ul>\n                      <li><mat-icon>check_circle</mat-icon> Les concepts fondamentaux de React</li>\n                      <li><mat-icon>check_circle</mat-icon> Création et utilisation de composants</li>\n                      <li><mat-icon>check_circle</mat-icon> Gestion de l'état avec les hooks</li>\n                      <li><mat-icon>check_circle</mat-icon> Bonnes pratiques de développement</li>\n                    </ul>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-tab>\n\n            <mat-tab label=\"Contenu\">\n              <mat-card class=\"tab-card\">\n                <mat-card-title>Contenu du cours</mat-card-title>\n                <mat-card-content>\n                  <div class=\"content-list\">\n                    <div *ngFor=\"let content of course.contenus; let i = index\" class=\"content-item\">\n                      <div class=\"content-icon-wrapper\" \n                           [ngClass]=\"{\n                             'completed': content.estComplete, \n                             'unlocked': content.estDebloque && !content.estComplete, \n                             'locked': !content.estDebloque\n                           }\">\n                        <mat-icon *ngIf=\"content.estComplete\">check_circle</mat-icon>\n                        <mat-icon *ngIf=\"!content.estComplete && content.estDebloque\">{{ getContentIcon(content.typeContenu) }}</mat-icon>\n                        <mat-icon *ngIf=\"!content.estDebloque\">lock</mat-icon>\n                      </div>\n                      <div class=\"content-details\">\n                        <h4>{{ content.titre }}</h4>\n                        <p class=\"content-description\">{{ content.description }}</p>\n                        <span *ngIf=\"content.duree\" class=\"content-duration\">{{ content.duree }} minutes</span>\n                      </div>\n                      <button mat-stroked-button color=\"primary\" \n                              *ngIf=\"course.estAchete && content.estDebloque\" \n                              (click)=\"handleStartContent(content.id, content.typeContenu)\">\n                        {{ content.estComplete ? 'Revoir' : 'Commencer' }}\n                      </button>\n                    </div>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-tab>\n\n            <mat-tab label=\"Formateur\">\n              <mat-card class=\"tab-card\">\n                <mat-card-title>À propos du formateur</mat-card-title>\n                <mat-card-content>\n                  <div class=\"instructor-info\">\n                    <div class=\"instructor-avatar\">\n                      <span>{{ course.formateur.prenom[0] }}{{ course.formateur.nom[0] }}</span>\n                    </div>\n                    <div>\n                      <h3>{{ course.formateur.prenom }} {{ course.formateur.nom }}</h3>\n                      <p class=\"instructor-bio\">Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs.</p>\n                    </div>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-tab>\n          </mat-tab-group>\n        </div>\n\n        <!-- Sidebar -->\n        <div class=\"sidebar\">\n          <mat-card class=\"sticky-card\">\n            <mat-card-content>\n              <div class=\"price-section\" *ngIf=\"!course.estGratuit\">\n                <span class=\"price-value\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <div class=\"price-section\" *ngIf=\"course.estGratuit\">\n                <mat-chip-listbox>\n                  <mat-chip class=\"free-chip-large\">Cours Gratuit</mat-chip>\n                </mat-chip-listbox>\n              </div>\n\n              <div class=\"action-buttons\">\n                <button mat-raised-button color=\"accent\" class=\"full-width-btn\" \n                        *ngIf=\"!course.estAchete && course.estGratuit\" \n                        (click)=\"enrollInCourse(course.id)\">\n                  Commencer le cours\n                </button>\n                <button mat-raised-button color=\"primary\" class=\"full-width-btn\" \n                        *ngIf=\"!course.estAchete && !course.estGratuit\" \n                        [routerLink]=\"['/payment', course.id]\">\n                  <mat-icon>euro_symbol</mat-icon>\n                  Acheter le cours\n                </button>\n                <button mat-raised-button color=\"primary\" class=\"full-width-btn\" \n                        *ngIf=\"course.estAchete\" \n                        (click)=\"selectedTabIndex = 1\">\n                  Continuer le cours\n                </button>\n                <mat-chip-listbox *ngIf=\"course.estAchete\" class=\"purchased-chip\">\n                  <mat-chip class=\"purchased-chip-item\">Cours acheté</mat-chip>\n                </mat-chip-listbox>\n              </div>\n\n              <div class=\"course-summary\">\n                <div class=\"summary-item\">\n                  <span>Durée totale:</span>\n                  <span>{{ course.duree }} minutes</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Niveau:</span>\n                  <span>{{ course.niveau }}</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Étudiants:</span>\n                  <span>{{ course.nombreEtudiants }}</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Note:</span>\n                  <div class=\"rating-display\">\n                    <mat-icon class=\"star-icon\">star</mat-icon>\n                    <span>{{ course.note }}</span>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .course-detail-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n    }\n\n    .content-wrapper {\n      display: grid;\n      grid-template-columns: 2fr 1fr;\n      gap: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .main-content {\n      grid-column: span 2; /* Default to full width on small screens */\n    }\n\n    @media (min-width: 960px) {\n      .main-content {\n        grid-column: span 2 / span 2;\n      }\n      .sidebar {\n        grid-column: span 1 / span 1;\n      }\n    }\n\n    .course-header {\n      margin-bottom: 2rem;\n    }\n\n    .course-header h1 {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-header .description {\n      font-size: 1.1rem;\n      color: #666;\n      margin-bottom: 1.5rem;\n    }\n\n    .course-meta {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      align-items: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .course-meta .mat-chip {\n      font-size: 0.9rem;\n      padding: 0.4rem 0.8rem;\n      height: auto;\n    }\n\n    .course-meta .mat-chip.bg-green-100 { background-color: #d4edda; color: #155724; }\n    .course-meta .mat-chip.bg-yellow-100 { background-color: #fff3cd; color: #856404; }\n    .course-meta .mat-chip.bg-red-100 { background-color: #f8d7da; color: #721c24; }\n\n    .meta-item {\n      display: flex;\n      align-items: center;\n      gap: 0.3rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .meta-item mat-icon {\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n      color: #888;\n    }\n\n    .meta-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .progress-section {\n      margin-top: 1.5rem;\n    }\n\n    .progress-label {\n      display: flex;\n      justify-content: space-between;\n      font-size: 0.9rem;\n      font-weight: 500;\n      margin-bottom: 0.5rem;\n      color: #444;\n    }\n\n    mat-progress-bar {\n      height: 8px;\n      border-radius: 4px;\n    }\n\n    .tab-card {\n      margin-top: 1.5rem;\n      padding: 1.5rem;\n    }\n\n    .tab-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .description-full {\n      font-size: 1rem;\n      line-height: 1.6;\n      color: #444;\n    }\n\n    .learning-outcomes {\n      margin-top: 2rem;\n    }\n\n    .learning-outcomes h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .learning-outcomes ul {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .learning-outcomes li {\n      display: flex;\n      align-items: center;\n      margin-bottom: 0.8rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .learning-outcomes li mat-icon {\n      color: #4caf50; /* Green */\n      margin-right: 0.8rem;\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n    }\n\n    .content-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .content-item {\n      display: flex;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #eee;\n      border-radius: 8px;\n      background-color: #fff;\n      transition: box-shadow 0.2s ease-in-out;\n    }\n\n    .content-item:hover {\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n    }\n\n    .content-icon-wrapper {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 1rem;\n      flex-shrink: 0;\n    }\n\n    .content-icon-wrapper mat-icon {\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .content-icon-wrapper.completed {\n      background-color: #e8f5e9; /* Light green */\n    }\n    .content-icon-wrapper.completed mat-icon {\n      color: #4caf50; /* Green */\n    }\n\n    .content-icon-wrapper.unlocked {\n      background-color: #e3f2fd; /* Light blue */\n    }\n    .content-icon-wrapper.unlocked mat-icon {\n      color: #2196f3; /* Blue */\n    }\n\n    .content-icon-wrapper.locked {\n      background-color: #f5f5f5; /* Light gray */\n    }\n    .content-icon-wrapper.locked mat-icon {\n      color: #9e9e9e; /* Gray */\n    }\n\n    .content-details {\n      flex-grow: 1;\n    }\n\n    .content-details h4 {\n      font-size: 1.1rem;\n      font-weight: 500;\n      margin-bottom: 0.2rem;\n      color: #333;\n    }\n\n    .content-description {\n      font-size: 0.85rem;\n      color: #777;\n      margin-bottom: 0.4rem;\n    }\n\n    .content-duration {\n      font-size: 0.8rem;\n      color: #888;\n    }\n\n    .content-item button {\n      margin-left: 1rem;\n      flex-shrink: 0;\n    }\n\n    .instructor-info {\n      display: flex;\n      align-items: flex-start;\n      gap: 1.5rem;\n      padding: 1rem 0;\n    }\n\n    .instructor-avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background-color: #e1bee7; /* Light purple */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 1.8rem;\n      font-weight: bold;\n      color: #8e24aa; /* Dark purple */\n      flex-shrink: 0;\n    }\n\n    .instructor-info h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .instructor-bio {\n      font-size: 0.95rem;\n      line-height: 1.5;\n      color: #555;\n    }\n\n    .sidebar {\n      grid-column: span 1;\n    }\n\n    .sticky-card {\n      position: sticky;\n      top: 2rem; /* Adjust as needed */\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .price-section {\n      text-align: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .price-value {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .free-chip-large {\n      background-color: #e6ffed;\n      color: #28a745;\n      font-size: 1.2rem;\n      padding: 0.8rem 1.5rem;\n      height: auto;\n    }\n\n    .action-buttons {\n      display: flex;\n      flex-direction: column;\n      gap: 0.8rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .full-width-btn {\n      width: 100%;\n      font-size: 1rem;\n      padding: 0.8rem 1rem;\n    }\n\n    .full-width-btn mat-icon {\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n      margin-right: 0.5rem;\n    }\n\n    .purchased-chip {\n      text-align: center;\n      margin-top: 1rem;\n    }\n\n    .purchased-chip-item {\n      background-color: #e8f5e9;\n      color: #4caf50;\n      font-size: 0.9rem;\n      padding: 0.4rem 0.8rem;\n      height: auto;\n    }\n\n    .course-summary {\n      margin-top: 1.5rem;\n      padding-top: 1.5rem;\n      border-top: 1px solid #eee;\n      display: flex;\n      flex-direction: column;\n      gap: 0.8rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .summary-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .rating-display {\n      display: flex;\n      align-items: center;\n      gap: 0.3rem;\n    }\n\n    .rating-display .star-icon {\n      color: #ffc107; /* Yellow */\n      font-size: 1.1rem;\n      width: 1.1rem;\n      height: 1.1rem;\n    }\n  `,\n  ],\n})\nexport class CourseDetailComponent implements OnInit {\n  courseId!: number\n  course!: Course\n  currentUser!: User | null\n  selectedTabIndex = 0\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private courseService: CourseService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.route.paramMap.subscribe((params) => {\n      this.courseId = Number(params.get(\"id\"))\n      this.loadCourseDetails()\n    })\n\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n    })\n  }\n\n  loadCourseDetails(): void {\n    // Mock data for demonstration\n    this.course = {\n      id: this.courseId,\n      titre: \"React Fundamentals\",\n      description:\n        \"Apprenez les bases de React avec des exemples pratiques et des projets concrets. Ce cours couvre tous les concepts essentiels pour débuter avec React : composants, props, state, hooks, et bien plus encore.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"<EMAIL>\",\n        role: \"Formateur\",\n        bio: \"Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs.\",\n      },\n      contenus: [\n        {\n          id: 1,\n          titre: \"Introduction à React\",\n          description: \"Découvrez React et ses concepts de base\",\n          typeContenu: \"Video\",\n          duree: 30,\n          estComplete: true,\n          estDebloque: true,\n          ordre: 1,\n          coursId: this.courseId,\n        },\n        {\n          id: 2,\n          titre: \"Components et Props\",\n          description: \"Apprenez à créer et utiliser des composants\",\n          typeContenu: \"Video\",\n          duree: 45,\n          estComplete: true,\n          estDebloque: true,\n          ordre: 2,\n          coursId: this.courseId,\n        },\n        {\n          id: 3,\n          titre: \"Quiz - Bases de React\",\n          description: \"Testez vos connaissances sur les bases\",\n          typeContenu: \"Quiz\",\n          estComplete: false,\n          estDebloque: true,\n          ordre: 3,\n          coursId: this.courseId,\n        },\n        {\n          id: 4,\n          titre: \"State et Hooks\",\n          description: \"Gérez l'état de vos composants\",\n          typeContenu: \"Video\",\n          duree: 50,\n          estComplete: false,\n          estDebloque: false,\n          ordre: 4,\n          coursId: this.courseId,\n        },\n        {\n          id: 5,\n          titre: \"Résumé du chapitre\",\n          description: \"Points clés à retenir\",\n          typeContenu: \"Resume\",\n          estComplete: false,\n          estDebloque: false,\n          ordre: 5,\n          coursId: this.courseId,\n        },\n      ],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n      estAchete: true, // Simulate if purchased\n      progression: 40, // Simulate progress\n    }\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(this.courseId).subscribe({\n      next: (data) => {\n        this.course = data;\n        // TODO: Fetch user enrollment status and progress\n        this.course.estAchete = true; // Example\n        this.course.progression = 40; // Example\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  getLevelColor(niveau: string): string {\n    switch (niveau) {\n      case \"Débutant\":\n        return \"bg-green-100\"\n      case \"Intermédiaire\":\n        return \"bg-yellow-100\"\n      case \"Avancé\":\n        return \"bg-red-100\"\n      default:\n        return \"bg-gray-100\"\n    }\n  }\n\n  getContentIcon(type: string): string {\n    switch (type) {\n      case \"Video\":\n        return \"play_circle\"\n      case \"Quiz\":\n        return \"quiz\"\n      case \"Resume\":\n        return \"description\"\n      default:\n        return \"book\"\n    }\n  }\n\n  handleStartContent(contentId: number, typeContenu: string): void {\n    if (!this.course.estAchete) {\n      this.snackBar.open(\"Veuillez acheter le cours pour accéder au contenu.\", \"Fermer\", { duration: 3000 })\n      return\n    }\n\n    const content = this.course.contenus.find((c) => c.id === contentId)\n    if (!content?.estDebloque) {\n      this.snackBar.open(\"Ce contenu n'est pas encore débloqué.\", \"Fermer\", { duration: 3000 })\n      return\n    }\n\n    switch (typeContenu) {\n      case \"Quiz\":\n        this.router.navigate([\"/quiz\", contentId])\n        break\n      case \"Video\":\n        this.router.navigate([\"/video\", contentId]) // Assuming a video player component\n        break\n      case \"Resume\":\n        this.router.navigate([\"/resume\", contentId]) // Assuming a resume viewer component\n        break\n      default:\n        this.snackBar.open(\"Type de contenu non pris en charge.\", \"Fermer\", { duration: 3000 })\n    }\n  }\n\n  enrollInCourse(courseId: number): void {\n    if (!this.currentUser) {\n      this.snackBar.open(\"Veuillez vous connecter pour vous inscrire.\", \"Fermer\", { duration: 3000 })\n      this.router.navigate([\"/auth/login\"])\n      return\n    }\n\n    // Call the service to enroll in a free course\n    // this.courseService.enrollInCourse(courseId).subscribe({\n    //   next: (res) => {\n    //     this.snackBar.open('Inscription réussie au cours gratuit !', 'Fermer', { duration: 3000 });\n    //     this.course.estAchete = true; // Update UI\n    //     this.course.progression = 0;\n    //   },\n    //   error: (err) => {\n    //     this.snackBar.open('Erreur lors de l\\'inscription.', 'Fermer', { duration: 3000 });\n    //     console.error(err);\n    //   }\n    // });\n    this.snackBar.open(\"Inscription réussie au cours gratuit (simulé) !\", \"Fermer\", { duration: 3000 })\n    this.course.estAchete = true // Simulate UI update\n    this.course.progression = 0\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;IAiCYA,EAAA,CAAAC,cAAA,cAAuD;IAE7CD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExCH,EAAA,CAAAI,SAAA,2BAAqF;IACvFJ,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAC,MAAA,CAAAC,WAAA,MAAyB;IAEIT,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAU,UAAA,UAAAH,MAAA,CAAAC,MAAA,CAAAC,WAAA,CAA4B;;;;;IAoCvDT,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC7DH,EAAA,CAAAC,cAAA,eAA8D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAApDH,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAW,iBAAA,CAAAC,OAAA,CAAAC,cAAA,CAAAC,UAAA,CAAAC,WAAA,EAAyC;;;;;IACvGf,EAAA,CAAAC,cAAA,eAAuC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAKtDH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlCH,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,kBAAA,KAAAQ,UAAA,CAAAE,KAAA,aAA2B;;;;;;IAElFhB,EAAA,CAAAC,cAAA,iBAEsE;IAA9DD,EAAA,CAAAiB,UAAA,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAN,UAAA,GAAAd,EAAA,CAAAqB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAwB,WAAA,CAAAD,OAAA,CAAAE,kBAAA,CAAAX,UAAA,CAAAY,EAAA,EAAAZ,UAAA,CAAAC,WAAA,CAAmD;IAAA,EAAC;IACnEf,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAQ,UAAA,CAAAa,WAAA,+BACF;;;;;;;;;;;;IApBF3B,EAAA,CAAAC,cAAA,cAAiF;IAO7ED,EAAA,CAAA4B,UAAA,IAAAC,gDAAA,uBAA6D;IAC7D7B,EAAA,CAAA4B,UAAA,IAAAE,gDAAA,uBAAkH;IAClH9B,EAAA,CAAA4B,UAAA,IAAAG,gDAAA,uBAAsD;IACxD/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IACvBD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAA4B,UAAA,KAAAI,6CAAA,mBAAuF;IACzFhC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA4B,UAAA,KAAAK,+CAAA,qBAIS;IACXjC,EAAA,CAAAG,YAAA,EAAM;;;;;IAnBCH,EAAA,CAAAK,SAAA,GAIE;IAJFL,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAArB,UAAA,CAAAa,WAAA,EAAAb,UAAA,CAAAsB,WAAA,KAAAtB,UAAA,CAAAa,WAAA,GAAAb,UAAA,CAAAsB,WAAA,EAIE;IACMpC,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAU,UAAA,SAAAI,UAAA,CAAAa,WAAA,CAAyB;IACzB3B,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAU,UAAA,UAAAI,UAAA,CAAAa,WAAA,IAAAb,UAAA,CAAAsB,WAAA,CAAiD;IACjDpC,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAU,UAAA,UAAAI,UAAA,CAAAsB,WAAA,CAA0B;IAGjCpC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,iBAAA,CAAAG,UAAA,CAAAuB,KAAA,CAAmB;IACQrC,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAW,iBAAA,CAAAG,UAAA,CAAAwB,WAAA,CAAyB;IACjDtC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAU,UAAA,SAAAI,UAAA,CAAAE,KAAA,CAAmB;IAGnBhB,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAU,UAAA,SAAA6B,MAAA,CAAA/B,MAAA,CAAAgC,SAAA,IAAA1B,UAAA,CAAAsB,WAAA,CAA6C;;;;;;;;IAiC9DpC,EAAA,CAAAC,cAAA,cAAsD;IAC1BD,EAAA,CAAAE,MAAA,GAAwD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA/DH,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAyC,WAAA,OAAAzC,EAAA,CAAA0C,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAApC,MAAA,CAAAqC,IAAA,GAAwD;;;;;IAEpF7C,EAAA,CAAAC,cAAA,cAAqD;IAEfD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAK5DH,EAAA,CAAAC,cAAA,iBAE4C;IAApCD,EAAA,CAAAiB,UAAA,mBAAA6B,iEAAA;MAAA9C,EAAA,CAAAmB,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAwB,WAAA,CAAAwB,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAxC,MAAA,CAAAkB,EAAA,CAAyB;IAAA,EAAC;IACzC1B,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAE+C;IACnCD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHDH,EAAA,CAAAU,UAAA,eAAAV,EAAA,CAAA0C,eAAA,IAAAQ,GAAA,EAAAC,MAAA,CAAA3C,MAAA,CAAAkB,EAAA,EAAsC;;;;;;IAI9C1B,EAAA,CAAAC,cAAA,iBAEuC;IAA/BD,EAAA,CAAAiB,UAAA,mBAAAmC,iEAAA;MAAApD,EAAA,CAAAmB,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAA8B,OAAA,CAAAC,gBAAA,GAA4B,CAAC;IAAA,EAAC;IACpCvD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAC,cAAA,2BAAkE;IAC1BD,EAAA,CAAAE,MAAA,wBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;AA4Y/E,OAAM,MAAOqD,qBAAqB;EAMhCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAP,gBAAgB,GAAG,CAAC;EAQjB;EAEHQ,QAAQA,CAAA;IACN,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,IAAI,CAACC,QAAQ,GAAGC,MAAM,CAACF,MAAM,CAACG,GAAG,CAAC,IAAI,CAAC,CAAC;MACxC,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACT,WAAW,CAACU,YAAY,CAACN,SAAS,CAAEO,IAAI,IAAI;MAC/C,IAAI,CAACC,WAAW,GAAGD,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAF,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC9D,MAAM,GAAG;MACZkB,EAAE,EAAE,IAAI,CAACyC,QAAQ;MACjB9B,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EACT,+MAA+M;MACjNO,IAAI,EAAE,KAAK;MACX7B,KAAK,EAAE,GAAG;MACV0D,MAAM,EAAE,UAAU;MAClBC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QACTlD,EAAE,EAAE,CAAC;QACLmD,GAAG,EAAE,QAAQ;QACbC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,WAAW;QACjBC,GAAG,EAAE;OACN;MACDC,QAAQ,EAAE,CACR;QACExD,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,sBAAsB;QAC7BC,WAAW,EAAE,yCAAyC;QACtDvB,WAAW,EAAE,OAAO;QACpBC,KAAK,EAAE,EAAE;QACTW,WAAW,EAAE,IAAI;QACjBS,WAAW,EAAE,IAAI;QACjB+C,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,IAAI,CAACjB;OACf,EACD;QACEzC,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,qBAAqB;QAC5BC,WAAW,EAAE,6CAA6C;QAC1DvB,WAAW,EAAE,OAAO;QACpBC,KAAK,EAAE,EAAE;QACTW,WAAW,EAAE,IAAI;QACjBS,WAAW,EAAE,IAAI;QACjB+C,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,IAAI,CAACjB;OACf,EACD;QACEzC,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE,wCAAwC;QACrDvB,WAAW,EAAE,MAAM;QACnBY,WAAW,EAAE,KAAK;QAClBS,WAAW,EAAE,IAAI;QACjB+C,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,IAAI,CAACjB;OACf,EACD;QACEzC,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,gBAAgB;QACvBC,WAAW,EAAE,gCAAgC;QAC7CvB,WAAW,EAAE,OAAO;QACpBC,KAAK,EAAE,EAAE;QACTW,WAAW,EAAE,KAAK;QAClBS,WAAW,EAAE,KAAK;QAClB+C,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,IAAI,CAACjB;OACf,EACD;QACEzC,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,oBAAoB;QAC3BC,WAAW,EAAE,uBAAuB;QACpCvB,WAAW,EAAE,QAAQ;QACrBY,WAAW,EAAE,KAAK;QAClBS,WAAW,EAAE,KAAK;QAClB+C,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,IAAI,CAACjB;OACf,CACF;MACDkB,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTC,UAAU,EAAE,KAAK;MACjB/C,SAAS,EAAE,IAAI;MACf/B,WAAW,EAAE,EAAE,CAAE;KAClB;IAED;IACA;;;;;;;;;;;;;;;EAeF;;EAEA+E,aAAaA,CAACd,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,cAAc;MACvB,KAAK,eAAe;QAClB,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,YAAY;MACrB;QACE,OAAO,aAAa;;EAE1B;EAEA7D,cAAcA,CAAC4E,IAAY;IACzB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,aAAa;MACtB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,QAAQ;QACX,OAAO,aAAa;MACtB;QACE,OAAO,MAAM;;EAEnB;EAEAhE,kBAAkBA,CAACiE,SAAiB,EAAE3E,WAAmB;IACvD,IAAI,CAAC,IAAI,CAACP,MAAM,CAACgC,SAAS,EAAE;MAC1B,IAAI,CAACsB,QAAQ,CAAC6B,IAAI,CAAC,oDAAoD,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACtG;;IAGF,MAAMC,OAAO,GAAG,IAAI,CAACrF,MAAM,CAAC0E,QAAQ,CAACY,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrE,EAAE,KAAKgE,SAAS,CAAC;IACpE,IAAI,CAACG,OAAO,EAAEzD,WAAW,EAAE;MACzB,IAAI,CAAC0B,QAAQ,CAAC6B,IAAI,CAAC,uCAAuC,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzF;;IAGF,QAAQ7E,WAAW;MACjB,KAAK,MAAM;QACT,IAAI,CAAC4C,MAAM,CAACqC,QAAQ,CAAC,CAAC,OAAO,EAAEN,SAAS,CAAC,CAAC;QAC1C;MACF,KAAK,OAAO;QACV,IAAI,CAAC/B,MAAM,CAACqC,QAAQ,CAAC,CAAC,QAAQ,EAAEN,SAAS,CAAC,CAAC,EAAC;QAC5C;MACF,KAAK,QAAQ;QACX,IAAI,CAAC/B,MAAM,CAACqC,QAAQ,CAAC,CAAC,SAAS,EAAEN,SAAS,CAAC,CAAC,EAAC;QAC7C;MACF;QACE,IAAI,CAAC5B,QAAQ,CAAC6B,IAAI,CAAC,qCAAqC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;EAE7F;EAEA3C,cAAcA,CAACkB,QAAgB;IAC7B,IAAI,CAAC,IAAI,CAACM,WAAW,EAAE;MACrB,IAAI,CAACX,QAAQ,CAAC6B,IAAI,CAAC,6CAA6C,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC/F,IAAI,CAACjC,MAAM,CAACqC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAClC,QAAQ,CAAC6B,IAAI,CAAC,iDAAiD,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IACnG,IAAI,CAACpF,MAAM,CAACgC,SAAS,GAAG,IAAI,EAAC;IAC7B,IAAI,CAAChC,MAAM,CAACC,WAAW,GAAG,CAAC;EAC7B;;;uBAvMW+C,qBAAqB,EAAAxD,EAAA,CAAAiG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnG,EAAA,CAAAiG,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAApG,EAAA,CAAAiG,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAAtG,EAAA,CAAAiG,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAxG,EAAA,CAAAiG,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAArBlD,qBAAqB;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjhB9BjH,EAAA,CAAAC,cAAA,aAAqC;UAKzBD,EAAA,CAAAE,MAAA,GAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,WAAuB;UAAAD,EAAA,CAAAE,MAAA,GAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEnDH,EAAA,CAAAC,cAAA,aAAyB;UAE4BD,EAAA,CAAAE,MAAA,IAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEjFH,EAAA,CAAAC,cAAA,cAAuB;UACOD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3CH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzEH,EAAA,CAAAC,cAAA,cAAuB;UACXD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI3CH,EAAA,CAAA4B,UAAA,KAAAuF,qCAAA,iBAMM;UACRnH,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,wBAA4E;UAArCD,EAAA,CAAAiB,UAAA,iCAAAmG,6EAAAC,MAAA;YAAA,OAAAH,GAAA,CAAA3D,gBAAA,GAAA8D,MAAA;UAAA,EAAoC;UACzErH,EAAA,CAAAC,cAAA,mBAAwB;UAEJD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACrDH,EAAA,CAAAC,cAAA,wBAAkB;UACYD,EAAA,CAAAE,MAAA,IAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAExDH,EAAA,CAAAC,cAAA,eAA+B;UACzBD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAC,cAAA,UAAI;UACYD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,2CAAkC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7EH,EAAA,CAAAC,cAAA,UAAI;UAAUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,mDAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChFH,EAAA,CAAAC,cAAA,UAAI;UAAUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,8CAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3EH,EAAA,CAAAC,cAAA,UAAI;UAAUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,+CAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAOtFH,EAAA,CAAAC,cAAA,mBAAyB;UAELD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjDH,EAAA,CAAAC,cAAA,wBAAkB;UAEdD,EAAA,CAAA4B,UAAA,KAAA0F,qCAAA,oBAqBM;UACRtH,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAC,cAAA,mBAA2B;UAEPD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACtDH,EAAA,CAAAC,cAAA,wBAAkB;UAGND,EAAA,CAAAE,MAAA,IAA6D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5EH,EAAA,CAAAC,cAAA,WAAK;UACCD,EAAA,CAAAE,MAAA,IAAwD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjEH,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,qLAA0I;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAUtLH,EAAA,CAAAC,cAAA,eAAqB;UAGfD,EAAA,CAAA4B,UAAA,KAAA2F,qCAAA,kBAEM;UACNvH,EAAA,CAAA4B,UAAA,KAAA4F,qCAAA,kBAIM;UAENxH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAA4B,UAAA,KAAA6F,wCAAA,qBAIS;UACTzH,EAAA,CAAA4B,UAAA,KAAA8F,wCAAA,qBAKS;UACT1H,EAAA,CAAA4B,UAAA,KAAA+F,wCAAA,qBAIS;UACT3H,EAAA,CAAA4B,UAAA,KAAAgG,kDAAA,+BAEmB;UACrB5H,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA4B;UAElBD,EAAA,CAAAE,MAAA,0BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzCH,EAAA,CAAAC,cAAA,eAA0B;UAClBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAElCH,EAAA,CAAAC,cAAA,eAA0B;UAClBD,EAAA,CAAAE,MAAA,uBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE3CH,EAAA,CAAAC,cAAA,eAA0B;UAClBD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAA4B;UACED,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3CH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,KAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;UArJlCH,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAW,iBAAA,CAAAuG,GAAA,CAAA1G,MAAA,CAAA6B,KAAA,CAAkB;UACCrC,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAW,iBAAA,CAAAuG,GAAA,CAAA1G,MAAA,CAAA8B,WAAA,CAAwB;UAIjCtC,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAA6H,UAAA,CAAAX,GAAA,CAAA1B,aAAA,CAAA0B,GAAA,CAAA1G,MAAA,CAAAkE,MAAA,EAAsC;UAAC1E,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAW,iBAAA,CAAAuG,GAAA,CAAA1G,MAAA,CAAAkE,MAAA,CAAmB;UAI9D1E,EAAA,CAAAK,SAAA,GAA0D;UAA1DL,EAAA,CAAA8H,kBAAA,KAAAZ,GAAA,CAAA1G,MAAA,CAAA8E,IAAA,QAAA4B,GAAA,CAAA1G,MAAA,CAAA6E,eAAA,qBAA0D;UAI1DrF,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAM,kBAAA,KAAA4G,GAAA,CAAA1G,MAAA,CAAAQ,KAAA,aAA0B;UAILhB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAU,UAAA,SAAAwG,GAAA,CAAA1G,MAAA,CAAAgC,SAAA,CAAsB;UAShBxC,EAAA,CAAAK,SAAA,GAAoC;UAApCL,EAAA,CAAAU,UAAA,kBAAAwG,GAAA,CAAA3D,gBAAA,CAAoC;UAKvCvD,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAW,iBAAA,CAAAuG,GAAA,CAAA1G,MAAA,CAAA8B,WAAA,CAAwB;UAoBzBtC,EAAA,CAAAK,SAAA,IAAoB;UAApBL,EAAA,CAAAU,UAAA,YAAAwG,GAAA,CAAA1G,MAAA,CAAA0E,QAAA,CAAoB;UAiCrClF,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAA8H,kBAAA,KAAAZ,GAAA,CAAA1G,MAAA,CAAAoE,SAAA,CAAAE,MAAA,SAAAoC,GAAA,CAAA1G,MAAA,CAAAoE,SAAA,CAAAC,GAAA,QAA6D;UAG/D7E,EAAA,CAAAK,SAAA,GAAwD;UAAxDL,EAAA,CAAA8H,kBAAA,KAAAZ,GAAA,CAAA1G,MAAA,CAAAoE,SAAA,CAAAE,MAAA,OAAAoC,GAAA,CAAA1G,MAAA,CAAAoE,SAAA,CAAAC,GAAA,KAAwD;UAcxC7E,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAU,UAAA,UAAAwG,GAAA,CAAA1G,MAAA,CAAA+E,UAAA,CAAwB;UAGxBvF,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAU,UAAA,SAAAwG,GAAA,CAAA1G,MAAA,CAAA+E,UAAA,CAAuB;UAQxCvF,EAAA,CAAAK,SAAA,GAA4C;UAA5CL,EAAA,CAAAU,UAAA,UAAAwG,GAAA,CAAA1G,MAAA,CAAAgC,SAAA,IAAA0E,GAAA,CAAA1G,MAAA,CAAA+E,UAAA,CAA4C;UAK5CvF,EAAA,CAAAK,SAAA,GAA6C;UAA7CL,EAAA,CAAAU,UAAA,UAAAwG,GAAA,CAAA1G,MAAA,CAAAgC,SAAA,KAAA0E,GAAA,CAAA1G,MAAA,CAAA+E,UAAA,CAA6C;UAM7CvF,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAU,UAAA,SAAAwG,GAAA,CAAA1G,MAAA,CAAAgC,SAAA,CAAsB;UAIZxC,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAU,UAAA,SAAAwG,GAAA,CAAA1G,MAAA,CAAAgC,SAAA,CAAsB;UAQjCxC,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAM,kBAAA,KAAA4G,GAAA,CAAA1G,MAAA,CAAAQ,KAAA,aAA0B;UAI1BhB,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAW,iBAAA,CAAAuG,GAAA,CAAA1G,MAAA,CAAAkE,MAAA,CAAmB;UAInB1E,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAW,iBAAA,CAAAuG,GAAA,CAAA1G,MAAA,CAAA6E,eAAA,CAA4B;UAM1BrF,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAW,iBAAA,CAAAuG,GAAA,CAAA1G,MAAA,CAAA8E,IAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}