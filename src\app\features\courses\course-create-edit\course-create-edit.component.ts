import { Component, type OnInit } from "@angular/core"
import { type Form<PERSON>uilder, type FormGroup, Validators, type FormArray } from "@angular/forms"
import type { ActivatedRoute, Router } from "@angular/router"
import type { CourseService } from "../../../core/services/course.service"
import type { MatSnackBar } from "@angular/material/snack-bar"
import type { Course, Contenu, Quiz, Video, Resume } from "../../../core/models/course.model"

@Component({
  selector: "app-course-create-edit",
  template: `
    <div class="course-form-container">
      <mat-card class="course-form-card">
        <mat-card-header>
          <mat-card-title>{{ isEditMode ? 'Modifier le cours' : 'Créer un nouveau cours' }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="courseForm" (ngSubmit)="onSubmit()">
            <!-- Course Details -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Titre du cours</mat-label>
              <input matInput formControlName="titre">
              <mat-error *ngIf="courseForm.get('titre')?.hasError('required')">Le titre est requis</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="4"></textarea>
              <mat-error *ngIf="courseForm.get('description')?.hasError('required')">La description est requise</mat-error>
            </mat-form-field>

            <div class="row-fields">
              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Durée (minutes)</mat-label>
                <input matInput type="number" formControlName="duree">
                <mat-error *ngIf="courseForm.get('duree')?.hasError('required')">La durée est requise</mat-error>
                <mat-error *ngIf="courseForm.get('duree')?.hasError('min')">La durée doit être positive</mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Niveau</mat-label>
                <mat-select formControlName="niveau">
                  <mat-option value="Débutant">Débutant</mat-option>
                  <mat-option value="Intermédiaire">Intermédiaire</mat-option>
                  <mat-option value="Avancé">Avancé</mat-option>
                </mat-select>
                <mat-error *ngIf="courseForm.get('niveau')?.hasError('required')">Le niveau est requis</mat-error>
              </mat-form-field>
            </div>

            <mat-checkbox formControlName="estGratuit" (change)="togglePriceField()">Cours gratuit</mat-checkbox>

            <mat-form-field appearance="outline" class="full-width" *ngIf="!courseForm.get('estGratuit')?.value">
              <mat-label>Prix (€)</mat-label>
              <input matInput type="number" formControlName="prix">
              <mat-error *ngIf="courseForm.get('prix')?.hasError('required')">Le prix est requis</mat-error>
              <mat-error *ngIf="courseForm.get('prix')?.hasError('min')">Le prix doit être positif</mat-error>
            </mat-form-field>

            <!-- Contents Section -->
            <mat-card class="contents-card">
              <mat-card-title>Contenus du cours</mat-card-title>
              <mat-card-content>
                <div formArrayName="contenus" class="content-list">
                  <div *ngFor="let content of contenus.controls; let i = index" [formGroupName]="i" class="content-item">
                    <mat-form-field appearance="outline" class="content-type-select">
                      <mat-label>Type de contenu</mat-label>
                      <mat-select formControlName="typeContenu" (selectionChange)="onContentTypeChange(i)">
                        <mat-option value="Video">Vidéo</mat-option>
                        <mat-option value="Quiz">Quiz</mat-option>
                        <mat-option value="Resume">Résumé</mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="content-title-input">
                      <mat-label>Titre du contenu</mat-label>
                      <input matInput formControlName="titre">
                    </mat-form-field>

                    <!-- Specific fields for Video -->
                    <mat-form-field appearance="outline" class="content-duration-input" *ngIf="content.get('typeContenu')?.value === 'Video'">
                      <mat-label>Durée vidéo (min)</mat-label>
                      <input matInput type="number" formControlName="duree">
                    </mat-form-field>

                    <!-- Specific fields for Quiz -->
                    <ng-container *ngIf="content.get('typeContenu')?.value === 'Quiz'">
                      <mat-form-field appearance="outline" class="content-quiz-threshold">
                        <mat-label>Seuil de réussite (%)</mat-label>
                        <input matInput type="number" formControlName="seuilReussite">
                      </mat-form-field>
                      <!-- Add quiz questions management here if needed -->
                    </ng-container>

                    <!-- Specific fields for Resume -->
                    <mat-form-field appearance="outline" class="content-resume-text" *ngIf="content.get('typeContenu')?.value === 'Resume'">
                      <mat-label>Contenu texte</mat-label>
                      <textarea matInput formControlName="contenuTexte" rows="3"></textarea>
                    </mat-form-field>

                    <button mat-icon-button color="warn" (click)="removeContenu(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <button mat-raised-button color="accent" (click)="addContenu()">
                  <mat-icon>add</mat-icon> Ajouter un contenu
                </button>
              </mat-card-content>
            </mat-card>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="courseForm.invalid || isLoading">
                <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
                <span *ngIf="!isLoading">{{ isEditMode ? 'Mettre à jour' : 'Créer le cours' }}</span>
              </button>
              <button mat-stroked-button color="warn" type="button" (click)="onCancel()">Annuler</button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [
    `
    .course-form-container {
      padding: 2rem;
      background-color: #f5f5f5;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: flex-start;
    }

    .course-form-card {
      max-width: 800px;
      width: 100%;
      padding: 1.5rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .course-form-card mat-card-title {
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .full-width {
      width: 100%;
      margin-bottom: 1rem;
    }

    .row-fields {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .half-width {
      flex: 1;
    }

    mat-checkbox {
      margin-bottom: 1.5rem;
    }

    .contents-card {
      margin-top: 2rem;
      padding: 1.5rem;
      background-color: #f9f9f9;
      border: 1px solid #eee;
    }

    .contents-card mat-card-title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      text-align: left;
    }

    .content-list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .content-item {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: center;
      padding: 1rem;
      border: 1px solid #ddd;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
    }

    .content-type-select {
      flex: 1 1 180px;
    }

    .content-title-input {
      flex: 2 1 250px;
    }

    .content-duration-input, .content-quiz-threshold {
      flex: 1 1 150px;
    }

    .content-resume-text {
      flex: 3 1 300px;
    }

    .content-item button {
      flex-shrink: 0;
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 2rem;
    }

    .form-actions button {
      padding: 0.8rem 1.5rem;
      font-size: 1rem;
    }

    mat-spinner {
      margin-right: 0.5rem;
    }

    @media (max-width: 768px) {
      .row-fields {
        flex-direction: column;
      }
      .half-width {
        width: 100%;
      }
      .content-item {
        flex-direction: column;
        align-items: stretch;
      }
      .content-item mat-form-field {
        width: 100%;
      }
      .form-actions {
        flex-direction: column;
      }
    }
  `,
  ],
})
export class CourseCreateEditComponent implements OnInit {
  courseForm!: FormGroup
  isEditMode = false
  courseId: number | null = null
  isLoading = false

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private courseService: CourseService,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.initForm()
    this.route.paramMap.subscribe((params) => {
      const id = params.get("id")
      if (id) {
        this.courseId = Number(id)
        this.isEditMode = true
        this.loadCourse(this.courseId)
      }
    })
  }

  initForm(): void {
    this.courseForm = this.fb.group({
      titre: ["", Validators.required],
      description: ["", Validators.required],
      duree: [null, [Validators.required, Validators.min(1)]],
      niveau: ["", Validators.required],
      estGratuit: [false],
      prix: [{ value: null, disabled: false }, [Validators.required, Validators.min(0)]],
      contenus: this.fb.array([]),
    })

    // Disable price if estGratuit is true initially
    if (this.courseForm.get("estGratuit")?.value) {
      this.courseForm.get("prix")?.disable()
    }
  }

  loadCourse(id: number): void {
    this.isLoading = true
    // Mock data for demonstration
    const mockCourse: Course = {
      id: id,
      titre: "React Fundamentals (Edit)",
      description: "Apprenez les bases de React avec des exemples pratiques et des projets concrets.",
      prix: 99.99,
      duree: 120,
      niveau: "Débutant",
      formateurId: 1,
      formateur: { id: 1, nom: "Dupont", prenom: "Jean" },
      contenus: [
        {
          id: 1,
          titre: "Introduction à React",
          typeContenu: "Video",
          duree: 30,
          coursId: id,
          estComplete: false,
          estDebloque: true,
          ordre: 1,
        },
        {
          id: 3,
          titre: "Quiz - Bases de React",
          typeContenu: "Quiz",
          seuilReussite: 70,
          coursId: id,
          estComplete: false,
          estDebloque: true,
          ordre: 3,
        },
      ],
      nombreEtudiants: 245,
      note: 4.8,
      estGratuit: false,
    }
    this.courseForm.patchValue(mockCourse)
    mockCourse.contenus.forEach((content) => {
      this.addContenu(content)
    })
    this.togglePriceField()
    this.isLoading = false

    // Uncomment to fetch from API
    /*
    this.courseService.getCours(id).subscribe({
      next: (data) => {
        this.courseForm.patchValue(data);
        data.contenus.forEach(content => {
          this.addContenu(content);
        });
        this.togglePriceField();
        this.isLoading = false;
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.isLoading = false;
        this.router.navigate(['/courses']);
      }
    });
    */
  }

  get contenus(): FormArray {
    return this.courseForm.get("contenus") as FormArray
  }

  addContenu(content?: Contenu): void {
    let contentGroup: FormGroup
    if (content) {
      if (content.typeContenu === "Video") {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, Validators.required],
          typeContenu: [content.typeContenu, Validators.required],
          duree: [(content as Video).duree, [Validators.min(1)]],
          coursId: [content.coursId],
        })
      } else if (content.typeContenu === "Quiz") {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, Validators.required],
          typeContenu: [content.typeContenu, Validators.required],
          seuilReussite: [(content as Quiz).seuilReussite, [Validators.min(0), Validators.max(100)]],
          coursId: [content.coursId],
        })
      } else if (content.typeContenu === "Resume") {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, Validators.required],
          typeContenu: [content.typeContenu, Validators.required],
          contenuTexte: [(content as Resume).contenuTexte],
          coursId: [content.coursId],
        })
      } else {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, Validators.required],
          typeContenu: [content.typeContenu, Validators.required],
          coursId: [content.coursId],
        })
      }
    } else {
      contentGroup = this.fb.group({
        id: [0], // Temp ID for new content
        titre: ["", Validators.required],
        typeContenu: ["Video", Validators.required], // Default to Video
        duree: [null, [Validators.min(1)]], // For video
        seuilReussite: [null, [Validators.min(0), Validators.max(100)]], // For quiz
        contenuTexte: [""], // For resume
        coursId: [this.courseId],
      })
    }
    this.contenus.push(contentGroup)
  }

  removeContenu(index: number): void {
    this.contenus.removeAt(index)
  }

  onContentTypeChange(index: number): void {
    const contentGroup = this.contenus.at(index) as FormGroup
    const type = contentGroup.get("typeContenu")?.value

    // Reset and re-apply validators based on type
    contentGroup.get("duree")?.clearValidators()
    contentGroup.get("duree")?.updateValueAndValidity()
    contentGroup.get("seuilReussite")?.clearValidators()
    contentGroup.get("seuilReussite")?.updateValueAndValidity()
    contentGroup.get("contenuTexte")?.clearValidators()
    contentGroup.get("contenuTexte")?.updateValueAndValidity()

    if (type === "Video") {
      contentGroup.get("duree")?.setValidators([Validators.min(1)])
    } else if (type === "Quiz") {
      contentGroup.get("seuilReussite")?.setValidators([Validators.min(0), Validators.max(100)])
    }
    // No specific validators for Resume contentText, it's optional
  }

  togglePriceField(): void {
    const estGratuitControl = this.courseForm.get("estGratuit")
    const prixControl = this.courseForm.get("prix")

    if (estGratuitControl?.value) {
      prixControl?.disable()
      prixControl?.setValue(0)
    } else {
      prixControl?.enable()
      prixControl?.setValue(null) // Clear value when re-enabling
    }
    prixControl?.updateValueAndValidity()
  }

  onSubmit(): void {
    if (this.courseForm.valid) {
      this.isLoading = true
      const courseData = this.courseForm.value
      // Ensure prix is 0 if estGratuit is true, even if disabled field value is not picked up
      if (courseData.estGratuit) {
        courseData.prix = 0
      }

      if (this.isEditMode && this.courseId) {
        // Update existing course
        this.courseService.modifierCours(this.courseId, courseData).subscribe({
          next: (res) => {
            this.snackBar.open("Cours mis à jour avec succès !", "Fermer", { duration: 3000 })
            this.isLoading = false
            this.router.navigate(["/courses", this.courseId])
          },
          error: (err) => {
            this.snackBar.open("Erreur lors de la mise à jour du cours.", "Fermer", { duration: 3000 })
            console.error(err)
            this.isLoading = false
          },
        })
      } else {
        // Create new course
        // Note: Your backend's FormateurController has AjouterCours, but it expects FormateurId in URL.
        // You might need a dedicated POST /api/cours endpoint or adjust this.
        // For now, simulating success.
        this.snackBar.open("Cours créé avec succès (simulé) !", "Fermer", { duration: 3000 })
        this.isLoading = false
        this.router.navigate(["/courses"])
        /*
        this.courseService.createCourse(courseData).subscribe({
          next: (res) => {
            this.snackBar.open('Cours créé avec succès !', 'Fermer', { duration: 3000 });
            this.isLoading = false;
            this.router.navigate(['/courses']);
          },
          error: (err) => {
            this.snackBar.open('Erreur lors de la création du cours.', 'Fermer', { duration: 3000 });
            console.error(err);
            this.isLoading = false;
          }
        });
        */
      }
    } else {
      this.snackBar.open("Veuillez remplir tous les champs requis.", "Fermer", { duration: 3000 })
      this.courseForm.markAllAsTouched() // Show validation errors
    }
  }

  onCancel(): void {
    this.router.navigate(["/courses"])
  }
}
