{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class MessageService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlApi}messages`;\n  }\n  // POST: Envoyer un message (correspond à POST /api/messages)\n  envoyerMessage(message) {\n    return this.http.post(this.apiUrl, message);\n  }\n  // GET: Messages entre deux utilisateurs (correspond à GET /api/messages/entre/{id1}/{id2})\n  getMessages(id1, id2) {\n    return this.http.get(`${this.apiUrl}/entre/${id1}/${id2}`);\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "MessageService", "constructor", "http", "apiUrl", "urlApi", "envoyer<PERSON>essage", "message", "post", "getMessages", "id1", "id2", "get", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\message.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Message } from \"../models/message.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class MessageService {\n  private apiUrl = `${environment.urlApi}messages`\n\n  constructor(private http: HttpClient) {}\n\n  // POST: Envoyer un message (correspond à POST /api/messages)\n  envoyerMessage(message: Message): Observable<string> {\n    return this.http.post<string>(this.apiUrl, message)\n  }\n\n  // GET: Messages entre deux utilisateurs (correspond à GET /api/messages/entre/{id1}/{id2})\n  getMessages(id1: number, id2: number): Observable<Message[]> {\n    return this.http.get<Message[]>(`${this.apiUrl}/entre/${id1}/${id2}`)\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,MAAM,UAAU;EAET;EAEvC;EACAC,cAAcA,CAACC,OAAgB;IAC7B,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAS,IAAI,CAACJ,MAAM,EAAEG,OAAO,CAAC;EACrD;EAEA;EACAE,WAAWA,CAACC,GAAW,EAAEC,GAAW;IAClC,OAAO,IAAI,CAACR,IAAI,CAACS,GAAG,CAAY,GAAG,IAAI,CAACR,MAAM,UAAUM,GAAG,IAAIC,GAAG,EAAE,CAAC;EACvE;;;uBAbWV,cAAc,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdf,cAAc;MAAAgB,OAAA,EAAdhB,cAAc,CAAAiB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}