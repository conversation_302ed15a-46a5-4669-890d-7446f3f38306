{"ast": null, "code": "import { BrowserModule } from \"@angular/platform-browser\";\nimport { BrowserAnimationsModule } from \"@angular/platform-browser/animations\";\nimport { HttpClientModule, HTTP_INTERCEPTORS } from \"@angular/common/http\";\nimport { ReactiveFormsModule, FormsModule } from \"@angular/forms\";\n// Angular Material\nimport { MatToolbarModule } from \"@angular/material/toolbar\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatSelectModule } from \"@angular/material/select\";\nimport { MatCheckboxModule } from \"@angular/material/checkbox\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatBadgeModule } from \"@angular/material/badge\";\nimport { MatTabsModule } from \"@angular/material/tabs\";\nimport { MatDialogModule } from \"@angular/material/dialog\";\nimport { MatSnackBarModule } from \"@angular/material/snack-bar\";\nimport { MatSidenavModule } from \"@angular/material/sidenav\";\nimport { MatListModule } from \"@angular/material/list\";\nimport { MatGridListModule } from \"@angular/material/grid-list\";\nimport { MatRadioModule } from \"@angular/material/radio\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { AppRoutingModule } from \"./app-routing.module\";\nimport { AppComponent } from \"./app.component\";\nimport { AuthInterceptor } from \"./core/interceptors/auth.interceptor\";\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule, AppRoutingModule,\n      // Angular Material\n      MatToolbarModule, MatButtonModule, MatCardModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatCheckboxModule, MatIconModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTabsModule, MatDialogModule, MatSnackBarModule, MatSidenavModule, MatListModule, MatGridListModule, MatRadioModule, MatProgressSpinnerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule, AppRoutingModule,\n    // Angular Material\n    MatToolbarModule, MatButtonModule, MatCardModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatCheckboxModule, MatIconModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTabsModule, MatDialogModule, MatSnackBarModule, MatSidenavModule, MatListModule, MatGridListModule, MatRadioModule, MatProgressSpinnerModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "ReactiveFormsModule", "FormsModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatCheckboxModule", "MatIconModule", "MatProgressBarModule", "MatChipsModule", "MatBadgeModule", "MatTabsModule", "MatDialogModule", "MatSnackBarModule", "MatSidenavModule", "MatListModule", "MatGridListModule", "MatRadioModule", "MatProgressSpinnerModule", "AppRoutingModule", "AppComponent", "AuthInterceptor", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\e-learning\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { BrowserModule } from \"@angular/platform-browser\"\nimport { BrowserAnimationsModule } from \"@angular/platform-browser/animations\"\nimport { HttpClientModule, HTTP_INTERCEPTORS } from \"@angular/common/http\"\nimport { ReactiveFormsModule, FormsModule } from \"@angular/forms\"\n\n// Angular Material\nimport { MatToolbarModule } from \"@angular/material/toolbar\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatCheckboxModule } from \"@angular/material/checkbox\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatBadgeModule } from \"@angular/material/badge\"\nimport { MatTabsModule } from \"@angular/material/tabs\"\nimport { MatDialogModule } from \"@angular/material/dialog\"\nimport { MatSnackBarModule } from \"@angular/material/snack-bar\"\nimport { MatSidenavModule } from \"@angular/material/sidenav\"\nimport { MatListModule } from \"@angular/material/list\"\nimport { MatGridListModule } from \"@angular/material/grid-list\"\nimport { MatRadioModule } from \"@angular/material/radio\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { AppRoutingModule } from \"./app-routing.module\"\nimport { AppComponent } from \"./app.component\"\nimport { AuthInterceptor } from \"./core/interceptors/auth.interceptor\"\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n    FormsModule,\n    AppRoutingModule,\n\n    // Angular Material\n    MatToolbarModule,\n    MatButtonModule,\n    MatCardModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatIconModule,\n    MatProgressBarModule,\n    MatChipsModule,\n    MatBadgeModule,\n    MatTabsModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatGridListModule,\n    MatRadioModule,\n    MatProgressSpinnerModule,\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true,\n    },\n  ],\n  bootstrap: [AppComponent],\n})\nexport class AppModule {}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;;AA0CtE,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRH,YAAY;IAAA;EAAA;;;iBAPb,CACT;QACEI,OAAO,EAAE3B,iBAAiB;QAC1B4B,QAAQ,EAAEJ,eAAe;QACzBK,KAAK,EAAE;OACR,CACF;MAAAC,OAAA,GAlCCjC,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,mBAAmB,EACnBC,WAAW,EACXoB,gBAAgB;MAEhB;MACAnB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,iBAAiB,EACjBC,cAAc,EACdC,wBAAwB;IAAA;EAAA;;;2EAWfI,SAAS;IAAAM,YAAA,GAvCLR,YAAY;IAAAO,OAAA,GAEzBjC,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,mBAAmB,EACnBC,WAAW,EACXoB,gBAAgB;IAEhB;IACAnB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,iBAAiB,EACjBC,cAAc,EACdC,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}