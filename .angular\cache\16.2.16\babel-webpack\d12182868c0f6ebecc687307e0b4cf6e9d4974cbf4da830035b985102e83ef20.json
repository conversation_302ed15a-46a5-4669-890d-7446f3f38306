{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { BrowserModule } from \"@angular/platform-browser\";\nimport { BrowserAnimationsModule } from \"@angular/platform-browser/animations\";\nimport { HttpClientModule, HTTP_INTERCEPTORS } from \"@angular/common/http\";\nimport { ReactiveFormsModule, FormsModule } from \"@angular/forms\";\n// Angular Material\nimport { MatToolbarModule } from \"@angular/material/toolbar\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatSelectModule } from \"@angular/material/select\";\nimport { MatCheckboxModule } from \"@angular/material/checkbox\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatBadgeModule } from \"@angular/material/badge\";\nimport { MatTabsModule } from \"@angular/material/tabs\";\nimport { MatDialogModule } from \"@angular/material/dialog\";\nimport { MatSnackBarModule } from \"@angular/material/snack-bar\";\nimport { MatSidenavModule } from \"@angular/material/sidenav\";\nimport { MatListModule } from \"@angular/material/list\";\nimport { MatGridListModule } from \"@angular/material/grid-list\";\nimport { MatRadioModule } from \"@angular/material/radio\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { AppRoutingModule } from \"./app-routing.module\";\nimport { AppComponent } from \"./app.component\";\nimport { AuthInterceptor } from \"./core/interceptors/auth.interceptor\";\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule, AppRoutingModule,\n  // Angular Material\n  MatToolbarModule, MatButtonModule, MatCardModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatCheckboxModule, MatIconModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTabsModule, MatDialogModule, MatSnackBarModule, MatSidenavModule, MatListModule, MatGridListModule, MatRadioModule, MatProgressSpinnerModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "ReactiveFormsModule", "FormsModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatCheckboxModule", "MatIconModule", "MatProgressBarModule", "MatChipsModule", "MatBadgeModule", "MatTabsModule", "MatDialogModule", "MatSnackBarModule", "MatSidenavModule", "MatListModule", "MatGridListModule", "MatRadioModule", "MatProgressSpinnerModule", "AppRoutingModule", "AppComponent", "AuthInterceptor", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["C:\\e-learning\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { BrowserModule } from \"@angular/platform-browser\"\nimport { BrowserAnimationsModule } from \"@angular/platform-browser/animations\"\nimport { HttpClientModule, HTTP_INTERCEPTORS } from \"@angular/common/http\"\nimport { ReactiveFormsModule, FormsModule } from \"@angular/forms\"\n\n// Angular Material\nimport { MatToolbarModule } from \"@angular/material/toolbar\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatCheckboxModule } from \"@angular/material/checkbox\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatBadgeModule } from \"@angular/material/badge\"\nimport { MatTabsModule } from \"@angular/material/tabs\"\nimport { MatDialogModule } from \"@angular/material/dialog\"\nimport { MatSnackBarModule } from \"@angular/material/snack-bar\"\nimport { MatSidenavModule } from \"@angular/material/sidenav\"\nimport { MatListModule } from \"@angular/material/list\"\nimport { MatGridListModule } from \"@angular/material/grid-list\"\nimport { MatRadioModule } from \"@angular/material/radio\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { AppRoutingModule } from \"./app-routing.module\"\nimport { AppComponent } from \"./app.component\"\nimport { AuthInterceptor } from \"./core/interceptors/auth.interceptor\"\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n    FormsModule,\n    AppRoutingModule,\n\n    // Angular Material\n    MatToolbarModule,\n    MatButtonModule,\n    MatCardModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatIconModule,\n    MatProgressBarModule,\n    MatChipsModule,\n    MatBadgeModule,\n    MatTabsModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatGridListModule,\n    MatRadioModule,\n    MatProgressSpinnerModule,\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true,\n    },\n  ],\n  bootstrap: [AppComponent],\n})\nexport class AppModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;AA0C/D,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAG;AAAZA,SAAS,GAAAC,UAAA,EAxCrB9B,QAAQ,CAAC;EACR+B,YAAY,EAAE,CAACJ,YAAY,CAAC;EAC5BK,OAAO,EAAE,CACP/B,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,mBAAmB,EACnBC,WAAW,EACXoB,gBAAgB;EAEhB;EACAnB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,iBAAiB,EACjBC,cAAc,EACdC,wBAAwB,CACzB;EACDQ,SAAS,EAAE,CACT;IACEC,OAAO,EAAE9B,iBAAiB;IAC1B+B,QAAQ,EAAEP,eAAe;IACzBQ,KAAK,EAAE;GACR,CACF;EACDC,SAAS,EAAE,CAACV,YAAY;CACzB,CAAC,C,EACWE,SAAS,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}