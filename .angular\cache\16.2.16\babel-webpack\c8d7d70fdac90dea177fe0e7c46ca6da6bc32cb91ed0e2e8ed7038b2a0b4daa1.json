{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatDividerModule } from \"@angular/material/divider\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { PaymentComponent } from \"./payment.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class PaymentModule {\n  static {\n    this.ɵfac = function PaymentModule_Factory(t) {\n      return new (t || PaymentModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PaymentModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatDividerModule, MatProgressSpinnerModule, RouterModule.forChild([{\n        path: \":courseId\",\n        component: PaymentComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PaymentModule, {\n    declarations: [PaymentComponent],\n    imports: [CommonModule, ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatDividerModule, MatProgressSpinnerModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatDividerModule", "MatProgressSpinnerModule", "PaymentComponent", "PaymentModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\payment\\payment.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatDividerModule } from \"@angular/material/divider\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { PaymentComponent } from \"./payment.component\"\n\n@NgModule({\n  declarations: [PaymentComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([{ path: \":courseId\", component: PaymentComponent }]),\n  ],\n})\nexport class PaymentModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,gBAAgB,QAAQ,qBAAqB;;;AAiBtD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAZtBX,YAAY,EACZE,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,wBAAwB,EACxBR,YAAY,CAACW,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,WAAW;QAAEC,SAAS,EAAEJ;MAAgB,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAGlEC,aAAa;IAAAI,YAAA,GAdTL,gBAAgB;IAAAM,OAAA,GAE7BhB,YAAY,EACZE,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,wBAAwB,EAAAQ,EAAA,CAAAhB,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}