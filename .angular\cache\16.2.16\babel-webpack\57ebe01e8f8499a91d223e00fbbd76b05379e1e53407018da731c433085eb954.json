{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function debounce(durationSelector) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    let durationSubscriber = null;\n    const emit = () => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      hasValue = true;\n      lastValue = value;\n      durationSubscriber = createOperatorSubscriber(subscriber, emit, noop);\n      innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n    }, () => {\n      emit();\n      subscriber.complete();\n    }, undefined, () => {\n      lastValue = durationSubscriber = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "innerFrom", "debounce", "durationSelector", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "emit", "unsubscribe", "value", "next", "subscribe", "complete", "undefined"], "sources": ["C:/e-learning/node_modules/rxjs/dist/esm/internal/operators/debounce.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function debounce(durationSelector) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        let lastValue = null;\n        let durationSubscriber = null;\n        const emit = () => {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            durationSubscriber = null;\n            if (hasValue) {\n                hasValue = false;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            hasValue = true;\n            lastValue = value;\n            durationSubscriber = createOperatorSubscriber(subscriber, emit, noop);\n            innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n        }, () => {\n            emit();\n            subscriber.complete();\n        }, undefined, () => {\n            lastValue = durationSubscriber = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,QAAQA,CAACC,gBAAgB,EAAE;EACvC,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,MAAMC,IAAI,GAAGA,CAAA,KAAM;MACfD,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,WAAW,CAAC,CAAC;MACxGF,kBAAkB,GAAG,IAAI;MACzB,IAAIF,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,MAAMK,KAAK,GAAGJ,SAAS;QACvBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;MAC1B;IACJ,CAAC;IACDP,MAAM,CAACS,SAAS,CAACb,wBAAwB,CAACK,UAAU,EAAGM,KAAK,IAAK;MAC7DH,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,WAAW,CAAC,CAAC;MACxGJ,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGI,KAAK;MACjBH,kBAAkB,GAAGR,wBAAwB,CAACK,UAAU,EAAEI,IAAI,EAAEV,IAAI,CAAC;MACrEE,SAAS,CAACE,gBAAgB,CAACQ,KAAK,CAAC,CAAC,CAACE,SAAS,CAACL,kBAAkB,CAAC;IACpE,CAAC,EAAE,MAAM;MACLC,IAAI,CAAC,CAAC;MACNJ,UAAU,CAACS,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEC,SAAS,EAAE,MAAM;MAChBR,SAAS,GAAGC,kBAAkB,GAAG,IAAI;IACzC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}