{"version": 3, "file": "src_app_features_auth_auth_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAC8C;AACA;AACM;AACE;AACW;AACT;AACE;AACI;AACJ;AACJ;AACuB;AAErB;AACS;;;AAsB3D,MAAOa,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAjBnBb,yDAAY,EACZE,+DAAmB,EACnBC,iEAAa,EACbC,4EAAkB,EAClBC,mEAAc,EACdC,qEAAe,EACfC,yEAAiB,EACjBC,sEAAe,EACfC,kEAAa,EACbC,yFAAwB,EACxBT,0DAAY,CAACa,QAAQ,CAAC,CACpB;QAAEC,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAEL,kEAAcA;MAAA,CAAE,EAC5C;QAAEI,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEJ,2EAAiBA;MAAA,CAAE,EAClD;QAAEG,IAAI,EAAE,EAAE;QAAEE,UAAU,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAM,CAAE,CACrD,CAAC;IAAA;EAAA;;;sHAGOL,UAAU;IAAAM,YAAA,GAnBNR,kEAAc,EAAEC,2EAAiB;IAAAQ,OAAA,GAE9CpB,yDAAY,EACZE,+DAAmB,EACnBC,iEAAa,EACbC,4EAAkB,EAClBC,mEAAc,EACdC,qEAAe,EACfC,yEAAiB,EACjBC,sEAAe,EACfC,kEAAa,EACbC,yFAAwB,EAAAW,0DAAA;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3BuC;;;;;;;;;;;;;;;;IA8BnDE,4DAAA,gBAAgE;IAC9DA,oDAAA,2BACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAA6D;IAC3DA,oDAAA,gCACF;IAAAA,0DAAA,EAAY;;;;;IASZA,4DAAA,gBAAmE;IACjEA,oDAAA,mCACF;IAAAA,0DAAA,EAAY;;;;;IASZA,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAO;;;AA0FtD,MAAOZ,cAAc;EAKzBiB,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;EAOd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChB,sDAAU,CAACiB,QAAQ,EAAEjB,sDAAU,CAACgB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAElB,sDAAU,CAACiB,QAAQ,CAAC;MACnCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,SAAS,CAACO,KAAK,EAAE;MACxB,IAAI,CAACT,SAAS,GAAG,IAAI;MACrB,MAAM;QAAEI,KAAK;QAAEE;MAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACQ,KAAK;MAEhD,IAAI,CAACd,WAAW,CAACe,KAAK,CAAC;QAAEC,KAAK,EAAER,KAAK;QAAES,QAAQ,EAAEP;MAAQ,CAAE,CAAC,CAACQ,SAAS,CAAC;QACrEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAChB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACmB,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACvE,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACpB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACmB,IAAI,CAAC,iDAAiD,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACrG;OACD,CAAC;;EAEN;;;uBArCWzC,cAAc,EAAAY,+DAAA,CAAAF,uDAAA,GAAAE,+DAAA,CAAAkC,oEAAA,GAAAlC,+DAAA,CAAAoC,mDAAA,GAAApC,+DAAA,CAAAsC,oEAAA;IAAA;EAAA;;;YAAdlD,cAAc;MAAAoD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1IvB9C,4DAAA,aAA4B;UAKVA,oDAAA,aAAM;UAAAA,0DAAA,EAAW;UAC3BA,4DAAA,SAAI;UAAAA,oDAAA,wBAAiB;UAAAA,0DAAA,EAAK;UAE5BA,4DAAA,QAAG;UAAAA,oDAAA,uCAAgC;UAAAA,0DAAA,EAAI;UAIzCA,4DAAA,mBAA4B;UAERA,oDAAA,iBAAS;UAAAA,0DAAA,EAAiB;UAG5CA,4DAAA,wBAAkB;UACcA,wDAAA,sBAAAiD,kDAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UACnDnB,4DAAA,yBAAwD;UAC3CA,oDAAA,cAAM;UAAAA,0DAAA,EAAY;UAC7BA,uDAAA,gBAAmF;UACnFA,wDAAA,KAAAmD,oCAAA,uBAEY;UACZnD,wDAAA,KAAAoD,oCAAA,uBAEY;UACdpD,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,oBAAY;UAAAA,0DAAA,EAAY;UACnCA,uDAAA,gBAAuF;UACvFA,4DAAA,kBAAuF;UAArDA,wDAAA,mBAAAqD,iDAAA;YAAA,OAAAN,GAAA,CAAArC,YAAA,IAAAqC,GAAA,CAAArC,YAAA;UAAA,EAAsC;UACtEV,4DAAA,gBAAU;UAAAA,oDAAA,IAAkD;UAAAA,0DAAA,EAAW;UAEzEA,wDAAA,KAAAsD,oCAAA,uBAEY;UACdtD,0DAAA,EAAiB;UAEjBA,4DAAA,eAAgC;UACaA,oDAAA,0BAAkB;UAAAA,0DAAA,EAAe;UAG9EA,4DAAA,kBACkF;UAChFA,wDAAA,KAAAuD,sCAAA,0BAA2D;UAC3DvD,wDAAA,KAAAwD,+BAAA,kBAA4C;UAC9CxD,0DAAA,EAAS;UAGXA,4DAAA,eAAwB;UAEpBA,oDAAA,gCACA;UAAAA,4DAAA,aAA4C;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAI;;;;;;UArCxDA,uDAAA,IAAuB;UAAvBA,wDAAA,cAAA+C,GAAA,CAAAlC,SAAA,CAAuB;UAIbb,uDAAA,GAAkD;UAAlDA,wDAAA,UAAA2D,OAAA,GAAAZ,GAAA,CAAAlC,SAAA,CAAA+C,GAAA,4BAAAD,OAAA,CAAAE,QAAA,aAAkD;UAGlD7D,uDAAA,GAA+C;UAA/CA,wDAAA,UAAA8D,OAAA,GAAAf,GAAA,CAAAlC,SAAA,CAAA+C,GAAA,4BAAAE,OAAA,CAAAD,QAAA,UAA+C;UAO3C7D,uDAAA,GAA2C;UAA3CA,wDAAA,SAAA+C,GAAA,CAAArC,YAAA,uBAA2C;UAE/CV,uDAAA,GAAkD;UAAlDA,+DAAA,CAAA+C,GAAA,CAAArC,YAAA,mCAAkD;UAElDV,uDAAA,GAAqD;UAArDA,wDAAA,UAAAgE,OAAA,GAAAjB,GAAA,CAAAlC,SAAA,CAAA+C,GAAA,+BAAAI,OAAA,CAAAH,QAAA,aAAqD;UAU3D7D,uDAAA,GAA2C;UAA3CA,wDAAA,aAAA+C,GAAA,CAAAlC,SAAA,CAAAoD,OAAA,IAAAlB,GAAA,CAAApC,SAAA,CAA2C;UACrBX,uDAAA,GAAe;UAAfA,wDAAA,SAAA+C,GAAA,CAAApC,SAAA,CAAe;UACpCX,uDAAA,GAAgB;UAAhBA,wDAAA,UAAA+C,GAAA,CAAApC,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxD4B;;;;;;;;;;;;;;;;;;IAqBjDX,4DAAA,gBAAuE;IACrEA,oDAAA,kCACF;IAAAA,0DAAA,EAAY;;;;;IAMZA,4DAAA,gBAAsE;IACpEA,oDAAA,0BACF;IAAAA,0DAAA,EAAY;;;;;IAOdA,4DAAA,gBAAmE;IACjEA,oDAAA,2BACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAAgE;IAC9DA,oDAAA,gCACF;IAAAA,0DAAA,EAAY;;;;;IASZA,4DAAA,gBAAsE;IACpEA,oDAAA,mCACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAAuE;IACrEA,oDAAA,iEACF;IAAAA,0DAAA,EAAY;;;;;IASZA,4DAAA,gBAA6E;IAC3EA,oDAAA,oCACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAA6D;IAC3DA,oDAAA,+CACF;IAAAA,0DAAA,EAAY;;;;;IAUZA,4DAAA,gBAAyE;IACvEA,oDAAA,4CACF;IAAAA,0DAAA,EAAY;;;;;IAOZA,4DAAA,gBAAyE;IACvEA,oDAAA,2CACF;IAAAA,0DAAA,EAAY;;;;;IAKZA,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAO;;;AA4EpD,MAAOX,iBAAiB;EAM5BgB,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAwD,mBAAmB,GAAG,IAAI;IAC1B,KAAAvD,SAAS,GAAG,KAAK;EAOd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACuD,YAAY,GAAG,IAAI,CAAC7D,EAAE,CAACQ,KAAK,CAC/B;MACEsD,SAAS,EAAE,CAAC,EAAE,EAAErE,sDAAU,CAACiB,QAAQ,CAAC;MACpCqD,QAAQ,EAAE,CAAC,EAAE,EAAEtE,sDAAU,CAACiB,QAAQ,CAAC;MACnCD,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChB,sDAAU,CAACiB,QAAQ,EAAEjB,sDAAU,CAACgB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAClB,sDAAU,CAACiB,QAAQ,EAAEjB,sDAAU,CAACuE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,eAAe,EAAE,CAAC,EAAE,EAAExE,sDAAU,CAACiB,QAAQ,CAAC;MAC1CwD,WAAW,EAAE,CAAC,EAAE,EAAEzE,sDAAU,CAACiB,QAAQ,CAAC;MACtCyD,WAAW,EAAE,CAAC,KAAK,EAAE1E,sDAAU,CAAC2E,YAAY;KAC7C,EACD;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC5C;EACH;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAM5D,QAAQ,GAAG4D,IAAI,CAACjB,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMW,eAAe,GAAGM,IAAI,CAACjB,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAI3C,QAAQ,IAAIsD,eAAe,IAAItD,QAAQ,CAACI,KAAK,KAAKkD,eAAe,CAAClD,KAAK,EAAE;MAC3E,OAAO;QAAEyD,gBAAgB,EAAE;MAAI,CAAE;;IAEnC,OAAO,IAAI;EACb;EAEA3D,QAAQA,CAAA;IACN,IAAI,IAAI,CAACgD,YAAY,CAAC/C,KAAK,EAAE;MAC3B,IAAI,CAACT,SAAS,GAAG,IAAI;MACrB,MAAMoE,QAAQ,GAAG,IAAI,CAACZ,YAAY,CAAC9C,KAAK;MAExC,IAAI,CAACd,WAAW,CAACyE,QAAQ,CAACD,QAAQ,CAAC,CAACtD,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAChB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACmB,IAAI,CAAC,uBAAuB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACzE,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACpB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACmB,IAAI,CAAC,mDAAmD,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACvG;OACD,CAAC;;EAEN;;;uBAvDWxC,iBAAiB,EAAAW,+DAAA,CAAAF,uDAAA,GAAAE,+DAAA,CAAAkC,oEAAA,GAAAlC,+DAAA,CAAAoC,mDAAA,GAAApC,+DAAA,CAAAsC,oEAAA;IAAA;EAAA;;;YAAjBjD,iBAAiB;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqC,2BAAAnC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtK1B9C,4DAAA,aAA4B;UAIJA,oDAAA,kBAAW;UAAAA,0DAAA,EAAiB;UAG9CA,4DAAA,uBAAkB;UACiBA,wDAAA,sBAAAkF,oDAAA;YAAA,OAAYnC,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UACtDnB,4DAAA,aAAsB;UAEPA,oDAAA,mBAAM;UAAAA,0DAAA,EAAY;UAC7BA,uDAAA,gBAAuE;UACvEA,wDAAA,KAAAmF,uCAAA,uBAEY;UACdnF,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,WAAG;UAAAA,0DAAA,EAAY;UAC1BA,uDAAA,gBAAmE;UACnEA,wDAAA,KAAAoF,uCAAA,uBAEY;UACdpF,0DAAA,EAAiB;UAGnBA,4DAAA,yBAAwD;UAC3CA,oDAAA,cAAM;UAAAA,0DAAA,EAAY;UAC7BA,uDAAA,iBAAmF;UACnFA,wDAAA,KAAAqF,uCAAA,uBAEY;UACZrF,wDAAA,KAAAsF,uCAAA,uBAEY;UACdtF,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,oBAAY;UAAAA,0DAAA,EAAY;UACnCA,uDAAA,iBAAuF;UACvFA,4DAAA,kBAAuF;UAArDA,wDAAA,mBAAAuF,oDAAA;YAAA,OAAAxC,GAAA,CAAArC,YAAA,IAAAqC,GAAA,CAAArC,YAAA;UAAA,EAAsC;UACtEV,4DAAA,gBAAU;UAAAA,oDAAA,IAAkD;UAAAA,0DAAA,EAAW;UAEzEA,wDAAA,KAAAwF,uCAAA,uBAEY;UACZxF,wDAAA,KAAAyF,uCAAA,uBAEY;UACdzF,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,iCAAyB;UAAAA,0DAAA,EAAY;UAChDA,uDAAA,iBAAqG;UACrGA,4DAAA,kBAAqG;UAAnEA,wDAAA,mBAAA0F,oDAAA;YAAA,OAAA3C,GAAA,CAAAmB,mBAAA,IAAAnB,GAAA,CAAAmB,mBAAA;UAAA,EAAoD;UACpFlE,4DAAA,gBAAU;UAAAA,oDAAA,IAAyD;UAAAA,0DAAA,EAAW;UAEhFA,wDAAA,KAAA2F,uCAAA,uBAEY;UACZ3F,wDAAA,KAAA4F,uCAAA,uBAEY;UACd5F,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,sBAAc;UAAAA,0DAAA,EAAY;UACrCA,4DAAA,sBAA0C;UACbA,oDAAA,0BAAkB;UAAAA,0DAAA,EAAa;UAC1DA,4DAAA,sBAA8B;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAa;UACpDA,4DAAA,sBAA0B;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAa;UAEvDA,wDAAA,KAAA6F,uCAAA,uBAEY;UACd7F,0DAAA,EAAiB;UAEjBA,4DAAA,eAAgC;UAE5BA,oDAAA,uBAAc;UAAAA,4DAAA,aAA8C;UAAAA,oDAAA,gCAAwB;UAAAA,0DAAA,EAAI;UAE1FA,wDAAA,KAAA8F,uCAAA,uBAEY;UACd9F,0DAAA,EAAM;UAENA,4DAAA,kBACqF;UACnFA,wDAAA,KAAA+F,yCAAA,0BAA2D;UAC3D/F,wDAAA,KAAAgG,kCAAA,kBAA0C;UAC5ChG,0DAAA,EAAS;UAGXA,4DAAA,eAAwB;UAEpBA,oDAAA,oCACA;UAAAA,4DAAA,aAAyC;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAI;;;;;;;;;;;;UAzFvDA,uDAAA,GAA0B;UAA1BA,wDAAA,cAAA+C,GAAA,CAAAoB,YAAA,CAA0B;UAKdnE,uDAAA,GAAyD;UAAzDA,wDAAA,UAAA2D,OAAA,GAAAZ,GAAA,CAAAoB,YAAA,CAAAP,GAAA,gCAAAD,OAAA,CAAAE,QAAA,aAAyD;UAQzD7D,uDAAA,GAAwD;UAAxDA,wDAAA,UAAA8D,OAAA,GAAAf,GAAA,CAAAoB,YAAA,CAAAP,GAAA,+BAAAE,OAAA,CAAAD,QAAA,aAAwD;UAS1D7D,uDAAA,GAAqD;UAArDA,wDAAA,UAAAiG,OAAA,GAAAlD,GAAA,CAAAoB,YAAA,CAAAP,GAAA,4BAAAqC,OAAA,CAAApC,QAAA,aAAqD;UAGrD7D,uDAAA,GAAkD;UAAlDA,wDAAA,UAAAkG,OAAA,GAAAnD,GAAA,CAAAoB,YAAA,CAAAP,GAAA,4BAAAsC,OAAA,CAAArC,QAAA,UAAkD;UAO9C7D,uDAAA,GAA2C;UAA3CA,wDAAA,SAAA+C,GAAA,CAAArC,YAAA,uBAA2C;UAE/CV,uDAAA,GAAkD;UAAlDA,+DAAA,CAAA+C,GAAA,CAAArC,YAAA,mCAAkD;UAElDV,uDAAA,GAAwD;UAAxDA,wDAAA,UAAAmG,OAAA,GAAApD,GAAA,CAAAoB,YAAA,CAAAP,GAAA,+BAAAuC,OAAA,CAAAtC,QAAA,aAAwD;UAGxD7D,uDAAA,GAAyD;UAAzDA,wDAAA,UAAAoG,OAAA,GAAArD,GAAA,CAAAoB,YAAA,CAAAP,GAAA,+BAAAwC,OAAA,CAAAvC,QAAA,cAAyD;UAOrD7D,uDAAA,GAAkD;UAAlDA,wDAAA,SAAA+C,GAAA,CAAAmB,mBAAA,uBAAkD;UAEtDlE,uDAAA,GAAyD;UAAzDA,+DAAA,CAAA+C,GAAA,CAAAmB,mBAAA,mCAAyD;UAEzDlE,uDAAA,GAA+D;UAA/DA,wDAAA,UAAAqG,QAAA,GAAAtD,GAAA,CAAAoB,YAAA,CAAAP,GAAA,sCAAAyC,QAAA,CAAAxC,QAAA,aAA+D;UAG/D7D,uDAAA,GAA+C;UAA/CA,wDAAA,SAAA+C,GAAA,CAAAoB,YAAA,CAAAN,QAAA,qBAA+C;UAY/C7D,uDAAA,IAA2D;UAA3DA,wDAAA,UAAAsG,QAAA,GAAAvD,GAAA,CAAAoB,YAAA,CAAAP,GAAA,kCAAA0C,QAAA,CAAAzC,QAAA,aAA2D;UAS3D7D,uDAAA,GAA2D;UAA3DA,wDAAA,UAAAuG,QAAA,GAAAxD,GAAA,CAAAoB,YAAA,CAAAP,GAAA,kCAAA2C,QAAA,CAAA1C,QAAA,aAA2D;UAMjE7D,uDAAA,GAA8C;UAA9CA,wDAAA,aAAA+C,GAAA,CAAAoB,YAAA,CAAAF,OAAA,IAAAlB,GAAA,CAAApC,SAAA,CAA8C;UACxBX,uDAAA,GAAe;UAAfA,wDAAA,SAAA+C,GAAA,CAAApC,SAAA,CAAe;UACpCX,uDAAA,GAAgB;UAAhBA,wDAAA,UAAA+C,GAAA,CAAApC,SAAA,CAAgB", "sources": ["./src/app/features/auth/auth.module.ts", "./src/app/features/auth/login/login.component.ts", "./src/app/features/auth/register/register.component.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCheckboxModule } from \"@angular/material/checkbox\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { LoginComponent } from \"./login/login.component\"\nimport { RegisterComponent } from \"./register/register.component\"\n\n@NgModule({\n  declarations: [LoginComponent, RegisterComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatCheckboxModule,\n    MatSelectModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([\n      { path: \"login\", component: LoginComponent },\n      { path: \"register\", component: RegisterComponent },\n      { path: \"\", redirectTo: \"login\", pathMatch: \"full\" },\n    ]),\n  ],\n})\nexport class AuthModule {}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { Form<PERSON>uilder, FormGroup, Validators } from \"@angular/forms\"\nimport { Router } from \"@angular/router\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { AuthService } from \"../../../core/services/auth.service\"\n\n@Component({\n  selector: \"app-login\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <!-- Header -->\n        <div class=\"auth-header\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <h1>Training Platform</h1>\n          </div>\n          <p>Plateforme de formation en ligne</p>\n        </div>\n\n        <!-- Login Form -->\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Connexion</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"rememberMe\">Se souvenir de moi</mat-checkbox>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"loginForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">Se connecter</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Pas encore de compte ? \n                <a routerLink=\"/auth/register\" class=\"link\">S'inscrire</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .auth-header {\n      text-align: center;\n      margin-bottom: 2rem;\n      color: white;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .logo mat-icon {\n      font-size: 2.5rem;\n      margin-right: 0.5rem;\n    }\n\n    .logo h1 {\n      font-size: 2rem;\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `,\n  ],\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup\n  hidePassword = true\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.loginForm = this.fb.group({\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", Validators.required],\n      rememberMe: [false],\n    })\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true\n      const { email, password } = this.loginForm.value\n\n      this.authService.login({ Email: email, Password: password }).subscribe({\n        next: (response) => {\n          this.isLoading = false\n          this.snackBar.open(\"Connexion réussie !\", \"Fermer\", { duration: 3000 })\n          this.router.navigate([\"/dashboard\"])\n        },\n        error: (error) => {\n          this.isLoading = false\n          this.snackBar.open(\"Erreur de connexion. Vérifiez vos identifiants.\", \"Fermer\", { duration: 5000 })\n        },\n      })\n    }\n  }\n}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { FormBuilder, FormGroup, Validators } from \"@angular/forms\"\nimport { Router } from \"@angular/router\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { AuthService } from \"../../../core/services/auth.service\"\n\n@Component({\n  selector: \"app-register\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Inscription</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n              <div class=\"name-row\">\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Prénom</mat-label>\n                  <input matInput formControlName=\"firstName\" placeholder=\"Votre prénom\">\n                  <mat-error *ngIf=\"registerForm.get('firstName')?.hasError('required')\">\n                    Le prénom est requis\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Nom</mat-label>\n                  <input matInput formControlName=\"lastName\" placeholder=\"Votre nom\">\n                  <mat-error *ngIf=\"registerForm.get('lastName')?.hasError('required')\">\n                    Le nom est requis\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"registerForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"registerForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.get('password')?.hasError('minlength')\">\n                  Le mot de passe doit contenir au moins 6 caractères\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Confirmer le mot de passe</mat-label>\n                <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\" formControlName=\"confirmPassword\">\n                <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n                  <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"registerForm.get('confirmPassword')?.hasError('required')\">\n                  La confirmation est requise\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.hasError('passwordMismatch')\">\n                  Les mots de passe ne correspondent pas\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Type de compte</mat-label>\n                <mat-select formControlName=\"accountType\">\n                  <mat-option value=\"client\">Client (Apprenant)</mat-option>\n                  <mat-option value=\"formateur\">Formateur</mat-option>\n                  <mat-option value=\"admin\">Administrateur</mat-option>\n                </mat-select>\n                <mat-error *ngIf=\"registerForm.get('accountType')?.hasError('required')\">\n                  Sélectionnez un type de compte\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"acceptTerms\">\n                  J'accepte les <a href=\"/terms\" target=\"_blank\" class=\"link\">conditions d'utilisation</a>\n                </mat-checkbox>\n                <mat-error *ngIf=\"registerForm.get('acceptTerms')?.hasError('required')\">\n                  Vous devez accepter les conditions\n                </mat-error>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"registerForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">S'inscrire</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Déjà un compte ? \n                <a routerLink=\"/auth/login\" class=\"link\">Se connecter</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 500px;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .name-row {\n      display: flex;\n      gap: 1rem;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `,\n  ],\n})\nexport class RegisterComponent implements OnInit {\n  registerForm!: FormGroup\n  hidePassword = true\n  hideConfirmPassword = true\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.registerForm = this.fb.group(\n      {\n        firstName: [\"\", Validators.required],\n        lastName: [\"\", Validators.required],\n        email: [\"\", [Validators.required, Validators.email]],\n        password: [\"\", [Validators.required, Validators.minLength(6)]],\n        confirmPassword: [\"\", Validators.required],\n        accountType: [\"\", Validators.required],\n        acceptTerms: [false, Validators.requiredTrue],\n      },\n      { validators: this.passwordMatchValidator },\n    )\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get(\"password\")\n    const confirmPassword = form.get(\"confirmPassword\")\n\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      return { passwordMismatch: true }\n    }\n    return null\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true\n      const formData = this.registerForm.value\n\n      this.authService.register(formData).subscribe({\n        next: (response) => {\n          this.isLoading = false\n          this.snackBar.open(\"Inscription réussie !\", \"Fermer\", { duration: 3000 })\n          this.router.navigate([\"/dashboard\"])\n        },\n        error: (error) => {\n          this.isLoading = false\n          this.snackBar.open(\"Erreur lors de l'inscription. Veuillez réessayer.\", \"Fermer\", { duration: 5000 })\n        },\n      })\n    }\n  }\n}\n"], "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatCheckboxModule", "MatSelectModule", "MatIconModule", "MatProgressSpinnerModule", "LoginComponent", "RegisterComponent", "AuthModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "redirectTo", "pathMatch", "declarations", "imports", "i1", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "constructor", "fb", "authService", "router", "snackBar", "hidePassword", "isLoading", "ngOnInit", "loginForm", "group", "email", "required", "password", "rememberMe", "onSubmit", "valid", "value", "login", "Email", "Password", "subscribe", "next", "response", "open", "duration", "navigate", "error", "ɵɵdirectiveInject", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_15_listener", "ɵɵtemplate", "LoginComponent_mat_error_20_Template", "LoginComponent_mat_error_21_Template", "LoginComponent_Template_button_click_26_listener", "LoginComponent_mat_error_29_Template", "LoginComponent_mat_spinner_34_Template", "LoginComponent_span_35_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "ɵɵtextInterpolate", "tmp_5_0", "invalid", "hideConfirmPassword", "registerForm", "firstName", "lastName", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "accountType", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "form", "passwordMismatch", "formData", "register", "RegisterComponent_Template", "RegisterComponent_Template_form_ngSubmit_7_listener", "RegisterComponent_mat_error_13_Template", "RegisterComponent_mat_error_18_Template", "RegisterComponent_mat_error_23_Template", "RegisterComponent_mat_error_24_Template", "RegisterComponent_Template_button_click_29_listener", "RegisterComponent_mat_error_32_Template", "RegisterComponent_mat_error_33_Template", "RegisterComponent_Template_button_click_38_listener", "RegisterComponent_mat_error_41_Template", "RegisterComponent_mat_error_42_Template", "RegisterComponent_mat_error_53_Template", "RegisterComponent_mat_error_59_Template", "RegisterComponent_mat_spinner_61_Template", "RegisterComponent_span_62_Template", "tmp_3_0", "tmp_4_0", "tmp_7_0", "tmp_8_0", "tmp_11_0", "tmp_13_0", "tmp_14_0"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}