{"version": 3, "file": "src_app_features_auth_auth_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AAG+D;;;AAMzD,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,kEAAW,CAACK,MAAM,QAAQ;EAEP;EAEvC;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAW,IAAI,CAACH,MAAM,CAAC;EAC7C;EAEA;EACAI,SAASA,CAACC,EAAU;IAClB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,IAAIK,EAAE,EAAE,CAAC;EACtD;EAEA;EACAC,YAAYA,CAACC,MAAc;IACzB;IACA,MAAMC,UAAU,GAAG;MACjBC,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBC,GAAG,EAAEH,MAAM,CAACG,GAAG;MACfC,MAAM,EAAEJ,MAAM,CAACI,MAAM;MACrBC,IAAI,EAAE,QAAQ;MACd;MACAC,SAAS,EAAE,EAAE;MACbC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE;KAChB;IAEDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAET,UAAU,CAAC,CAAC,CAAC;IACzD,OAAO,IAAI,CAACT,IAAI,CAACmB,IAAI,CAAS,IAAI,CAAClB,MAAM,EAAEQ,UAAU,CAAC;EACxD;EAEA;EACAW,YAAYA,CAACd,EAAU,EAAEE,MAAc;IACrC,OAAO,IAAI,CAACR,IAAI,CAACqB,GAAG,CAAC,GAAG,IAAI,CAACpB,MAAM,IAAIK,EAAE,EAAE,EAAEE,MAAM,CAAC;EACtD;EAEA;EACAc,YAAYA,CAAChB,EAAU;IACrB,OAAO,IAAI,CAACN,IAAI,CAACuB,MAAM,CAAC,GAAG,IAAI,CAACtB,MAAM,IAAIK,EAAE,EAAE,CAAC;EACjD;EAEA;EACAkB,SAASA,CAACC,QAAgB,EAAEC,OAAe;IACzC,OAAO,IAAI,CAAC1B,IAAI,CAACmB,IAAI,CAAC,GAAG,IAAI,CAAClB,MAAM,IAAIwB,QAAQ,aAAaC,OAAO,EAAE,EAAE,EAAE,CAAC;EAC7E;EAEA;EACAC,cAAcA,CAACF,QAAgB,EAAEC,OAAe;IAC9C,OAAO,IAAI,CAAC1B,IAAI,CAACmB,IAAI,CAAC,GAAG,IAAI,CAAClB,MAAM,IAAIwB,QAAQ,YAAYC,OAAO,EAAE,EAAE,EAAE,CAAC;EAC5E;;;uBAnDW5B,aAAa,EAAA8B,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAbhC,aAAa;MAAAkC,OAAA,EAAblC,aAAa,CAAAmC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN0B;AACA;AACM;AACE;AACW;AACT;AACE;AACI;AACJ;AACJ;AACuB;AAErB;AACS;;;AAsB3D,MAAOc,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAjBnBb,yDAAY,EACZE,+DAAmB,EACnBC,iEAAa,EACbC,4EAAkB,EAClBC,mEAAc,EACdC,qEAAe,EACfC,yEAAiB,EACjBC,sEAAe,EACfC,kEAAa,EACbC,yFAAwB,EACxBT,0DAAY,CAACa,QAAQ,CAAC,CACpB;QAAEC,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAEL,kEAAcA;MAAA,CAAE,EAC5C;QAAEI,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEJ,2EAAiBA;MAAA,CAAE,EAClD;QAAEG,IAAI,EAAE,EAAE;QAAEE,UAAU,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAM,CAAE,CACrD,CAAC;IAAA;EAAA;;;sHAGOL,UAAU;IAAAM,YAAA,GAnBNR,kEAAc,EAAEC,2EAAiB;IAAAQ,OAAA,GAE9CpB,yDAAY,EACZE,+DAAmB,EACnBC,iEAAa,EACbC,4EAAkB,EAClBC,mEAAc,EACdC,qEAAe,EACfC,yEAAiB,EACjBC,sEAAe,EACfC,kEAAa,EACbC,yFAAwB,EAAAf,0DAAA;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3BuC;;;;;;;;;;;;;;;;IA8BnDF,4DAAA,gBAAgE;IAC9DA,oDAAA,2BACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAA6D;IAC3DA,oDAAA,gCACF;IAAAA,0DAAA,EAAY;;;;;IASZA,4DAAA,gBAAmE;IACjEA,oDAAA,mCACF;IAAAA,0DAAA,EAAY;;;;;IASZA,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAO;;;AA0FtD,MAAOkB,cAAc;EAKzB/C,YACU8D,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;EAOd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC7B3D,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC8C,sDAAU,CAACc,QAAQ,EAAEd,sDAAU,CAAC9C,KAAK,CAAC,CAAC;MACpD6D,QAAQ,EAAE,CAAC,EAAE,EAAEf,sDAAU,CAACc,QAAQ,CAAC;MACnCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;MACxB,IAAI,CAACR,SAAS,GAAG,IAAI;MACrB,MAAM;QAAExD,KAAK;QAAE6D;MAAQ,CAAE,GAAG,IAAI,CAACH,SAAS,CAACO,KAAK;MAEhD,IAAI,CAACb,WAAW,CAACc,KAAK,CAAC;QAAEC,KAAK,EAAEnE,KAAK;QAAEoE,QAAQ,EAAEP;MAAQ,CAAE,CAAC,CAACQ,SAAS,CAAC;QACrEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACf,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACkB,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACvE,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACkB,IAAI,CAAC,iDAAiD,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACrG;OACD,CAAC;;EAEN;;;uBArCWrC,cAAc,EAAAlB,+DAAA,CAAAE,uDAAA,GAAAF,+DAAA,CAAA4D,oEAAA,GAAA5D,+DAAA,CAAA8D,mDAAA,GAAA9D,+DAAA,CAAAgE,oEAAA;IAAA;EAAA;;;YAAd9C,cAAc;MAAAgD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1IvBxE,4DAAA,aAA4B;UAKVA,oDAAA,aAAM;UAAAA,0DAAA,EAAW;UAC3BA,4DAAA,SAAI;UAAAA,oDAAA,wBAAiB;UAAAA,0DAAA,EAAK;UAE5BA,4DAAA,QAAG;UAAAA,oDAAA,uCAAgC;UAAAA,0DAAA,EAAI;UAIzCA,4DAAA,mBAA4B;UAERA,oDAAA,iBAAS;UAAAA,0DAAA,EAAiB;UAG5CA,4DAAA,wBAAkB;UACcA,wDAAA,sBAAA2E,kDAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UACnD7C,4DAAA,yBAAwD;UAC3CA,oDAAA,cAAM;UAAAA,0DAAA,EAAY;UAC7BA,uDAAA,gBAAmF;UACnFA,wDAAA,KAAA6E,oCAAA,uBAEY;UACZ7E,wDAAA,KAAA8E,oCAAA,uBAEY;UACd9E,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,oBAAY;UAAAA,0DAAA,EAAY;UACnCA,uDAAA,gBAAuF;UACvFA,4DAAA,kBAAuF;UAArDA,wDAAA,mBAAA+E,iDAAA;YAAA,OAAAN,GAAA,CAAApC,YAAA,IAAAoC,GAAA,CAAApC,YAAA;UAAA,EAAsC;UACtErC,4DAAA,gBAAU;UAAAA,oDAAA,IAAkD;UAAAA,0DAAA,EAAW;UAEzEA,wDAAA,KAAAgF,oCAAA,uBAEY;UACdhF,0DAAA,EAAiB;UAEjBA,4DAAA,eAAgC;UACaA,oDAAA,0BAAkB;UAAAA,0DAAA,EAAe;UAG9EA,4DAAA,kBACkF;UAChFA,wDAAA,KAAAiF,sCAAA,0BAA2D;UAC3DjF,wDAAA,KAAAkF,+BAAA,kBAA4C;UAC9ClF,0DAAA,EAAS;UAGXA,4DAAA,eAAwB;UAEpBA,oDAAA,gCACA;UAAAA,4DAAA,aAA4C;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAI;;;;;;UArCxDA,uDAAA,IAAuB;UAAvBA,wDAAA,cAAAyE,GAAA,CAAAjC,SAAA,CAAuB;UAIbxC,uDAAA,GAAkD;UAAlDA,wDAAA,UAAAqF,OAAA,GAAAZ,GAAA,CAAAjC,SAAA,CAAAhE,GAAA,4BAAA6G,OAAA,CAAAC,QAAA,aAAkD;UAGlDtF,uDAAA,GAA+C;UAA/CA,wDAAA,UAAAuF,OAAA,GAAAd,GAAA,CAAAjC,SAAA,CAAAhE,GAAA,4BAAA+G,OAAA,CAAAD,QAAA,UAA+C;UAO3CtF,uDAAA,GAA2C;UAA3CA,wDAAA,SAAAyE,GAAA,CAAApC,YAAA,uBAA2C;UAE/CrC,uDAAA,GAAkD;UAAlDA,+DAAA,CAAAyE,GAAA,CAAApC,YAAA,mCAAkD;UAElDrC,uDAAA,GAAqD;UAArDA,wDAAA,UAAAyF,OAAA,GAAAhB,GAAA,CAAAjC,SAAA,CAAAhE,GAAA,+BAAAiH,OAAA,CAAAH,QAAA,aAAqD;UAU3DtF,uDAAA,GAA2C;UAA3CA,wDAAA,aAAAyE,GAAA,CAAAjC,SAAA,CAAAkD,OAAA,IAAAjB,GAAA,CAAAnC,SAAA,CAA2C;UACrBtC,uDAAA,GAAe;UAAfA,wDAAA,SAAAyE,GAAA,CAAAnC,SAAA,CAAe;UACpCtC,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAyE,GAAA,CAAAnC,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxD4B;;;;;;;;;;;;;;;;;;;IAsBjDtC,4DAAA,gBAAuE;IACrEA,oDAAA,kCACF;IAAAA,0DAAA,EAAY;;;;;IAMZA,4DAAA,gBAAsE;IACpEA,oDAAA,0BACF;IAAAA,0DAAA,EAAY;;;;;IAOdA,4DAAA,gBAAmE;IACjEA,oDAAA,2BACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAAgE;IAC9DA,oDAAA,gCACF;IAAAA,0DAAA,EAAY;;;;;IASZA,4DAAA,gBAAsE;IACpEA,oDAAA,mCACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAAuE;IACrEA,oDAAA,iEACF;IAAAA,0DAAA,EAAY;;;;;IASZA,4DAAA,gBAA6E;IAC3EA,oDAAA,oCACF;IAAAA,0DAAA,EAAY;;;;;IACZA,4DAAA,gBAA6D;IAC3DA,oDAAA,+CACF;IAAAA,0DAAA,EAAY;;;;;IAUZA,4DAAA,gBAAyE;IACvEA,oDAAA,4CACF;IAAAA,0DAAA,EAAY;;;;;IAOZA,4DAAA,gBAAyE;IACvEA,oDAAA,2CACF;IAAAA,0DAAA,EAAY;;;;;IAKZA,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAO;;;AA4EpD,MAAOmB,iBAAiB;EAM5BhD,YACU8D,EAAe,EACfC,WAAwB,EACxByD,aAA4B,EAC5BxD,MAAc,EACdC,QAAqB;IAJrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAyD,aAAa,GAAbA,aAAa;IACb,KAAAxD,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAuD,mBAAmB,GAAG,IAAI;IAC1B,KAAAtD,SAAS,GAAG,KAAK;EAQd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACsD,YAAY,GAAG,IAAI,CAAC5D,EAAE,CAACQ,KAAK,CAC/B;MACEqD,SAAS,EAAE,CAAC,EAAE,EAAElE,sDAAU,CAACc,QAAQ,CAAC;MACpCqD,QAAQ,EAAE,CAAC,EAAE,EAAEnE,sDAAU,CAACc,QAAQ,CAAC;MACnC5D,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC8C,sDAAU,CAACc,QAAQ,EAAEd,sDAAU,CAAC9C,KAAK,CAAC,CAAC;MACpD6D,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACf,sDAAU,CAACc,QAAQ,EAAEd,sDAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,eAAe,EAAE,CAAC,EAAE,EAAErE,sDAAU,CAACc,QAAQ,CAAC;MAC1CwD,WAAW,EAAE,CAAC,EAAE,EAAEtE,sDAAU,CAACc,QAAQ,CAAC;MACtCyD,WAAW,EAAE,CAAC,KAAK,EAAEvE,sDAAU,CAACwE,YAAY;KAC7C,EACD;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC5C;EACH;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAM5D,QAAQ,GAAG4D,IAAI,CAAC/H,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMyH,eAAe,GAAGM,IAAI,CAAC/H,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAImE,QAAQ,IAAIsD,eAAe,IAAItD,QAAQ,CAACI,KAAK,KAAKkD,eAAe,CAAClD,KAAK,EAAE;MAC3E,OAAO;QAAEyD,gBAAgB,EAAE;MAAI,CAAE;;IAEnC,OAAO,IAAI;EACb;EAEA3D,QAAQA,CAAA;IACN,IAAI,IAAI,CAACgD,YAAY,CAAC/C,KAAK,EAAE;MAC3B,IAAI,CAACR,SAAS,GAAG,IAAI;MACrB,MAAMmE,QAAQ,GAAG,IAAI,CAACZ,YAAY,CAAC9C,KAAK;MAExC,IAAI,CAACb,WAAW,CAACwE,QAAQ,CAACD,QAAQ,CAAC,CAACtD,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACf,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACkB,IAAI,CAAC,uBAAuB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACzE,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACkB,IAAI,CAAC,mDAAmD,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACvG;OACD,CAAC;;EAEN;;;uBAxDWpC,iBAAiB,EAAAnB,+DAAA,CAAAE,uDAAA,GAAAF,+DAAA,CAAA4D,oEAAA,GAAA5D,+DAAA,CAAA8D,wEAAA,GAAA9D,+DAAA,CAAAgE,mDAAA,GAAAhE,+DAAA,CAAA2G,oEAAA;IAAA;EAAA;;;YAAjBxF,iBAAiB;MAAA+C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAsC,2BAAApC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtK1BxE,4DAAA,aAA4B;UAIJA,oDAAA,kBAAW;UAAAA,0DAAA,EAAiB;UAG9CA,4DAAA,uBAAkB;UACiBA,wDAAA,sBAAA6G,oDAAA;YAAA,OAAYpC,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UACtD7C,4DAAA,aAAsB;UAEPA,oDAAA,mBAAM;UAAAA,0DAAA,EAAY;UAC7BA,uDAAA,gBAAuE;UACvEA,wDAAA,KAAA8G,uCAAA,uBAEY;UACd9G,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,WAAG;UAAAA,0DAAA,EAAY;UAC1BA,uDAAA,gBAAmE;UACnEA,wDAAA,KAAA+G,uCAAA,uBAEY;UACd/G,0DAAA,EAAiB;UAGnBA,4DAAA,yBAAwD;UAC3CA,oDAAA,cAAM;UAAAA,0DAAA,EAAY;UAC7BA,uDAAA,iBAAmF;UACnFA,wDAAA,KAAAgH,uCAAA,uBAEY;UACZhH,wDAAA,KAAAiH,uCAAA,uBAEY;UACdjH,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,oBAAY;UAAAA,0DAAA,EAAY;UACnCA,uDAAA,iBAAuF;UACvFA,4DAAA,kBAAuF;UAArDA,wDAAA,mBAAAkH,oDAAA;YAAA,OAAAzC,GAAA,CAAApC,YAAA,IAAAoC,GAAA,CAAApC,YAAA;UAAA,EAAsC;UACtErC,4DAAA,gBAAU;UAAAA,oDAAA,IAAkD;UAAAA,0DAAA,EAAW;UAEzEA,wDAAA,KAAAmH,uCAAA,uBAEY;UACZnH,wDAAA,KAAAoH,uCAAA,uBAEY;UACdpH,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,iCAAyB;UAAAA,0DAAA,EAAY;UAChDA,uDAAA,iBAAqG;UACrGA,4DAAA,kBAAqG;UAAnEA,wDAAA,mBAAAqH,oDAAA;YAAA,OAAA5C,GAAA,CAAAmB,mBAAA,IAAAnB,GAAA,CAAAmB,mBAAA;UAAA,EAAoD;UACpF5F,4DAAA,gBAAU;UAAAA,oDAAA,IAAyD;UAAAA,0DAAA,EAAW;UAEhFA,wDAAA,KAAAsH,uCAAA,uBAEY;UACZtH,wDAAA,KAAAuH,uCAAA,uBAEY;UACdvH,0DAAA,EAAiB;UAEjBA,4DAAA,yBAAwD;UAC3CA,oDAAA,sBAAc;UAAAA,0DAAA,EAAY;UACrCA,4DAAA,sBAA0C;UACbA,oDAAA,0BAAkB;UAAAA,0DAAA,EAAa;UAC1DA,4DAAA,sBAA8B;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAa;UACpDA,4DAAA,sBAA0B;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAa;UAEvDA,wDAAA,KAAAwH,uCAAA,uBAEY;UACdxH,0DAAA,EAAiB;UAEjBA,4DAAA,eAAgC;UAE5BA,oDAAA,uBAAc;UAAAA,4DAAA,aAA8C;UAAAA,oDAAA,gCAAwB;UAAAA,0DAAA,EAAI;UAE1FA,wDAAA,KAAAyH,uCAAA,uBAEY;UACdzH,0DAAA,EAAM;UAENA,4DAAA,kBACqF;UACnFA,wDAAA,KAAA0H,yCAAA,0BAA2D;UAC3D1H,wDAAA,KAAA2H,kCAAA,kBAA0C;UAC5C3H,0DAAA,EAAS;UAGXA,4DAAA,eAAwB;UAEpBA,oDAAA,oCACA;UAAAA,4DAAA,aAAyC;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAI;;;;;;;;;;;;UAzFvDA,uDAAA,GAA0B;UAA1BA,wDAAA,cAAAyE,GAAA,CAAAoB,YAAA,CAA0B;UAKd7F,uDAAA,GAAyD;UAAzDA,wDAAA,UAAAqF,OAAA,GAAAZ,GAAA,CAAAoB,YAAA,CAAArH,GAAA,gCAAA6G,OAAA,CAAAC,QAAA,aAAyD;UAQzDtF,uDAAA,GAAwD;UAAxDA,wDAAA,UAAAuF,OAAA,GAAAd,GAAA,CAAAoB,YAAA,CAAArH,GAAA,+BAAA+G,OAAA,CAAAD,QAAA,aAAwD;UAS1DtF,uDAAA,GAAqD;UAArDA,wDAAA,UAAA4H,OAAA,GAAAnD,GAAA,CAAAoB,YAAA,CAAArH,GAAA,4BAAAoJ,OAAA,CAAAtC,QAAA,aAAqD;UAGrDtF,uDAAA,GAAkD;UAAlDA,wDAAA,UAAA6H,OAAA,GAAApD,GAAA,CAAAoB,YAAA,CAAArH,GAAA,4BAAAqJ,OAAA,CAAAvC,QAAA,UAAkD;UAO9CtF,uDAAA,GAA2C;UAA3CA,wDAAA,SAAAyE,GAAA,CAAApC,YAAA,uBAA2C;UAE/CrC,uDAAA,GAAkD;UAAlDA,+DAAA,CAAAyE,GAAA,CAAApC,YAAA,mCAAkD;UAElDrC,uDAAA,GAAwD;UAAxDA,wDAAA,UAAA8H,OAAA,GAAArD,GAAA,CAAAoB,YAAA,CAAArH,GAAA,+BAAAsJ,OAAA,CAAAxC,QAAA,aAAwD;UAGxDtF,uDAAA,GAAyD;UAAzDA,wDAAA,UAAA+H,OAAA,GAAAtD,GAAA,CAAAoB,YAAA,CAAArH,GAAA,+BAAAuJ,OAAA,CAAAzC,QAAA,cAAyD;UAOrDtF,uDAAA,GAAkD;UAAlDA,wDAAA,SAAAyE,GAAA,CAAAmB,mBAAA,uBAAkD;UAEtD5F,uDAAA,GAAyD;UAAzDA,+DAAA,CAAAyE,GAAA,CAAAmB,mBAAA,mCAAyD;UAEzD5F,uDAAA,GAA+D;UAA/DA,wDAAA,UAAAgI,QAAA,GAAAvD,GAAA,CAAAoB,YAAA,CAAArH,GAAA,sCAAAwJ,QAAA,CAAA1C,QAAA,aAA+D;UAG/DtF,uDAAA,GAA+C;UAA/CA,wDAAA,SAAAyE,GAAA,CAAAoB,YAAA,CAAAP,QAAA,qBAA+C;UAY/CtF,uDAAA,IAA2D;UAA3DA,wDAAA,UAAAiI,QAAA,GAAAxD,GAAA,CAAAoB,YAAA,CAAArH,GAAA,kCAAAyJ,QAAA,CAAA3C,QAAA,aAA2D;UAS3DtF,uDAAA,GAA2D;UAA3DA,wDAAA,UAAAkI,QAAA,GAAAzD,GAAA,CAAAoB,YAAA,CAAArH,GAAA,kCAAA0J,QAAA,CAAA5C,QAAA,aAA2D;UAMjEtF,uDAAA,GAA8C;UAA9CA,wDAAA,aAAAyE,GAAA,CAAAoB,YAAA,CAAAH,OAAA,IAAAjB,GAAA,CAAAnC,SAAA,CAA8C;UACxBtC,uDAAA,GAAe;UAAfA,wDAAA,SAAAyE,GAAA,CAAAnC,SAAA,CAAe;UACpCtC,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAyE,GAAA,CAAAnC,SAAA,CAAgB", "sources": ["./src/app/core/services/client.service.ts", "./src/app/features/auth/auth.module.ts", "./src/app/features/auth/login/login.component.ts", "./src/app/features/auth/register/register.component.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Client } from \"../models/user.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class ClientService {\n  private apiUrl = `${environment.urlApi}client`\n\n  constructor(private http: HttpClient) {}\n\n  // GET: Tous les clients (correspond à GET /api/client)\n  getAllClients(): Observable<Client[]> {\n    return this.http.get<Client[]>(this.apiUrl)\n  }\n\n  // GET: Un client par ID (correspond à GET /api/client/{id})\n  getClient(id: number): Observable<Client> {\n    return this.http.get<Client>(`${this.apiUrl}/${id}`)\n  }\n\n  // POST: Créer un client (correspond à POST /api/client)\n  createClient(client: Client): Observable<Client> {\n    // ✅ Préparer les données selon le modèle .NET Client\n    const clientData = {\n      email: client.email,\n      nom: client.nom,\n      prenom: client.prenom,\n      role: \"Client\",\n      // Initialiser les collections vides pour correspondre au modèle .NET\n      paiements: [],\n      coursConsultes: [],\n      resultatsQuiz: []\n    };\n\n    console.log('Données envoyées au serveur:', clientData); // ✅ Debug\n    return this.http.post<Client>(this.apiUrl, clientData)\n  }\n\n  // PUT: Modifier un client (correspond à PUT /api/client/{id})\n  updateClient(id: number, client: Client): Observable<any> {\n    return this.http.put(`${this.apiUrl}/${id}`, client)\n  }\n\n  // DELETE: Supprimer un client (correspond à DELETE /api/client/{id})\n  deleteClient(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/${id}`)\n  }\n\n  // POST: S'inscrire à un cours gratuit (correspond à POST /api/client/{clientId}/inscrire/{coursId})\n  sInscrire(clientId: number, coursId: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/${clientId}/inscrire/${coursId}`, {})\n  }\n\n  // POST: Acheter un cours payant (correspond à POST /api/client/{clientId}/acheter/{coursId})\n  acheterContenu(clientId: number, coursId: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/${clientId}/acheter/${coursId}`, {})\n  }\n}\n", "import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCheckboxModule } from \"@angular/material/checkbox\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { LoginComponent } from \"./login/login.component\"\nimport { RegisterComponent } from \"./register/register.component\"\n\n@NgModule({\n  declarations: [LoginComponent, RegisterComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatCheckboxModule,\n    MatSelectModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([\n      { path: \"login\", component: LoginComponent },\n      { path: \"register\", component: RegisterComponent },\n      { path: \"\", redirectTo: \"login\", pathMatch: \"full\" },\n    ]),\n  ],\n})\nexport class AuthModule {}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { Form<PERSON>uilder, FormGroup, Validators } from \"@angular/forms\"\nimport { Router } from \"@angular/router\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { AuthService } from \"../../../core/services/auth.service\"\n\n@Component({\n  selector: \"app-login\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <!-- Header -->\n        <div class=\"auth-header\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <h1>Training Platform</h1>\n          </div>\n          <p>Plateforme de formation en ligne</p>\n        </div>\n\n        <!-- Login Form -->\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Connexion</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"rememberMe\">Se souvenir de moi</mat-checkbox>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"loginForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">Se connecter</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Pas encore de compte ? \n                <a routerLink=\"/auth/register\" class=\"link\">S'inscrire</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .auth-header {\n      text-align: center;\n      margin-bottom: 2rem;\n      color: white;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .logo mat-icon {\n      font-size: 2.5rem;\n      margin-right: 0.5rem;\n    }\n\n    .logo h1 {\n      font-size: 2rem;\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `,\n  ],\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup\n  hidePassword = true\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.loginForm = this.fb.group({\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", Validators.required],\n      rememberMe: [false],\n    })\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true\n      const { email, password } = this.loginForm.value\n\n      this.authService.login({ Email: email, Password: password }).subscribe({\n        next: (response) => {\n          this.isLoading = false\n          this.snackBar.open(\"Connexion réussie !\", \"Fermer\", { duration: 3000 })\n          this.router.navigate([\"/dashboard\"])\n        },\n        error: (error) => {\n          this.isLoading = false\n          this.snackBar.open(\"Erreur de connexion. Vérifiez vos identifiants.\", \"Fermer\", { duration: 5000 })\n        },\n      })\n    }\n  }\n}\n", "import { Component, OnInit } from \"@angular/core\"\nimport { Form<PERSON>uilder, FormGroup, Validators } from \"@angular/forms\"\nimport { Router } from \"@angular/router\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { AuthService } from \"../../../core/services/auth.service\"\nimport { ClientService } from \"../../../core/services/client.service\"\n\n@Component({\n  selector: \"app-register\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Inscription</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n              <div class=\"name-row\">\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Prénom</mat-label>\n                  <input matInput formControlName=\"firstName\" placeholder=\"Votre prénom\">\n                  <mat-error *ngIf=\"registerForm.get('firstName')?.hasError('required')\">\n                    Le prénom est requis\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Nom</mat-label>\n                  <input matInput formControlName=\"lastName\" placeholder=\"Votre nom\">\n                  <mat-error *ngIf=\"registerForm.get('lastName')?.hasError('required')\">\n                    Le nom est requis\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"registerForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"registerForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.get('password')?.hasError('minlength')\">\n                  Le mot de passe doit contenir au moins 6 caractères\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Confirmer le mot de passe</mat-label>\n                <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\" formControlName=\"confirmPassword\">\n                <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n                  <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"registerForm.get('confirmPassword')?.hasError('required')\">\n                  La confirmation est requise\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.hasError('passwordMismatch')\">\n                  Les mots de passe ne correspondent pas\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Type de compte</mat-label>\n                <mat-select formControlName=\"accountType\">\n                  <mat-option value=\"client\">Client (Apprenant)</mat-option>\n                  <mat-option value=\"formateur\">Formateur</mat-option>\n                  <mat-option value=\"admin\">Administrateur</mat-option>\n                </mat-select>\n                <mat-error *ngIf=\"registerForm.get('accountType')?.hasError('required')\">\n                  Sélectionnez un type de compte\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"acceptTerms\">\n                  J'accepte les <a href=\"/terms\" target=\"_blank\" class=\"link\">conditions d'utilisation</a>\n                </mat-checkbox>\n                <mat-error *ngIf=\"registerForm.get('acceptTerms')?.hasError('required')\">\n                  Vous devez accepter les conditions\n                </mat-error>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"registerForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">S'inscrire</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Déjà un compte ? \n                <a routerLink=\"/auth/login\" class=\"link\">Se connecter</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 500px;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .name-row {\n      display: flex;\n      gap: 1rem;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `,\n  ],\n})\nexport class RegisterComponent implements OnInit {\n  registerForm!: FormGroup\n  hidePassword = true\n  hideConfirmPassword = true\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private clientService: ClientService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.registerForm = this.fb.group(\n      {\n        firstName: [\"\", Validators.required],\n        lastName: [\"\", Validators.required],\n        email: [\"\", [Validators.required, Validators.email]],\n        password: [\"\", [Validators.required, Validators.minLength(6)]],\n        confirmPassword: [\"\", Validators.required],\n        accountType: [\"\", Validators.required],\n        acceptTerms: [false, Validators.requiredTrue],\n      },\n      { validators: this.passwordMatchValidator },\n    )\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get(\"password\")\n    const confirmPassword = form.get(\"confirmPassword\")\n\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      return { passwordMismatch: true }\n    }\n    return null\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true\n      const formData = this.registerForm.value\n\n      this.authService.register(formData).subscribe({\n        next: (response) => {\n          this.isLoading = false\n          this.snackBar.open(\"Inscription réussie !\", \"Fermer\", { duration: 3000 })\n          this.router.navigate([\"/dashboard\"])\n        },\n        error: (error) => {\n          this.isLoading = false\n          this.snackBar.open(\"Erreur lors de l'inscription. Veuillez réessayer.\", \"Fermer\", { duration: 5000 })\n        },\n      })\n    }\n  }\n}\n"], "names": ["environment", "ClientService", "constructor", "http", "apiUrl", "urlApi", "getAllClients", "get", "getClient", "id", "createClient", "client", "clientData", "email", "nom", "prenom", "role", "paiements", "coursConsultes", "resultatsQuiz", "console", "log", "post", "updateClient", "put", "deleteClient", "delete", "sInscrire", "clientId", "coursId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn", "CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatCheckboxModule", "MatSelectModule", "MatIconModule", "MatProgressSpinnerModule", "LoginComponent", "RegisterComponent", "AuthModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "redirectTo", "pathMatch", "declarations", "imports", "Validators", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "fb", "authService", "router", "snackBar", "hidePassword", "isLoading", "ngOnInit", "loginForm", "group", "required", "password", "rememberMe", "onSubmit", "valid", "value", "login", "Email", "Password", "subscribe", "next", "response", "open", "duration", "navigate", "error", "ɵɵdirectiveInject", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_15_listener", "ɵɵtemplate", "LoginComponent_mat_error_20_Template", "LoginComponent_mat_error_21_Template", "LoginComponent_Template_button_click_26_listener", "LoginComponent_mat_error_29_Template", "LoginComponent_mat_spinner_34_Template", "LoginComponent_span_35_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "ɵɵtextInterpolate", "tmp_5_0", "invalid", "clientService", "hideConfirmPassword", "registerForm", "firstName", "lastName", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "accountType", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "form", "passwordMismatch", "formData", "register", "i5", "RegisterComponent_Template", "RegisterComponent_Template_form_ngSubmit_7_listener", "RegisterComponent_mat_error_13_Template", "RegisterComponent_mat_error_18_Template", "RegisterComponent_mat_error_23_Template", "RegisterComponent_mat_error_24_Template", "RegisterComponent_Template_button_click_29_listener", "RegisterComponent_mat_error_32_Template", "RegisterComponent_mat_error_33_Template", "RegisterComponent_Template_button_click_38_listener", "RegisterComponent_mat_error_41_Template", "RegisterComponent_mat_error_42_Template", "RegisterComponent_mat_error_53_Template", "RegisterComponent_mat_error_59_Template", "RegisterComponent_mat_spinner_61_Template", "RegisterComponent_span_62_Template", "tmp_3_0", "tmp_4_0", "tmp_7_0", "tmp_8_0", "tmp_11_0", "tmp_13_0", "tmp_14_0"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}