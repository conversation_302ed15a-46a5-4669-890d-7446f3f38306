{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(subscriber => {\n    const resource = resourceFactory();\n    const result = observableFactory(resource);\n    const source = result ? innerFrom(result) : EMPTY;\n    source.subscribe(subscriber);\n    return () => {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "EMPTY", "using", "resourceFactory", "observableFactory", "subscriber", "resource", "result", "source", "subscribe", "unsubscribe"], "sources": ["C:/e-learning/node_modules/rxjs/dist/esm/internal/observable/using.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n    return new Observable((subscriber) => {\n        const resource = resourceFactory();\n        const result = observableFactory(resource);\n        const source = result ? innerFrom(result) : EMPTY;\n        source.subscribe(subscriber);\n        return () => {\n            if (resource) {\n                resource.unsubscribe();\n            }\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,KAAKA,CAACC,eAAe,EAAEC,iBAAiB,EAAE;EACtD,OAAO,IAAIL,UAAU,CAAEM,UAAU,IAAK;IAClC,MAAMC,QAAQ,GAAGH,eAAe,CAAC,CAAC;IAClC,MAAMI,MAAM,GAAGH,iBAAiB,CAACE,QAAQ,CAAC;IAC1C,MAAME,MAAM,GAAGD,MAAM,GAAGP,SAAS,CAACO,MAAM,CAAC,GAAGN,KAAK;IACjDO,MAAM,CAACC,SAAS,CAACJ,UAAU,CAAC;IAC5B,OAAO,MAAM;MACT,IAAIC,QAAQ,EAAE;QACVA,QAAQ,CAACI,WAAW,CAAC,CAAC;MAC1B;IACJ,CAAC;EACL,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}