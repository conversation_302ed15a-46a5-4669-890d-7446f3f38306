import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Formateur } from "../models/user.model"
import { Course } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class FormateurService {
  private apiUrl = `${environment.urlApi}formateur`

  constructor(private http: HttpClient) {}

  // GET: Tous les formateurs (correspond à GET /api/formateur)
  getAllFormateurs(): Observable<Formateur[]> {
    return this.http.get<Formateur[]>(this.apiUrl)
  }

  // GET: Un formateur par ID (correspond à GET /api/formateur/{id})
  getFormateur(id: number): Observable<Formateur> {
    return this.http.get<Formateur>(`${this.apiUrl}/${id}`)
  }

  // POST: Déposer CV et diplôme (correspond à POST /api/formateur/{id}/deposer-documents)
  deposerDocuments(id: number, documents: { cv: string; diplome: string }): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}/deposer-documents`, documents)
  }

  // POST: Ajouter un cours (correspond à POST /api/formateur/{id}/ajouter-cours)
  ajouterCours(formateurId: number, cours: Course): Observable<Course> {
    return this.http.post<Course>(`${this.apiUrl}/${formateurId}/ajouter-cours`, cours)
  }

  // DELETE: Supprimer un formateur (correspond à DELETE /api/formateur/{id})
  supprimerFormateur(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`)
  }
}
