import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Formateur } from "../models/user.model"
import type { Cours } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class FormateurService {
  constructor(private http: HttpClient) {}

  // GET: Tous les formateurs
  getAllFormateurs(): Observable<Formateur[]> {
    return this.http.get<Formateur[]>(`${environment.urlApi}formateur`)
  }

  // GET: Un formateur par ID
  getFormateur(id: number): Observable<Formateur> {
    return this.http.get<Formateur>(`${environment.urlApi}formateur/${id}`)
  }

  // POST: Déposer CV et diplôme
  deposerDocuments(id: number, documents: { cv: string; diplome: string }): Observable<any> {
    return this.http.post(`${environment.urlApi}formateur/${id}/deposer-documents`, documents)
  }

  // POST: Ajouter un cours
  ajouterCours(formateurId: number, cours: Cours): Observable<Cours> {
    return this.http.post<Cours>(`${environment.urlApi}formateur/${formateurId}/ajouter-cours`, cours)
  }

  // DELETE: Supprimer un formateur
  supprimerFormateur(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}formateur/${id}`)
  }
}
