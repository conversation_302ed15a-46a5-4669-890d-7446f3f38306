{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatInputModule } from \"@angular/material/input\";\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\nimport { MatDividerModule } from \"@angular/material/divider\";\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\";\nimport { PaymentComponent } from \"./payment.component\";\nexport let PaymentModule = class PaymentModule {};\nPaymentModule = __decorate([NgModule({\n  declarations: [PaymentComponent],\n  imports: [CommonModule, ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatInputModule, MatFormFieldModule, MatDividerModule, MatProgressSpinnerModule, RouterModule.forChild([{\n    path: \":courseId\",\n    component: PaymentComponent\n  }])]\n})], PaymentModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatDividerModule", "MatProgressSpinnerModule", "PaymentComponent", "PaymentModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component"], "sources": ["C:\\e-learning\\src\\app\\features\\payment\\payment.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { ReactiveFormsModule } from \"@angular/forms\"\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatDividerModule } from \"@angular/material/divider\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { PaymentComponent } from \"./payment.component\"\n\n@NgModule({\n  declarations: [PaymentComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    RouterModule.forChild([{ path: \":courseId\", component: PaymentComponent }]),\n  ],\n})\nexport class PaymentModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,gBAAgB,QAAQ,qBAAqB;AAiB/C,WAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAG;AAAhBA,aAAa,GAAAC,UAAA,EAfzBb,QAAQ,CAAC;EACRc,YAAY,EAAE,CAACH,gBAAgB,CAAC;EAChCI,OAAO,EAAE,CACPd,YAAY,EACZE,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,wBAAwB,EACxBR,YAAY,CAACc,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEP;EAAgB,CAAE,CAAC,CAAC;CAE9E,CAAC,C,EACWC,aAAa,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}