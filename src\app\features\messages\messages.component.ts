import { Component, type OnInit } from "@angular/core"
import type { MessageService } from "../../core/services/message.service"
import type { AuthService } from "../../core/services/auth.service"
import type { Message } from "../../core/models/message.model"
import type { User } from "../../core/models/user.model"
import type { MatSnackBar } from "@angular/material/snack-bar"

interface ConversationPreview {
  participantId: number
  participantName: string
  participantRole: "Client" | "Formateur" | "Admin"
  lastMessageContent: string
  lastMessageDate: Date
  unreadCount: number
}

@Component({
  selector: "app-messages",
  template: `
    <div class="messages-container">
      <div class="messages-wrapper">
        <!-- Conversation List -->
        <mat-card class="conversation-list-card">
          <mat-card-header>
            <mat-card-title class="card-title-with-icon">
              <mat-icon>message</mat-icon>
              Messages
            </mat-card-title>
            <button mat-icon-button><mat-icon>add</mat-icon></button>
          </mat-card-header>
          <mat-card-content class="conversation-search">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Rechercher une conversation...</mat-label>
              <input matInput [(ngModel)]="searchTerm" (input)="applySearch()">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
          </mat-card-content>
          <mat-nav-list class="conversations-scroll-area">
            <mat-list-item *ngFor="let conv of filteredConversations" 
                           (click)="selectConversation(conv.participantId)"
                           [ngClass]="{'selected-conversation': selectedConversationId === conv.participantId}">
              <div matListItemAvatar class="avatar-placeholder">
                <span>{{ getInitials(conv.participantName) }}</span>
              </div>
              <div matListItemTitle class="conversation-title">
                <span>{{ conv.participantName }}</span>
                <mat-chip-listbox>
                  <mat-chip [class]="getRoleChipClass(conv.participantRole)">{{ conv.participantRole }}</mat-chip>
                </mat-chip-listbox>
              </div>
              <div matListItemLine class="conversation-last-message">
                <span class="message-content">{{ conv.lastMessageContent }}</span>
                <span class="message-date">{{ formatDate(conv.lastMessageDate) }}</span>
              </div>
              <mat-chip-listbox *ngIf="conv.unreadCount > 0" matListItemMeta>
                <mat-chip color="warn" selected>{{ conv.unreadCount }}</mat-chip>
              </mat-chip-listbox>
              <mat-divider></mat-divider>
            </mat-list-item>
            <div *ngIf="filteredConversations.length === 0" class="no-conversations">
              <mat-icon>chat_bubble_outline</mat-icon>
              <p>Aucune conversation trouvée.</p>
            </div>
          </mat-nav-list>
        </mat-card>

        <!-- Chat Area -->
        <mat-card class="chat-area-card">
          <ng-container *ngIf="selectedConversationId; else noConversationSelected">
            <mat-card-header class="chat-header">
              <div class="chat-header-info">
                <div matListItemAvatar class="avatar-placeholder">
                  <span>{{ getInitials(selectedParticipant?.participantName || '') }}</span>
                </div>
                <div>
                  <mat-card-title>{{ selectedParticipant?.participantName }}</mat-card-title>
                  <mat-chip-listbox>
                    <mat-chip [class]="getRoleChipClass(selectedParticipant?.participantRole || 'Client')">
                      {{ selectedParticipant?.participantRole }}
                    </mat-chip>
                  </mat-chip-listbox>
                </div>
              </div>
            </mat-card-header>

            <mat-card-content class="messages-display-area">
              <div *ngFor="let msg of messages" 
                   class="message-bubble-wrapper" 
                   [ngClass]="{'my-message': msg.expediteurId === currentUser?.id, 'other-message': msg.expediteurId !== currentUser?.id}">
                <div class="message-bubble">
                  <p class="message-content">{{ msg.contenu }}</p>
                  <span class="message-timestamp">{{ formatDate(msg.dateEnvoi!) }}</span>
                </div>
              </div>
            </mat-card-content>

            <mat-card-actions class="message-input-area">
              <mat-form-field appearance="outline" class="full-width message-input-field">
                <textarea matInput placeholder="Tapez votre message..." 
                          [(ngModel)]="newMessageContent" 
                          (keydown.enter)="sendMessage($event)"
                          rows="1"></textarea>
              </mat-form-field>
              <button mat-mini-fab color="primary" (click)="sendMessage()" [disabled]="!newMessageContent.trim()">
                <mat-icon>send</mat-icon>
              </button>
            </mat-card-actions>
          </ng-container>

          <ng-template #noConversationSelected>
            <div class="no-conversation-selected">
              <mat-icon>chat</mat-icon>
              <p>Sélectionnez une conversation pour commencer</p>
            </div>
          </ng-template>
        </mat-card>
      </div>
    </div>
  `,
  styles: [
    `
    .messages-container {
      min-height: 100vh;
      background-color: #f5f5f5;
      padding: 2rem;
    }

    .messages-wrapper {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 1.5rem;
      max-width: 1400px;
      margin: 0 auto;
      height: calc(100vh - 4rem); /* Adjust height to fit viewport */
    }

    @media (max-width: 960px) {
      .messages-wrapper {
        grid-template-columns: 1fr;
        height: auto;
      }
      .conversation-list-card {
        height: auto;
        min-height: 300px;
      }
      .chat-area-card {
        height: 600px; /* Fixed height for chat on small screens */
      }
    }

    .conversation-list-card, .chat-area-card {
      display: flex;
      flex-direction: column;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .conversation-list-card mat-card-header {
      padding-bottom: 0;
    }

    .card-title-with-icon {
      display: flex;
      align-items: center;
      font-size: 1.4rem;
      font-weight: 600;
    }

    .card-title-with-icon mat-icon {
      margin-right: 0.5rem;
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    .conversation-search {
      padding: 1rem 1.5rem 0.5rem;
    }

    .conversation-search .full-width {
      width: 100%;
    }

    .conversations-scroll-area {
      flex-grow: 1;
      overflow-y: auto;
      padding: 0;
    }

    .mat-list-item {
      height: auto !important;
      padding: 1rem 1.5rem;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
    }

    .mat-list-item:hover {
      background-color: #f5f5f5;
    }

    .mat-list-item.selected-conversation {
      background-color: #ede7f6; /* Light purple */
      border-left: 4px solid #673ab7; /* Purple */
    }

    .avatar-placeholder {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background-color: #e1bee7; /* Light purple */
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.1rem;
      font-weight: bold;
      color: #8e24aa; /* Dark purple */
      flex-shrink: 0;
    }

    .conversation-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
      font-size: 1rem;
    }

    .conversation-last-message {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.85rem;
      color: #777;
      margin-top: 0.2rem;
    }

    .message-content {
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 0.5rem;
    }

    .message-date {
      flex-shrink: 0;
    }

    .mat-chip {
      font-size: 0.7rem;
      padding: 0.2rem 0.5rem;
      height: auto;
    }

    .role-chip-Client { background-color: #e0e0e0; color: #424242; }
    .role-chip-Formateur { background-color: #bbdefb; color: #1976d2; }
    .role-chip-Admin { background-color: #e1bee7; color: #8e24aa; }

    .no-conversations {
      text-align: center;
      padding: 2rem;
      color: #999;
    }

    .no-conversations mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
    }

    /* Chat Area */
    .chat-area-card {
      background-color: #fff;
    }

    .chat-header {
      border-bottom: 1px solid #eee;
      padding: 1rem 1.5rem;
    }

    .chat-header-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .chat-header-info mat-card-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.2rem;
    }

    .messages-display-area {
      flex-grow: 1;
      overflow-y: auto;
      padding: 1.5rem;
      background-color: #fcfcfc;
    }

    .message-bubble-wrapper {
      display: flex;
      margin-bottom: 1rem;
    }

    .message-bubble-wrapper.my-message {
      justify-content: flex-end;
    }

    .message-bubble-wrapper.other-message {
      justify-content: flex-start;
    }

    .message-bubble {
      max-width: 70%;
      padding: 0.8rem 1.2rem;
      border-radius: 18px;
      position: relative;
      word-wrap: break-word;
    }

    .my-message .message-bubble {
      background-color: #673ab7; /* Purple */
      color: white;
      border-bottom-right-radius: 4px;
    }

    .other-message .message-bubble {
      background-color: #e0e0e0; /* Light gray */
      color: #333;
      border-bottom-left-radius: 4px;
    }

    .message-bubble p {
      margin: 0;
      font-size: 0.95rem;
      line-height: 1.4;
    }

    .message-timestamp {
      font-size: 0.75rem;
      margin-top: 0.5rem;
      display: block;
      text-align: right;
      color: rgba(255, 255, 255, 0.7); /* For my messages */
    }

    .other-message .message-timestamp {
      color: #777;
    }

    .message-input-area {
      border-top: 1px solid #eee;
      padding: 1rem 1.5rem;
      display: flex;
      align-items: flex-end;
      gap: 0.8rem;
    }

    .message-input-field {
      flex-grow: 1;
    }

    .message-input-field textarea {
      min-height: 40px;
      max-height: 120px;
      overflow-y: auto;
    }

    .message-input-area button {
      flex-shrink: 0;
    }

    .no-conversation-selected {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #999;
      text-align: center;
    }

    .no-conversation-selected mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
    }
  `,
  ],
})
export class MessagesComponent implements OnInit {
  conversations: ConversationPreview[] = []
  filteredConversations: ConversationPreview[] = []
  selectedConversationId: number | null = null
  messages: Message[] = []
  newMessageContent = ""
  searchTerm = ""
  currentUser: User | null = null
  selectedParticipant: ConversationPreview | null = null

  constructor(
    private messageService: MessageService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user
      if (this.currentUser) {
        this.loadConversations()
      }
    })
  }

  loadConversations(): void {
    // Mock data for demonstration
    this.conversations = [
      {
        participantId: 2, // Formateur Jean Dupont
        participantName: "Jean Dupont",
        participantRole: "Formateur",
        lastMessageContent: "Bonjour, avez-vous des questions sur le cours React ?",
        lastMessageDate: new Date("2024-01-15T10:30:00Z"),
        unreadCount: 2,
      },
      {
        participantId: 3, // Client Sophie Bernard
        participantName: "Sophie Bernard",
        participantRole: "Client",
        lastMessageContent: "Merci pour votre aide !",
        lastMessageDate: new Date("2024-01-14T15:45:00Z"),
        unreadCount: 0,
      },
      {
        participantId: 4, // Admin Admin System
        participantName: "Admin System",
        participantRole: "Admin",
        lastMessageContent: "Votre demande a été traitée.",
        lastMessageDate: new Date("2024-01-13T11:00:00Z"),
        unreadCount: 0,
      },
    ]
    this.applySearch()

    // Uncomment to fetch from API
    /*
    // This would require a backend endpoint to get conversation previews for a user
    // For example: this.messageService.getConversations(this.currentUser.id).subscribe(...)
    */
  }

  applySearch(): void {
    this.filteredConversations = this.conversations.filter((conv) =>
      conv.participantName.toLowerCase().includes(this.searchTerm.toLowerCase()),
    )
  }

  selectConversation(participantId: number): void {
    this.selectedConversationId = participantId
    this.selectedParticipant = this.conversations.find((c) => c.participantId === participantId) || null
    this.loadMessages(participantId)
  }

  loadMessages(participantId: number): void {
    if (!this.currentUser) return

    // Mock messages for the selected conversation
    this.messages = [
      {
        id: 1,
        expediteurId: participantId,
        destinataireId: this.currentUser.id,
        contenu: "Bonjour, j'espère que vous appréciez le cours React !",
        dateEnvoi: new Date("2024-01-15T09:00:00Z"),
        expediteur: { id: participantId, nom: "Dupont", prenom: "Jean", email: "", role: "Formateur" },
        destinataire: this.currentUser,
      },
      {
        id: 2,
        expediteurId: this.currentUser.id,
        destinataireId: participantId,
        contenu: "Bonjour Jean, oui c'est très intéressant ! J'ai une question sur les hooks.",
        dateEnvoi: new Date("2024-01-15T09:15:00Z"),
        expediteur: this.currentUser,
        destinataire: { id: participantId, nom: "Dupont", prenom: "Jean", email: "", role: "Formateur" },
      },
      {
        id: 3,
        expediteurId: participantId,
        destinataireId: this.currentUser.id,
        contenu:
          "Parfait ! N'hésitez pas à me poser toutes vos questions. Les hooks peuvent sembler complexes au début.",
        dateEnvoi: new Date("2024-01-15T09:30:00Z"),
        expediteur: { id: participantId, nom: "Dupont", prenom: "Jean", email: "", role: "Formateur" },
        destinataire: this.currentUser,
      },
      {
        id: 4,
        expediteurId: participantId,
        destinataireId: this.currentUser.id,
        contenu: "Bonjour, avez-vous des questions sur le cours React ?",
        dateEnvoi: new Date("2024-01-15T10:30:00Z"),
        expediteur: { id: participantId, nom: "Dupont", prenom: "Jean", email: "", role: "Formateur" },
        destinataire: this.currentUser,
      },
    ]

    // Uncomment to fetch from API
    /*
    this.messageService.getMessages(this.currentUser.id, participantId).subscribe({
      next: (data) => {
        this.messages = data;
        // Mark messages as read
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement des messages.', 'Fermer', { duration: 3000 });
        console.error(err);
      }
    });
    */
  }

  sendMessage(event?: KeyboardEvent): void {
    if (event && event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
    } else if (event && event.key !== "Enter") {
      return // Only proceed on Enter key press
    }

    if (!this.newMessageContent.trim() || !this.selectedConversationId || !this.currentUser) return

    const message: Message = {
      expediteurId: this.currentUser.id,
      destinataireId: this.selectedConversationId,
      contenu: this.newMessageContent,
      dateEnvoi: new Date(),
    }

    // Mock message sending
    this.messages.push(message)
    this.newMessageContent = ""
    this.snackBar.open("Message envoyé (simulé) !", "Fermer", { duration: 1000 })

    // Uncomment to send via API
    /*
    this.messageService.envoyerMessage(message).subscribe({
      next: (res) => {
        this.messages.push(message); // Add to local list after successful send
        this.newMessageContent = '';
        this.snackBar.open('Message envoyé !', 'Fermer', { duration: 1000 });
      },
      error: (err) => {
        this.snackBar.open('Erreur lors de l\'envoi du message.', 'Fermer', { duration: 3000 });
        console.error(err);
      }
    });
    */
  }

  getInitials(name: string): string {
    const parts = name.split(" ")
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[1][0]}`.toUpperCase()
    } else if (parts.length === 1 && parts[0].length > 0) {
      return parts[0][0].toUpperCase()
    }
    return ""
  }

  formatDate(date: Date): string {
    const d = new Date(date)
    const now = new Date()
    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24 && d.getDate() === now.getDate()) {
      return d.toLocaleTimeString("fr-FR", { hour: "2-digit", minute: "2-digit" })
    } else if (diffInHours < 48 && d.getDate() === now.getDate() - 1) {
      return "Hier"
    } else {
      return d.toLocaleDateString("fr-FR", { day: "2-digit", month: "2-digit" })
    }
  }

  getRoleChipClass(role: string): string {
    switch (role) {
      case "Client":
        return "role-chip-Client"
      case "Formateur":
        return "role-chip-Formateur"
      case "Admin":
        return "role-chip-Admin"
      default:
        return ""
    }
  }
}
