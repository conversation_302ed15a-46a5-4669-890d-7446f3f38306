import { Component, OnInit } from "@angular/core"
import { ActivatedRoute, Router } from "@angular/router"
import { CourseService } from "../../../core/services/course.service"
import { MatSnackBar } from "@angular/material/snack-bar"
import { Course } from "../../../core/models/course.model"
import { AuthService } from "../../../core/services/auth.service"
import { User } from "../../../core/models/user.model"

@Component({
  selector: "app-course-detail",
  template: `
    <div class="course-detail-container">
      <div class="content-wrapper">
        <!-- Main Content -->
        <div class="main-content">
          <div class="course-header">
            <h1>{{ course.titre }}</h1>
            <p class="description">{{ course.description }}</p>

            <div class="course-meta">
              <mat-chip-listbox>
                <mat-chip [class]="getLevelColor(course.niveau || 'Débutant')">{{ course.niveau }}</mat-chip>
              </mat-chip-listbox>
              <div class="meta-item">
                <mat-icon class="star-icon">star</mat-icon>
                <span>{{ course.note }} ({{ course.nombreEtudiants }} étudiants)</span>
              </div>
              <div class="meta-item">
                <mat-icon>schedule</mat-icon>
                <span>{{ course.duree }} minutes</span>
              </div>
            </div>

            <div class="progress-section" *ngIf="course.estAchete">
              <div class="progress-label">
                <span>Progression du cours</span>
                <span>{{ course.progression }}%</span>
              </div>
              <mat-progress-bar mode="determinate" [value]="course.progression"></mat-progress-bar>
            </div>
          </div>

          <mat-tab-group animationDuration="0ms" [selectedIndex]="selectedTabIndex" (selectedIndexChange)="selectedTabIndex = $event">
            <mat-tab label="Aperçu">
              <mat-card class="tab-card">
                <mat-card-title>Description du cours</mat-card-title>
                <mat-card-content>
                  <p class="description-full">{{ course.description }}</p>

                  <div class="learning-outcomes">
                    <h3>Ce que vous apprendrez :</h3>
                    <ul>
                      <li><mat-icon>check_circle</mat-icon> Les concepts fondamentaux de React</li>
                      <li><mat-icon>check_circle</mat-icon> Création et utilisation de composants</li>
                      <li><mat-icon>check_circle</mat-icon> Gestion de l'état avec les hooks</li>
                      <li><mat-icon>check_circle</mat-icon> Bonnes pratiques de développement</li>
                    </ul>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-tab>

            <mat-tab label="Contenu">
              <mat-card class="tab-card">
                <mat-card-title>Contenu du cours</mat-card-title>
                <mat-card-content>
                  <div class="content-list">
                    <div *ngFor="let content of course.contenus; let i = index" class="content-item">
                      <div class="content-icon-wrapper" 
                           [ngClass]="{
                             'completed': content.estComplete, 
                             'unlocked': content.estDebloque && !content.estComplete, 
                             'locked': !content.estDebloque
                           }">
                        <mat-icon *ngIf="content.estComplete">check_circle</mat-icon>
                        <mat-icon *ngIf="!content.estComplete && content.estDebloque">{{ getContentIcon(content.typeContenu) }}</mat-icon>
                        <mat-icon *ngIf="!content.estDebloque">lock</mat-icon>
                      </div>
                      <div class="content-details">
                        <h4>{{ content.titre }}</h4>
                        <p class="content-description">{{ content.description }}</p>
                        <span *ngIf="content.duree" class="content-duration">{{ content.duree }} minutes</span>
                      </div>
                      <button mat-stroked-button color="primary" 
                              *ngIf="course.estAchete && content.estDebloque" 
                              (click)="handleStartContent(content.id, content.typeContenu)">
                        {{ content.estComplete ? 'Revoir' : 'Commencer' }}
                      </button>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-tab>

            <mat-tab label="Formateur">
              <mat-card class="tab-card">
                <mat-card-title>À propos du formateur</mat-card-title>
                <mat-card-content>
                  <div class="instructor-info">
                    <div class="instructor-avatar">
                      <span>{{ course.formateur?.prenom?.[0] }}{{ course.formateur?.nom?.[0] }}</span>
                    </div>
                    <div>
                      <h3>{{ course.formateur?.prenom }} {{ course.formateur?.nom }}</h3>
                      <p class="instructor-bio">Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs.</p>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-tab>
          </mat-tab-group>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
          <mat-card class="sticky-card">
            <mat-card-content>
              <div class="price-section" *ngIf="!course.estGratuit">
                <span class="price-value">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>
              </div>
              <div class="price-section" *ngIf="course.estGratuit">
                <mat-chip-listbox>
                  <mat-chip class="free-chip-large">Cours Gratuit</mat-chip>
                </mat-chip-listbox>
              </div>

              <div class="action-buttons">
                <button mat-raised-button color="accent" class="full-width-btn" 
                        *ngIf="!course.estAchete && course.estGratuit" 
                        (click)="enrollInCourse(course.id)">
                  Commencer le cours
                </button>
                <button mat-raised-button color="primary" class="full-width-btn" 
                        *ngIf="!course.estAchete && !course.estGratuit" 
                        [routerLink]="['/payment', course.id]">
                  <mat-icon>euro_symbol</mat-icon>
                  Acheter le cours
                </button>
                <button mat-raised-button color="primary" class="full-width-btn" 
                        *ngIf="course.estAchete" 
                        (click)="selectedTabIndex = 1">
                  Continuer le cours
                </button>
                <mat-chip-listbox *ngIf="course.estAchete" class="purchased-chip">
                  <mat-chip class="purchased-chip-item">Cours acheté</mat-chip>
                </mat-chip-listbox>
              </div>

              <div class="course-summary">
                <div class="summary-item">
                  <span>Durée totale:</span>
                  <span>{{ course.duree }} minutes</span>
                </div>
                <div class="summary-item">
                  <span>Niveau:</span>
                  <span>{{ course.niveau }}</span>
                </div>
                <div class="summary-item">
                  <span>Étudiants:</span>
                  <span>{{ course.nombreEtudiants }}</span>
                </div>
                <div class="summary-item">
                  <span>Note:</span>
                  <div class="rating-display">
                    <mat-icon class="star-icon">star</mat-icon>
                    <span>{{ course.note }}</span>
                  </div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
    .course-detail-container {
      padding: 2rem;
      background-color: #f5f5f5;
      min-height: 100vh;
    }

    .content-wrapper {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .main-content {
      grid-column: span 2; /* Default to full width on small screens */
    }

    @media (min-width: 960px) {
      .main-content {
        grid-column: span 2 / span 2;
      }
      .sidebar {
        grid-column: span 1 / span 1;
      }
    }

    .course-header {
      margin-bottom: 2rem;
    }

    .course-header h1 {
      font-size: 2.5rem;
      font-weight: bold;
      color: #333;
      margin-bottom: 0.5rem;
    }

    .course-header .description {
      font-size: 1.1rem;
      color: #666;
      margin-bottom: 1.5rem;
    }

    .course-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .course-meta .mat-chip {
      font-size: 0.9rem;
      padding: 0.4rem 0.8rem;
      height: auto;
    }

    .course-meta .mat-chip.bg-green-100 { background-color: #d4edda; color: #155724; }
    .course-meta .mat-chip.bg-yellow-100 { background-color: #fff3cd; color: #856404; }
    .course-meta .mat-chip.bg-red-100 { background-color: #f8d7da; color: #721c24; }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      font-size: 0.95rem;
      color: #555;
    }

    .meta-item mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      color: #888;
    }

    .meta-item .star-icon {
      color: #ffc107; /* Yellow */
    }

    .progress-section {
      margin-top: 1.5rem;
    }

    .progress-label {
      display: flex;
      justify-content: space-between;
      font-size: 0.9rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: #444;
    }

    mat-progress-bar {
      height: 8px;
      border-radius: 4px;
    }

    .tab-card {
      margin-top: 1.5rem;
      padding: 1.5rem;
    }

    .tab-card mat-card-title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .description-full {
      font-size: 1rem;
      line-height: 1.6;
      color: #444;
    }

    .learning-outcomes {
      margin-top: 2rem;
    }

    .learning-outcomes h3 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #333;
    }

    .learning-outcomes ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .learning-outcomes li {
      display: flex;
      align-items: center;
      margin-bottom: 0.8rem;
      font-size: 0.95rem;
      color: #555;
    }

    .learning-outcomes li mat-icon {
      color: #4caf50; /* Green */
      margin-right: 0.8rem;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    .content-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .content-item {
      display: flex;
      align-items: center;
      padding: 1rem;
      border: 1px solid #eee;
      border-radius: 8px;
      background-color: #fff;
      transition: box-shadow 0.2s ease-in-out;
    }

    .content-item:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .content-icon-wrapper {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
      flex-shrink: 0;
    }

    .content-icon-wrapper mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    .content-icon-wrapper.completed {
      background-color: #e8f5e9; /* Light green */
    }
    .content-icon-wrapper.completed mat-icon {
      color: #4caf50; /* Green */
    }

    .content-icon-wrapper.unlocked {
      background-color: #e3f2fd; /* Light blue */
    }
    .content-icon-wrapper.unlocked mat-icon {
      color: #2196f3; /* Blue */
    }

    .content-icon-wrapper.locked {
      background-color: #f5f5f5; /* Light gray */
    }
    .content-icon-wrapper.locked mat-icon {
      color: #9e9e9e; /* Gray */
    }

    .content-details {
      flex-grow: 1;
    }

    .content-details h4 {
      font-size: 1.1rem;
      font-weight: 500;
      margin-bottom: 0.2rem;
      color: #333;
    }

    .content-description {
      font-size: 0.85rem;
      color: #777;
      margin-bottom: 0.4rem;
    }

    .content-duration {
      font-size: 0.8rem;
      color: #888;
    }

    .content-item button {
      margin-left: 1rem;
      flex-shrink: 0;
    }

    .instructor-info {
      display: flex;
      align-items: flex-start;
      gap: 1.5rem;
      padding: 1rem 0;
    }

    .instructor-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #e1bee7; /* Light purple */
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.8rem;
      font-weight: bold;
      color: #8e24aa; /* Dark purple */
      flex-shrink: 0;
    }

    .instructor-info h3 {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .instructor-bio {
      font-size: 0.95rem;
      line-height: 1.5;
      color: #555;
    }

    .sidebar {
      grid-column: span 1;
    }

    .sticky-card {
      position: sticky;
      top: 2rem; /* Adjust as needed */
      padding: 1.5rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .price-section {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .price-value {
      font-size: 2.5rem;
      font-weight: bold;
      color: #673ab7; /* Purple */
    }

    .free-chip-large {
      background-color: #e6ffed;
      color: #28a745;
      font-size: 1.2rem;
      padding: 0.8rem 1.5rem;
      height: auto;
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.8rem;
      margin-bottom: 1.5rem;
    }

    .full-width-btn {
      width: 100%;
      font-size: 1rem;
      padding: 0.8rem 1rem;
    }

    .full-width-btn mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      margin-right: 0.5rem;
    }

    .purchased-chip {
      text-align: center;
      margin-top: 1rem;
    }

    .purchased-chip-item {
      background-color: #e8f5e9;
      color: #4caf50;
      font-size: 0.9rem;
      padding: 0.4rem 0.8rem;
      height: auto;
    }

    .course-summary {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid #eee;
      display: flex;
      flex-direction: column;
      gap: 0.8rem;
      font-size: 0.95rem;
      color: #555;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .rating-display {
      display: flex;
      align-items: center;
      gap: 0.3rem;
    }

    .rating-display .star-icon {
      color: #ffc107; /* Yellow */
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  `,
  ],
})
export class CourseDetailComponent implements OnInit {
  courseId!: number
  course!: Course
  currentUser!: User | null
  selectedTabIndex = 0

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private courseService: CourseService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      this.courseId = Number(params.get("id"))
      this.loadCourseDetails()
    })

    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user
    })
  }

  loadCourseDetails(): void {
    // Mock data for demonstration
    this.course = {
      id: this.courseId,
      titre: "React Fundamentals",
      description:
        "Apprenez les bases de React avec des exemples pratiques et des projets concrets. Ce cours couvre tous les concepts essentiels pour débuter avec React : composants, props, state, hooks, et bien plus encore.",
      prix: 99.99,
      duree: 120,
      niveau: "Débutant",
      formateurId: 1,
      formateur: {
        id: 1,
        nom: "Dupont",
        prenom: "Jean",
        email: "<EMAIL>",
        role: "Formateur",
        bio: "Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs.",
      },
      contenus: [
        {
          id: 1,
          titre: "Introduction à React",
          description: "Découvrez React et ses concepts de base",
          typeContenu: "Video",
          duree: 30,
          estComplete: true,
          estDebloque: true,
          ordre: 1,
          coursId: this.courseId,
        },
        {
          id: 2,
          titre: "Components et Props",
          description: "Apprenez à créer et utiliser des composants",
          typeContenu: "Video",
          duree: 45,
          estComplete: true,
          estDebloque: true,
          ordre: 2,
          coursId: this.courseId,
        },
        {
          id: 3,
          titre: "Quiz - Bases de React",
          description: "Testez vos connaissances sur les bases",
          typeContenu: "Quiz",
          estComplete: false,
          estDebloque: true,
          ordre: 3,
          coursId: this.courseId,
        },
        {
          id: 4,
          titre: "State et Hooks",
          description: "Gérez l'état de vos composants",
          typeContenu: "Video",
          duree: 50,
          estComplete: false,
          estDebloque: false,
          ordre: 4,
          coursId: this.courseId,
        },
        {
          id: 5,
          titre: "Résumé du chapitre",
          description: "Points clés à retenir",
          typeContenu: "Resume",
          estComplete: false,
          estDebloque: false,
          ordre: 5,
          coursId: this.courseId,
        },
      ],
      nombreEtudiants: 245,
      note: 4.8,
      estGratuit: false,
      estAchete: true, // Simulate if purchased
      progression: 40, // Simulate progress
    }

    // Uncomment to fetch from API
    /*
    this.courseService.getCours(this.courseId).subscribe({
      next: (data) => {
        this.course = data;
        // TODO: Fetch user enrollment status and progress
        this.course.estAchete = true; // Example
        this.course.progression = 40; // Example
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.router.navigate(['/courses']);
      }
    });
    */
  }

  getLevelColor(niveau: string): string {
    switch (niveau) {
      case "Débutant":
        return "bg-green-100"
      case "Intermédiaire":
        return "bg-yellow-100"
      case "Avancé":
        return "bg-red-100"
      default:
        return "bg-gray-100"
    }
  }

  getContentIcon(type: string): string {
    switch (type) {
      case "Video":
        return "play_circle"
      case "Quiz":
        return "quiz"
      case "Resume":
        return "description"
      default:
        return "book"
    }
  }

  handleStartContent(contentId: number, typeContenu: string): void {
    if (!this.course.estAchete) {
      this.snackBar.open("Veuillez acheter le cours pour accéder au contenu.", "Fermer", { duration: 3000 })
      return
    }

    const content = this.course.contenus?.find((c) => c.id === contentId)
    if (!content?.estDebloque) {
      this.snackBar.open("Ce contenu n'est pas encore débloqué.", "Fermer", { duration: 3000 })
      return
    }

    switch (typeContenu) {
      case "Quiz":
        this.router.navigate(["/quiz", contentId])
        break
      case "Video":
        this.router.navigate(["/video", contentId]) // Assuming a video player component
        break
      case "Resume":
        this.router.navigate(["/resume", contentId]) // Assuming a resume viewer component
        break
      default:
        this.snackBar.open("Type de contenu non pris en charge.", "Fermer", { duration: 3000 })
    }
  }

  enrollInCourse(courseId: number): void {
    if (!this.currentUser) {
      this.snackBar.open("Veuillez vous connecter pour vous inscrire.", "Fermer", { duration: 3000 })
      this.router.navigate(["/auth/login"])
      return
    }

    // Call the service to enroll in a free course
    // this.courseService.enrollInCourse(courseId).subscribe({
    //   next: (res) => {
    //     this.snackBar.open('Inscription réussie au cours gratuit !', 'Fermer', { duration: 3000 });
    //     this.course.estAchete = true; // Update UI
    //     this.course.progression = 0;
    //   },
    //   error: (err) => {
    //     this.snackBar.open('Erreur lors de l\'inscription.', 'Fermer', { duration: 3000 });
    //     console.error(err);
    //   }
    // });
    this.snackBar.open("Inscription réussie au cours gratuit (simulé) !", "Fermer", { duration: 3000 })
    this.course.estAchete = true // Simulate UI update
    this.course.progression = 0
  }
}
