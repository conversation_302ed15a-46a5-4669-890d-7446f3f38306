import { NgModule } from "@angular/core"
import { CommonModule } from "@angular/common"
import { RouterModule } from "@angular/router"
import { MatCardModule } from "@angular/material/card"
import { MatButtonModule } from "@angular/material/button"
import { MatIconModule } from "@angular/material/icon"
import { MatToolbarModule } from "@angular/material/toolbar"
import { MatChipsModule } from "@angular/material/chips"
import { MatGridListModule } from "@angular/material/grid-list"

import { DashboardComponent } from "./dashboard.component"

@NgModule({
  declarations: [DashboardComponent],
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatChipsModule,
    MatGridListModule,
    RouterModule.forChild([{ path: "", component: DashboardComponent }]),
  ],
})
export class DashboardModule {}
