import { Component, OnInit } from "@angular/core"
import { CertificateService } from "../../core/services/certificate.service"
import { AuthService } from "../../core/services/auth.service"
import { Certificat } from "../../core/models/certificate.model"
import { User } from "../../core/models/user.model"
import { MatSnackBar } from "@angular/material/snack-bar"

@Component({
  selector: "app-certificates",
  template: `
    <div class="certificates-container">
      <div class="header-section">
        <h1>{{ getPageTitle() }}</h1>
        <p>{{ getPageSubtitle() }}</p>
      </div>

      <div *ngIf="certificates.length === 0; else certificatesList" class="no-certificates-card">
        <mat-card>
          <mat-card-content class="no-certificates-content">
            <mat-icon>emoji_events</mat-icon>
            <h3>Aucun certificat disponible</h3>
            <p>{{ getNoCertificatesMessage() }}</p>
          </mat-card-content>
        </mat-card>
      </div>

      <ng-template #certificatesList>
        <div class="certificates-grid">
          <mat-card *ngFor="let cert of certificates" class="certificate-card">
            <mat-card-header class="certificate-header">
              <div class="icon-wrapper">
                <mat-icon>emoji_events</mat-icon>
              </div>
              <div>
                <mat-card-title>{{ cert.cours.titre }}</mat-card-title>
                <mat-card-subtitle>
                  Par {{ cert.cours.formateur.prenom }} {{ cert.cours.formateur.nom }}
                </mat-card-subtitle>
              </div>
            </mat-card-header>

            <mat-card-content class="certificate-details">
              <div class="detail-item">
                <mat-icon>person</mat-icon>
                <span>Étudiant: <strong>{{ cert.client.prenom }} {{ cert.client.nom }}</strong></span>
              </div>
              <div class="detail-item">
                <mat-icon>calendar_today</mat-icon>
                <span>Date d'obtention: {{ formatDate(cert.dateGeneration) }}</span>
              </div>
              <div class="detail-item">
                <mat-icon>book</mat-icon>
                <span>Score: 
                  <strong [ngClass]="getScoreColorClass(cert.scoreQuiz, cert.seuilReussite)">
                    {{ cert.scoreQuiz }}%
                  </strong>
                  (seuil: {{ cert.seuilReussite }}%)
                </span>
              </div>
              <div class="detail-item">
                <span>N° de série: 
                  <mat-chip-listbox>
                    <mat-chip class="serial-number-chip">{{ cert.numeroSerie }}</mat-chip>
                  </mat-chip-listbox>
                </span>
              </div>

              <div class="card-actions">
                <button mat-stroked-button color="primary" (click)="previewCertificate(cert.id!)">
                  <mat-icon>visibility</mat-icon> Aperçu
                </button>
                <button mat-raised-button color="accent" (click)="downloadCertificate(cert)">
                  <mat-icon>download</mat-icon> PDF
                </button>
              </div>

              <div class="validation-badge">
                <mat-chip-listbox>
                  <mat-chip class="validated-chip">
                    <mat-icon>check_circle</mat-icon> Certificat Validé
                  </mat-chip>
                </mat-chip-listbox>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <!-- Statistics for Formateurs and Admins -->
        <mat-card *ngIf="currentUser?.role === 'Formateur' || currentUser?.role === 'Admin'" class="stats-card">
          <mat-card-header>
            <mat-card-title>Statistiques des Certificats</mat-card-title>
          </mat-card-header>
          <mat-card-content class="stats-grid">
            <div class="stat-item">
              <div class="stat-value blue">{{ certificates.length }}</div>
              <div class="stat-label">Total émis</div>
            </div>
            <div class="stat-item">
              <div class="stat-value green">{{ averageScore() }}%</div>
              <div class="stat-label">Score moyen</div>
            </div>
            <div class="stat-item">
              <div class="stat-value yellow">{{ excellentResultsCount() }}</div>
              <div class="stat-label">Excellents résultats</div>
            </div>
            <div class="stat-item">
              <div class="stat-value purple">{{ uniqueStudentsCount() }}</div>
              <div class="stat-label">Étudiants certifiés</div>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-template>
    </div>
  `,
  styles: [
    `
    .certificates-container {
      padding: 2rem;
      background-color: #f5f5f5;
      min-height: 100vh;
    }

    .header-section {
      margin-bottom: 2rem;
      text-align: center;
    }

    .header-section h1 {
      font-size: 2.5rem;
      font-weight: bold;
      color: #333;
      margin-bottom: 0.5rem;
    }

    .header-section p {
      font-size: 1.1rem;
      color: #666;
    }

    .no-certificates-card {
      max-width: 600px;
      margin: 0 auto;
      text-align: center;
      padding: 2rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .no-certificates-content mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #aaa;
    }

    .no-certificates-content h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .no-certificates-content p {
      color: #777;
    }

    .certificates-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .certificate-card {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .certificate-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .certificate-header {
      display: flex;
      align-items: center;
      padding-bottom: 0.5rem;
    }

    .icon-wrapper {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background-color: #fff3e0; /* Light orange */
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
      flex-shrink: 0;
    }

    .icon-wrapper mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: #ffb300; /* Orange */
    }

    .certificate-header mat-card-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.2rem;
    }

    .certificate-header mat-card-subtitle {
      font-size: 0.9rem;
      color: #666;
    }

    .certificate-details {
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.8rem;
      font-size: 0.95rem;
      color: #555;
    }

    .detail-item mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      margin-right: 0.8rem;
      color: #999;
    }

    .serial-number-chip {
      font-family: 'monospace';
      font-size: 0.8rem;
      padding: 0.2rem 0.6rem;
      height: auto;
      background-color: #f0f0f0;
      color: #555;
    }

    .score-green { color: #4caf50; }
    .score-blue { color: #2196f3; }
    .score-yellow { color: #ffc107; }
    .score-red { color: #f44336; }

    .card-actions {
      display: flex;
      gap: 0.8rem;
      margin-top: 1.5rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .card-actions button {
      flex: 1;
      font-size: 0.9rem;
      padding: 0.6rem 1rem;
    }

    .card-actions button mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      margin-right: 0.3rem;
    }

    .validation-badge {
      text-align: center;
      margin-top: 1.5rem;
    }

    .validated-chip {
      background-color: #e8f5e9; /* Light green */
      color: #388e3c; /* Dark green */
      font-size: 0.9rem;
      padding: 0.4rem 0.8rem;
      height: auto;
    }

    .validated-chip mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      margin-right: 0.3rem;
    }

    .stats-card {
      margin-top: 2rem;
      padding: 1.5rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .stats-card mat-card-title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1.5rem;
      text-align: center;
    }

    .stat-item {
      padding: 1rem;
      background-color: #f9f9f9;
      border-radius: 8px;
      border: 1px solid #eee;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0.3rem;
    }

    .stat-value.blue { color: #2196f3; }
    .stat-value.green { color: #4caf50; }
    .stat-value.yellow { color: #ffc107; }
    .stat-value.purple { color: #9c27b0; }

    .stat-label {
      font-size: 0.9rem;
      color: #666;
    }

    @media (max-width: 768px) {
      .certificates-grid {
        grid-template-columns: 1fr;
      }
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `,
  ],
})
export class CertificatesComponent implements OnInit {
  certificates: Certificat[] = []
  currentUser: User | null = null

  constructor(
    private certificateService: CertificateService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user
      if (this.currentUser) {
        this.loadCertificates()
      }
    })
  }

  loadCertificates(): void {
    // Mock data for demonstration
    this.certificates = [
      {
        id: 1,
        coursId: 1,
        cours: {
          id: 1,
          titre: "React Fundamentals",
          formateur: { id: 1, nom: "Dupont", prenom: "Jean" },
        },
        clientId: 1,
        client: { id: 1, nom: "Martin", prenom: "Pierre" },
        adminId: 1,
        admin: { id: 1, nom: "Admin", prenom: "System" },
        dateGeneration: new Date("2024-01-15T14:30:00Z"),
        scoreQuiz: 85,
        seuilReussite: 70,
        numeroSerie: "CERT-2024-001",
      },
      {
        id: 2,
        coursId: 2,
        cours: {
          id: 2,
          titre: "JavaScript Avancé",
          formateur: { id: 2, nom: "Bernard", prenom: "Sophie" },
        },
        clientId: 1,
        client: { id: 1, nom: "Martin", prenom: "Pierre" },
        adminId: 1,
        admin: { id: 1, nom: "Admin", prenom: "System" },
        dateGeneration: new Date("2024-01-10T16:45:00Z"),
        scoreQuiz: 92,
        seuilReussite: 75,
        numeroSerie: "CERT-2024-002",
      },
    ]

    // Uncomment to fetch from API if you have an endpoint for listing certificates
    /*
    // Example: this.certificateService.getCertificatesForUser(this.currentUser.id).subscribe(...)
    */
  }

  getPageTitle(): string {
    switch (this.currentUser?.role) {
      case "Client":
        return "Mes Certificats"
      case "Formateur":
        return "Certificats Émis"
      case "Admin":
        return "Gestion des Certificats"
      default:
        return "Certificats"
    }
  }

  getPageSubtitle(): string {
    switch (this.currentUser?.role) {
      case "Client":
        return "Vos certificats de réussite aux cours"
      case "Formateur":
        return "Certificats émis pour vos cours"
      case "Admin":
        return "Tous les certificats générés sur la plateforme"
      default:
        return "Consultez les certificats de la plateforme"
    }
  }

  getNoCertificatesMessage(): string {
    switch (this.currentUser?.role) {
      case "Client":
        return "Terminez un cours avec succès pour obtenir votre premier certificat."
      case "Formateur":
        return "Aucun certificat n'a encore été généré pour vos cours."
      case "Admin":
        return "Aucun certificat n'a encore été généré sur la plateforme."
      default:
        return "Aucun certificat n'est disponible pour le moment."
    }
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "2-digit",
    })
  }

  getScoreColorClass(score: number, seuil: number): string {
    if (score >= seuil + 20) return "score-green"
    if (score >= seuil + 10) return "score-blue"
    if (score >= seuil) return "score-yellow"
    return "score-red"
  }

  downloadCertificate(cert: Certificat): void {
    if (!cert.cours || !cert.client) {
      this.snackBar.open("Informations du certificat incomplètes.", "Fermer", { duration: 3000 })
      return
    }

    this.certificateService
      .telechargerCertificat(cert.client.nom, cert.client.prenom, cert.cours.titre, cert.scoreQuiz)
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.href = url
          a.download = `certificat-${cert.numeroSerie}.pdf`
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(url)
          this.snackBar.open("Certificat téléchargé !", "Fermer", { duration: 3000 })
        },
        error: (err) => {
          this.snackBar.open("Erreur lors du téléchargement du certificat.", "Fermer", { duration: 3000 })
          console.error(err)
        },
      })
  }

  previewCertificate(certificateId: number): void {
    this.snackBar.open("Fonctionnalité d'aperçu non implémentée pour le moment.", "Fermer", { duration: 3000 })
    // You would typically open a new tab/window with a route that renders the PDF
    // Example: window.open(`/certificates/${certificateId}/preview`, '_blank');
  }

  averageScore(): number {
    if (this.certificates.length === 0) return 0
    const totalScore = this.certificates.reduce((sum, cert) => sum + cert.scoreQuiz, 0)
    return Math.round(totalScore / this.certificates.length)
  }

  excellentResultsCount(): number {
    return this.certificates.filter((cert) => cert.scoreQuiz >= cert.seuilReussite + 20).length
  }

  uniqueStudentsCount(): number {
    const uniqueClientIds = new Set(this.certificates.map((cert) => cert.clientId))
    return uniqueClientIds.size
  }
}
