import { Component, OnInit } from "@angular/core"
import { Router } from "@angular/router"
import { AuthService } from "../../core/services/auth.service"
import { User } from "../../core/models/user.model"

interface DashboardStats {
  totalCourses: number
  totalStudents: number
  totalRevenue: number
  completedCertificates: number
  pendingMessages: number
}

@Component({
  selector: "app-dashboard",
  template: `
    <div class="dashboard-container">
      <!-- Header -->
      <mat-toolbar color="primary" class="dashboard-header">
        <div class="header-content">
          <div class="logo">
            <mat-icon>school</mat-icon>
            <span>Training Platform</span>
          </div>
          <div class="user-info">
            <mat-chip-set>
              <mat-chip class="role-chip">{{ currentUser?.role }}</mat-chip>
            </mat-chip-set>
            <button mat-button (click)="logout()">
              <mat-icon>logout</mat-icon>
              Déconnexion
            </button>
          </div>
        </div>
      </mat-toolbar>

      <!-- Main Content -->
      <div class="dashboard-content">
        <div class="welcome-section">
          <h1>{{ getRoleTitle() }}</h1>
          <p>Bienvenue ! Voici un aperçu de votre activité.</p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
          <mat-card *ngFor="let stat of getStatsForRole()" class="stat-card">
            <mat-card-content>
              <div class="stat-content">
                <div class="stat-info">
                  <p class="stat-label">{{ stat.title }}</p>
                  <p class="stat-value">{{ stat.value }}</p>
                </div>
                <mat-icon [class]="stat.color" class="stat-icon">{{ stat.icon }}</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <!-- Quick Actions & Recent Activity -->
        <div class="dashboard-grid">
          <mat-card class="actions-card">
            <mat-card-header>
              <mat-card-title>Actions rapides</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="actions-list">
                <ng-container *ngIf="currentUser?.role === 'Formateur'">
                  <button mat-stroked-button routerLink="/courses/create" class="action-btn">
                    <mat-icon>add</mat-icon>
                    Créer un nouveau cours
                  </button>
                  <button mat-stroked-button routerLink="/dashboard/students" class="action-btn">
                    <mat-icon>people</mat-icon>
                    Voir les étudiants
                  </button>
                </ng-container>
                
                <ng-container *ngIf="currentUser?.role === 'Client'">
                  <button mat-stroked-button routerLink="/courses" class="action-btn">
                    <mat-icon>book</mat-icon>
                    Parcourir les cours
                  </button>
                  <button mat-stroked-button routerLink="/certificates" class="action-btn">
                    <mat-icon>emoji_events</mat-icon>
                    Voir mes certificats
                  </button>
                </ng-container>
                
                <ng-container *ngIf="currentUser?.role === 'Admin'">
                  <button mat-stroked-button routerLink="/admin/users" class="action-btn">
                    <mat-icon>admin_panel_settings</mat-icon>
                    Gérer les utilisateurs
                  </button>
                  <button mat-stroked-button routerLink="/admin/analytics" class="action-btn">
                    <mat-icon>analytics</mat-icon>
                    Voir les analyses
                  </button>
                </ng-container>
                
                <button mat-stroked-button routerLink="/messages" class="action-btn">
                  <mat-icon>message</mat-icon>
                  Messages
                </button>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="activity-card">
            <mat-card-header>
              <mat-card-title>Activité récente</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="activity-list">
                <div class="activity-item">
                  <div class="activity-dot green"></div>
                  <p>Nouvel étudiant inscrit à "React Fundamentals"</p>
                </div>
                <div class="activity-item">
                  <div class="activity-dot blue"></div>
                  <p>Certificat généré pour John Doe</p>
                </div>
                <div class="activity-item">
                  <div class="activity-dot yellow"></div>
                  <p>Paiement reçu : 299€</p>
                </div>
                <div class="activity-item">
                  <div class="activity-dot purple"></div>
                  <p>Nouveau message d'un étudiant</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
    .dashboard-container {
      min-height: 100vh;
      background-color: #f5f5f5;
    }

    .dashboard-header {
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-content {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1.5rem;
      font-weight: bold;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .role-chip {
      text-transform: capitalize;
    }

    .dashboard-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }

    .welcome-section {
      margin-bottom: 2rem;
    }

    .welcome-section h1 {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .welcome-section p {
      color: #666;
      font-size: 1.1rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      padding: 1rem;
    }

    .stat-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 0.5rem;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: bold;
      color: #333;
      margin: 0;
    }

    .stat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .stat-icon.blue { color: #2196f3; }
    .stat-icon.purple { color: #9c27b0; }
    .stat-icon.green { color: #4caf50; }
    .stat-icon.yellow { color: #ff9800; }

    .dashboard-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
    }

    .actions-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .action-btn {
      justify-content: flex-start;
      padding: 1rem;
      text-align: left;
    }

    .action-btn mat-icon {
      margin-right: 1rem;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .activity-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .activity-dot.green { background-color: #4caf50; }
    .activity-dot.blue { background-color: #2196f3; }
    .activity-dot.yellow { background-color: #ff9800; }
    .activity-dot.purple { background-color: #9c27b0; }

    .activity-item p {
      margin: 0;
      font-size: 0.9rem;
      color: #666;
    }

    @media (max-width: 768px) {
      .dashboard-grid {
        grid-template-columns: 1fr;
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `,
  ],
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null
  stats: DashboardStats = {
    totalCourses: 12,
    totalStudents: 245,
    totalRevenue: 15420,
    completedCertificates: 89,
    pendingMessages: 5,
  }

  constructor(
    private authService: AuthService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user
      if (!user) {
        // Redirect to login if no user is logged in
        this.router.navigate(["/auth/login"])
      }
    })

    // Mock user data if not logged in (for development)
    if (!this.currentUser) {
      const mockUser: User = {
        id: 1,
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        nom: "Doe",
        prenom: "John",
        role: "Formateur", // Change this to 'Client' or 'Admin' to test different dashboards
      }
      // Simulate setting user and role in local storage for initial load
      localStorage.setItem("token", "mock-jwt-token")
      localStorage.setItem("userRole", mockUser.role)
      this.authService["currentUserSubject"].next(mockUser) // Directly update subject for mock
      this.currentUser = mockUser
    }
  }

  logout(): void {
    this.authService.logout()
  }

  getRoleTitle(): string {
    switch (this.currentUser?.role) {
      case "Client":
        return "Tableau de bord Étudiant"
      case "Formateur":
        return "Tableau de bord Formateur"
      case "Admin":
        return "Tableau de bord Administrateur"
      default:
        return "Tableau de bord"
    }
  }

  getStatsForRole() {
    switch (this.currentUser?.role) {
      case "Client":
        return [
          { title: "Cours inscrits", value: this.stats.totalCourses, icon: "book", color: "blue" },
          {
            title: "Certificats obtenus",
            value: this.stats.completedCertificates,
            icon: "emoji_events",
            color: "yellow",
          },
          { title: "Messages", value: this.stats.pendingMessages, icon: "message", color: "green" },
        ]
      case "Formateur":
        return [
          { title: "Cours publiés", value: this.stats.totalCourses, icon: "book", color: "blue" },
          { title: "Total étudiants", value: this.stats.totalStudents, icon: "people", color: "purple" },
          { title: "Revenus (€)", value: this.stats.totalRevenue, icon: "euro_symbol", color: "green" },
          { title: "Certificats émis", value: this.stats.completedCertificates, icon: "emoji_events", color: "yellow" },
        ]
      case "Admin":
        return [
          { title: "Total cours", value: this.stats.totalCourses, icon: "book", color: "blue" },
          { title: "Total utilisateurs", value: this.stats.totalStudents, icon: "people", color: "purple" },
          { title: "Revenus plateforme (€)", value: this.stats.totalRevenue, icon: "trending_up", color: "green" },
          {
            title: "Certificats générés",
            value: this.stats.completedCertificates,
            icon: "emoji_events",
            color: "yellow",
          },
        ]
      default:
        return []
    }
  }
}
