{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, NgModule, Injector, TemplateRef, Injectable, Optional, SkipSelf } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport * as i3$2 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayModule, OverlayConfig } from '@angular/cdk/overlay';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    this._afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    this._onAction = new Subject();\n    /** Whether the snack bar was dismissed using the action button. */\n    this._dismissedByAction = false;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  constructor() {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    this.politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    this.announcementMessage = '';\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    this.duration = 0;\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** The horizontal position to place the snack bar. */\n    this.horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    this.verticalPosition = 'bottom';\n  }\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n  static {\n    this.ɵfac = function MatSnackBarLabel_Factory(t) {\n      return new (t || MatSnackBarLabel)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarLabel,\n      selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n  static {\n    this.ɵfac = function MatSnackBarActions_Factory(t) {\n      return new (t || MatSnackBarActions)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarActions,\n      selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n  static {\n    this.ɵfac = function MatSnackBarAction_Factory(t) {\n      return new (t || MatSnackBarAction)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarAction,\n      selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\nclass SimpleSnackBar {\n  constructor(snackBarRef, data) {\n    this.snackBarRef = snackBarRef;\n    this.data = data;\n  }\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n  static {\n    this.ɵfac = function SimpleSnackBar_Factory(t) {\n      return new (t || SimpleSnackBar)(i0.ɵɵdirectiveInject(MatSnackBarRef), i0.ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SimpleSnackBar,\n      selectors: [[\"simple-snack-bar\"]],\n      hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n      exportAs: [\"matSnackBar\"],\n      decls: 3,\n      vars: 2,\n      consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\", 4, \"ngIf\"], [\"matSnackBarActions\", \"\"], [\"mat-button\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n      template: function SimpleSnackBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, SimpleSnackBar_div_2_Template, 3, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasAction);\n        }\n      },\n      dependencies: [i2.NgIf, i3.MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n<div matSnackBarActions *ngIf=\\\"hasAction\\\">\\n  <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n    {{data.action}}\\n  </button>\\n</div>\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatSnackBarRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SNACK_BAR_DATA]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: trigger('state', [state('void, hidden', style({\n    transform: 'scale(0.8)',\n    opacity: 0\n  })), state('visible', style({\n    transform: 'scale(1)',\n    opacity: 1\n  })), transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => void, * => hidden', animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', style({\n    opacity: 0\n  })))])\n};\nlet uniqueId = 0;\n/**\n * Base class for snack bar containers.\n * @docs-private\n */\nclass _MatSnackBarContainerBase extends BasePortalOutlet {\n  constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, /** The snack bar configuration. */\n  snackBarConfig) {\n    super();\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._platform = _platform;\n    this.snackBarConfig = snackBarConfig;\n    this._document = inject(DOCUMENT);\n    this._trackedModals = new Set();\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    this._announceDelay = 150;\n    /** Whether the component has been destroyed. */\n    this._destroyed = false;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    this._onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    this._onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    this._onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    this._animationState = 'void';\n    /** Unique ID of the aria-live element. */\n    this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    };\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n      this._live = 'assertive';\n    } else if (snackBarConfig.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(event) {\n    const {\n      fromState,\n      toState\n    } = event;\n    if (toState === 'void' && fromState !== 'void' || toState === 'hidden') {\n      this._completeExit();\n    }\n    if (toState === 'visible') {\n      // Note: we shouldn't use `this` inside the zone callback,\n      // because it can cause a memory leak.\n      const onEnter = this._onEnter;\n      this._ngZone.run(() => {\n        onEnter.next();\n        onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n  }\n  /**\n   * Waits for the zone to settle before removing the element. Helps prevent\n   * errors where we end up removing an element which is in the middle of an animation.\n   */\n  _completeExit() {\n    this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n      this._ngZone.run(() => {\n        this._onExit.next();\n        this._onExit.complete();\n      });\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (!this._announceTimeoutId) {\n      this._ngZone.runOutsideAngular(() => {\n        this._announceTimeoutId = setTimeout(() => {\n          const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n          const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n          if (inertElement && liveElement) {\n            // If an element in the snack bar content is focused before being moved\n            // track it and restore focus after moving to the live region.\n            let focusedElement = null;\n            if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n              focusedElement = document.activeElement;\n            }\n            inertElement.removeAttribute('aria-hidden');\n            liveElement.appendChild(inertElement);\n            focusedElement?.focus();\n            this._onAnnounce.next();\n            this._onAnnounce.complete();\n          }\n        }, this._announceDelay);\n      });\n    }\n  }\n  static {\n    this.ɵfac = function _MatSnackBarContainerBase_Factory(t) {\n      return new (t || _MatSnackBarContainerBase)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MatSnackBarConfig));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatSnackBarContainerBase,\n      viewQuery: function _MatSnackBarContainerBase_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSnackBarContainerBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.Platform\n    }, {\n      type: MatSnackBarConfig\n    }];\n  }, {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends _MatSnackBarContainerBase {\n  /** Applies the correct CSS class to the label based on its content. */\n  _afterPortalAttached() {\n    super._afterPortalAttached();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatSnackBarContainer_BaseFactory;\n      return function MatSnackBarContainer_Factory(t) {\n        return (ɵMatSnackBarContainer_BaseFactory || (ɵMatSnackBarContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSnackBarContainer)))(t || MatSnackBarContainer);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSnackBarContainer,\n      selectors: [[\"mat-snack-bar-container\"]],\n      viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\", \"mdc-snackbar--open\"],\n      hostVars: 1,\n      hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵsyntheticHostListener(\"@state.done\", function MatSnackBarContainer_animation_state_done_HostBindingHandler($event) {\n            return ctx.onAnimationEnd($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@state\", ctx._animationState);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 6,\n      vars: 3,\n      consts: [[1, \"mdc-snackbar__surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"label\", \"\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n      template: function MatSnackBarContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1, 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n        }\n      },\n      dependencies: [i3$1.CdkPortalOutlet],\n      styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;--mdc-snackbar-container-shape:4px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSnackBarAnimations.snackBarState]\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      animations: [matSnackBarAnimations.snackBarState],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n        '[@state]': '_animationState',\n        '(@state.done)': 'onAnimationEnd($event)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet></ng-template>\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;--mdc-snackbar-container-shape:4px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"]\n    }]\n  }], null, {\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\nclass MatSnackBarModule {\n  static {\n    this.ɵfac = function MatSnackBarModule_Factory(t) {\n      return new (t || MatSnackBarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSnackBarModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule],\n      exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      declarations: [SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction]\n    }]\n  }], null, null);\n})();\n\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\nclass _MatSnackBarBase {\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n    this._overlay = _overlay;\n    this._live = _live;\n    this._injector = _injector;\n    this._breakpointObserver = _breakpointObserver;\n    this._parentSnackBar = _parentSnackBar;\n    this._defaultConfig = _defaultConfig;\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    this._snackBarRefAtThisLevel = null;\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = {\n      ...this._defaultConfig,\n      ...config\n    };\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = {\n      ...new MatSnackBarConfig(),\n      ...this._defaultConfig,\n      ...userConfig\n    };\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    let positionStrategy = this._overlay.position().global();\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    return this._overlay.create(overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n  static {\n    this.ɵfac = function _MatSnackBarBase_Factory(t) {\n      return new (t || _MatSnackBarBase)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(_MatSnackBarBase, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: _MatSnackBarBase,\n      factory: _MatSnackBarBase.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSnackBarBase, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i2$1.LiveAnnouncer\n    }, {\n      type: i0.Injector\n    }, {\n      type: i3$2.BreakpointObserver\n    }, {\n      type: _MatSnackBarBase,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: MatSnackBarConfig,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar extends _MatSnackBarBase {\n  constructor(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig) {\n    super(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig);\n    this.simpleSnackBarComponent = SimpleSnackBar;\n    this.snackBarContainerComponent = MatSnackBarContainer;\n    this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n  }\n  static {\n    this.ɵfac = function MatSnackBar_Factory(t) {\n      return new (t || MatSnackBar)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(MatSnackBar, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSnackBar,\n      factory: MatSnackBar.ɵfac,\n      providedIn: MatSnackBarModule\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: MatSnackBarModule\n    }]\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i2$1.LiveAnnouncer\n    }, {\n      type: i0.Injector\n    }, {\n      type: i3$2.BreakpointObserver\n    }, {\n      type: MatSnackBar,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: MatSnackBarConfig,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, _MatSnackBarBase, _MatSnackBarContainerBase, matSnackBarAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "inject", "ViewChild", "NgModule", "Injector", "TemplateRef", "Injectable", "Optional", "SkipSelf", "Subject", "i2", "DOCUMENT", "CommonModule", "i3", "MatButtonModule", "trigger", "state", "style", "transition", "animate", "i3$1", "BasePortalOutlet", "CdkPortalOutlet", "PortalModule", "ComponentPortal", "TemplatePortal", "i1", "take", "takeUntil", "i2$1", "i3$2", "Breakpoints", "i1$1", "OverlayModule", "OverlayConfig", "MatCommonModule", "SimpleSnackBar_div_2_Template", "rf", "ctx", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "SimpleSnackBar_div_2_Template_button_click_1_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "action", "ɵɵtext", "ɵɵelementEnd", "ctx_r0", "ɵɵadvance", "ɵɵtextInterpolate1", "data", "_c0", "MatSnackBarContainer_ng_template_4_Template", "MAX_TIMEOUT", "Math", "pow", "MatSnackBarRef", "constructor", "containerInstance", "_overlayRef", "_afterDismissed", "_afterOpened", "_onAction", "_dismissedByAction", "_onExit", "subscribe", "_finishDismiss", "dismiss", "closed", "exit", "clearTimeout", "_durationTimeoutId", "dismissWithAction", "next", "complete", "closeWithAction", "_dismissAfter", "duration", "setTimeout", "min", "_open", "dispose", "dismissedByAction", "afterDismissed", "afterOpened", "_onEnter", "onAction", "MAT_SNACK_BAR_DATA", "MatSnackBarConfig", "politeness", "announcementMessage", "horizontalPosition", "verticalPosition", "MatSnackBarLabel", "ɵfac", "MatSnackBarLabel_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "MatSnackBarActions", "MatSnackBarActions_Factory", "MatSnackBarAction", "MatSnackBarAction_Factory", "SimpleSnackBar", "snackBarRef", "hasAction", "SimpleSnackBar_Factory", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "exportAs", "decls", "vars", "consts", "template", "SimpleSnackBar_Template", "ɵɵtemplate", "message", "ɵɵproperty", "dependencies", "NgIf", "MatButton", "styles", "encapsulation", "changeDetection", "None", "OnPush", "undefined", "decorators", "matSnackBarAnimations", "snackBarState", "transform", "opacity", "uniqueId", "_MatSnackBarContainerBase", "_ngZone", "_elementRef", "_changeDetectorRef", "_platform", "snackBarConfig", "_document", "_trackedModals", "Set", "_announce<PERSON><PERSON>y", "_destroyed", "_onAnnounce", "_animationState", "_liveElementId", "attachDomPortal", "portal", "_assertNotAttached", "result", "_portalOutlet", "_afterPortalAttached", "_live", "FIREFOX", "_role", "attachComponentPortal", "attachTemplatePortal", "onAnimationEnd", "event", "fromState", "toState", "_completeExit", "onEnter", "run", "enter", "detectChanges", "_screenReaderAnnounce", "nativeElement", "setAttribute", "_announceTimeoutId", "ngOnDestroy", "_clearFromModals", "onMicrotaskEmpty", "pipe", "element", "panelClasses", "panelClass", "Array", "isArray", "for<PERSON>ach", "cssClass", "classList", "add", "_exposeToModals", "id", "modals", "querySelectorAll", "i", "length", "modal", "ariaOwns", "getAttribute", "indexOf", "newValue", "replace", "trim", "removeAttribute", "clear", "has<PERSON>tta<PERSON>", "Error", "runOutsideAngular", "inertElement", "querySelector", "liveElement", "focusedElement", "<PERSON><PERSON><PERSON><PERSON>", "document", "activeElement", "HTMLElement", "contains", "append<PERSON><PERSON><PERSON>", "focus", "_MatSnackBarContainerBase_Factory", "NgZone", "ElementRef", "ChangeDetectorRef", "Platform", "viewQuery", "_MatSnackBarContainerBase_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "features", "ɵɵInheritDefinitionFeature", "static", "MatSnackBarContainer", "label", "_label", "labelClass", "toggle", "ɵMatSnackBarContainer_BaseFactory", "MatSnackBarContainer_Factory", "ɵɵgetInheritedFactory", "MatSnackBarContainer_Query", "hostVars", "hostBindings", "MatSnackBarContainer_HostBindings", "ɵɵsyntheticHostListener", "MatSnackBarContainer_animation_state_done_HostBindingHandler", "$event", "ɵɵsyntheticHostProperty", "MatSnackBarContainer_Template", "ɵɵelement", "ɵɵattribute", "animation", "<PERSON><PERSON><PERSON>", "animations", "MatSnackBarModule", "MatSnackBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations", "MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "providedIn", "factory", "_MatSnackBarBase", "_openedSnackBarRef", "parent", "_parentSnackBar", "_snackBarRefAtThisLevel", "value", "_overlay", "_injector", "_breakpointObserver", "_defaultConfig", "openFromComponent", "component", "config", "_attach", "openFromTemplate", "open", "_config", "simpleSnackBarComponent", "_attachSnackBarContainer", "overlayRef", "userInjector", "viewContainerRef", "injector", "create", "providers", "provide", "useValue", "containerPortal", "snackBarContainerComponent", "containerRef", "attach", "instance", "content", "userConfig", "_createOverlay", "container", "$implicit", "_createInjector", "contentRef", "observe", "HandsetPortrait", "detachments", "overlayElement", "handsetCssClass", "matches", "announce", "_animateSnackBar", "overlayConfig", "direction", "positionStrategy", "position", "global", "isRtl", "isLeft", "isRight", "left", "right", "centerHorizontally", "top", "bottom", "_MatSnackBarBase_Factory", "ɵɵinject", "Overlay", "LiveAnnouncer", "BreakpointObserver", "ɵprov", "ɵɵdefineInjectable", "token", "MatSnackBar", "overlay", "live", "breakpointObserver", "parentSnackBar", "defaultConfig", "MatSnackBar_Factory"], "sources": ["C:/e-learning/node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, NgModule, Injector, TemplateRef, Injectable, Optional, SkipSelf } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport * as i3$2 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayModule, OverlayConfig } from '@angular/cdk/overlay';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n    constructor(containerInstance, _overlayRef) {\n        this._overlayRef = _overlayRef;\n        /** Subject for notifying the user that the snack bar has been dismissed. */\n        this._afterDismissed = new Subject();\n        /** Subject for notifying the user that the snack bar has opened and appeared. */\n        this._afterOpened = new Subject();\n        /** Subject for notifying the user that the snack bar action was called. */\n        this._onAction = new Subject();\n        /** Whether the snack bar was dismissed using the action button. */\n        this._dismissedByAction = false;\n        this.containerInstance = containerInstance;\n        containerInstance._onExit.subscribe(() => this._finishDismiss());\n    }\n    /** Dismisses the snack bar. */\n    dismiss() {\n        if (!this._afterDismissed.closed) {\n            this.containerInstance.exit();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /** Marks the snackbar action clicked. */\n    dismissWithAction() {\n        if (!this._onAction.closed) {\n            this._dismissedByAction = true;\n            this._onAction.next();\n            this._onAction.complete();\n            this.dismiss();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /**\n     * Marks the snackbar action clicked.\n     * @deprecated Use `dismissWithAction` instead.\n     * @breaking-change 8.0.0\n     */\n    closeWithAction() {\n        this.dismissWithAction();\n    }\n    /** Dismisses the snack bar after some duration */\n    _dismissAfter(duration) {\n        // Note that we need to cap the duration to the maximum value for setTimeout, because\n        // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n        this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n    }\n    /** Marks the snackbar as opened */\n    _open() {\n        if (!this._afterOpened.closed) {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    /** Cleans up the DOM after closing. */\n    _finishDismiss() {\n        this._overlayRef.dispose();\n        if (!this._onAction.closed) {\n            this._onAction.complete();\n        }\n        this._afterDismissed.next({ dismissedByAction: this._dismissedByAction });\n        this._afterDismissed.complete();\n        this._dismissedByAction = false;\n    }\n    /** Gets an observable that is notified when the snack bar is finished closing. */\n    afterDismissed() {\n        return this._afterDismissed;\n    }\n    /** Gets an observable that is notified when the snack bar has opened and appeared. */\n    afterOpened() {\n        return this.containerInstance._onEnter;\n    }\n    /** Gets an observable that is notified when the snack bar action is called. */\n    onAction() {\n        return this._onAction;\n    }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n    constructor() {\n        /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n        this.politeness = 'assertive';\n        /**\n         * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n         * component or template, the announcement message will default to the specified message.\n         */\n        this.announcementMessage = '';\n        /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n        this.duration = 0;\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** The horizontal position to place the snack bar. */\n        this.horizontalPosition = 'center';\n        /** The vertical position to place the snack bar. */\n        this.verticalPosition = 'bottom';\n    }\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\", host: { classAttribute: \"mat-mdc-snack-bar-label mdc-snackbar__label\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarLabel]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarActions, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\", host: { classAttribute: \"mat-mdc-snack-bar-actions mdc-snackbar__actions\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarActions]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarAction, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\", host: { classAttribute: \"mat-mdc-snack-bar-action mdc-snackbar__action\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarAction]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n                    },\n                }]\n        }] });\n\nclass SimpleSnackBar {\n    constructor(snackBarRef, data) {\n        this.snackBarRef = snackBarRef;\n        this.data = data;\n    }\n    /** Performs the action on the snack bar. */\n    action() {\n        this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n        return !!this.data.action;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: SimpleSnackBar, deps: [{ token: MatSnackBarRef }, { token: MAT_SNACK_BAR_DATA }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: SimpleSnackBar, selector: \"simple-snack-bar\", host: { classAttribute: \"mat-mdc-simple-snack-bar\" }, exportAs: [\"matSnackBar\"], ngImport: i0, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n<div matSnackBarActions *ngIf=\\\"hasAction\\\">\\n  <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n    {{data.action}}\\n  </button>\\n</div>\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\"], dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i3.MatButton, selector: \"    button[mat-button], button[mat-raised-button], button[mat-flat-button],    button[mat-stroked-button]  \", inputs: [\"disabled\", \"disableRipple\", \"color\"], exportAs: [\"matButton\"] }, { kind: \"directive\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\" }, { kind: \"directive\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\" }, { kind: \"directive\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: SimpleSnackBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'simple-snack-bar', exportAs: 'matSnackBar', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-mdc-simple-snack-bar',\n                    }, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n<div matSnackBarActions *ngIf=\\\"hasAction\\\">\\n  <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n    {{data.action}}\\n  </button>\\n</div>\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\"] }]\n        }], ctorParameters: function () { return [{ type: MatSnackBarRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DATA]\n                }] }]; } });\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n    /** Animation that shows and hides a snack bar. */\n    snackBarState: trigger('state', [\n        state('void, hidden', style({\n            transform: 'scale(0.8)',\n            opacity: 0,\n        })),\n        state('visible', style({\n            transform: 'scale(1)',\n            opacity: 1,\n        })),\n        transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n        transition('* => void, * => hidden', animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', style({\n            opacity: 0,\n        }))),\n    ]),\n};\n\nlet uniqueId = 0;\n/**\n * Base class for snack bar containers.\n * @docs-private\n */\nclass _MatSnackBarContainerBase extends BasePortalOutlet {\n    constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, \n    /** The snack bar configuration. */\n    snackBarConfig) {\n        super();\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._platform = _platform;\n        this.snackBarConfig = snackBarConfig;\n        this._document = inject(DOCUMENT);\n        this._trackedModals = new Set();\n        /** The number of milliseconds to wait before announcing the snack bar's content. */\n        this._announceDelay = 150;\n        /** Whether the component has been destroyed. */\n        this._destroyed = false;\n        /** Subject for notifying that the snack bar has announced to screen readers. */\n        this._onAnnounce = new Subject();\n        /** Subject for notifying that the snack bar has exited from view. */\n        this._onExit = new Subject();\n        /** Subject for notifying that the snack bar has finished entering the view. */\n        this._onEnter = new Subject();\n        /** The state of the snack bar animations. */\n        this._animationState = 'void';\n        /** Unique ID of the aria-live element. */\n        this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n        /**\n         * Attaches a DOM portal to the snack bar container.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            this._assertNotAttached();\n            const result = this._portalOutlet.attachDomPortal(portal);\n            this._afterPortalAttached();\n            return result;\n        };\n        // Use aria-live rather than a live role like 'alert' or 'status'\n        // because NVDA and JAWS have show inconsistent behavior with live roles.\n        if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n            this._live = 'assertive';\n        }\n        else if (snackBarConfig.politeness === 'off') {\n            this._live = 'off';\n        }\n        else {\n            this._live = 'polite';\n        }\n        // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n        // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n        if (this._platform.FIREFOX) {\n            if (this._live === 'polite') {\n                this._role = 'status';\n            }\n            if (this._live === 'assertive') {\n                this._role = 'alert';\n            }\n        }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(event) {\n        const { fromState, toState } = event;\n        if ((toState === 'void' && fromState !== 'void') || toState === 'hidden') {\n            this._completeExit();\n        }\n        if (toState === 'visible') {\n            // Note: we shouldn't use `this` inside the zone callback,\n            // because it can cause a memory leak.\n            const onEnter = this._onEnter;\n            this._ngZone.run(() => {\n                onEnter.next();\n                onEnter.complete();\n            });\n        }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n        if (!this._destroyed) {\n            this._animationState = 'visible';\n            this._changeDetectorRef.detectChanges();\n            this._screenReaderAnnounce();\n        }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n        // It's common for snack bars to be opened by random outside calls like HTTP requests or\n        // errors. Run inside the NgZone to ensure that it functions correctly.\n        this._ngZone.run(() => {\n            // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n            // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n            // `MatSnackBar.open`).\n            this._animationState = 'hidden';\n            // Mark this element with an 'exit' attribute to indicate that the snackbar has\n            // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n            // test harness.\n            this._elementRef.nativeElement.setAttribute('mat-exit', '');\n            // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n            // long enough to visually read it either, so clear the timeout for announcing.\n            clearTimeout(this._announceTimeoutId);\n        });\n        return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n        this._destroyed = true;\n        this._clearFromModals();\n        this._completeExit();\n    }\n    /**\n     * Waits for the zone to settle before removing the element. Helps prevent\n     * errors where we end up removing an element which is in the middle of an animation.\n     */\n    _completeExit() {\n        this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n            this._ngZone.run(() => {\n                this._onExit.next();\n                this._onExit.complete();\n            });\n        });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n        const element = this._elementRef.nativeElement;\n        const panelClasses = this.snackBarConfig.panelClass;\n        if (panelClasses) {\n            if (Array.isArray(panelClasses)) {\n                // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n                panelClasses.forEach(cssClass => element.classList.add(cssClass));\n            }\n            else {\n                element.classList.add(panelClasses);\n            }\n        }\n        this._exposeToModals();\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n        // `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const id = this._liveElementId;\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            this._trackedModals.add(modal);\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n        this._trackedModals.forEach(modal => {\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (ariaOwns) {\n                const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n                if (newValue.length > 0) {\n                    modal.setAttribute('aria-owns', newValue);\n                }\n                else {\n                    modal.removeAttribute('aria-owns');\n                }\n            }\n        });\n        this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Attempting to attach snack bar content after content is already attached');\n        }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n        if (!this._announceTimeoutId) {\n            this._ngZone.runOutsideAngular(() => {\n                this._announceTimeoutId = setTimeout(() => {\n                    const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n                    const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n                    if (inertElement && liveElement) {\n                        // If an element in the snack bar content is focused before being moved\n                        // track it and restore focus after moving to the live region.\n                        let focusedElement = null;\n                        if (this._platform.isBrowser &&\n                            document.activeElement instanceof HTMLElement &&\n                            inertElement.contains(document.activeElement)) {\n                            focusedElement = document.activeElement;\n                        }\n                        inertElement.removeAttribute('aria-hidden');\n                        liveElement.appendChild(inertElement);\n                        focusedElement?.focus();\n                        this._onAnnounce.next();\n                        this._onAnnounce.complete();\n                    }\n                }, this._announceDelay);\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatSnackBarContainerBase, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.Platform }, { token: MatSnackBarConfig }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatSnackBarContainerBase, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatSnackBarContainerBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.Platform }, { type: MatSnackBarConfig }]; }, propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends _MatSnackBarContainerBase {\n    /** Applies the correct CSS class to the label based on its content. */\n    _afterPortalAttached() {\n        super._afterPortalAttached();\n        // Check to see if the attached component or template uses the MDC template structure,\n        // specifically the MDC label. If not, the container should apply the MDC label class to this\n        // component's label container, which will apply MDC's label styles to the attached view.\n        const label = this._label.nativeElement;\n        const labelClass = 'mdc-snackbar__label';\n        label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarContainer, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSnackBarContainer, selector: \"mat-snack-bar-container\", host: { listeners: { \"@state.done\": \"onAnimationEnd($event)\" }, properties: { \"@state\": \"_animationState\" }, classAttribute: \"mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open\" }, viewQueries: [{ propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet></ng-template>\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;--mdc-snackbar-container-shape:4px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"], dependencies: [{ kind: \"directive\", type: i3$1.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], animations: [matSnackBarAnimations.snackBarState], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-snack-bar-container', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, animations: [matSnackBarAnimations.snackBarState], host: {\n                        'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n                        '[@state]': '_animationState',\n                        '(@state.done)': 'onAnimationEnd($event)',\n                    }, template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet></ng-template>\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;--mdc-snackbar-container-shape:4px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"] }]\n        }], propDecorators: { _label: [{\n                type: ViewChild,\n                args: ['label', { static: true }]\n            }] } });\n\nclass MatSnackBarModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarModule, declarations: [SimpleSnackBar,\n            MatSnackBarContainer,\n            MatSnackBarLabel,\n            MatSnackBarActions,\n            MatSnackBarAction], imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule], exports: [MatCommonModule,\n            MatSnackBarContainer,\n            MatSnackBarLabel,\n            MatSnackBarActions,\n            MatSnackBarAction] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarModule, imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule],\n                    exports: [\n                        MatCommonModule,\n                        MatSnackBarContainer,\n                        MatSnackBarLabel,\n                        MatSnackBarActions,\n                        MatSnackBarAction,\n                    ],\n                    declarations: [\n                        SimpleSnackBar,\n                        MatSnackBarContainer,\n                        MatSnackBarLabel,\n                        MatSnackBarActions,\n                        MatSnackBarAction,\n                    ],\n                }]\n        }] });\n\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n    return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n});\nclass _MatSnackBarBase {\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n        const parent = this._parentSnackBar;\n        return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n        if (this._parentSnackBar) {\n            this._parentSnackBar._openedSnackBarRef = value;\n        }\n        else {\n            this._snackBarRefAtThisLevel = value;\n        }\n    }\n    constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n        this._overlay = _overlay;\n        this._live = _live;\n        this._injector = _injector;\n        this._breakpointObserver = _breakpointObserver;\n        this._parentSnackBar = _parentSnackBar;\n        this._defaultConfig = _defaultConfig;\n        /**\n         * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n         * If there is a parent snack-bar service, all operations should delegate to that parent\n         * via `_openedSnackBarRef`.\n         */\n        this._snackBarRefAtThisLevel = null;\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n        return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n        return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n        const _config = { ...this._defaultConfig, ...config };\n        // Since the user doesn't have access to the component, we can\n        // override the data to pass in our own message and action.\n        _config.data = { message, action };\n        // Since the snack bar has `role=\"alert\"`, we don't\n        // want to announce the same message twice.\n        if (_config.announcementMessage === message) {\n            _config.announcementMessage = undefined;\n        }\n        return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n        if (this._openedSnackBarRef) {\n            this._openedSnackBarRef.dismiss();\n        }\n    }\n    ngOnDestroy() {\n        // Only dismiss the snack bar at the current level on destroy.\n        if (this._snackBarRefAtThisLevel) {\n            this._snackBarRefAtThisLevel.dismiss();\n        }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        const injector = Injector.create({\n            parent: userInjector || this._injector,\n            providers: [{ provide: MatSnackBarConfig, useValue: config }],\n        });\n        const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n        const containerRef = overlayRef.attach(containerPortal);\n        containerRef.instance.snackBarConfig = config;\n        return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n        const config = { ...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig };\n        const overlayRef = this._createOverlay(config);\n        const container = this._attachSnackBarContainer(overlayRef, config);\n        const snackBarRef = new MatSnackBarRef(container, overlayRef);\n        if (content instanceof TemplateRef) {\n            const portal = new TemplatePortal(content, null, {\n                $implicit: config.data,\n                snackBarRef,\n            });\n            snackBarRef.instance = container.attachTemplatePortal(portal);\n        }\n        else {\n            const injector = this._createInjector(config, snackBarRef);\n            const portal = new ComponentPortal(content, undefined, injector);\n            const contentRef = container.attachComponentPortal(portal);\n            // We can't pass this via the injector, because the injector is created earlier.\n            snackBarRef.instance = contentRef.instance;\n        }\n        // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n        // appropriate. This class is applied to the overlay element because the overlay must expand to\n        // fill the width of the screen for full width snackbars.\n        this._breakpointObserver\n            .observe(Breakpoints.HandsetPortrait)\n            .pipe(takeUntil(overlayRef.detachments()))\n            .subscribe(state => {\n            overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n        });\n        if (config.announcementMessage) {\n            // Wait until the snack bar contents have been announced then deliver this message.\n            container._onAnnounce.subscribe(() => {\n                this._live.announce(config.announcementMessage, config.politeness);\n            });\n        }\n        this._animateSnackBar(snackBarRef, config);\n        this._openedSnackBarRef = snackBarRef;\n        return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n        // When the snackbar is dismissed, clear the reference to it.\n        snackBarRef.afterDismissed().subscribe(() => {\n            // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n            if (this._openedSnackBarRef == snackBarRef) {\n                this._openedSnackBarRef = null;\n            }\n            if (config.announcementMessage) {\n                this._live.clear();\n            }\n        });\n        if (this._openedSnackBarRef) {\n            // If a snack bar is already in view, dismiss it and enter the\n            // new snack bar after exit animation is complete.\n            this._openedSnackBarRef.afterDismissed().subscribe(() => {\n                snackBarRef.containerInstance.enter();\n            });\n            this._openedSnackBarRef.dismiss();\n        }\n        else {\n            // If no snack bar is in view, enter the new snack bar.\n            snackBarRef.containerInstance.enter();\n        }\n        // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n        if (config.duration && config.duration > 0) {\n            snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n        }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n        const overlayConfig = new OverlayConfig();\n        overlayConfig.direction = config.direction;\n        let positionStrategy = this._overlay.position().global();\n        // Set horizontal position.\n        const isRtl = config.direction === 'rtl';\n        const isLeft = config.horizontalPosition === 'left' ||\n            (config.horizontalPosition === 'start' && !isRtl) ||\n            (config.horizontalPosition === 'end' && isRtl);\n        const isRight = !isLeft && config.horizontalPosition !== 'center';\n        if (isLeft) {\n            positionStrategy.left('0');\n        }\n        else if (isRight) {\n            positionStrategy.right('0');\n        }\n        else {\n            positionStrategy.centerHorizontally();\n        }\n        // Set horizontal position.\n        if (config.verticalPosition === 'top') {\n            positionStrategy.top('0');\n        }\n        else {\n            positionStrategy.bottom('0');\n        }\n        overlayConfig.positionStrategy = positionStrategy;\n        return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        return Injector.create({\n            parent: userInjector || this._injector,\n            providers: [\n                { provide: MatSnackBarRef, useValue: snackBarRef },\n                { provide: MAT_SNACK_BAR_DATA, useValue: config.data },\n            ],\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatSnackBarBase, deps: [{ token: i1$1.Overlay }, { token: i2$1.LiveAnnouncer }, { token: i0.Injector }, { token: i3$2.BreakpointObserver }, { token: _MatSnackBarBase, optional: true, skipSelf: true }, { token: MAT_SNACK_BAR_DEFAULT_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatSnackBarBase }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatSnackBarBase, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i2$1.LiveAnnouncer }, { type: i0.Injector }, { type: i3$2.BreakpointObserver }, { type: _MatSnackBarBase, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: MatSnackBarConfig, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar extends _MatSnackBarBase {\n    constructor(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig) {\n        super(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig);\n        this.simpleSnackBarComponent = SimpleSnackBar;\n        this.snackBarContainerComponent = MatSnackBarContainer;\n        this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBar, deps: [{ token: i1$1.Overlay }, { token: i2$1.LiveAnnouncer }, { token: i0.Injector }, { token: i3$2.BreakpointObserver }, { token: MatSnackBar, optional: true, skipSelf: true }, { token: MAT_SNACK_BAR_DEFAULT_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBar, providedIn: MatSnackBarModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSnackBar, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: MatSnackBarModule }]\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i2$1.LiveAnnouncer }, { type: i0.Injector }, { type: i3$2.BreakpointObserver }, { type: MatSnackBar, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: MatSnackBarConfig, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n                }] }]; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, _MatSnackBarBase, _MatSnackBarContainerBase, matSnackBarAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC5M,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,IAAI,MAAM,qBAAqB;AAC3C,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AACtH,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAChD,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,qBAAqB;AAC3C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AACnE,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AAAA,SAAAC,8BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA4GoG7C,EAAE,CAAA8C,gBAAA;IAAF9C,EAAE,CAAA+C,cAAA,YAuDqP,CAAC,eAAD,CAAC;IAvDxP/C,EAAE,CAAAgD,UAAA,mBAAAC,sDAAA;MAAFjD,EAAE,CAAAkD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFnD,EAAE,CAAAoD,aAAA;MAAA,OAAFpD,EAAE,CAAAqD,WAAA,CAuDySF,MAAA,CAAAG,MAAA,CAAO,EAAC;IAAA,CAAC,CAAC;IAvDrTtD,EAAE,CAAAuD,MAAA,EAuD4U,CAAC;IAvD/UvD,EAAE,CAAAwD,YAAA,CAuDqV,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAc,MAAA,GAvDxVzD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA0D,SAAA,EAuD4U,CAAC;IAvD/U1D,EAAE,CAAA2D,kBAAA,MAAAF,MAAA,CAAAG,IAAA,CAAAN,MAAA,KAuD4U,CAAC;EAAA;AAAA;AAAA,MAAAO,GAAA;AAAA,SAAAC,4CAAAnB,EAAA,EAAAC,GAAA;AAlKnb,MAAMmB,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACvC;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,iBAAiB,EAAEC,WAAW,EAAE;IACxC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACC,eAAe,GAAG,IAAIvD,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACwD,YAAY,GAAG,IAAIxD,OAAO,CAAC,CAAC;IACjC;IACA,IAAI,CAACyD,SAAS,GAAG,IAAIzD,OAAO,CAAC,CAAC;IAC9B;IACA,IAAI,CAAC0D,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACL,iBAAiB,GAAGA,iBAAiB;IAC1CA,iBAAiB,CAACM,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;EACpE;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACP,eAAe,CAACQ,MAAM,EAAE;MAC9B,IAAI,CAACV,iBAAiB,CAACW,IAAI,CAAC,CAAC;IACjC;IACAC,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACV,SAAS,CAACM,MAAM,EAAE;MACxB,IAAI,CAACL,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACD,SAAS,CAACW,IAAI,CAAC,CAAC;MACrB,IAAI,CAACX,SAAS,CAACY,QAAQ,CAAC,CAAC;MACzB,IAAI,CAACP,OAAO,CAAC,CAAC;IAClB;IACAG,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACII,eAAeA,CAAA,EAAG;IACd,IAAI,CAACH,iBAAiB,CAAC,CAAC;EAC5B;EACA;EACAI,aAAaA,CAACC,QAAQ,EAAE;IACpB;IACA;IACA,IAAI,CAACN,kBAAkB,GAAGO,UAAU,CAAC,MAAM,IAAI,CAACX,OAAO,CAAC,CAAC,EAAEb,IAAI,CAACyB,GAAG,CAACF,QAAQ,EAAExB,WAAW,CAAC,CAAC;EAC/F;EACA;EACA2B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACnB,YAAY,CAACO,MAAM,EAAE;MAC3B,IAAI,CAACP,YAAY,CAACY,IAAI,CAAC,CAAC;MACxB,IAAI,CAACZ,YAAY,CAACa,QAAQ,CAAC,CAAC;IAChC;EACJ;EACA;EACAR,cAAcA,CAAA,EAAG;IACb,IAAI,CAACP,WAAW,CAACsB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACnB,SAAS,CAACM,MAAM,EAAE;MACxB,IAAI,CAACN,SAAS,CAACY,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAI,CAACd,eAAe,CAACa,IAAI,CAAC;MAAES,iBAAiB,EAAE,IAAI,CAACnB;IAAmB,CAAC,CAAC;IACzE,IAAI,CAACH,eAAe,CAACc,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACX,kBAAkB,GAAG,KAAK;EACnC;EACA;EACAoB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACvB,eAAe;EAC/B;EACA;EACAwB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC1B,iBAAiB,CAAC2B,QAAQ;EAC1C;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxB,SAAS;EACzB;AACJ;;AAEA;AACA,MAAMyB,kBAAkB,GAAG,IAAIhG,cAAc,CAAC,iBAAiB,CAAC;AAChE;AACA;AACA;AACA,MAAMiG,iBAAiB,CAAC;EACpB/B,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACgC,UAAU,GAAG,WAAW;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACb,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAAC3B,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACyC,kBAAkB,GAAG,QAAQ;IAClC;IACA,IAAI,CAACC,gBAAgB,GAAG,QAAQ;EACpC;AACJ;;AAEA;AACA,MAAMC,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACI,IAAI,kBAD8E3G,EAAE,CAAA4G,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,SAAA;IAAA,EAA0H;EAAE;AAC9O;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGhH,EAAE,CAAAiH,iBAAA,CAGXV,gBAAgB,EAAc,CAAC;IAC9GM,IAAI,EAAE3G,SAAS;IACfgH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,oBAAmB;MAC9BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACb,IAAI,YAAAc,2BAAAZ,CAAA;MAAA,YAAAA,CAAA,IAAwFW,kBAAkB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACV,IAAI,kBAf8E3G,EAAE,CAAA4G,iBAAA;MAAAC,IAAA,EAeJQ,kBAAkB;MAAAP,SAAA;MAAAC,SAAA;IAAA,EAAgI;EAAE;AACtP;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjBoGhH,EAAE,CAAAiH,iBAAA,CAiBXI,kBAAkB,EAAc,CAAC;IAChHR,IAAI,EAAE3G,SAAS;IACfgH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,sBAAqB;MAChCC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMG,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACf,IAAI,YAAAgB,0BAAAd,CAAA;MAAA,YAAAA,CAAA,IAAwFa,iBAAiB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACZ,IAAI,kBA7B8E3G,EAAE,CAAA4G,iBAAA;MAAAC,IAAA,EA6BJU,iBAAiB;MAAAT,SAAA;MAAAC,SAAA;IAAA,EAA6H;EAAE;AAClP;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/BoGhH,EAAE,CAAAiH,iBAAA,CA+BXM,iBAAiB,EAAc,CAAC;IAC/GV,IAAI,EAAE3G,SAAS;IACfgH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,qBAAoB;MAC/BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,cAAc,CAAC;EACjBtD,WAAWA,CAACuD,WAAW,EAAE9D,IAAI,EAAE;IAC3B,IAAI,CAAC8D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC9D,IAAI,GAAGA,IAAI;EACpB;EACA;EACAN,MAAMA,CAAA,EAAG;IACL,IAAI,CAACoE,WAAW,CAACxC,iBAAiB,CAAC,CAAC;EACxC;EACA;EACA,IAAIyC,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAAC/D,IAAI,CAACN,MAAM;EAC7B;EACA;IAAS,IAAI,CAACkD,IAAI,YAAAoB,uBAAAlB,CAAA;MAAA,YAAAA,CAAA,IAAwFe,cAAc,EAtDxBzH,EAAE,CAAA6H,iBAAA,CAsDwC3D,cAAc,GAtDxDlE,EAAE,CAAA6H,iBAAA,CAsDmE5B,kBAAkB;IAAA,CAA4C;EAAE;EACrO;IAAS,IAAI,CAAC6B,IAAI,kBAvD8E9H,EAAE,CAAA+H,iBAAA;MAAAlB,IAAA,EAuDJY,cAAc;MAAAX,SAAA;MAAAC,SAAA;MAAAiB,QAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAA1F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvDZ3C,EAAE,CAAA+C,cAAA,YAuDyK,CAAC;UAvD5K/C,EAAE,CAAAuD,MAAA,EAuD+L,CAAC;UAvDlMvD,EAAE,CAAAwD,YAAA,CAuDqM,CAAC;UAvDxMxD,EAAE,CAAAsI,UAAA,IAAA5F,6BAAA,gBAuD6V,CAAC;QAAA;QAAA,IAAAC,EAAA;UAvDhW3C,EAAE,CAAA0D,SAAA,EAuD+L,CAAC;UAvDlM1D,EAAE,CAAA2D,kBAAA,MAAAf,GAAA,CAAAgB,IAAA,CAAA2E,OAAA,MAuD+L,CAAC;UAvDlMvI,EAAE,CAAA0D,SAAA,EAuDkP,CAAC;UAvDrP1D,EAAE,CAAAwI,UAAA,SAAA5F,GAAA,CAAA+E,SAuDkP,CAAC;QAAA;MAAA;MAAAc,YAAA,GAA+MzH,EAAE,CAAC0H,IAAI,EAA6FvH,EAAE,CAACwH,SAAS,EAAiOpC,gBAAgB,EAA+Dc,kBAAkB,EAAiEE,iBAAiB;MAAAqB,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAqI;EAAE;AACnrC;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KAzDoGhH,EAAE,CAAAiH,iBAAA,CAyDXQ,cAAc,EAAc,CAAC;IAC5GZ,IAAI,EAAE1G,SAAS;IACf+G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEa,QAAQ,EAAE,aAAa;MAAEa,aAAa,EAAEzI,iBAAiB,CAAC2I,IAAI;MAAED,eAAe,EAAEzI,uBAAuB,CAAC2I,MAAM;MAAE5B,IAAI,EAAE;QAClJ,OAAO,EAAE;MACb,CAAC;MAAEgB,QAAQ,EAAE,8MAA8M;MAAEQ,MAAM,EAAE,CAAC,yCAAyC;IAAE,CAAC;EAC9R,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/B,IAAI,EAAE3C;IAAe,CAAC,EAAE;MAAE2C,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACxFrC,IAAI,EAAEvG,MAAM;QACZ4G,IAAI,EAAE,CAACjB,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA,MAAMkD,qBAAqB,GAAG;EAC1B;EACAC,aAAa,EAAE/H,OAAO,CAAC,OAAO,EAAE,CAC5BC,KAAK,CAAC,cAAc,EAAEC,KAAK,CAAC;IACxB8H,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,EACHhI,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IACnB8H,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,EACH9H,UAAU,CAAC,cAAc,EAAEC,OAAO,CAAC,kCAAkC,CAAC,CAAC,EACvED,UAAU,CAAC,wBAAwB,EAAEC,OAAO,CAAC,mCAAmC,EAAEF,KAAK,CAAC;IACpF+H,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CACP;AACL,CAAC;AAED,IAAIC,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,SAAS7H,gBAAgB,CAAC;EACrDwC,WAAWA,CAACsF,OAAO,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,SAAS,EAC/D;EACAC,cAAc,EAAE;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGvJ,MAAM,CAACU,QAAQ,CAAC;IACjC,IAAI,CAAC8I,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,GAAG;IACzB;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,WAAW,GAAG,IAAIpJ,OAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAAC2D,OAAO,GAAG,IAAI3D,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACgF,QAAQ,GAAG,IAAIhF,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACqJ,eAAe,GAAG,MAAM;IAC7B;IACA,IAAI,CAACC,cAAc,GAAI,gCAA+Bd,QAAQ,EAAG,EAAC;IAClE;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACe,eAAe,GAAIC,MAAM,IAAK;MAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACJ,eAAe,CAACC,MAAM,CAAC;MACzD,IAAI,CAACI,oBAAoB,CAAC,CAAC;MAC3B,OAAOF,MAAM;IACjB,CAAC;IACD;IACA;IACA,IAAIZ,cAAc,CAAC1D,UAAU,KAAK,WAAW,IAAI,CAAC0D,cAAc,CAACzD,mBAAmB,EAAE;MAClF,IAAI,CAACwE,KAAK,GAAG,WAAW;IAC5B,CAAC,MACI,IAAIf,cAAc,CAAC1D,UAAU,KAAK,KAAK,EAAE;MAC1C,IAAI,CAACyE,KAAK,GAAG,KAAK;IACtB,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAG,QAAQ;IACzB;IACA;IACA;IACA,IAAI,IAAI,CAAChB,SAAS,CAACiB,OAAO,EAAE;MACxB,IAAI,IAAI,CAACD,KAAK,KAAK,QAAQ,EAAE;QACzB,IAAI,CAACE,KAAK,GAAG,QAAQ;MACzB;MACA,IAAI,IAAI,CAACF,KAAK,KAAK,WAAW,EAAE;QAC5B,IAAI,CAACE,KAAK,GAAG,OAAO;MACxB;IACJ;EACJ;EACA;EACAC,qBAAqBA,CAACR,MAAM,EAAE;IAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACK,qBAAqB,CAACR,MAAM,CAAC;IAC/D,IAAI,CAACI,oBAAoB,CAAC,CAAC;IAC3B,OAAOF,MAAM;EACjB;EACA;EACAO,oBAAoBA,CAACT,MAAM,EAAE;IACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACM,oBAAoB,CAACT,MAAM,CAAC;IAC9D,IAAI,CAACI,oBAAoB,CAAC,CAAC;IAC3B,OAAOF,MAAM;EACjB;EACA;EACAQ,cAAcA,CAACC,KAAK,EAAE;IAClB,MAAM;MAAEC,SAAS;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IACpC,IAAKE,OAAO,KAAK,MAAM,IAAID,SAAS,KAAK,MAAM,IAAKC,OAAO,KAAK,QAAQ,EAAE;MACtE,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;IACA,IAAID,OAAO,KAAK,SAAS,EAAE;MACvB;MACA;MACA,MAAME,OAAO,GAAG,IAAI,CAACvF,QAAQ;MAC7B,IAAI,CAAC0D,OAAO,CAAC8B,GAAG,CAAC,MAAM;QACnBD,OAAO,CAACnG,IAAI,CAAC,CAAC;QACdmG,OAAO,CAAClG,QAAQ,CAAC,CAAC;MACtB,CAAC,CAAC;IACN;EACJ;EACA;EACAoG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACtB,UAAU,EAAE;MAClB,IAAI,CAACE,eAAe,GAAG,SAAS;MAChC,IAAI,CAACT,kBAAkB,CAAC8B,aAAa,CAAC,CAAC;MACvC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACA;EACA3G,IAAIA,CAAA,EAAG;IACH;IACA;IACA,IAAI,CAAC0E,OAAO,CAAC8B,GAAG,CAAC,MAAM;MACnB;MACA;MACA;MACA,IAAI,CAACnB,eAAe,GAAG,QAAQ;MAC/B;MACA;MACA;MACA,IAAI,CAACV,WAAW,CAACiC,aAAa,CAACC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3D;MACA;MACA5G,YAAY,CAAC,IAAI,CAAC6G,kBAAkB,CAAC;IACzC,CAAC,CAAC;IACF,OAAO,IAAI,CAACnH,OAAO;EACvB;EACA;EACAoH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5B,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC6B,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACV,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC5B,OAAO,CAACuC,gBAAgB,CAACC,IAAI,CAAChK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0C,SAAS,CAAC,MAAM;MACxD,IAAI,CAAC8E,OAAO,CAAC8B,GAAG,CAAC,MAAM;QACnB,IAAI,CAAC7G,OAAO,CAACS,IAAI,CAAC,CAAC;QACnB,IAAI,CAACT,OAAO,CAACU,QAAQ,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIuF,oBAAoBA,CAAA,EAAG;IACnB,MAAMuB,OAAO,GAAG,IAAI,CAACxC,WAAW,CAACiC,aAAa;IAC9C,MAAMQ,YAAY,GAAG,IAAI,CAACtC,cAAc,CAACuC,UAAU;IACnD,IAAID,YAAY,EAAE;MACd,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;QAC7B;QACAA,YAAY,CAACI,OAAO,CAACC,QAAQ,IAAIN,OAAO,CAACO,SAAS,CAACC,GAAG,CAACF,QAAQ,CAAC,CAAC;MACrE,CAAC,MACI;QACDN,OAAO,CAACO,SAAS,CAACC,GAAG,CAACP,YAAY,CAAC;MACvC;IACJ;IACA,IAAI,CAACQ,eAAe,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIA,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAG,IAAI,CAACvC,cAAc;IAC9B,MAAMwC,MAAM,GAAG,IAAI,CAAC/C,SAAS,CAACgD,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,KAAK,GAAGJ,MAAM,CAACE,CAAC,CAAC;MACvB,MAAMG,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAACpD,cAAc,CAAC2C,GAAG,CAACO,KAAK,CAAC;MAC9B,IAAI,CAACC,QAAQ,EAAE;QACXD,KAAK,CAACrB,YAAY,CAAC,WAAW,EAAEgB,EAAE,CAAC;MACvC,CAAC,MACI,IAAIM,QAAQ,CAACE,OAAO,CAACR,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCK,KAAK,CAACrB,YAAY,CAAC,WAAW,EAAEsB,QAAQ,GAAG,GAAG,GAAGN,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;EACAb,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAChC,cAAc,CAACwC,OAAO,CAACU,KAAK,IAAI;MACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAID,QAAQ,EAAE;QACV,MAAMG,QAAQ,GAAGH,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACjD,cAAc,EAAE,EAAE,CAAC,CAACkD,IAAI,CAAC,CAAC;QACjE,IAAIF,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;UACrBC,KAAK,CAACrB,YAAY,CAAC,WAAW,EAAEyB,QAAQ,CAAC;QAC7C,CAAC,MACI;UACDJ,KAAK,CAACO,eAAe,CAAC,WAAW,CAAC;QACtC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACzD,cAAc,CAAC0D,KAAK,CAAC,CAAC;EAC/B;EACA;EACAjD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACE,aAAa,CAACgD,WAAW,CAAC,CAAC,KAAK,OAAO1G,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrF,MAAM2G,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;EACIjC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACG,kBAAkB,EAAE;MAC1B,IAAI,CAACpC,OAAO,CAACmE,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAC/B,kBAAkB,GAAGrG,UAAU,CAAC,MAAM;UACvC,MAAMqI,YAAY,GAAG,IAAI,CAACnE,WAAW,CAACiC,aAAa,CAACmC,aAAa,CAAC,eAAe,CAAC;UAClF,MAAMC,WAAW,GAAG,IAAI,CAACrE,WAAW,CAACiC,aAAa,CAACmC,aAAa,CAAC,aAAa,CAAC;UAC/E,IAAID,YAAY,IAAIE,WAAW,EAAE;YAC7B;YACA;YACA,IAAIC,cAAc,GAAG,IAAI;YACzB,IAAI,IAAI,CAACpE,SAAS,CAACqE,SAAS,IACxBC,QAAQ,CAACC,aAAa,YAAYC,WAAW,IAC7CP,YAAY,CAACQ,QAAQ,CAACH,QAAQ,CAACC,aAAa,CAAC,EAAE;cAC/CH,cAAc,GAAGE,QAAQ,CAACC,aAAa;YAC3C;YACAN,YAAY,CAACL,eAAe,CAAC,aAAa,CAAC;YAC3CO,WAAW,CAACO,WAAW,CAACT,YAAY,CAAC;YACrCG,cAAc,EAAEO,KAAK,CAAC,CAAC;YACvB,IAAI,CAACpE,WAAW,CAAChF,IAAI,CAAC,CAAC;YACvB,IAAI,CAACgF,WAAW,CAAC/E,QAAQ,CAAC,CAAC;UAC/B;QACJ,CAAC,EAAE,IAAI,CAAC6E,cAAc,CAAC;MAC3B,CAAC,CAAC;IACN;EACJ;EACA;IAAS,IAAI,CAACzD,IAAI,YAAAgI,kCAAA9H,CAAA;MAAA,YAAAA,CAAA,IAAwF8C,yBAAyB,EAnUnCxJ,EAAE,CAAA6H,iBAAA,CAmUmD7H,EAAE,CAACyO,MAAM,GAnU9DzO,EAAE,CAAA6H,iBAAA,CAmUyE7H,EAAE,CAAC0O,UAAU,GAnUxF1O,EAAE,CAAA6H,iBAAA,CAmUmG7H,EAAE,CAAC2O,iBAAiB,GAnUzH3O,EAAE,CAAA6H,iBAAA,CAmUoI7F,EAAE,CAAC4M,QAAQ,GAnUjJ5O,EAAE,CAAA6H,iBAAA,CAmU4J3B,iBAAiB;IAAA,CAA4C;EAAE;EAC7T;IAAS,IAAI,CAACS,IAAI,kBApU8E3G,EAAE,CAAA4G,iBAAA;MAAAC,IAAA,EAoUJ2C,yBAAyB;MAAAqF,SAAA,WAAAC,gCAAAnM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApUvB3C,EAAE,CAAA+O,WAAA,CAoU8FnN,eAAe;QAAA;QAAA,IAAAe,EAAA;UAAA,IAAAqM,EAAA;UApU/GhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAAtM,GAAA,CAAA8H,aAAA,GAAAsE,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,QAAA,GAAFpP,EAAE,CAAAqP,0BAAA;IAAA,EAoUyL;EAAE;AACjS;AACA;EAAA,QAAArI,SAAA,oBAAAA,SAAA,KAtUoGhH,EAAE,CAAAiH,iBAAA,CAsUXuC,yBAAyB,EAAc,CAAC;IACvH3C,IAAI,EAAE3G;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE2G,IAAI,EAAE7G,EAAE,CAACyO;IAAO,CAAC,EAAE;MAAE5H,IAAI,EAAE7G,EAAE,CAAC0O;IAAW,CAAC,EAAE;MAAE7H,IAAI,EAAE7G,EAAE,CAAC2O;IAAkB,CAAC,EAAE;MAAE9H,IAAI,EAAE7E,EAAE,CAAC4M;IAAS,CAAC,EAAE;MAAE/H,IAAI,EAAEX;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEwE,aAAa,EAAE,CAAC;MAC1M7D,IAAI,EAAErG,SAAS;MACf0G,IAAI,EAAE,CAACtF,eAAe,EAAE;QAAE0N,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAAS/F,yBAAyB,CAAC;EACzD;EACAmB,oBAAoBA,CAAA,EAAG;IACnB,KAAK,CAACA,oBAAoB,CAAC,CAAC;IAC5B;IACA;IACA;IACA,MAAM6E,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC9D,aAAa;IACvC,MAAM+D,UAAU,GAAG,qBAAqB;IACxCF,KAAK,CAAC/C,SAAS,CAACkD,MAAM,CAACD,UAAU,EAAE,CAACF,KAAK,CAAC1B,aAAa,CAAE,IAAG4B,UAAW,EAAC,CAAC,CAAC;EAC9E;EACA;IAAS,IAAI,CAAClJ,IAAI;MAAA,IAAAoJ,iCAAA;MAAA,gBAAAC,6BAAAnJ,CAAA;QAAA,QAAAkJ,iCAAA,KAAAA,iCAAA,GA3V8E5P,EAAE,CAAA8P,qBAAA,CA2VQP,oBAAoB,IAAA7I,CAAA,IAApB6I,oBAAoB;MAAA;IAAA,GAAqD;EAAE;EACrL;IAAS,IAAI,CAACzH,IAAI,kBA5V8E9H,EAAE,CAAA+H,iBAAA;MAAAlB,IAAA,EA4VJ0I,oBAAoB;MAAAzI,SAAA;MAAA+H,SAAA,WAAAkB,2BAAApN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5VlB3C,EAAE,CAAA+O,WAAA,CAAAlL,GAAA;QAAA;QAAA,IAAAlB,EAAA;UAAA,IAAAqM,EAAA;UAAFhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAAtM,GAAA,CAAA6M,MAAA,GAAAT,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAApI,SAAA;MAAAiJ,QAAA;MAAAC,YAAA,WAAAC,kCAAAvN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3C,EAAE,CAAAmQ,uBAAA,yBAAAC,6DAAAC,MAAA;YAAA,OA4VJzN,GAAA,CAAAqI,cAAA,CAAAoF,MAAqB,CAAC;UAAA;QAAA;QAAA,IAAA1N,EAAA;UA5VpB3C,EAAE,CAAAsQ,uBAAA,WAAA1N,GAAA,CAAAwH,eAAA;QAAA;MAAA;MAAAgF,QAAA,GAAFpP,EAAE,CAAAqP,0BAAA;MAAApH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmI,8BAAA5N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3C,EAAE,CAAA+C,cAAA,YA4Vwb,CAAC,eAAD,CAAC,YAAD,CAAC;UA5V3b/C,EAAE,CAAAsI,UAAA,IAAAxE,2CAAA,wBA4V+zB,CAAC;UA5Vl0B9D,EAAE,CAAAwD,YAAA,CA4V20B,CAAC;UA5V90BxD,EAAE,CAAAwQ,SAAA,SA4ViiC,CAAC;UA5VpiCxQ,EAAE,CAAAwD,YAAA,CA4V2iC,CAAC,CAAD,CAAC;QAAA;QAAA,IAAAb,EAAA;UA5V9iC3C,EAAE,CAAA0D,SAAA,EA4Vu+B,CAAC;UA5V1+B1D,EAAE,CAAAyQ,WAAA,cAAA7N,GAAA,CAAAgI,KA4Vu+B,CAAC,SAAAhI,GAAA,CAAAkI,KAAD,CAAC,OAAAlI,GAAA,CAAAyH,cAAD,CAAC;QAAA;MAAA;MAAA5B,YAAA,GAA2iH/G,IAAI,CAACE,eAAe;MAAAgH,MAAA;MAAAC,aAAA;MAAAjF,IAAA;QAAA8M,SAAA,EAAmI,CAACvH,qBAAqB,CAACC,aAAa;MAAC;IAAA,EAAkG;EAAE;AACz5J;AACA;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KA9VoGhH,EAAE,CAAAiH,iBAAA,CA8VXsI,oBAAoB,EAAc,CAAC;IAClH1I,IAAI,EAAE1G,SAAS;IACf+G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAE2B,eAAe,EAAEzI,uBAAuB,CAACsQ,OAAO;MAAE9H,aAAa,EAAEzI,iBAAiB,CAAC2I,IAAI;MAAE6H,UAAU,EAAE,CAACzH,qBAAqB,CAACC,aAAa,CAAC;MAAEhC,IAAI,EAAE;QACpL,OAAO,EAAE,6DAA6D;QACtE,UAAU,EAAE,iBAAiB;QAC7B,eAAe,EAAE;MACrB,CAAC;MAAEgB,QAAQ,EAAE,oqBAAoqB;MAAEQ,MAAM,EAAE,CAAC,o6GAAo6G;IAAE,CAAC;EAC/mI,CAAC,CAAC,QAAkB;IAAE6G,MAAM,EAAE,CAAC;MACvB5I,IAAI,EAAErG,SAAS;MACf0G,IAAI,EAAE,CAAC,OAAO,EAAE;QAAEoI,MAAM,EAAE;MAAK,CAAC;IACpC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuB,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACrK,IAAI,YAAAsK,0BAAApK,CAAA;MAAA,YAAAA,CAAA,IAAwFmK,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACE,IAAI,kBA5W8E/Q,EAAE,CAAAgR,gBAAA;MAAAnK,IAAA,EA4WSgK;IAAiB,EAQ/F;EAAE;EAC/B;IAAS,IAAI,CAACI,IAAI,kBArX8EjR,EAAE,CAAAkR,gBAAA;MAAAC,OAAA,GAqXsC5O,aAAa,EAAEV,YAAY,EAAEX,YAAY,EAAEE,eAAe,EAAEqB,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC9O;AACA;EAAA,QAAAuE,SAAA,oBAAAA,SAAA,KAvXoGhH,EAAE,CAAAiH,iBAAA,CAuXX4J,iBAAiB,EAAc,CAAC;IAC/GhK,IAAI,EAAEpG,QAAQ;IACdyG,IAAI,EAAE,CAAC;MACCiK,OAAO,EAAE,CAAC5O,aAAa,EAAEV,YAAY,EAAEX,YAAY,EAAEE,eAAe,EAAEqB,eAAe,CAAC;MACtF2O,OAAO,EAAE,CACL3O,eAAe,EACf8M,oBAAoB,EACpBhJ,gBAAgB,EAChBc,kBAAkB,EAClBE,iBAAiB,CACpB;MACD8J,YAAY,EAAE,CACV5J,cAAc,EACd8H,oBAAoB,EACpBhJ,gBAAgB,EAChBc,kBAAkB,EAClBE,iBAAiB;IAEzB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,SAAS+J,qCAAqCA,CAAA,EAAG;EAC7C,OAAO,IAAIpL,iBAAiB,CAAC,CAAC;AAClC;AACA;AACA,MAAMqL,6BAA6B,GAAG,IAAItR,cAAc,CAAC,+BAA+B,EAAE;EACtFuR,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF,MAAMI,gBAAgB,CAAC;EACnB;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe;IACnC,OAAOD,MAAM,GAAGA,MAAM,CAACD,kBAAkB,GAAG,IAAI,CAACG,uBAAuB;EAC5E;EACA,IAAIH,kBAAkBA,CAACI,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACF,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACF,kBAAkB,GAAGI,KAAK;IACnD,CAAC,MACI;MACD,IAAI,CAACD,uBAAuB,GAAGC,KAAK;IACxC;EACJ;EACA5N,WAAWA,CAAC6N,QAAQ,EAAEpH,KAAK,EAAEqH,SAAS,EAAEC,mBAAmB,EAAEL,eAAe,EAAEM,cAAc,EAAE;IAC1F,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACpH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACL,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACM,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACL,uBAAuB,GAAG,IAAI;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,iBAAiBA,CAACC,SAAS,EAAEC,MAAM,EAAE;IACjC,OAAO,IAAI,CAACC,OAAO,CAACF,SAAS,EAAEC,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,gBAAgBA,CAACpK,QAAQ,EAAEkK,MAAM,EAAE;IAC/B,OAAO,IAAI,CAACC,OAAO,CAACnK,QAAQ,EAAEkK,MAAM,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,IAAIA,CAAClK,OAAO,EAAEjF,MAAM,GAAG,EAAE,EAAEgP,MAAM,EAAE;IAC/B,MAAMI,OAAO,GAAG;MAAE,GAAG,IAAI,CAACP,cAAc;MAAE,GAAGG;IAAO,CAAC;IACrD;IACA;IACAI,OAAO,CAAC9O,IAAI,GAAG;MAAE2E,OAAO;MAAEjF;IAAO,CAAC;IAClC;IACA;IACA,IAAIoP,OAAO,CAACtM,mBAAmB,KAAKmC,OAAO,EAAE;MACzCmK,OAAO,CAACtM,mBAAmB,GAAG6C,SAAS;IAC3C;IACA,OAAO,IAAI,CAACmJ,iBAAiB,CAAC,IAAI,CAACO,uBAAuB,EAAED,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACI7N,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC8M,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAAC9M,OAAO,CAAC,CAAC;IACrC;EACJ;EACAiH,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,IAAI,CAACgG,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACjN,OAAO,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;EACI+N,wBAAwBA,CAACC,UAAU,EAAEP,MAAM,EAAE;IACzC,MAAMQ,YAAY,GAAGR,MAAM,IAAIA,MAAM,CAACS,gBAAgB,IAAIT,MAAM,CAACS,gBAAgB,CAACC,QAAQ;IAC1F,MAAMA,QAAQ,GAAGtS,QAAQ,CAACuS,MAAM,CAAC;MAC7BrB,MAAM,EAAEkB,YAAY,IAAI,IAAI,CAACb,SAAS;MACtCiB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEjN,iBAAiB;QAAEkN,QAAQ,EAAEd;MAAO,CAAC;IAChE,CAAC,CAAC;IACF,MAAMe,eAAe,GAAG,IAAIvR,eAAe,CAAC,IAAI,CAACwR,0BAA0B,EAAEhB,MAAM,CAACS,gBAAgB,EAAEC,QAAQ,CAAC;IAC/G,MAAMO,YAAY,GAAGV,UAAU,CAACW,MAAM,CAACH,eAAe,CAAC;IACvDE,YAAY,CAACE,QAAQ,CAAC5J,cAAc,GAAGyI,MAAM;IAC7C,OAAOiB,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;EACIlB,OAAOA,CAACmB,OAAO,EAAEC,UAAU,EAAE;IACzB,MAAMrB,MAAM,GAAG;MAAE,GAAG,IAAIpM,iBAAiB,CAAC,CAAC;MAAE,GAAG,IAAI,CAACiM,cAAc;MAAE,GAAGwB;IAAW,CAAC;IACpF,MAAMd,UAAU,GAAG,IAAI,CAACe,cAAc,CAACtB,MAAM,CAAC;IAC9C,MAAMuB,SAAS,GAAG,IAAI,CAACjB,wBAAwB,CAACC,UAAU,EAAEP,MAAM,CAAC;IACnE,MAAM5K,WAAW,GAAG,IAAIxD,cAAc,CAAC2P,SAAS,EAAEhB,UAAU,CAAC;IAC7D,IAAIa,OAAO,YAAY/S,WAAW,EAAE;MAChC,MAAM4J,MAAM,GAAG,IAAIxI,cAAc,CAAC2R,OAAO,EAAE,IAAI,EAAE;QAC7CI,SAAS,EAAExB,MAAM,CAAC1O,IAAI;QACtB8D;MACJ,CAAC,CAAC;MACFA,WAAW,CAAC+L,QAAQ,GAAGI,SAAS,CAAC7I,oBAAoB,CAACT,MAAM,CAAC;IACjE,CAAC,MACI;MACD,MAAMyI,QAAQ,GAAG,IAAI,CAACe,eAAe,CAACzB,MAAM,EAAE5K,WAAW,CAAC;MAC1D,MAAM6C,MAAM,GAAG,IAAIzI,eAAe,CAAC4R,OAAO,EAAEzK,SAAS,EAAE+J,QAAQ,CAAC;MAChE,MAAMgB,UAAU,GAAGH,SAAS,CAAC9I,qBAAqB,CAACR,MAAM,CAAC;MAC1D;MACA7C,WAAW,CAAC+L,QAAQ,GAAGO,UAAU,CAACP,QAAQ;IAC9C;IACA;IACA;IACA;IACA,IAAI,CAACvB,mBAAmB,CACnB+B,OAAO,CAAC5R,WAAW,CAAC6R,eAAe,CAAC,CACpCjI,IAAI,CAAC/J,SAAS,CAAC2Q,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,CAAC,CACzCxP,SAAS,CAACrD,KAAK,IAAI;MACpBuR,UAAU,CAACuB,cAAc,CAAC3H,SAAS,CAACkD,MAAM,CAAC,IAAI,CAAC0E,eAAe,EAAE/S,KAAK,CAACgT,OAAO,CAAC;IACnF,CAAC,CAAC;IACF,IAAIhC,MAAM,CAAClM,mBAAmB,EAAE;MAC5B;MACAyN,SAAS,CAAC1J,WAAW,CAACxF,SAAS,CAAC,MAAM;QAClC,IAAI,CAACiG,KAAK,CAAC2J,QAAQ,CAACjC,MAAM,CAAClM,mBAAmB,EAAEkM,MAAM,CAACnM,UAAU,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAI,CAACqO,gBAAgB,CAAC9M,WAAW,EAAE4K,MAAM,CAAC;IAC1C,IAAI,CAACX,kBAAkB,GAAGjK,WAAW;IACrC,OAAO,IAAI,CAACiK,kBAAkB;EAClC;EACA;EACA6C,gBAAgBA,CAAC9M,WAAW,EAAE4K,MAAM,EAAE;IAClC;IACA5K,WAAW,CAAC7B,cAAc,CAAC,CAAC,CAAClB,SAAS,CAAC,MAAM;MACzC;MACA,IAAI,IAAI,CAACgN,kBAAkB,IAAIjK,WAAW,EAAE;QACxC,IAAI,CAACiK,kBAAkB,GAAG,IAAI;MAClC;MACA,IAAIW,MAAM,CAAClM,mBAAmB,EAAE;QAC5B,IAAI,CAACwE,KAAK,CAAC6C,KAAK,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACkE,kBAAkB,EAAE;MACzB;MACA;MACA,IAAI,CAACA,kBAAkB,CAAC9L,cAAc,CAAC,CAAC,CAAClB,SAAS,CAAC,MAAM;QACrD+C,WAAW,CAACtD,iBAAiB,CAACoH,KAAK,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACmG,kBAAkB,CAAC9M,OAAO,CAAC,CAAC;IACrC,CAAC,MACI;MACD;MACA6C,WAAW,CAACtD,iBAAiB,CAACoH,KAAK,CAAC,CAAC;IACzC;IACA;IACA,IAAI8G,MAAM,CAAC/M,QAAQ,IAAI+M,MAAM,CAAC/M,QAAQ,GAAG,CAAC,EAAE;MACxCmC,WAAW,CAAC5B,WAAW,CAAC,CAAC,CAACnB,SAAS,CAAC,MAAM+C,WAAW,CAACpC,aAAa,CAACgN,MAAM,CAAC/M,QAAQ,CAAC,CAAC;IACzF;EACJ;EACA;AACJ;AACA;AACA;EACIqO,cAAcA,CAACtB,MAAM,EAAE;IACnB,MAAMmC,aAAa,GAAG,IAAIjS,aAAa,CAAC,CAAC;IACzCiS,aAAa,CAACC,SAAS,GAAGpC,MAAM,CAACoC,SAAS;IAC1C,IAAIC,gBAAgB,GAAG,IAAI,CAAC3C,QAAQ,CAAC4C,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACxD;IACA,MAAMC,KAAK,GAAGxC,MAAM,CAACoC,SAAS,KAAK,KAAK;IACxC,MAAMK,MAAM,GAAGzC,MAAM,CAACjM,kBAAkB,KAAK,MAAM,IAC9CiM,MAAM,CAACjM,kBAAkB,KAAK,OAAO,IAAI,CAACyO,KAAM,IAChDxC,MAAM,CAACjM,kBAAkB,KAAK,KAAK,IAAIyO,KAAM;IAClD,MAAME,OAAO,GAAG,CAACD,MAAM,IAAIzC,MAAM,CAACjM,kBAAkB,KAAK,QAAQ;IACjE,IAAI0O,MAAM,EAAE;MACRJ,gBAAgB,CAACM,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAC,MACI,IAAID,OAAO,EAAE;MACdL,gBAAgB,CAACO,KAAK,CAAC,GAAG,CAAC;IAC/B,CAAC,MACI;MACDP,gBAAgB,CAACQ,kBAAkB,CAAC,CAAC;IACzC;IACA;IACA,IAAI7C,MAAM,CAAChM,gBAAgB,KAAK,KAAK,EAAE;MACnCqO,gBAAgB,CAACS,GAAG,CAAC,GAAG,CAAC;IAC7B,CAAC,MACI;MACDT,gBAAgB,CAACU,MAAM,CAAC,GAAG,CAAC;IAChC;IACAZ,aAAa,CAACE,gBAAgB,GAAGA,gBAAgB;IACjD,OAAO,IAAI,CAAC3C,QAAQ,CAACiB,MAAM,CAACwB,aAAa,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIV,eAAeA,CAACzB,MAAM,EAAE5K,WAAW,EAAE;IACjC,MAAMoL,YAAY,GAAGR,MAAM,IAAIA,MAAM,CAACS,gBAAgB,IAAIT,MAAM,CAACS,gBAAgB,CAACC,QAAQ;IAC1F,OAAOtS,QAAQ,CAACuS,MAAM,CAAC;MACnBrB,MAAM,EAAEkB,YAAY,IAAI,IAAI,CAACb,SAAS;MACtCiB,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEjP,cAAc;QAAEkP,QAAQ,EAAE1L;MAAY,CAAC,EAClD;QAAEyL,OAAO,EAAElN,kBAAkB;QAAEmN,QAAQ,EAAEd,MAAM,CAAC1O;MAAK,CAAC;IAE9D,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC4C,IAAI,YAAA8O,yBAAA5O,CAAA;MAAA,YAAAA,CAAA,IAAwFgL,gBAAgB,EAzmB1B1R,EAAE,CAAAuV,QAAA,CAymB0CjT,IAAI,CAACkT,OAAO,GAzmBxDxV,EAAE,CAAAuV,QAAA,CAymBmEpT,IAAI,CAACsT,aAAa,GAzmBvFzV,EAAE,CAAAuV,QAAA,CAymBkGvV,EAAE,CAACU,QAAQ,GAzmB/GV,EAAE,CAAAuV,QAAA,CAymB0HnT,IAAI,CAACsT,kBAAkB,GAzmBnJ1V,EAAE,CAAAuV,QAAA,CAymB8J7D,gBAAgB,OAzmBhL1R,EAAE,CAAAuV,QAAA,CAymB2NhE,6BAA6B;IAAA,CAA6C;EAAE;EACzY;IAAS,IAAI,CAACoE,KAAK,kBA1mB6E3V,EAAE,CAAA4V,kBAAA;MAAAC,KAAA,EA0mBYnE,gBAAgB;MAAAD,OAAA,EAAhBC,gBAAgB,CAAAlL;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA5mBoGhH,EAAE,CAAAiH,iBAAA,CA4mBXyK,gBAAgB,EAAc,CAAC;IAC9G7K,IAAI,EAAEjG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiG,IAAI,EAAEvE,IAAI,CAACkT;IAAQ,CAAC,EAAE;MAAE3O,IAAI,EAAE1E,IAAI,CAACsT;IAAc,CAAC,EAAE;MAAE5O,IAAI,EAAE7G,EAAE,CAACU;IAAS,CAAC,EAAE;MAAEmG,IAAI,EAAEzE,IAAI,CAACsT;IAAmB,CAAC,EAAE;MAAE7O,IAAI,EAAE6K,gBAAgB;MAAExI,UAAU,EAAE,CAAC;QACrLrC,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAE/F;MACV,CAAC;IAAE,CAAC,EAAE;MAAE+F,IAAI,EAAEX,iBAAiB;MAAEgD,UAAU,EAAE,CAAC;QAC1CrC,IAAI,EAAEvG,MAAM;QACZ4G,IAAI,EAAE,CAACqK,6BAA6B;MACxC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,MAAMuE,WAAW,SAASpE,gBAAgB,CAAC;EACvCvN,WAAWA,CAAC4R,OAAO,EAAEC,IAAI,EAAEhD,QAAQ,EAAEiD,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,EAAE;IACpF,KAAK,CAACJ,OAAO,EAAEC,IAAI,EAAEhD,QAAQ,EAAEiD,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,CAAC;IACjF,IAAI,CAACxD,uBAAuB,GAAGlL,cAAc;IAC7C,IAAI,CAAC6L,0BAA0B,GAAG/D,oBAAoB;IACtD,IAAI,CAAC8E,eAAe,GAAG,2BAA2B;EACtD;EACA;IAAS,IAAI,CAAC7N,IAAI,YAAA4P,oBAAA1P,CAAA;MAAA,YAAAA,CAAA,IAAwFoP,WAAW,EAhoBrB9V,EAAE,CAAAuV,QAAA,CAgoBqCjT,IAAI,CAACkT,OAAO,GAhoBnDxV,EAAE,CAAAuV,QAAA,CAgoB8DpT,IAAI,CAACsT,aAAa,GAhoBlFzV,EAAE,CAAAuV,QAAA,CAgoB6FvV,EAAE,CAACU,QAAQ,GAhoB1GV,EAAE,CAAAuV,QAAA,CAgoBqHnT,IAAI,CAACsT,kBAAkB,GAhoB9I1V,EAAE,CAAAuV,QAAA,CAgoByJO,WAAW,OAhoBtK9V,EAAE,CAAAuV,QAAA,CAgoBiNhE,6BAA6B;IAAA,CAA6C;EAAE;EAC/X;IAAS,IAAI,CAACoE,KAAK,kBAjoB6E3V,EAAE,CAAA4V,kBAAA;MAAAC,KAAA,EAioBYC,WAAW;MAAArE,OAAA,EAAXqE,WAAW,CAAAtP,IAAA;MAAAgL,UAAA,EAAcX;IAAiB,EAAG;EAAE;AACjK;AACA;EAAA,QAAA7J,SAAA,oBAAAA,SAAA,KAnoBoGhH,EAAE,CAAAiH,iBAAA,CAmoBX6O,WAAW,EAAc,CAAC;IACzGjP,IAAI,EAAEjG,UAAU;IAChBsG,IAAI,EAAE,CAAC;MAAEsK,UAAU,EAAEX;IAAkB,CAAC;EAC5C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhK,IAAI,EAAEvE,IAAI,CAACkT;IAAQ,CAAC,EAAE;MAAE3O,IAAI,EAAE1E,IAAI,CAACsT;IAAc,CAAC,EAAE;MAAE5O,IAAI,EAAE7G,EAAE,CAACU;IAAS,CAAC,EAAE;MAAEmG,IAAI,EAAEzE,IAAI,CAACsT;IAAmB,CAAC,EAAE;MAAE7O,IAAI,EAAEiP,WAAW;MAAE5M,UAAU,EAAE,CAAC;QAChLrC,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAE/F;MACV,CAAC;IAAE,CAAC,EAAE;MAAE+F,IAAI,EAAEX,iBAAiB;MAAEgD,UAAU,EAAE,CAAC;QAC1CrC,IAAI,EAAEvG,MAAM;QACZ4G,IAAI,EAAE,CAACqK,6BAA6B;MACxC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;;AAEA,SAAStL,kBAAkB,EAAEsL,6BAA6B,EAAED,qCAAqC,EAAEwE,WAAW,EAAEvO,iBAAiB,EAAEF,kBAAkB,EAAEnB,iBAAiB,EAAEqJ,oBAAoB,EAAEhJ,gBAAgB,EAAEsK,iBAAiB,EAAE3M,cAAc,EAAEuD,cAAc,EAAEiK,gBAAgB,EAAElI,yBAAyB,EAAEL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}