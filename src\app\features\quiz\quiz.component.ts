import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core"
import { ActivatedRoute, Router } from "@angular/router"
import { QuizService } from "../../core/services/quiz.service"
import { MatSnackBar } from "@angular/material/snack-bar"
import { Quiz } from "../../core/models/course.model"
import { ResultatQuiz } from "../../core/models/resultat-quiz.model"
import { AuthService } from "../../core/services/auth.service"
import { interval, Subscription } from "rxjs"
import { User } from "../../core/models/user.model" // Import User model

@Component({
  selector: "app-quiz",
  template: `
    <div class="quiz-container">
      <div class="quiz-wrapper" *ngIf="quiz && !showResults; else resultsOrLoading">
        <!-- Header -->
        <div class="quiz-header">
          <div class="title-row">
            <h1>{{ quiz.titre }}</h1>
            <div class="timer">
              <mat-icon>timer</mat-icon>
              <span class="time-display">{{ formatTime(timeRemaining) }}</span>
            </div>
          </div>

          <div class="progress-row">
            <span class="question-count">Question {{ currentQuestionIndex + 1 }} sur {{ quiz.questions.length }}</span>
            <span class="threshold">Seuil de réussite: {{ quiz.seuilReussite }}%</span>
          </div>
          <mat-progress-bar mode="determinate" [value]="progress"></mat-progress-bar>
        </div>

        <!-- Question Card -->
        <mat-card class="question-card">
          <mat-card-title>{{ quiz.questions[currentQuestionIndex].texte }}</mat-card-title>
          <mat-card-content>
            <mat-radio-group 
              aria-label="Sélectionnez une option" 
              [(ngModel)]="selectedAnswers[quiz.questions[currentQuestionIndex].id]"
              class="options-group">
              <mat-radio-button 
                *ngFor="let option of quiz.questions[currentQuestionIndex].options; let i = index" 
                [value]="i" 
                class="option-item">
                {{ option }}
              </mat-radio-button>
            </mat-radio-group>
          </mat-card-content>
        </mat-card>

        <!-- Navigation Buttons -->
        <div class="navigation-buttons">
          <button mat-stroked-button (click)="previousQuestion()" [disabled]="currentQuestionIndex === 0">
            Question précédente
          </button>

          <button mat-raised-button color="primary" 
                  (click)="nextQuestion()" 
                  [disabled]="selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined"
                  *ngIf="currentQuestionIndex < quiz.questions.length - 1">
            Question suivante
          </button>

          <button mat-raised-button color="accent" 
                  (click)="submitQuiz()" 
                  [disabled]="selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined || isLoading"
                  *ngIf="currentQuestionIndex === quiz.questions.length - 1">
            <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
            <span *ngIf="!isLoading">Terminer le quiz</span>
          </button>
        </div>

        <!-- Questions Overview -->
        <mat-card class="overview-card">
          <mat-card-title>Aperçu des questions</mat-card-title>
          <mat-card-content>
            <div class="question-dots">
              <button mat-mini-fab *ngFor="let q of quiz.questions; let i = index" 
                      [ngClass]="{
                        'current': i === currentQuestionIndex,
                        'answered': selectedAnswers[q.id] !== undefined && i !== currentQuestionIndex
                      }"
                      (click)="goToQuestion(i)">
                {{ i + 1 }}
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <ng-template #resultsOrLoading>
        <div *ngIf="isLoading" class="loading-spinner">
          <mat-spinner></mat-spinner>
          <p>Chargement du quiz...</p>
        </div>
        <div *ngIf="!isLoading && showResults && results" class="results-container">
          <mat-card class="results-card">
            <mat-card-header class="results-header">
              <div class="result-icon-wrapper" [ngClass]="{'success': results.reussi, 'fail': !results.reussi}">
                <mat-icon *ngIf="results.reussi">check_circle</mat-icon>
                <mat-icon *ngIf="!results.reussi">cancel</mat-icon>
              </div>
              <mat-card-title>{{ results.reussi ? 'Félicitations !' : 'Quiz non réussi' }}</mat-card-title>
              <mat-card-subtitle>
                {{ results.reussi ? 'Vous avez réussi le quiz avec succès !' : 'Il vous faut ' + quiz.seuilReussite + '% pour réussir ce quiz.' }}
              </mat-card-subtitle>
            </mat-card-header>

            <mat-card-content class="results-content">
              <div class="score-display">
                <span class="percentage">{{ results.pourcentage }}%</span>
                <p class="score-count">{{ results.score }} sur {{ results.totalQuestions }} questions correctes</p>
              </div>

              <mat-progress-bar mode="determinate" [value]="results.pourcentage" class="results-progress"></mat-progress-bar>

              <div class="certificate-info" *ngIf="results.reussi">
                <mat-icon>emoji_events</mat-icon>
                <p>Un certificat sera généré automatiquement !</p>
              </div>

              <div class="results-actions">
                <button mat-stroked-button [routerLink]="['/courses', quiz.coursId]">
                  Retour au cours
                </button>
                <button mat-raised-button color="primary" *ngIf="!results.reussi" (click)="restartQuiz()">
                  Recommencer
                </button>
              </div>

              <div class="answers-detail">
                <h3>Détail des réponses :</h3>
                <div *ngFor="let question of quiz.questions; let i = index" class="answer-item">
                  <div class="answer-header">
                    <h4>Question {{ i + 1 }}</h4>
                    <mat-icon *ngIf="results.reponses[i].estCorrecte" class="correct-icon">check_circle</mat-icon>
                    <mat-icon *ngIf="!results.reponses[i].estCorrecte" class="incorrect-icon">cancel</mat-icon>
                  </div>
                  <p class="question-text">{{ question.texte }}</p>
                  <div class="options-detail">
                    <div *ngFor="let option of question.options; let j = index" 
                         [ngClass]="{
                           'correct-option': j === question.bonneReponse,
                           'user-incorrect-option': j === results.reponses[i].reponseChoisie && !results.reponses[i].estCorrecte
                         }"
                         class="option-detail-item">
                      {{ option }}
                      <span *ngIf="j === question.bonneReponse" class="correct-label">✓ Bonne réponse</span>
                      <span *ngIf="j === results.reponses[i].reponseChoisie && !results.reponses[i].estCorrecte" class="incorrect-label">✗ Votre réponse</span>
                    </div>
                  </div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </ng-template>
    </div>
  `,
  styles: [
    `
    .quiz-container {
      min-height: 100vh;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
    }

    .quiz-wrapper, .results-container {
      width: 100%;
      max-width: 800px;
    }

    .loading-spinner {
      text-align: center;
      padding: 4rem;
    }

    .quiz-header {
      margin-bottom: 2rem;
      background-color: #fff;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .title-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .quiz-header h1 {
      font-size: 1.8rem;
      font-weight: bold;
      color: #333;
      margin: 0;
    }

    .timer {
      display: flex;
      align-items: center;
      color: #d32f2f; /* Red */
      font-weight: 500;
    }

    .timer mat-icon {
      margin-right: 0.3rem;
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    .time-display {
      font-size: 1.5rem;
      font-family: 'monospace';
    }

    .progress-row {
      display: flex;
      justify-content: space-between;
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 0.8rem;
    }

    mat-progress-bar {
      height: 8px;
      border-radius: 4px;
    }

    .question-card {
      margin-bottom: 2rem;
      padding: 1.5rem;
    }

    .question-card mat-card-title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #333;
    }

    .options-group {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .option-item {
      padding: 0.8rem 1rem;
      border: 1px solid #ddd;
      border-radius: 8px;
      background-color: #fff;
      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    }

    .option-item:hover {
      background-color: #f5f5f5;
      border-color: #bbb;
    }

    .option-item.mat-radio-checked {
      border-color: #673ab7; /* Purple */
      background-color: #ede7f6; /* Light purple */
    }

    .navigation-buttons {
      display: flex;
      justify-content: space-between;
      margin-bottom: 2rem;
    }

    .navigation-buttons button {
      padding: 0.8rem 1.5rem;
      font-size: 1rem;
    }

    .overview-card {
      padding: 1.5rem;
    }

    .overview-card mat-card-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .question-dots {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .question-dots button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #e0e0e0; /* Light gray */
      color: #666;
      font-weight: 500;
      transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    }

    .question-dots button.current {
      background-color: #673ab7; /* Purple */
      color: white;
    }

    .question-dots button.answered {
      background-color: #c8e6c9; /* Light green */
      color: #388e3c; /* Dark green */
    }

    /* Results styles */
    .results-card {
      padding: 2rem;
      text-align: center;
    }

    .results-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .result-icon-wrapper {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
    }

    .result-icon-wrapper mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
    }

    .result-icon-wrapper.success {
      background-color: #e8f5e9; /* Light green */
    }
    .result-icon-wrapper.success mat-icon {
      color: #4caf50; /* Green */
    }

    .result-icon-wrapper.fail {
      background-color: #ffebee; /* Light red */
    }
    .result-icon-wrapper.fail mat-icon {
      color: #f44336; /* Red */
    }

    .results-card mat-card-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .results-card mat-card-subtitle {
      font-size: 1rem;
      color: #666;
    }

    .score-display {
      margin-bottom: 1.5rem;
    }

    .percentage {
      font-size: 3.5rem;
      font-weight: bold;
      color: #673ab7; /* Purple */
    }

    .score-count {
      font-size: 1rem;
      color: #666;
    }

    .results-progress {
      margin-bottom: 1.5rem;
    }

    .certificate-info {
      background-color: #e8f5e9;
      border: 1px solid #c8e6c9;
      border-radius: 8px;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.8rem;
      color: #388e3c;
      font-weight: 500;
      margin-bottom: 2rem;
    }

    .certificate-info mat-icon {
      font-size: 1.8rem;
      width: 1.8rem;
      height: 1.8rem;
    }

    .results-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 2rem;
    }

    .results-actions button {
      padding: 0.8rem 1.5rem;
      font-size: 1rem;
    }

    .answers-detail {
      text-align: left;
      margin-top: 2rem;
      border-top: 1px solid #eee;
      padding-top: 2rem;
    }

    .answers-detail h3 {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #333;
    }

    .answer-item {
      border: 1px solid #eee;
      border-radius: 8px;
      padding: 1.2rem;
      margin-bottom: 1rem;
      background-color: #fff;
    }

    .answer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.8rem;
    }

    .answer-header h4 {
      font-size: 1.1rem;
      font-weight: 500;
      color: #444;
      margin: 0;
    }

    .correct-icon { color: #4caf50; }
    .incorrect-icon { color: #f44336; }

    .question-text {
      font-size: 1rem;
      color: #333;
      margin-bottom: 1rem;
    }

    .options-detail {
      display: flex;
      flex-direction: column;
      gap: 0.6rem;
    }

    .option-detail-item {
      padding: 0.6rem 1rem;
      border-radius: 6px;
      font-size: 0.9rem;
      color: #555;
      background-color: #f9f9f9;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .correct-option {
      background-color: #e8f5e9;
      color: #388e3c;
      border: 1px solid #c8e6c9;
    }

    .user-incorrect-option {
      background-color: #ffebee;
      color: #d32f2f;
      border: 1px solid #ffcdd2;
    }

    .correct-label {
      color: #388e3c;
      font-weight: 500;
    }

    .incorrect-label {
      color: #d32f2f;
      font-weight: 500;
    }
  `,
  ],
})
export class QuizComponent implements OnInit, OnDestroy {
  quizId!: number
  quiz!: Quiz
  currentQuestionIndex = 0
  selectedAnswers: { [key: number]: number } = {} // { questionId: selectedOptionIndex }
  showResults = false
  results: ResultatQuiz | null = null
  isLoading = true
  timeRemaining = 0
  timerSubscription!: Subscription
  currentUser!: User | null

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private quizService: QuizService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user
    })

    this.route.paramMap.subscribe((params) => {
      this.quizId = Number(params.get("id"))
      this.loadQuiz()
    })
  }

  ngOnDestroy(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe()
    }
  }

  loadQuiz(): void {
    this.isLoading = true
    // Mock data for demonstration
    this.quiz = {
      id: this.quizId,
      titre: "Quiz - Bases de React",
      description: "Testez vos connaissances sur les concepts fondamentaux de React",
      seuilReussite: 70,
      dureeEstimee: 15, // in minutes
      coursId: 1,
      typeContenu: "Quiz",
      estComplete: false,
      estDebloque: true,
      ordre: 3,
      questions: [
        {
          id: 1,
          texte: "Qu'est-ce que React ?",
          options: [
            "Un framework CSS",
            "Une bibliothèque JavaScript pour créer des interfaces utilisateur",
            "Un serveur web",
            "Un langage de programmation",
          ],
          bonneReponse: 1,
        },
        {
          id: 2,
          texte: "Que sont les props en React ?",
          options: [
            "Des propriétés passées aux composants",
            "Des méthodes de classe",
            "Des variables globales",
            "Des styles CSS",
          ],
          bonneReponse: 0,
        },
        {
          id: 3,
          texte: "Comment créer un composant fonctionnel en React ?",
          options: [
            "class MyComponent extends React.Component",
            "function MyComponent() { return <div></div>; }",
            "const MyComponent = React.createClass()",
            "React.component('MyComponent')",
          ],
          bonneReponse: 1,
        },
        {
          id: 4,
          texte: "Qu'est-ce que le JSX ?",
          options: [
            "Un nouveau langage de programmation",
            "Une extension de syntaxe JavaScript",
            "Un framework CSS",
            "Une base de données",
          ],
          bonneReponse: 1,
        },
        {
          id: 5,
          texte: "Comment gérer l'état dans un composant fonctionnel ?",
          options: ["Avec this.state", "Avec le hook useState", "Avec des variables globales", "Avec localStorage"],
          bonneReponse: 1,
        },
      ],
    }
    this.timeRemaining = this.quiz.dureeEstimee * 60 // Convert minutes to seconds
    this.startTimer()
    this.isLoading = false

    // Uncomment to fetch from API
    /*
    this.quizService.getQuizById(this.quizId).subscribe({
      next: (data) => {
        this.quiz = data;
        this.timeRemaining = this.quiz.dureeEstimee * 60;
        this.startTimer();
        this.isLoading = false;
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement du quiz.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.isLoading = false;
        this.router.navigate(['/courses']);
      }
    });
    */
  }

  startTimer(): void {
    this.timerSubscription = interval(1000).subscribe(() => {
      if (this.timeRemaining > 0 && !this.showResults) {
        this.timeRemaining--
      } else if (this.timeRemaining === 0 && !this.showResults) {
        this.submitQuiz()
        this.timerSubscription.unsubscribe()
      }
    })
  }

  previousQuestion(): void {
    if (this.currentQuestionIndex > 0) {
      this.currentQuestionIndex--
    }
  }

  nextQuestion(): void {
    if (this.currentQuestionIndex < this.quiz.questions.length - 1) {
      this.currentQuestionIndex++
    }
  }

  goToQuestion(index: number): void {
    this.currentQuestionIndex = index
  }

  submitQuiz(): void {
    if (!this.quiz || !this.currentUser) return

    this.isLoading = true
    this.timerSubscription.unsubscribe()

    let score = 0
    const reponses: { questionId: number; reponseChoisie: number; estCorrecte: boolean }[] = []

    this.quiz.questions.forEach((question) => {
      const selectedAnswer = this.selectedAnswers[question.id]
      const isCorrect = selectedAnswer === question.bonneReponse
      if (isCorrect) {
        score++
      }
      reponses.push({
        questionId: question.id,
        reponseChoisie: selectedAnswer ?? -1,
        estCorrecte: isCorrect,
      })
    })

    const pourcentage = Math.round((score / this.quiz.questions.length) * 100)
    const reussi = pourcentage >= (this.quiz.seuilReussite || 0)

    const resultatQuiz: ResultatQuiz = {
      clientId: this.currentUser.id,
      quizId: this.quiz.id,
      score: pourcentage, // Store percentage as score
      dateSoumission: new Date(),
    }

    // Mock submission
    this.results = {
      score: score,
      totalQuestions: this.quiz.questions.length,
      pourcentage: pourcentage,
      reussi: reussi,
      reponses: reponses,
    } as any // Cast to any because ResultatQuiz doesn't have all these properties
    this.showResults = true
    this.isLoading = false

    // Uncomment to submit to API
    /*
    this.quizService.soumettreResultat(this.quiz.id, resultatQuiz).subscribe({
      next: (res) => {
        this.results = {
          score: score,
          totalQuestions: this.quiz.questions.length,
          pourcentage: pourcentage,
          reussi: reussi,
          reponses: reponses
        } as any; // Cast to any because ResultatQuiz doesn't have all these properties
        this.showResults = true;
        this.isLoading = false;
        this.snackBar.open('Résultat du quiz enregistré !', 'Fermer', { duration: 3000 });
      },
      error: (err) => {
        this.snackBar.open('Erreur lors de la soumission du quiz.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.isLoading = false;
      }
    });
    */
  }

  restartQuiz(): void {
    this.currentQuestionIndex = 0
    this.selectedAnswers = {}
    this.showResults = false
    this.results = null
    this.timeRemaining = this.quiz.dureeEstimee * 60
    this.startTimer()
  }

  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  get progress(): number {
    return ((this.currentQuestionIndex + 1) / this.quiz.questions.length) * 100
  }
}
