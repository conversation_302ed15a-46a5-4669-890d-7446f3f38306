{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatToolbarModule } from \"@angular/material/toolbar\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatGridListModule } from \"@angular/material/grid-list\";\nimport { DashboardComponent } from \"./dashboard.component\";\nexport let DashboardModule = class DashboardModule {};\nDashboardModule = __decorate([NgModule({\n  declarations: [DashboardComponent],\n  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatToolbarModule, MatChipsModule, MatGridListModule, RouterModule.forChild([{\n    path: \"\",\n    component: DashboardComponent\n  }])]\n})], DashboardModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatToolbarModule", "MatChipsModule", "MatGridListModule", "DashboardComponent", "DashboardModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component"], "sources": ["C:\\e-learning\\src\\app\\features\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatToolbarModule } from \"@angular/material/toolbar\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatGridListModule } from \"@angular/material/grid-list\"\n\nimport { DashboardComponent } from \"./dashboard.component\"\n\n@NgModule({\n  declarations: [DashboardComponent],\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatToolbarModule,\n    MatChipsModule,\n    MatGridListModule,\n    RouterModule.forChild([{ path: \"\", component: DashboardComponent }]),\n  ],\n})\nexport class DashboardModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D,SAASC,kBAAkB,QAAQ,uBAAuB;AAenD,WAAMC,eAAe,GAArB,MAAMA,eAAe,GAAG;AAAlBA,eAAe,GAAAC,UAAA,EAb3BX,QAAQ,CAAC;EACRY,YAAY,EAAE,CAACH,kBAAkB,CAAC;EAClCI,OAAO,EAAE,CACPZ,YAAY,EACZE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,iBAAiB,EACjBN,YAAY,CAACY,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEP;EAAkB,CAAE,CAAC,CAAC;CAEvE,CAAC,C,EACWC,eAAe,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}