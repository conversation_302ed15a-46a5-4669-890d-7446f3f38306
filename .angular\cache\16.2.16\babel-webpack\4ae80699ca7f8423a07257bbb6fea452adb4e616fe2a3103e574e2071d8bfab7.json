{"ast": null, "code": "import { BehaviorSubject, tap } from \"rxjs\";\nimport { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.apiUrl = `${environment.urlApi}auth`;\n    const token = this.getToken();\n    const role = this.getUserRole();\n    if (token && role) {\n      // Décoder le token pour récupérer les infos utilisateur\n      const userInfo = this.decodeToken(token);\n      if (userInfo) {\n        this.currentUserSubject.next({\n          id: userInfo.id || 0,\n          email: userInfo.email || '',\n          nom: userInfo.nom || '',\n          prenom: userInfo.prenom || '',\n          role: role\n        });\n      }\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/login`, credentials).pipe(tap(response => {\n      localStorage.setItem(\"token\", response.token);\n      localStorage.setItem(\"userRole\", response.role);\n      // Décoder le token pour récupérer les infos utilisateur\n      const userInfo = this.decodeToken(response.token);\n      if (userInfo) {\n        this.currentUserSubject.next({\n          id: userInfo.id || 0,\n          email: userInfo.email || credentials.Email,\n          nom: userInfo.nom || '',\n          prenom: userInfo.prenom || '',\n          role: response.role\n        });\n      }\n    }));\n  }\n  // Note: Votre backend n'a pas d'endpoint register, utilisez les contrôleurs spécifiques\n  register(userData) {\n    const endpoint = userData.accountType === 'formateur' ? 'formateur' : 'client';\n    // ✅ Préparer les données selon le modèle .NET\n    const registrationData = {\n      email: userData.email,\n      nom: userData.lastName,\n      prenom: userData.firstName,\n      role: userData.accountType === 'formateur' ? 'Formateur' : 'Client',\n      // Initialiser les collections pour éviter les erreurs\n      ...(userData.accountType === 'client' && {\n        paiements: [],\n        coursConsultes: [],\n        resultatsQuiz: []\n      }),\n      ...(userData.accountType === 'formateur' && {\n        coursCree: [],\n        estValide: false\n      })\n    };\n    console.log('Données d\\'inscription envoyées:', JSON.stringify(registrationData, null, 2));\n    console.log('Endpoint utilisé:', `${environment.urlApi}${endpoint}`);\n    return this.http.post(`${environment.urlApi}${endpoint}`, registrationData);\n  }\n  // Décoder le token JWT pour extraire les informations utilisateur\n  decodeToken(token) {\n    try {\n      const payload = token.split('.')[1];\n      const decoded = atob(payload);\n      return JSON.parse(decoded);\n    } catch (error) {\n      console.error('Erreur lors du décodage du token:', error);\n      return null;\n    }\n  }\n  logout() {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"userRole\");\n    this.currentUserSubject.next(null);\n    this.router.navigate([\"/\"]);\n  }\n  getToken() {\n    return localStorage.getItem(\"token\");\n  }\n  getUserRole() {\n    return localStorage.getItem(\"userRole\");\n  }\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  getCurrentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "environment", "AuthService", "constructor", "http", "router", "currentUserSubject", "currentUser$", "asObservable", "apiUrl", "urlApi", "token", "getToken", "role", "getUserRole", "userInfo", "decodeToken", "next", "id", "email", "nom", "prenom", "login", "credentials", "post", "pipe", "response", "localStorage", "setItem", "Email", "register", "userData", "endpoint", "accountType", "registrationData", "lastName", "firstName", "paiements", "coursConsultes", "resultatsQuiz", "coursCree", "estValide", "console", "log", "JSON", "stringify", "payload", "split", "decoded", "atob", "parse", "error", "logout", "removeItem", "navigate", "getItem", "isAuthenticated", "getCurrentUserValue", "value", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient, HttpHeaders } from \"@angular/common/http\"\nimport { BehaviorSubject, Observable, tap } from \"rxjs\"\nimport { Router } from \"@angular/router\"\nimport { environment } from \"../../../environments/environment\"\nimport { User, LoginRequest, AuthResponse } from \"../models/user.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AuthService {\n  private currentUserSubject = new BehaviorSubject<User | null>(null)\n  public currentUser$ = this.currentUserSubject.asObservable()\n  private apiUrl = `${environment.urlApi}auth`\n\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n  ) {\n    const token = this.getToken()\n    const role = this.getUserRole()\n    if (token && role) {\n      // Décoder le token pour récupérer les infos utilisateur\n      const userInfo = this.decodeToken(token)\n      if (userInfo) {\n        this.currentUserSubject.next({\n          id: userInfo.id || 0,\n          email: userInfo.email || '',\n          nom: userInfo.nom || '',\n          prenom: userInfo.prenom || '',\n          role: role as \"Client\" | \"Formateur\" | \"Admin\",\n        })\n      }\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, credentials).pipe(\n      tap((response) => {\n        localStorage.setItem(\"token\", response.token)\n        localStorage.setItem(\"userRole\", response.role)\n\n        // Décoder le token pour récupérer les infos utilisateur\n        const userInfo = this.decodeToken(response.token)\n        if (userInfo) {\n          this.currentUserSubject.next({\n            id: userInfo.id || 0,\n            email: userInfo.email || credentials.Email,\n            nom: userInfo.nom || '',\n            prenom: userInfo.prenom || '',\n            role: response.role as \"Client\" | \"Formateur\" | \"Admin\",\n          })\n        }\n      }),\n    )\n  }\n\n  // Note: Votre backend n'a pas d'endpoint register, utilisez les contrôleurs spécifiques\n  register(userData: any): Observable<any> {\n    const endpoint = userData.accountType === 'formateur' ? 'formateur' : 'client'\n\n    // ✅ Préparer les données selon le modèle .NET\n    const registrationData = {\n      email: userData.email,\n      nom: userData.lastName,\n      prenom: userData.firstName,\n      role: userData.accountType === 'formateur' ? 'Formateur' : 'Client',\n      // Initialiser les collections pour éviter les erreurs\n      ...(userData.accountType === 'client' && {\n        paiements: [],\n        coursConsultes: [],\n        resultatsQuiz: []\n      }),\n      ...(userData.accountType === 'formateur' && {\n        coursCree: [],\n        estValide: false\n      })\n    }\n\n    console.log('Données d\\'inscription envoyées:', JSON.stringify(registrationData, null, 2))\n    console.log('Endpoint utilisé:', `${environment.urlApi}${endpoint}`)\n    return this.http.post(`${environment.urlApi}${endpoint}`, registrationData)\n  }\n\n  // Décoder le token JWT pour extraire les informations utilisateur\n  private decodeToken(token: string): any {\n    try {\n      const payload = token.split('.')[1]\n      const decoded = atob(payload)\n      return JSON.parse(decoded)\n    } catch (error) {\n      console.error('Erreur lors du décodage du token:', error)\n      return null\n    }\n  }\n\n  logout(): void {\n    localStorage.removeItem(\"token\")\n    localStorage.removeItem(\"userRole\")\n    this.currentUserSubject.next(null)\n    this.router.navigate([\"/\"])\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem(\"token\")\n  }\n\n  getUserRole(): string | null {\n    return localStorage.getItem(\"userRole\")\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getToken()\n  }\n\n  getCurrentUserValue(): User | null {\n    return this.currentUserSubject.value\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;AAEvD,SAASC,WAAW,QAAQ,mCAAmC;;;;AAM/D,OAAM,MAAOC,WAAW;EAKtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IANR,KAAAC,kBAAkB,GAAG,IAAIP,eAAe,CAAc,IAAI,CAAC;IAC5D,KAAAQ,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IACpD,KAAAC,MAAM,GAAG,GAAGR,WAAW,CAACS,MAAM,MAAM;IAM1C,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAACC,WAAW,EAAE;IAC/B,IAAIH,KAAK,IAAIE,IAAI,EAAE;MACjB;MACA,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;MACxC,IAAII,QAAQ,EAAE;QACZ,IAAI,CAACT,kBAAkB,CAACW,IAAI,CAAC;UAC3BC,EAAE,EAAEH,QAAQ,CAACG,EAAE,IAAI,CAAC;UACpBC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,IAAI,EAAE;UAC3BC,GAAG,EAAEL,QAAQ,CAACK,GAAG,IAAI,EAAE;UACvBC,MAAM,EAAEN,QAAQ,CAACM,MAAM,IAAI,EAAE;UAC7BR,IAAI,EAAEA;SACP,CAAC;;;EAGR;EAEAS,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACf,MAAM,QAAQ,EAAEc,WAAW,CAAC,CAACE,IAAI,CAC3EzB,GAAG,CAAE0B,QAAQ,IAAI;MACfC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAACf,KAAK,CAAC;MAC7CgB,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAACb,IAAI,CAAC;MAE/C;MACA,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACU,QAAQ,CAACf,KAAK,CAAC;MACjD,IAAII,QAAQ,EAAE;QACZ,IAAI,CAACT,kBAAkB,CAACW,IAAI,CAAC;UAC3BC,EAAE,EAAEH,QAAQ,CAACG,EAAE,IAAI,CAAC;UACpBC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,IAAII,WAAW,CAACM,KAAK;UAC1CT,GAAG,EAAEL,QAAQ,CAACK,GAAG,IAAI,EAAE;UACvBC,MAAM,EAAEN,QAAQ,CAACM,MAAM,IAAI,EAAE;UAC7BR,IAAI,EAAEa,QAAQ,CAACb;SAChB,CAAC;;IAEN,CAAC,CAAC,CACH;EACH;EAEA;EACAiB,QAAQA,CAACC,QAAa;IACpB,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,WAAW,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ;IAE9E;IACA,MAAMC,gBAAgB,GAAG;MACvBf,KAAK,EAAEY,QAAQ,CAACZ,KAAK;MACrBC,GAAG,EAAEW,QAAQ,CAACI,QAAQ;MACtBd,MAAM,EAAEU,QAAQ,CAACK,SAAS;MAC1BvB,IAAI,EAAEkB,QAAQ,CAACE,WAAW,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ;MACnE;MACA,IAAIF,QAAQ,CAACE,WAAW,KAAK,QAAQ,IAAI;QACvCI,SAAS,EAAE,EAAE;QACbC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE;OAChB,CAAC;MACF,IAAIR,QAAQ,CAACE,WAAW,KAAK,WAAW,IAAI;QAC1CO,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;OACZ;KACF;IAEDC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEC,IAAI,CAACC,SAAS,CAACX,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1FQ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,GAAG1C,WAAW,CAACS,MAAM,GAAGsB,QAAQ,EAAE,CAAC;IACpE,OAAO,IAAI,CAAC5B,IAAI,CAACoB,IAAI,CAAC,GAAGvB,WAAW,CAACS,MAAM,GAAGsB,QAAQ,EAAE,EAAEE,gBAAgB,CAAC;EAC7E;EAEA;EACQlB,WAAWA,CAACL,KAAa;IAC/B,IAAI;MACF,MAAMmC,OAAO,GAAGnC,KAAK,CAACoC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnC,MAAMC,OAAO,GAAGC,IAAI,CAACH,OAAO,CAAC;MAC7B,OAAOF,IAAI,CAACM,KAAK,CAACF,OAAO,CAAC;KAC3B,CAAC,OAAOG,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;;EAEf;EAEAC,MAAMA,CAAA;IACJzB,YAAY,CAAC0B,UAAU,CAAC,OAAO,CAAC;IAChC1B,YAAY,CAAC0B,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC/C,kBAAkB,CAACW,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACZ,MAAM,CAACiD,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEA1C,QAAQA,CAAA;IACN,OAAOe,YAAY,CAAC4B,OAAO,CAAC,OAAO,CAAC;EACtC;EAEAzC,WAAWA,CAAA;IACT,OAAOa,YAAY,CAAC4B,OAAO,CAAC,UAAU,CAAC;EACzC;EAEAC,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAAC5C,QAAQ,EAAE;EAC1B;EAEA6C,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACnD,kBAAkB,CAACoD,KAAK;EACtC;;;uBA3GWxD,WAAW,EAAAyD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX9D,WAAW;MAAA+D,OAAA,EAAX/D,WAAW,CAAAgE,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}