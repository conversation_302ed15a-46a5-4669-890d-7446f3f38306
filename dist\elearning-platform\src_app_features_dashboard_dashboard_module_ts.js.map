{"version": 3, "file": "src_app_features_dashboard_dashboard_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6CUA,4DAAA,mBAAmE;IAIrCA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAC1CA,4DAAA,YAAsB;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAE5CA,4DAAA,mBAAiD;IAAAA,oDAAA,GAAe;IAAAA,0DAAA,EAAW;;;;IAHnDA,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAM,OAAA,CAAAC,KAAA,CAAgB;IAChBP,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAM,OAAA,CAAAE,KAAA,CAAgB;IAE9BR,uDAAA,GAAoB;IAApBA,wDAAA,CAAAM,OAAA,CAAAI,KAAA,CAAoB;IAAmBV,uDAAA,GAAe;IAAfA,+DAAA,CAAAM,OAAA,CAAAK,IAAA,CAAe;;;;;IAchEX,qEAAA,GAAwD;IACtDA,4DAAA,iBAA2E;IAC/DA,oDAAA,UAAG;IAAAA,0DAAA,EAAW;IACxBA,oDAAA,oCACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,iBAA+E;IACnEA,oDAAA,aAAM;IAAAA,0DAAA,EAAW;IAC3BA,oDAAA,gCACF;IAAAA,0DAAA,EAAS;IACXA,mEAAA,EAAe;;;;;IAEfA,qEAAA,GAAqD;IACnDA,4DAAA,iBAAoE;IACxDA,oDAAA,WAAI;IAAAA,0DAAA,EAAW;IACzBA,oDAAA,4BACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,iBAAyE;IAC7DA,oDAAA,mBAAY;IAAAA,0DAAA,EAAW;IACjCA,oDAAA,6BACF;IAAAA,0DAAA,EAAS;IACXA,mEAAA,EAAe;;;;;IAEfA,qEAAA,GAAoD;IAClDA,4DAAA,iBAAwE;IAC5DA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAW;IACzCA,oDAAA,oCACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,iBAA4E;IAChEA,oDAAA,gBAAS;IAAAA,0DAAA,EAAW;IAC9BA,oDAAA,0BACF;IAAAA,0DAAA,EAAS;IACXA,mEAAA,EAAe;;;AA0MzB,MAAOc,kBAAkB;EAU7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAXhB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,KAAK,GAAmB;MACtBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE,KAAK;MACnBC,qBAAqB,EAAE,EAAE;MACzBC,eAAe,EAAE;KAClB;EAKE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,CAACU,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACV,WAAW,GAAGU,IAAI;MACvB,IAAI,CAACA,IAAI,EAAE;QACT;QACA,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;IAEzC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACrB,MAAMY,QAAQ,GAAS;QACrBC,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,kBAAkB;QACzBC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,WAAW,CAAE;OACpB;MACD;MACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC;MAC/CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEP,QAAQ,CAACK,IAAI,CAAC;MAC/C,IAAI,CAACnB,WAAW,CAAC,oBAAoB,CAAC,CAACsB,IAAI,CAACR,QAAQ,CAAC,EAAC;MACtD,IAAI,CAACZ,WAAW,GAAGY,QAAQ;;EAE/B;EAEAS,MAAMA,CAAA;IACJ,IAAI,CAACvB,WAAW,CAACuB,MAAM,EAAE;EAC3B;EAEAC,YAAYA,CAAA;IACV,QAAQ,IAAI,CAACtB,WAAW,EAAEiB,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,0BAA0B;MACnC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,OAAO;QACV,OAAO,gCAAgC;MACzC;QACE,OAAO,iBAAiB;;EAE9B;EAEAM,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACvB,WAAW,EAAEiB,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,CACL;UAAE5B,KAAK,EAAE,gBAAgB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACC,YAAY;UAAET,IAAI,EAAE,MAAM;UAAED,KAAK,EAAE;QAAM,CAAE,EACxF;UACEH,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACI,qBAAqB;UACvCZ,IAAI,EAAE,cAAc;UACpBD,KAAK,EAAE;SACR,EACD;UAAEH,KAAK,EAAE,UAAU;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACK,eAAe;UAAEb,IAAI,EAAE,SAAS;UAAED,KAAK,EAAE;QAAO,CAAE,CAC1F;MACH,KAAK,WAAW;QACd,OAAO,CACL;UAAEH,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACC,YAAY;UAAET,IAAI,EAAE,MAAM;UAAED,KAAK,EAAE;QAAM,CAAE,EACvF;UAAEH,KAAK,EAAE,iBAAiB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACE,aAAa;UAAEV,IAAI,EAAE,QAAQ;UAAED,KAAK,EAAE;QAAQ,CAAE,EAC9F;UAAEH,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACG,YAAY;UAAEX,IAAI,EAAE,aAAa;UAAED,KAAK,EAAE;QAAO,CAAE,EAC7F;UAAEH,KAAK,EAAE,kBAAkB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACI,qBAAqB;UAAEZ,IAAI,EAAE,cAAc;UAAED,KAAK,EAAE;QAAQ,CAAE,CAC9G;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEH,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACC,YAAY;UAAET,IAAI,EAAE,MAAM;UAAED,KAAK,EAAE;QAAM,CAAE,EACrF;UAAEH,KAAK,EAAE,oBAAoB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACE,aAAa;UAAEV,IAAI,EAAE,QAAQ;UAAED,KAAK,EAAE;QAAQ,CAAE,EACjG;UAAEH,KAAK,EAAE,wBAAwB;UAAEC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACG,YAAY;UAAEX,IAAI,EAAE,aAAa;UAAED,KAAK,EAAE;QAAO,CAAE,EACxG;UACEH,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACI,qBAAqB;UACvCZ,IAAI,EAAE,cAAc;UACpBD,KAAK,EAAE;SACR,CACF;MACH;QACE,OAAO,EAAE;;EAEf;;;uBA7FWI,kBAAkB,EAAAd,+DAAA,CAAA2C,oEAAA,GAAA3C,+DAAA,CAAA6C,mDAAA;IAAA;EAAA;;;YAAlB/B,kBAAkB;MAAAiC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3R3BrD,4DAAA,aAAiC;UAKfA,oDAAA,aAAM;UAAAA,0DAAA,EAAW;UAC3BA,4DAAA,WAAM;UAAAA,oDAAA,wBAAiB;UAAAA,0DAAA,EAAO;UAEhCA,4DAAA,aAAuB;UAESA,oDAAA,IAAuB;UAAAA,0DAAA,EAAW;UAEhEA,4DAAA,iBAAsC;UAAnBA,wDAAA,mBAAAwD,qDAAA;YAAA,OAASF,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UACnCvC,4DAAA,gBAAU;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UAC3BA,oDAAA,0BACF;UAAAA,0DAAA,EAAS;UAMfA,4DAAA,cAA+B;UAEvBA,oDAAA,IAAoB;UAAAA,0DAAA,EAAK;UAC7BA,4DAAA,SAAG;UAAAA,oDAAA,gEAA8C;UAAAA,0DAAA,EAAI;UAIvDA,4DAAA,cAAwB;UACtBA,wDAAA,KAAA0D,uCAAA,wBAUW;UACb1D,0DAAA,EAAM;UAGNA,4DAAA,eAA4B;UAGNA,oDAAA,uBAAe;UAAAA,0DAAA,EAAiB;UAElDA,4DAAA,wBAAkB;UAEdA,wDAAA,KAAA2D,2CAAA,2BASe;UAEf3D,wDAAA,KAAA4D,2CAAA,2BASe;UAEf5D,wDAAA,KAAA6D,2CAAA,2BASe;UAEf7D,4DAAA,kBAAqE;UACzDA,oDAAA,eAAO;UAAAA,0DAAA,EAAW;UAC5BA,oDAAA,kBACF;UAAAA,0DAAA,EAAS;UAKfA,4DAAA,oBAAgC;UAEZA,oDAAA,kCAAgB;UAAAA,0DAAA,EAAiB;UAEnDA,4DAAA,wBAAkB;UAGZA,uDAAA,eAAsC;UACtCA,4DAAA,SAAG;UAAAA,oDAAA,kEAA8C;UAAAA,0DAAA,EAAI;UAEvDA,4DAAA,eAA2B;UACzBA,uDAAA,eAAqC;UACrCA,4DAAA,SAAG;UAAAA,oDAAA,sDAA+B;UAAAA,0DAAA,EAAI;UAExCA,4DAAA,eAA2B;UACzBA,uDAAA,eAAuC;UACvCA,4DAAA,SAAG;UAAAA,oDAAA,sCAAoB;UAAAA,0DAAA,EAAI;UAE7BA,4DAAA,eAA2B;UACzBA,uDAAA,eAAuC;UACvCA,4DAAA,SAAG;UAAAA,oDAAA,0CAA6B;UAAAA,0DAAA,EAAI;;;UArGZA,uDAAA,IAAuB;UAAvBA,+DAAA,CAAAsD,GAAA,CAAApC,WAAA,kBAAAoC,GAAA,CAAApC,WAAA,CAAAiB,IAAA,CAAuB;UAanDnC,uDAAA,GAAoB;UAApBA,+DAAA,CAAAsD,GAAA,CAAAd,YAAA,GAAoB;UAMGxC,uDAAA,GAAoB;UAApBA,wDAAA,YAAAsD,GAAA,CAAAb,eAAA,GAAoB;UAqB1BzC,uDAAA,GAAuC;UAAvCA,wDAAA,UAAAsD,GAAA,CAAApC,WAAA,kBAAAoC,GAAA,CAAApC,WAAA,CAAAiB,IAAA,kBAAuC;UAWvCnC,uDAAA,GAAoC;UAApCA,wDAAA,UAAAsD,GAAA,CAAApC,WAAA,kBAAAoC,GAAA,CAAApC,WAAA,CAAAiB,IAAA,eAAoC;UAWpCnC,uDAAA,GAAmC;UAAnCA,wDAAA,UAAAsD,GAAA,CAAApC,WAAA,kBAAAoC,GAAA,CAAApC,WAAA,CAAAiB,IAAA,cAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvFpB;AACA;AACQ;AACI;AACJ;AACM;AACJ;AACO;AAEL;;;AAepD,MAAOqC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAVxBR,yDAAY,EACZE,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,uEAAgB,EAChBC,mEAAc,EACdC,0EAAiB,EACjBN,yDAAY,CAACQ,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE7D,oEAAkBA;MAAA,CAAE,CAAC,CAAC;IAAA;EAAA;;;sHAG3D0D,eAAe;IAAAI,YAAA,GAZX9D,oEAAkB;IAAA+D,OAAA,GAE/Bb,yDAAY,EACZE,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,uEAAgB,EAChBC,mEAAc,EACdC,0EAAiB,EAAA5B,yDAAA;EAAA;AAAA,K", "sources": ["./src/app/features/dashboard/dashboard.component.ts", "./src/app/features/dashboard/dashboard.module.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { Router } from \"@angular/router\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { User } from \"../../core/models/user.model\"\n\ninterface DashboardStats {\n  totalCourses: number\n  totalStudents: number\n  totalRevenue: number\n  completedCertificates: number\n  pendingMessages: number\n}\n\n@Component({\n  selector: \"app-dashboard\",\n  template: `\n    <div class=\"dashboard-container\">\n      <!-- Header -->\n      <mat-toolbar color=\"primary\" class=\"dashboard-header\">\n        <div class=\"header-content\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <span>Training Platform</span>\n          </div>\n          <div class=\"user-info\">\n            <mat-chip-set>\n              <mat-chip class=\"role-chip\">{{ currentUser?.role }}</mat-chip>\n            </mat-chip-set>\n            <button mat-button (click)=\"logout()\">\n              <mat-icon>logout</mat-icon>\n              Déconnexion\n            </button>\n          </div>\n        </div>\n      </mat-toolbar>\n\n      <!-- Main Content -->\n      <div class=\"dashboard-content\">\n        <div class=\"welcome-section\">\n          <h1>{{ getRoleTitle() }}</h1>\n          <p>Bienvenue ! Voici un aperçu de votre activité.</p>\n        </div>\n\n        <!-- Stats Grid -->\n        <div class=\"stats-grid\">\n          <mat-card *ngFor=\"let stat of getStatsForRole()\" class=\"stat-card\">\n            <mat-card-content>\n              <div class=\"stat-content\">\n                <div class=\"stat-info\">\n                  <p class=\"stat-label\">{{ stat.title }}</p>\n                  <p class=\"stat-value\">{{ stat.value }}</p>\n                </div>\n                <mat-icon [class]=\"stat.color\" class=\"stat-icon\">{{ stat.icon }}</mat-icon>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Quick Actions & Recent Activity -->\n        <div class=\"dashboard-grid\">\n          <mat-card class=\"actions-card\">\n            <mat-card-header>\n              <mat-card-title>Actions rapides</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"actions-list\">\n                <ng-container *ngIf=\"currentUser?.role === 'Formateur'\">\n                  <button mat-stroked-button routerLink=\"/courses/create\" class=\"action-btn\">\n                    <mat-icon>add</mat-icon>\n                    Créer un nouveau cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/dashboard/students\" class=\"action-btn\">\n                    <mat-icon>people</mat-icon>\n                    Voir les étudiants\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Client'\">\n                  <button mat-stroked-button routerLink=\"/courses\" class=\"action-btn\">\n                    <mat-icon>book</mat-icon>\n                    Parcourir les cours\n                  </button>\n                  <button mat-stroked-button routerLink=\"/certificates\" class=\"action-btn\">\n                    <mat-icon>emoji_events</mat-icon>\n                    Voir mes certificats\n                  </button>\n                </ng-container>\n                \n                <ng-container *ngIf=\"currentUser?.role === 'Admin'\">\n                  <button mat-stroked-button routerLink=\"/admin/users\" class=\"action-btn\">\n                    <mat-icon>admin_panel_settings</mat-icon>\n                    Gérer les utilisateurs\n                  </button>\n                  <button mat-stroked-button routerLink=\"/admin/analytics\" class=\"action-btn\">\n                    <mat-icon>analytics</mat-icon>\n                    Voir les analyses\n                  </button>\n                </ng-container>\n                \n                <button mat-stroked-button routerLink=\"/messages\" class=\"action-btn\">\n                  <mat-icon>message</mat-icon>\n                  Messages\n                </button>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"activity-card\">\n            <mat-card-header>\n              <mat-card-title>Activité récente</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"activity-list\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot green\"></div>\n                  <p>Nouvel étudiant inscrit à \"React Fundamentals\"</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot blue\"></div>\n                  <p>Certificat généré pour John Doe</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot yellow\"></div>\n                  <p>Paiement reçu : 299€</p>\n                </div>\n                <div class=\"activity-item\">\n                  <div class=\"activity-dot purple\"></div>\n                  <p>Nouveau message d'un étudiant</p>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .dashboard-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n    }\n\n    .dashboard-header {\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n    }\n\n    .header-content {\n      width: 100%;\n      max-width: 1200px;\n      margin: 0 auto;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 1.5rem;\n      font-weight: bold;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .role-chip {\n      text-transform: capitalize;\n    }\n\n    .dashboard-content {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 2rem;\n    }\n\n    .welcome-section {\n      margin-bottom: 2rem;\n    }\n\n    .welcome-section h1 {\n      font-size: 2rem;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .welcome-section p {\n      color: #666;\n      font-size: 1.1rem;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1.5rem;\n      margin-bottom: 2rem;\n    }\n\n    .stat-card {\n      padding: 1rem;\n    }\n\n    .stat-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .stat-label {\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.5rem;\n    }\n\n    .stat-value {\n      font-size: 2rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .stat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .stat-icon.blue { color: #2196f3; }\n    .stat-icon.purple { color: #9c27b0; }\n    .stat-icon.green { color: #4caf50; }\n    .stat-icon.yellow { color: #ff9800; }\n\n    .dashboard-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 2rem;\n    }\n\n    .actions-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .action-btn {\n      justify-content: flex-start;\n      padding: 1rem;\n      text-align: left;\n    }\n\n    .action-btn mat-icon {\n      margin-right: 1rem;\n    }\n\n    .activity-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .activity-item {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .activity-dot {\n      width: 8px;\n      height: 8px;\n      border-radius: 50%;\n    }\n\n    .activity-dot.green { background-color: #4caf50; }\n    .activity-dot.blue { background-color: #2196f3; }\n    .activity-dot.yellow { background-color: #ff9800; }\n    .activity-dot.purple { background-color: #9c27b0; }\n\n    .activity-item p {\n      margin: 0;\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .dashboard-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `,\n  ],\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: User | null = null\n  stats: DashboardStats = {\n    totalCourses: 12,\n    totalStudents: 245,\n    totalRevenue: 15420,\n    completedCertificates: 89,\n    pendingMessages: 5,\n  }\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (!user) {\n        // Redirect to login if no user is logged in\n        this.router.navigate([\"/auth/login\"])\n      }\n    })\n\n    // Mock user data if not logged in (for development)\n    if (!this.currentUser) {\n      const mockUser: User = {\n        id: 1,\n        email: \"<EMAIL>\",\n        nom: \"Doe\",\n        prenom: \"John\",\n        role: \"Formateur\", // Change this to 'Client' or 'Admin' to test different dashboards\n      }\n      // Simulate setting user and role in local storage for initial load\n      localStorage.setItem(\"token\", \"mock-jwt-token\")\n      localStorage.setItem(\"userRole\", mockUser.role)\n      this.authService[\"currentUserSubject\"].next(mockUser) // Directly update subject for mock\n      this.currentUser = mockUser\n    }\n  }\n\n  logout(): void {\n    this.authService.logout()\n  }\n\n  getRoleTitle(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Tableau de bord Étudiant\"\n      case \"Formateur\":\n        return \"Tableau de bord Formateur\"\n      case \"Admin\":\n        return \"Tableau de bord Administrateur\"\n      default:\n        return \"Tableau de bord\"\n    }\n  }\n\n  getStatsForRole() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return [\n          { title: \"Cours inscrits\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          {\n            title: \"Certificats obtenus\",\n            value: this.stats.completedCertificates,\n            icon: \"emoji_events\",\n            color: \"yellow\",\n          },\n          { title: \"Messages\", value: this.stats.pendingMessages, icon: \"message\", color: \"green\" },\n        ]\n      case \"Formateur\":\n        return [\n          { title: \"Cours publiés\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          { title: \"Total étudiants\", value: this.stats.totalStudents, icon: \"people\", color: \"purple\" },\n          { title: \"Revenus (€)\", value: this.stats.totalRevenue, icon: \"euro_symbol\", color: \"green\" },\n          { title: \"Certificats émis\", value: this.stats.completedCertificates, icon: \"emoji_events\", color: \"yellow\" },\n        ]\n      case \"Admin\":\n        return [\n          { title: \"Total cours\", value: this.stats.totalCourses, icon: \"book\", color: \"blue\" },\n          { title: \"Total utilisateurs\", value: this.stats.totalStudents, icon: \"people\", color: \"purple\" },\n          { title: \"Revenus plateforme (€)\", value: this.stats.totalRevenue, icon: \"trending_up\", color: \"green\" },\n          {\n            title: \"Certificats générés\",\n            value: this.stats.completedCertificates,\n            icon: \"emoji_events\",\n            color: \"yellow\",\n          },\n        ]\n      default:\n        return []\n    }\n  }\n}\n", "import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatToolbarModule } from \"@angular/material/toolbar\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatGridListModule } from \"@angular/material/grid-list\"\n\nimport { DashboardComponent } from \"./dashboard.component\"\n\n@NgModule({\n  declarations: [DashboardComponent],\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatToolbarModule,\n    MatChipsModule,\n    MatGridListModule,\n    RouterModule.forChild([{ path: \"\", component: DashboardComponent }]),\n  ],\n})\nexport class DashboardModule {}\n"], "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "stat_r4", "title", "value", "ɵɵclassMap", "color", "icon", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "DashboardComponent", "constructor", "authService", "router", "currentUser", "stats", "totalCourses", "totalStudents", "totalRevenue", "completedCertificates", "pendingMessages", "ngOnInit", "currentUser$", "subscribe", "user", "navigate", "mockUser", "id", "email", "nom", "prenom", "role", "localStorage", "setItem", "next", "logout", "getRoleTitle", "getStatsForRole", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "DashboardComponent_Template_button_click_12_listener", "ɵɵtemplate", "DashboardComponent_mat_card_23_Template", "DashboardComponent_ng_container_31_Template", "DashboardComponent_ng_container_32_Template", "DashboardComponent_ng_container_33_Template", "ɵɵelement", "ɵɵproperty", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatToolbarModule", "MatChipsModule", "MatGridListModule", "DashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}