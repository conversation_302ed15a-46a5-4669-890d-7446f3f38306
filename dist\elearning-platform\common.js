"use strict";
(self["webpackChunkelearning_platform"] = self["webpackChunkelearning_platform"] || []).push([["common"],{

/***/ 8769:
/*!*************************************************!*\
  !*** ./src/app/core/services/course.service.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CourseService: () => (/* binding */ CourseService)
/* harmony export */ });
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common/http */ 6443);



class CourseService {
  constructor(http) {
    this.http = http;
    this.apiUrl = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlApi}cours`;
  }
  // GET: Tous les cours (correspond à GET /api/cours)
  getAllCours() {
    return this.http.get(this.apiUrl);
  }
  // GET: Un cours par ID (correspond à GET /api/cours/{id})
  getCours(id) {
    return this.http.get(`${this.apiUrl}/${id}`);
  }
  // PUT: Modifier un cours (correspond à PUT /api/cours/{id})
  modifierCours(id, cours) {
    return this.http.put(`${this.apiUrl}/${id}`, cours);
  }
  // DELETE: Supprimer un cours (correspond à DELETE /api/cours/{id})
  supprimerCours(id) {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
  // POST: Ajouter un contenu à un cours (correspond à POST /api/cours/{id}/ajouter-contenu)
  ajouterContenu(coursId, contenu) {
    return this.http.post(`${this.apiUrl}/${coursId}/ajouter-contenu`, contenu);
  }
  static {
    this.ɵfac = function CourseService_Factory(t) {
      return new (t || CourseService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: CourseService,
      factory: CourseService.ɵfac,
      providedIn: "root"
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=common.js.map