{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from \"@angular/core\";\nexport let MessagesComponent = class MessagesComponent {\n  constructor(messageService, authService, snackBar) {\n    this.messageService = messageService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.conversations = [];\n    this.filteredConversations = [];\n    this.selectedConversationId = null;\n    this.messages = [];\n    this.newMessageContent = \"\";\n    this.searchTerm = \"\";\n    this.currentUser = null;\n    this.selectedParticipant = null;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (this.currentUser) {\n        this.loadConversations();\n      }\n    });\n  }\n  loadConversations() {\n    // Mock data for demonstration\n    this.conversations = [{\n      participantId: 2,\n      participantName: \"<PERSON>\",\n      participantRole: \"Formateur\",\n      lastMessageContent: \"Bon<PERSON>r, avez-vous des questions sur le cours React ?\",\n      lastMessageDate: new Date(\"2024-01-15T10:30:00Z\"),\n      unreadCount: 2\n    }, {\n      participantId: 3,\n      participantName: \"<PERSON>\",\n      participantRole: \"Client\",\n      lastMessageContent: \"Merci pour votre aide !\",\n      lastMessageDate: new Date(\"2024-01-14T15:45:00Z\"),\n      unreadCount: 0\n    }, {\n      participantId: 4,\n      participantName: \"Admin System\",\n      participantRole: \"Admin\",\n      lastMessageContent: \"Votre demande a été traitée.\",\n      lastMessageDate: new Date(\"2024-01-13T11:00:00Z\"),\n      unreadCount: 0\n    }];\n    this.applySearch();\n    // Uncomment to fetch from API\n    /*\n    // This would require a backend endpoint to get conversation previews for a user\n    // For example: this.messageService.getConversations(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  applySearch() {\n    this.filteredConversations = this.conversations.filter(conv => conv.participantName.toLowerCase().includes(this.searchTerm.toLowerCase()));\n  }\n  selectConversation(participantId) {\n    this.selectedConversationId = participantId;\n    this.selectedParticipant = this.conversations.find(c => c.participantId === participantId) || null;\n    this.loadMessages(participantId);\n  }\n  loadMessages(participantId) {\n    if (!this.currentUser) return;\n    // Mock messages for the selected conversation\n    this.messages = [{\n      id: 1,\n      expediteurId: participantId,\n      destinataireId: this.currentUser.id,\n      contenu: \"Bonjour, j'espère que vous appréciez le cours React !\",\n      dateEnvoi: new Date(\"2024-01-15T09:00:00Z\"),\n      expediteur: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      },\n      destinataire: this.currentUser\n    }, {\n      id: 2,\n      expediteurId: this.currentUser.id,\n      destinataireId: participantId,\n      contenu: \"Bonjour Jean, oui c'est très intéressant ! J'ai une question sur les hooks.\",\n      dateEnvoi: new Date(\"2024-01-15T09:15:00Z\"),\n      expediteur: this.currentUser,\n      destinataire: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      }\n    }, {\n      id: 3,\n      expediteurId: participantId,\n      destinataireId: this.currentUser.id,\n      contenu: \"Parfait ! N'hésitez pas à me poser toutes vos questions. Les hooks peuvent sembler complexes au début.\",\n      dateEnvoi: new Date(\"2024-01-15T09:30:00Z\"),\n      expediteur: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      },\n      destinataire: this.currentUser\n    }, {\n      id: 4,\n      expediteurId: participantId,\n      destinataireId: this.currentUser.id,\n      contenu: \"Bonjour, avez-vous des questions sur le cours React ?\",\n      dateEnvoi: new Date(\"2024-01-15T10:30:00Z\"),\n      expediteur: {\n        id: participantId,\n        nom: \"Dupont\",\n        prenom: \"Jean\",\n        email: \"\",\n        role: \"Formateur\"\n      },\n      destinataire: this.currentUser\n    }];\n    // Uncomment to fetch from API\n    /*\n    this.messageService.getMessages(this.currentUser.id, participantId).subscribe({\n      next: (data) => {\n        this.messages = data;\n        // Mark messages as read\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des messages.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  sendMessage(event) {\n    if (event && event.key === \"Enter\" && !event.shiftKey) {\n      event.preventDefault();\n    } else if (event && event.key !== \"Enter\") {\n      return; // Only proceed on Enter key press\n    }\n\n    if (!this.newMessageContent.trim() || !this.selectedConversationId || !this.currentUser) return;\n    const message = {\n      expediteurId: this.currentUser.id,\n      destinataireId: this.selectedConversationId,\n      contenu: this.newMessageContent,\n      dateEnvoi: new Date()\n    };\n    // Mock message sending\n    this.messages.push(message);\n    this.newMessageContent = \"\";\n    this.snackBar.open(\"Message envoyé (simulé) !\", \"Fermer\", {\n      duration: 1000\n    });\n    // Uncomment to send via API\n    /*\n    this.messageService.envoyerMessage(message).subscribe({\n      next: (res) => {\n        this.messages.push(message); // Add to local list after successful send\n        this.newMessageContent = '';\n        this.snackBar.open('Message envoyé !', 'Fermer', { duration: 1000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de l\\'envoi du message.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  getInitials(name) {\n    const parts = name.split(\" \");\n    if (parts.length >= 2) {\n      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();\n    } else if (parts.length === 1 && parts[0].length > 0) {\n      return parts[0][0].toUpperCase();\n    }\n    return \"\";\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    const now = new Date();\n    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 24 && d.getDate() === now.getDate()) {\n      return d.toLocaleTimeString(\"fr-FR\", {\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n      });\n    } else if (diffInHours < 48 && d.getDate() === now.getDate() - 1) {\n      return \"Hier\";\n    } else {\n      return d.toLocaleDateString(\"fr-FR\", {\n        day: \"2-digit\",\n        month: \"2-digit\"\n      });\n    }\n  }\n  getRoleChipClass(role) {\n    switch (role) {\n      case \"Client\":\n        return \"role-chip-Client\";\n      case \"Formateur\":\n        return \"role-chip-Formateur\";\n      case \"Admin\":\n        return \"role-chip-Admin\";\n      default:\n        return \"\";\n    }\n  }\n};\nMessagesComponent = __decorate([Component({\n  selector: \"app-messages\",\n  template: `\n    <div class=\"messages-container\">\n      <div class=\"messages-wrapper\">\n        <!-- Conversation List -->\n        <mat-card class=\"conversation-list-card\">\n          <mat-card-header>\n            <mat-card-title class=\"card-title-with-icon\">\n              <mat-icon>message</mat-icon>\n              Messages\n            </mat-card-title>\n            <button mat-icon-button><mat-icon>add</mat-icon></button>\n          </mat-card-header>\n          <mat-card-content class=\"conversation-search\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Rechercher une conversation...</mat-label>\n              <input matInput [(ngModel)]=\"searchTerm\" (input)=\"applySearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n          </mat-card-content>\n          <mat-nav-list class=\"conversations-scroll-area\">\n            <mat-list-item *ngFor=\"let conv of filteredConversations\" \n                           (click)=\"selectConversation(conv.participantId)\"\n                           [ngClass]=\"{'selected-conversation': selectedConversationId === conv.participantId}\">\n              <div matListItemAvatar class=\"avatar-placeholder\">\n                <span>{{ getInitials(conv.participantName) }}</span>\n              </div>\n              <div matListItemTitle class=\"conversation-title\">\n                <span>{{ conv.participantName }}</span>\n                <mat-chip-listbox>\n                  <mat-chip [class]=\"getRoleChipClass(conv.participantRole)\">{{ conv.participantRole }}</mat-chip>\n                </mat-chip-listbox>\n              </div>\n              <div matListItemLine class=\"conversation-last-message\">\n                <span class=\"message-content\">{{ conv.lastMessageContent }}</span>\n                <span class=\"message-date\">{{ formatDate(conv.lastMessageDate) }}</span>\n              </div>\n              <mat-chip-listbox *ngIf=\"conv.unreadCount > 0\" matListItemMeta>\n                <mat-chip color=\"warn\" selected>{{ conv.unreadCount }}</mat-chip>\n              </mat-chip-listbox>\n              <mat-divider></mat-divider>\n            </mat-list-item>\n            <div *ngIf=\"filteredConversations.length === 0\" class=\"no-conversations\">\n              <mat-icon>chat_bubble_outline</mat-icon>\n              <p>Aucune conversation trouvée.</p>\n            </div>\n          </mat-nav-list>\n        </mat-card>\n\n        <!-- Chat Area -->\n        <mat-card class=\"chat-area-card\">\n          <ng-container *ngIf=\"selectedConversationId; else noConversationSelected\">\n            <mat-card-header class=\"chat-header\">\n              <div class=\"chat-header-info\">\n                <div matListItemAvatar class=\"avatar-placeholder\">\n                  <span>{{ getInitials(selectedParticipant?.participantName || '') }}</span>\n                </div>\n                <div>\n                  <mat-card-title>{{ selectedParticipant?.participantName }}</mat-card-title>\n                  <mat-chip-listbox>\n                    <mat-chip [class]=\"getRoleChipClass(selectedParticipant?.participantRole || 'Client')\">\n                      {{ selectedParticipant?.participantRole }}\n                    </mat-chip>\n                  </mat-chip-listbox>\n                </div>\n              </div>\n            </mat-card-header>\n\n            <mat-card-content class=\"messages-display-area\">\n              <div *ngFor=\"let msg of messages\" \n                   class=\"message-bubble-wrapper\" \n                   [ngClass]=\"{'my-message': msg.expediteurId === currentUser?.id, 'other-message': msg.expediteurId !== currentUser?.id}\">\n                <div class=\"message-bubble\">\n                  <p class=\"message-content\">{{ msg.contenu }}</p>\n                  <span class=\"message-timestamp\">{{ formatDate(msg.dateEnvoi!) }}</span>\n                </div>\n              </div>\n            </mat-card-content>\n\n            <mat-card-actions class=\"message-input-area\">\n              <mat-form-field appearance=\"outline\" class=\"full-width message-input-field\">\n                <textarea matInput placeholder=\"Tapez votre message...\" \n                          [(ngModel)]=\"newMessageContent\" \n                          (keydown.enter)=\"sendMessage($event as KeyboardEvent)\"\n                          rows=\"1\"></textarea>\n              </mat-form-field>\n              <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!newMessageContent.trim()\">\n                <mat-icon>send</mat-icon>\n              </button>\n            </mat-card-actions>\n          </ng-container>\n\n          <ng-template #noConversationSelected>\n            <div class=\"no-conversation-selected\">\n              <mat-icon>chat</mat-icon>\n              <p>Sélectionnez une conversation pour commencer</p>\n            </div>\n          </ng-template>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .messages-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      padding: 2rem;\n    }\n\n    .messages-wrapper {\n      display: grid;\n      grid-template-columns: 1fr 2fr;\n      gap: 1.5rem;\n      max-width: 1400px;\n      margin: 0 auto;\n      height: calc(100vh - 4rem); /* Adjust height to fit viewport */\n    }\n\n    @media (max-width: 960px) {\n      .messages-wrapper {\n        grid-template-columns: 1fr;\n        height: auto;\n      }\n      .conversation-list-card {\n        height: auto;\n        min-height: 300px;\n      }\n      .chat-area-card {\n        height: 600px; /* Fixed height for chat on small screens */\n      }\n    }\n\n    .conversation-list-card, .chat-area-card {\n      display: flex;\n      flex-direction: column;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .conversation-list-card mat-card-header {\n      padding-bottom: 0;\n    }\n\n    .card-title-with-icon {\n      display: flex;\n      align-items: center;\n      font-size: 1.4rem;\n      font-weight: 600;\n    }\n\n    .card-title-with-icon mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .conversation-search {\n      padding: 1rem 1.5rem 0.5rem;\n    }\n\n    .conversation-search .full-width {\n      width: 100%;\n    }\n\n    .conversations-scroll-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n\n    .mat-list-item {\n      height: auto !important;\n      padding: 1rem 1.5rem;\n      cursor: pointer;\n      transition: background-color 0.2s ease-in-out;\n    }\n\n    .mat-list-item:hover {\n      background-color: #f5f5f5;\n    }\n\n    .mat-list-item.selected-conversation {\n      background-color: #ede7f6; /* Light purple */\n      border-left: 4px solid #673ab7; /* Purple */\n    }\n\n    .avatar-placeholder {\n      width: 48px;\n      height: 48px;\n      border-radius: 50%;\n      background-color: #e1bee7; /* Light purple */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 1.1rem;\n      font-weight: bold;\n      color: #8e24aa; /* Dark purple */\n      flex-shrink: 0;\n    }\n\n    .conversation-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 500;\n      font-size: 1rem;\n    }\n\n    .conversation-last-message {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-size: 0.85rem;\n      color: #777;\n      margin-top: 0.2rem;\n    }\n\n    .message-content {\n      flex-grow: 1;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      margin-right: 0.5rem;\n    }\n\n    .message-date {\n      flex-shrink: 0;\n    }\n\n    .mat-chip {\n      font-size: 0.7rem;\n      padding: 0.2rem 0.5rem;\n      height: auto;\n    }\n\n    .role-chip-Client { background-color: #e0e0e0; color: #424242; }\n    .role-chip-Formateur { background-color: #bbdefb; color: #1976d2; }\n    .role-chip-Admin { background-color: #e1bee7; color: #8e24aa; }\n\n    .no-conversations {\n      text-align: center;\n      padding: 2rem;\n      color: #999;\n    }\n\n    .no-conversations mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    /* Chat Area */\n    .chat-area-card {\n      background-color: #fff;\n    }\n\n    .chat-header {\n      border-bottom: 1px solid #eee;\n      padding: 1rem 1.5rem;\n    }\n\n    .chat-header-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .chat-header-info mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .messages-display-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 1.5rem;\n      background-color: #fcfcfc;\n    }\n\n    .message-bubble-wrapper {\n      display: flex;\n      margin-bottom: 1rem;\n    }\n\n    .message-bubble-wrapper.my-message {\n      justify-content: flex-end;\n    }\n\n    .message-bubble-wrapper.other-message {\n      justify-content: flex-start;\n    }\n\n    .message-bubble {\n      max-width: 70%;\n      padding: 0.8rem 1.2rem;\n      border-radius: 18px;\n      position: relative;\n      word-wrap: break-word;\n    }\n\n    .my-message .message-bubble {\n      background-color: #673ab7; /* Purple */\n      color: white;\n      border-bottom-right-radius: 4px;\n    }\n\n    .other-message .message-bubble {\n      background-color: #e0e0e0; /* Light gray */\n      color: #333;\n      border-bottom-left-radius: 4px;\n    }\n\n    .message-bubble p {\n      margin: 0;\n      font-size: 0.95rem;\n      line-height: 1.4;\n    }\n\n    .message-timestamp {\n      font-size: 0.75rem;\n      margin-top: 0.5rem;\n      display: block;\n      text-align: right;\n      color: rgba(255, 255, 255, 0.7); /* For my messages */\n    }\n\n    .other-message .message-timestamp {\n      color: #777;\n    }\n\n    .message-input-area {\n      border-top: 1px solid #eee;\n      padding: 1rem 1.5rem;\n      display: flex;\n      align-items: flex-end;\n      gap: 0.8rem;\n    }\n\n    .message-input-field {\n      flex-grow: 1;\n    }\n\n    .message-input-field textarea {\n      min-height: 40px;\n      max-height: 120px;\n      overflow-y: auto;\n    }\n\n    .message-input-area button {\n      flex-shrink: 0;\n    }\n\n    .no-conversation-selected {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      color: #999;\n      text-align: center;\n    }\n\n    .no-conversation-selected mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n    }\n  `]\n})], MessagesComponent);", "map": {"version": 3, "names": ["Component", "MessagesComponent", "constructor", "messageService", "authService", "snackBar", "conversations", "filteredConversations", "selectedConversationId", "messages", "newMessageContent", "searchTerm", "currentUser", "selectedParticipant", "ngOnInit", "currentUser$", "subscribe", "user", "loadConversations", "participantId", "participantName", "participantRole", "lastMessageContent", "lastMessageDate", "Date", "unreadCount", "applySearch", "filter", "conv", "toLowerCase", "includes", "selectConversation", "find", "c", "loadMessages", "id", "expediteurId", "destinataireId", "contenu", "dateEnvoi", "expediteur", "nom", "prenom", "email", "role", "destina<PERSON>", "sendMessage", "event", "key", "shift<PERSON>ey", "preventDefault", "trim", "message", "push", "open", "duration", "getInitials", "name", "parts", "split", "length", "toUpperCase", "formatDate", "date", "d", "now", "diffInHours", "getTime", "getDate", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "day", "month", "getRoleChipClass", "__decorate", "selector", "template", "styles"], "sources": ["C:\\e-learning\\src\\app\\features\\messages\\messages.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { MessageService } from \"../../core/services/message.service\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { Message } from \"../../core/models/message.model\"\nimport { User } from \"../../core/models/user.model\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\n\ninterface ConversationPreview {\n  participantId: number\n  participantName: string\n  participantRole: \"Client\" | \"Formateur\" | \"Admin\"\n  lastMessageContent: string\n  lastMessageDate: Date\n  unreadCount: number\n}\n\n@Component({\n  selector: \"app-messages\",\n  template: `\n    <div class=\"messages-container\">\n      <div class=\"messages-wrapper\">\n        <!-- Conversation List -->\n        <mat-card class=\"conversation-list-card\">\n          <mat-card-header>\n            <mat-card-title class=\"card-title-with-icon\">\n              <mat-icon>message</mat-icon>\n              Messages\n            </mat-card-title>\n            <button mat-icon-button><mat-icon>add</mat-icon></button>\n          </mat-card-header>\n          <mat-card-content class=\"conversation-search\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Rechercher une conversation...</mat-label>\n              <input matInput [(ngModel)]=\"searchTerm\" (input)=\"applySearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n          </mat-card-content>\n          <mat-nav-list class=\"conversations-scroll-area\">\n            <mat-list-item *ngFor=\"let conv of filteredConversations\" \n                           (click)=\"selectConversation(conv.participantId)\"\n                           [ngClass]=\"{'selected-conversation': selectedConversationId === conv.participantId}\">\n              <div matListItemAvatar class=\"avatar-placeholder\">\n                <span>{{ getInitials(conv.participantName) }}</span>\n              </div>\n              <div matListItemTitle class=\"conversation-title\">\n                <span>{{ conv.participantName }}</span>\n                <mat-chip-listbox>\n                  <mat-chip [class]=\"getRoleChipClass(conv.participantRole)\">{{ conv.participantRole }}</mat-chip>\n                </mat-chip-listbox>\n              </div>\n              <div matListItemLine class=\"conversation-last-message\">\n                <span class=\"message-content\">{{ conv.lastMessageContent }}</span>\n                <span class=\"message-date\">{{ formatDate(conv.lastMessageDate) }}</span>\n              </div>\n              <mat-chip-listbox *ngIf=\"conv.unreadCount > 0\" matListItemMeta>\n                <mat-chip color=\"warn\" selected>{{ conv.unreadCount }}</mat-chip>\n              </mat-chip-listbox>\n              <mat-divider></mat-divider>\n            </mat-list-item>\n            <div *ngIf=\"filteredConversations.length === 0\" class=\"no-conversations\">\n              <mat-icon>chat_bubble_outline</mat-icon>\n              <p>Aucune conversation trouvée.</p>\n            </div>\n          </mat-nav-list>\n        </mat-card>\n\n        <!-- Chat Area -->\n        <mat-card class=\"chat-area-card\">\n          <ng-container *ngIf=\"selectedConversationId; else noConversationSelected\">\n            <mat-card-header class=\"chat-header\">\n              <div class=\"chat-header-info\">\n                <div matListItemAvatar class=\"avatar-placeholder\">\n                  <span>{{ getInitials(selectedParticipant?.participantName || '') }}</span>\n                </div>\n                <div>\n                  <mat-card-title>{{ selectedParticipant?.participantName }}</mat-card-title>\n                  <mat-chip-listbox>\n                    <mat-chip [class]=\"getRoleChipClass(selectedParticipant?.participantRole || 'Client')\">\n                      {{ selectedParticipant?.participantRole }}\n                    </mat-chip>\n                  </mat-chip-listbox>\n                </div>\n              </div>\n            </mat-card-header>\n\n            <mat-card-content class=\"messages-display-area\">\n              <div *ngFor=\"let msg of messages\" \n                   class=\"message-bubble-wrapper\" \n                   [ngClass]=\"{'my-message': msg.expediteurId === currentUser?.id, 'other-message': msg.expediteurId !== currentUser?.id}\">\n                <div class=\"message-bubble\">\n                  <p class=\"message-content\">{{ msg.contenu }}</p>\n                  <span class=\"message-timestamp\">{{ formatDate(msg.dateEnvoi!) }}</span>\n                </div>\n              </div>\n            </mat-card-content>\n\n            <mat-card-actions class=\"message-input-area\">\n              <mat-form-field appearance=\"outline\" class=\"full-width message-input-field\">\n                <textarea matInput placeholder=\"Tapez votre message...\" \n                          [(ngModel)]=\"newMessageContent\" \n                          (keydown.enter)=\"sendMessage($event as KeyboardEvent)\"\n                          rows=\"1\"></textarea>\n              </mat-form-field>\n              <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!newMessageContent.trim()\">\n                <mat-icon>send</mat-icon>\n              </button>\n            </mat-card-actions>\n          </ng-container>\n\n          <ng-template #noConversationSelected>\n            <div class=\"no-conversation-selected\">\n              <mat-icon>chat</mat-icon>\n              <p>Sélectionnez une conversation pour commencer</p>\n            </div>\n          </ng-template>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .messages-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      padding: 2rem;\n    }\n\n    .messages-wrapper {\n      display: grid;\n      grid-template-columns: 1fr 2fr;\n      gap: 1.5rem;\n      max-width: 1400px;\n      margin: 0 auto;\n      height: calc(100vh - 4rem); /* Adjust height to fit viewport */\n    }\n\n    @media (max-width: 960px) {\n      .messages-wrapper {\n        grid-template-columns: 1fr;\n        height: auto;\n      }\n      .conversation-list-card {\n        height: auto;\n        min-height: 300px;\n      }\n      .chat-area-card {\n        height: 600px; /* Fixed height for chat on small screens */\n      }\n    }\n\n    .conversation-list-card, .chat-area-card {\n      display: flex;\n      flex-direction: column;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .conversation-list-card mat-card-header {\n      padding-bottom: 0;\n    }\n\n    .card-title-with-icon {\n      display: flex;\n      align-items: center;\n      font-size: 1.4rem;\n      font-weight: 600;\n    }\n\n    .card-title-with-icon mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .conversation-search {\n      padding: 1rem 1.5rem 0.5rem;\n    }\n\n    .conversation-search .full-width {\n      width: 100%;\n    }\n\n    .conversations-scroll-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n\n    .mat-list-item {\n      height: auto !important;\n      padding: 1rem 1.5rem;\n      cursor: pointer;\n      transition: background-color 0.2s ease-in-out;\n    }\n\n    .mat-list-item:hover {\n      background-color: #f5f5f5;\n    }\n\n    .mat-list-item.selected-conversation {\n      background-color: #ede7f6; /* Light purple */\n      border-left: 4px solid #673ab7; /* Purple */\n    }\n\n    .avatar-placeholder {\n      width: 48px;\n      height: 48px;\n      border-radius: 50%;\n      background-color: #e1bee7; /* Light purple */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 1.1rem;\n      font-weight: bold;\n      color: #8e24aa; /* Dark purple */\n      flex-shrink: 0;\n    }\n\n    .conversation-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 500;\n      font-size: 1rem;\n    }\n\n    .conversation-last-message {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-size: 0.85rem;\n      color: #777;\n      margin-top: 0.2rem;\n    }\n\n    .message-content {\n      flex-grow: 1;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      margin-right: 0.5rem;\n    }\n\n    .message-date {\n      flex-shrink: 0;\n    }\n\n    .mat-chip {\n      font-size: 0.7rem;\n      padding: 0.2rem 0.5rem;\n      height: auto;\n    }\n\n    .role-chip-Client { background-color: #e0e0e0; color: #424242; }\n    .role-chip-Formateur { background-color: #bbdefb; color: #1976d2; }\n    .role-chip-Admin { background-color: #e1bee7; color: #8e24aa; }\n\n    .no-conversations {\n      text-align: center;\n      padding: 2rem;\n      color: #999;\n    }\n\n    .no-conversations mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    /* Chat Area */\n    .chat-area-card {\n      background-color: #fff;\n    }\n\n    .chat-header {\n      border-bottom: 1px solid #eee;\n      padding: 1rem 1.5rem;\n    }\n\n    .chat-header-info {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .chat-header-info mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .messages-display-area {\n      flex-grow: 1;\n      overflow-y: auto;\n      padding: 1.5rem;\n      background-color: #fcfcfc;\n    }\n\n    .message-bubble-wrapper {\n      display: flex;\n      margin-bottom: 1rem;\n    }\n\n    .message-bubble-wrapper.my-message {\n      justify-content: flex-end;\n    }\n\n    .message-bubble-wrapper.other-message {\n      justify-content: flex-start;\n    }\n\n    .message-bubble {\n      max-width: 70%;\n      padding: 0.8rem 1.2rem;\n      border-radius: 18px;\n      position: relative;\n      word-wrap: break-word;\n    }\n\n    .my-message .message-bubble {\n      background-color: #673ab7; /* Purple */\n      color: white;\n      border-bottom-right-radius: 4px;\n    }\n\n    .other-message .message-bubble {\n      background-color: #e0e0e0; /* Light gray */\n      color: #333;\n      border-bottom-left-radius: 4px;\n    }\n\n    .message-bubble p {\n      margin: 0;\n      font-size: 0.95rem;\n      line-height: 1.4;\n    }\n\n    .message-timestamp {\n      font-size: 0.75rem;\n      margin-top: 0.5rem;\n      display: block;\n      text-align: right;\n      color: rgba(255, 255, 255, 0.7); /* For my messages */\n    }\n\n    .other-message .message-timestamp {\n      color: #777;\n    }\n\n    .message-input-area {\n      border-top: 1px solid #eee;\n      padding: 1rem 1.5rem;\n      display: flex;\n      align-items: flex-end;\n      gap: 0.8rem;\n    }\n\n    .message-input-field {\n      flex-grow: 1;\n    }\n\n    .message-input-field textarea {\n      min-height: 40px;\n      max-height: 120px;\n      overflow-y: auto;\n    }\n\n    .message-input-area button {\n      flex-shrink: 0;\n    }\n\n    .no-conversation-selected {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      color: #999;\n      text-align: center;\n    }\n\n    .no-conversation-selected mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n    }\n  `,\n  ],\n})\nexport class MessagesComponent implements OnInit {\n  conversations: ConversationPreview[] = []\n  filteredConversations: ConversationPreview[] = []\n  selectedConversationId: number | null = null\n  messages: Message[] = []\n  newMessageContent = \"\"\n  searchTerm = \"\"\n  currentUser: User | null = null\n  selectedParticipant: ConversationPreview | null = null\n\n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (this.currentUser) {\n        this.loadConversations()\n      }\n    })\n  }\n\n  loadConversations(): void {\n    // Mock data for demonstration\n    this.conversations = [\n      {\n        participantId: 2, // Formateur Jean Dupont\n        participantName: \"Jean Dupont\",\n        participantRole: \"Formateur\",\n        lastMessageContent: \"Bonjour, avez-vous des questions sur le cours React ?\",\n        lastMessageDate: new Date(\"2024-01-15T10:30:00Z\"),\n        unreadCount: 2,\n      },\n      {\n        participantId: 3, // Client Sophie Bernard\n        participantName: \"Sophie Bernard\",\n        participantRole: \"Client\",\n        lastMessageContent: \"Merci pour votre aide !\",\n        lastMessageDate: new Date(\"2024-01-14T15:45:00Z\"),\n        unreadCount: 0,\n      },\n      {\n        participantId: 4, // Admin Admin System\n        participantName: \"Admin System\",\n        participantRole: \"Admin\",\n        lastMessageContent: \"Votre demande a été traitée.\",\n        lastMessageDate: new Date(\"2024-01-13T11:00:00Z\"),\n        unreadCount: 0,\n      },\n    ]\n    this.applySearch()\n\n    // Uncomment to fetch from API\n    /*\n    // This would require a backend endpoint to get conversation previews for a user\n    // For example: this.messageService.getConversations(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  applySearch(): void {\n    this.filteredConversations = this.conversations.filter((conv) =>\n      conv.participantName.toLowerCase().includes(this.searchTerm.toLowerCase()),\n    )\n  }\n\n  selectConversation(participantId: number): void {\n    this.selectedConversationId = participantId\n    this.selectedParticipant = this.conversations.find((c) => c.participantId === participantId) || null\n    this.loadMessages(participantId)\n  }\n\n  loadMessages(participantId: number): void {\n    if (!this.currentUser) return\n\n    // Mock messages for the selected conversation\n    this.messages = [\n      {\n        id: 1,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu: \"Bonjour, j'espère que vous appréciez le cours React !\",\n        dateEnvoi: new Date(\"2024-01-15T09:00:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n      {\n        id: 2,\n        expediteurId: this.currentUser.id,\n        destinataireId: participantId,\n        contenu: \"Bonjour Jean, oui c'est très intéressant ! J'ai une question sur les hooks.\",\n        dateEnvoi: new Date(\"2024-01-15T09:15:00Z\"),\n        expediteur: this.currentUser,\n        destinataire: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n      },\n      {\n        id: 3,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu:\n          \"Parfait ! N'hésitez pas à me poser toutes vos questions. Les hooks peuvent sembler complexes au début.\",\n        dateEnvoi: new Date(\"2024-01-15T09:30:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n      {\n        id: 4,\n        expediteurId: participantId,\n        destinataireId: this.currentUser.id,\n        contenu: \"Bonjour, avez-vous des questions sur le cours React ?\",\n        dateEnvoi: new Date(\"2024-01-15T10:30:00Z\"),\n        expediteur: { id: participantId, nom: \"Dupont\", prenom: \"Jean\", email: \"\", role: \"Formateur\" },\n        destinataire: this.currentUser,\n      },\n    ]\n\n    // Uncomment to fetch from API\n    /*\n    this.messageService.getMessages(this.currentUser.id, participantId).subscribe({\n      next: (data) => {\n        this.messages = data;\n        // Mark messages as read\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement des messages.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  sendMessage(event?: KeyboardEvent): void {\n    if (event && event.key === \"Enter\" && !event.shiftKey) {\n      event.preventDefault()\n    } else if (event && event.key !== \"Enter\") {\n      return // Only proceed on Enter key press\n    }\n\n    if (!this.newMessageContent.trim() || !this.selectedConversationId || !this.currentUser) return\n\n    const message: Message = {\n      expediteurId: this.currentUser.id,\n      destinataireId: this.selectedConversationId,\n      contenu: this.newMessageContent,\n      dateEnvoi: new Date(),\n    }\n\n    // Mock message sending\n    this.messages.push(message)\n    this.newMessageContent = \"\"\n    this.snackBar.open(\"Message envoyé (simulé) !\", \"Fermer\", { duration: 1000 })\n\n    // Uncomment to send via API\n    /*\n    this.messageService.envoyerMessage(message).subscribe({\n      next: (res) => {\n        this.messages.push(message); // Add to local list after successful send\n        this.newMessageContent = '';\n        this.snackBar.open('Message envoyé !', 'Fermer', { duration: 1000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de l\\'envoi du message.', 'Fermer', { duration: 3000 });\n        console.error(err);\n      }\n    });\n    */\n  }\n\n  getInitials(name: string): string {\n    const parts = name.split(\" \")\n    if (parts.length >= 2) {\n      return `${parts[0][0]}${parts[1][0]}`.toUpperCase()\n    } else if (parts.length === 1 && parts[0].length > 0) {\n      return parts[0][0].toUpperCase()\n    }\n    return \"\"\n  }\n\n  formatDate(date: Date): string {\n    const d = new Date(date)\n    const now = new Date()\n    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60)\n\n    if (diffInHours < 24 && d.getDate() === now.getDate()) {\n      return d.toLocaleTimeString(\"fr-FR\", { hour: \"2-digit\", minute: \"2-digit\" })\n    } else if (diffInHours < 48 && d.getDate() === now.getDate() - 1) {\n      return \"Hier\"\n    } else {\n      return d.toLocaleDateString(\"fr-FR\", { day: \"2-digit\", month: \"2-digit\" })\n    }\n  }\n\n  getRoleChipClass(role: string): string {\n    switch (role) {\n      case \"Client\":\n        return \"role-chip-Client\"\n      case \"Formateur\":\n        return \"role-chip-Formateur\"\n      case \"Admin\":\n        return \"role-chip-Admin\"\n      default:\n        return \"\"\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAuY1C,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAU5BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAZlB,KAAAC,aAAa,GAA0B,EAAE;IACzC,KAAAC,qBAAqB,GAA0B,EAAE;IACjD,KAAAC,sBAAsB,GAAkB,IAAI;IAC5C,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,mBAAmB,GAA+B,IAAI;EAMnD;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACV,WAAW,CAACW,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACL,WAAW,GAAGK,IAAI;MACvB,IAAI,IAAI,CAACL,WAAW,EAAE;QACpB,IAAI,CAACM,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACZ,aAAa,GAAG,CACnB;MACEa,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,aAAa;MAC9BC,eAAe,EAAE,WAAW;MAC5BC,kBAAkB,EAAE,uDAAuD;MAC3EC,eAAe,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;MACjDC,WAAW,EAAE;KACd,EACD;MACEN,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,gBAAgB;MACjCC,eAAe,EAAE,QAAQ;MACzBC,kBAAkB,EAAE,yBAAyB;MAC7CC,eAAe,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;MACjDC,WAAW,EAAE;KACd,EACD;MACEN,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,cAAc;MAC/BC,eAAe,EAAE,OAAO;MACxBC,kBAAkB,EAAE,8BAA8B;MAClDC,eAAe,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;MACjDC,WAAW,EAAE;KACd,CACF;IACD,IAAI,CAACC,WAAW,EAAE;IAElB;IACA;;;;EAIF;;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACnB,qBAAqB,GAAG,IAAI,CAACD,aAAa,CAACqB,MAAM,CAAEC,IAAI,IAC1DA,IAAI,CAACR,eAAe,CAACS,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnB,UAAU,CAACkB,WAAW,EAAE,CAAC,CAC3E;EACH;EAEAE,kBAAkBA,CAACZ,aAAqB;IACtC,IAAI,CAACX,sBAAsB,GAAGW,aAAa;IAC3C,IAAI,CAACN,mBAAmB,GAAG,IAAI,CAACP,aAAa,CAAC0B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACd,aAAa,KAAKA,aAAa,CAAC,IAAI,IAAI;IACpG,IAAI,CAACe,YAAY,CAACf,aAAa,CAAC;EAClC;EAEAe,YAAYA,CAACf,aAAqB;IAChC,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;IAEvB;IACA,IAAI,CAACH,QAAQ,GAAG,CACd;MACE0B,EAAE,EAAE,CAAC;MACLC,YAAY,EAAEjB,aAAa;MAC3BkB,cAAc,EAAE,IAAI,CAACzB,WAAW,CAACuB,EAAE;MACnCG,OAAO,EAAE,uDAAuD;MAChEC,SAAS,EAAE,IAAIf,IAAI,CAAC,sBAAsB,CAAC;MAC3CgB,UAAU,EAAE;QAAEL,EAAE,EAAEhB,aAAa;QAAEsB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAACjC;KACpB,EACD;MACEuB,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACuB,EAAE;MACjCE,cAAc,EAAElB,aAAa;MAC7BmB,OAAO,EAAE,6EAA6E;MACtFC,SAAS,EAAE,IAAIf,IAAI,CAAC,sBAAsB,CAAC;MAC3CgB,UAAU,EAAE,IAAI,CAAC5B,WAAW;MAC5BiC,YAAY,EAAE;QAAEV,EAAE,EAAEhB,aAAa;QAAEsB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW;KAC/F,EACD;MACET,EAAE,EAAE,CAAC;MACLC,YAAY,EAAEjB,aAAa;MAC3BkB,cAAc,EAAE,IAAI,CAACzB,WAAW,CAACuB,EAAE;MACnCG,OAAO,EACL,wGAAwG;MAC1GC,SAAS,EAAE,IAAIf,IAAI,CAAC,sBAAsB,CAAC;MAC3CgB,UAAU,EAAE;QAAEL,EAAE,EAAEhB,aAAa;QAAEsB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAACjC;KACpB,EACD;MACEuB,EAAE,EAAE,CAAC;MACLC,YAAY,EAAEjB,aAAa;MAC3BkB,cAAc,EAAE,IAAI,CAACzB,WAAW,CAACuB,EAAE;MACnCG,OAAO,EAAE,uDAAuD;MAChEC,SAAS,EAAE,IAAIf,IAAI,CAAC,sBAAsB,CAAC;MAC3CgB,UAAU,EAAE;QAAEL,EAAE,EAAEhB,aAAa;QAAEsB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAW,CAAE;MAC9FC,YAAY,EAAE,IAAI,CAACjC;KACpB,CACF;IAED;IACA;;;;;;;;;;;;EAYF;;EAEAkC,WAAWA,CAACC,KAAqB;IAC/B,IAAIA,KAAK,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MACrDF,KAAK,CAACG,cAAc,EAAE;KACvB,MAAM,IAAIH,KAAK,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzC,OAAM,CAAC;;;IAGT,IAAI,CAAC,IAAI,CAACtC,iBAAiB,CAACyC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC3C,sBAAsB,IAAI,CAAC,IAAI,CAACI,WAAW,EAAE;IAEzF,MAAMwC,OAAO,GAAY;MACvBhB,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACuB,EAAE;MACjCE,cAAc,EAAE,IAAI,CAAC7B,sBAAsB;MAC3C8B,OAAO,EAAE,IAAI,CAAC5B,iBAAiB;MAC/B6B,SAAS,EAAE,IAAIf,IAAI;KACpB;IAED;IACA,IAAI,CAACf,QAAQ,CAAC4C,IAAI,CAACD,OAAO,CAAC;IAC3B,IAAI,CAAC1C,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACL,QAAQ,CAACiD,IAAI,CAAC,2BAA2B,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAE7E;IACA;;;;;;;;;;;;;EAaF;;EAEAC,WAAWA,CAACC,IAAY;IACtB,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;MACrB,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACG,WAAW,EAAE;KACpD,MAAM,IAAIH,KAAK,CAACE,MAAM,KAAK,CAAC,IAAIF,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;MACpD,OAAOF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACG,WAAW,EAAE;;IAElC,OAAO,EAAE;EACX;EAEAC,UAAUA,CAACC,IAAU;IACnB,MAAMC,CAAC,GAAG,IAAIxC,IAAI,CAACuC,IAAI,CAAC;IACxB,MAAME,GAAG,GAAG,IAAIzC,IAAI,EAAE;IACtB,MAAM0C,WAAW,GAAG,CAACD,GAAG,CAACE,OAAO,EAAE,GAAGH,CAAC,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEpE,IAAID,WAAW,GAAG,EAAE,IAAIF,CAAC,CAACI,OAAO,EAAE,KAAKH,GAAG,CAACG,OAAO,EAAE,EAAE;MACrD,OAAOJ,CAAC,CAACK,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE,CAAC;KAC7E,MAAM,IAAIL,WAAW,GAAG,EAAE,IAAIF,CAAC,CAACI,OAAO,EAAE,KAAKH,GAAG,CAACG,OAAO,EAAE,GAAG,CAAC,EAAE;MAChE,OAAO,MAAM;KACd,MAAM;MACL,OAAOJ,CAAC,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAS,CAAE,CAAC;;EAE9E;EAEAC,gBAAgBA,CAAC/B,IAAY;IAC3B,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,kBAAkB;MAC3B,KAAK,WAAW;QACd,OAAO,qBAAqB;MAC9B,KAAK,OAAO;QACV,OAAO,iBAAiB;MAC1B;QACE,OAAO,EAAE;;EAEf;CACD;AA9MY3C,iBAAiB,GAAA2E,UAAA,EAvX7B5E,SAAS,CAAC;EACT6E,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoGT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4QD;CAEF,CAAC,C,EACW9E,iBAAiB,CA8M7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}