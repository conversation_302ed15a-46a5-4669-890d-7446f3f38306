{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CourseService {\n  constructor(http) {\n    this.http = http;\n  }\n  // GET: Tous les cours\n  getAllCours() {\n    return this.http.get(`${environment.urlApi}cours`);\n  }\n  // GET: Un cours par ID\n  getCours(id) {\n    return this.http.get(`${environment.urlApi}cours/${id}`);\n  }\n  // PUT: Modifier un cours\n  modifierCours(id, cours) {\n    return this.http.put(`${environment.urlApi}cours/${id}`, cours);\n  }\n  // DELETE: Supprimer un cours\n  supprimerCours(id) {\n    return this.http.delete(`${environment.urlApi}cours/${id}`);\n  }\n  // POST: Ajouter un contenu\n  ajouterContenu(coursId, contenu) {\n    return this.http.post(`${environment.urlApi}cours/${coursId}/ajouter-contenu`, contenu);\n  }\n  static {\n    this.ɵfac = function CourseService_Factory(t) {\n      return new (t || CourseService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CourseService,\n      factory: CourseService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "CourseService", "constructor", "http", "getAllCours", "get", "urlApi", "getCours", "id", "modifierCours", "cours", "put", "supprimerCours", "delete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coursId", "contenu", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\course.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Course, Contenu } from \"../models/course.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class CourseService {\n  constructor(private http: HttpClient) {}\n\n  // GET: Tous les cours\n  getAllCours(): Observable<Course[]> {\n    return this.http.get<Course[]>(`${environment.urlApi}cours`)\n  }\n\n  // GET: Un cours par ID\n  getCours(id: number): Observable<Course> {\n    return this.http.get<Course>(`${environment.urlApi}cours/${id}`)\n  }\n\n  // PUT: Modifier un cours\n  modifierCours(id: number, cours: Partial<Course>): Observable<any> {\n    return this.http.put(`${environment.urlApi}cours/${id}`, cours)\n  }\n\n  // DELETE: Supprimer un cours\n  supprimerCours(id: number): Observable<any> {\n    return this.http.delete(`${environment.urlApi}cours/${id}`)\n  }\n\n  // POST: Ajouter un contenu\n  ajouterContenu(coursId: number, contenu: Contenu): Observable<any> {\n    return this.http.post(`${environment.urlApi}cours/${coursId}/ajouter-contenu`, contenu)\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,aAAa;EACxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvC;EACAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACD,IAAI,CAACE,GAAG,CAAW,GAAGL,WAAW,CAACM,MAAM,OAAO,CAAC;EAC9D;EAEA;EACAC,QAAQA,CAACC,EAAU;IACjB,OAAO,IAAI,CAACL,IAAI,CAACE,GAAG,CAAS,GAAGL,WAAW,CAACM,MAAM,SAASE,EAAE,EAAE,CAAC;EAClE;EAEA;EACAC,aAAaA,CAACD,EAAU,EAAEE,KAAsB;IAC9C,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAC,GAAGX,WAAW,CAACM,MAAM,SAASE,EAAE,EAAE,EAAEE,KAAK,CAAC;EACjE;EAEA;EACAE,cAAcA,CAACJ,EAAU;IACvB,OAAO,IAAI,CAACL,IAAI,CAACU,MAAM,CAAC,GAAGb,WAAW,CAACM,MAAM,SAASE,EAAE,EAAE,CAAC;EAC7D;EAEA;EACAM,cAAcA,CAACC,OAAe,EAAEC,OAAgB;IAC9C,OAAO,IAAI,CAACb,IAAI,CAACc,IAAI,CAAC,GAAGjB,WAAW,CAACM,MAAM,SAASS,OAAO,kBAAkB,EAAEC,OAAO,CAAC;EACzF;;;uBA1BWf,aAAa,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAbpB,aAAa;MAAAqB,OAAA,EAAbrB,aAAa,CAAAsB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}