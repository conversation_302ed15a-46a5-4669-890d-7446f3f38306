import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import { BehaviorSubject, type Observable, tap } from "rxjs"
import type { Router } from "@angular/router"
import { environment } from "../../../environments/environment"
import type { User, LoginRequest, RegisterRequest, AuthResponse } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null)
  public currentUser$ = this.currentUserSubject.asObservable()

  constructor(
    private http: HttpClient,
    private router: Router,
  ) {
    const token = this.getToken()
    if (token) {
      // TODO: Validate token and get user info if needed
    }
  }

  login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${environment.urlApi}auth/login`, credentials).pipe(
      tap((response) => {
        localStorage.setItem("token", response.token)
        localStorage.setItem("userRole", response.role)
        // You might want to get user details after login
      }),
    )
  }

  register(userData: RegisterRequest): Observable<any> {
    // Note: Your backend doesn't have register endpoint, you might need to add it
    return this.http.post(`${environment.urlApi}auth/register`, userData)
  }

  getCurrentUser(): Observable<User> {
    return this.http.get<User>(`${environment.urlApi}auth/me`).pipe(tap((user) => this.currentUserSubject.next(user)))
  }

  logout(): void {
    localStorage.removeItem("token")
    localStorage.removeItem("userRole")
    this.currentUserSubject.next(null)
    this.router.navigate(["/"])
  }

  getToken(): string | null {
    return localStorage.getItem("token")
  }

  getUserRole(): string | null {
    return localStorage.getItem("userRole")
  }

  isAuthenticated(): boolean {
    return !!this.getToken()
  }

  getCurrentUserValue(): User | null {
    return this.currentUserSubject.value
  }
}
