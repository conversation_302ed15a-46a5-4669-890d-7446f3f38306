import { Injectable } from "@angular/core"
import { HttpClient, HttpHeaders } from "@angular/common/http"
import { BehaviorSubject, Observable, tap } from "rxjs"
import { Router } from "@angular/router"
import { environment } from "../../../environments/environment"
import { User, LoginRequest, AuthResponse } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null)
  public currentUser$ = this.currentUserSubject.asObservable()
  private apiUrl = `${environment.urlApi}auth`

  constructor(
    private http: HttpClient,
    private router: Router,
  ) {
    const token = this.getToken()
    const role = this.getUserRole()
    if (token && role) {
      // Décoder le token pour récupérer les infos utilisateur
      const userInfo = this.decodeToken(token)
      if (userInfo) {
        this.currentUserSubject.next({
          id: userInfo.id || 0,
          email: userInfo.email || '',
          nom: userInfo.nom || '',
          prenom: userInfo.prenom || '',
          role: role as "Client" | "Formateur" | "Admin",
        })
      }
    }
  }

  login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, credentials).pipe(
      tap((response) => {
        localStorage.setItem("token", response.token)
        localStorage.setItem("userRole", response.role)

        // Décoder le token pour récupérer les infos utilisateur
        const userInfo = this.decodeToken(response.token)
        if (userInfo) {
          this.currentUserSubject.next({
            id: userInfo.id || 0,
            email: userInfo.email || credentials.Email,
            nom: userInfo.nom || '',
            prenom: userInfo.prenom || '',
            role: response.role as "Client" | "Formateur" | "Admin",
          })
        }
      }),
    )
  }

  // Note: Votre backend n'a pas d'endpoint register, utilisez les contrôleurs spécifiques
  register(userData: any): Observable<any> {
    const endpoint = userData.accountType === 'formateur' ? 'formateur' : 'client'

    // ✅ Préparer les données selon le modèle .NET
    const registrationData = {
      email: userData.email,
      nom: userData.lastName,
      prenom: userData.firstName,
      role: userData.accountType === 'formateur' ? 'Formateur' : 'Client',
      // Initialiser les collections pour éviter les erreurs
      ...(userData.accountType === 'client' && {
        paiements: [],
        coursConsultes: [],
        resultatsQuiz: []
      }),
      ...(userData.accountType === 'formateur' && {
        coursCree: [],
        estValide: false
      })
    }

    console.log('Données d\'inscription envoyées:', registrationData)
    return this.http.post(`${environment.urlApi}${endpoint}`, registrationData)
  }

  // Décoder le token JWT pour extraire les informations utilisateur
  private decodeToken(token: string): any {
    try {
      const payload = token.split('.')[1]
      const decoded = atob(payload)
      return JSON.parse(decoded)
    } catch (error) {
      console.error('Erreur lors du décodage du token:', error)
      return null
    }
  }

  logout(): void {
    localStorage.removeItem("token")
    localStorage.removeItem("userRole")
    this.currentUserSubject.next(null)
    this.router.navigate(["/"])
  }

  getToken(): string | null {
    return localStorage.getItem("token")
  }

  getUserRole(): string | null {
    return localStorage.getItem("userRole")
  }

  isAuthenticated(): boolean {
    return !!this.getToken()
  }

  getCurrentUserValue(): User | null {
    return this.currentUserSubject.value
  }
}
