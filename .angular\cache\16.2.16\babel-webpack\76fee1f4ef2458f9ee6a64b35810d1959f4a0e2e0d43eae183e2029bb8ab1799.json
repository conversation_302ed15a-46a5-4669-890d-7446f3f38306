{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CertificateService {\n  constructor(http) {\n    this.http = http;\n  }\n  // GET: Télécharger un certificat PDF\n  telechargerCertificat(nomClient, prenomClient, titreCours, score) {\n    const params = {\n      nomClient,\n      prenomClient,\n      titreCours,\n      score: score.toString()\n    };\n    return this.http.get(`${environment.urlApi}certificats/telecharger`, {\n      params,\n      responseType: \"blob\"\n    });\n  }\n  static {\n    this.ɵfac = function CertificateService_Factory(t) {\n      return new (t || CertificateService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CertificateService,\n      factory: CertificateService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "CertificateService", "constructor", "http", "telechargerCertificat", "nomClient", "prenomClient", "titreCours", "score", "params", "toString", "get", "urlApi", "responseType", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\certificate.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class CertificateService {\n  constructor(private http: HttpClient) {}\n\n  // GET: Télécharger un certificat PDF\n  telechargerCertificat(nomClient: string, prenomClient: string, titreCours: string, score: number): Observable<Blob> {\n    const params = {\n      nomClient,\n      prenomClient,\n      titreCours,\n      score: score.toString(),\n    }\n\n    return this.http.get(`${environment.urlApi}certificats/telecharger`, {\n      params,\n      responseType: \"blob\",\n    })\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,kBAAkB;EAC7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvC;EACAC,qBAAqBA,CAACC,SAAiB,EAAEC,YAAoB,EAAEC,UAAkB,EAAEC,KAAa;IAC9F,MAAMC,MAAM,GAAG;MACbJ,SAAS;MACTC,YAAY;MACZC,UAAU;MACVC,KAAK,EAAEA,KAAK,CAACE,QAAQ;KACtB;IAED,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAC,GAAGX,WAAW,CAACY,MAAM,yBAAyB,EAAE;MACnEH,MAAM;MACNI,YAAY,EAAE;KACf,CAAC;EACJ;;;uBAhBWZ,kBAAkB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBhB,kBAAkB;MAAAiB,OAAA,EAAlBjB,kBAAkB,CAAAkB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}