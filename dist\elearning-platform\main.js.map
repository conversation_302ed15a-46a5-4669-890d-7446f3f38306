{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;AAC2D;AACP;;;AAEpD,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,0KAAqC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;CACnF,EACD;EACEJ,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,yKAAqC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,UAAU;CACnF,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAEA,CAAA,KAAM,8LAA+C,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,eAAe,CAAC;EAClGC,WAAW,EAAE,CAACT,8DAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,qOAA2C,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,aAAa;CAC5F,EACD;EACER,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,yKAAqC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,UAAU,CAAC;EACnFF,WAAW,EAAE,CAACT,8DAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,qOAA2C,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACO,aAAa,CAAC;EAC5FH,WAAW,EAAE,CAACT,8DAAS;CACxB,EACD;EACEE,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,wLAA6C,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACQ,cAAc,CAAC;EAC/FJ,WAAW,EAAE,CAACT,8DAAS;CACxB,EACD;EACEE,IAAI,EAAE,cAAc;EACpBC,YAAY,EAAEA,CAAA,KAAM,0MAAqD,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,kBAAkB,CAAC;EAC3GL,WAAW,EAAE,CAACT,8DAAS;CACxB,EACD;EACEE,IAAI,EAAE,IAAI;EACVa,UAAU,EAAE;CACb,CACF;AAMK,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBjB,yDAAY,CAACkB,OAAO,CAAChB,MAAM,CAAC,EAC5BF,yDAAY;IAAA;EAAA;;;sHAEXiB,gBAAgB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFjBrB,yDAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;AChClB,MAAOsB,YAAY;EAhBzBC,YAAA;IAiBE,KAAAC,KAAK,GAAG,oBAAoB;;;;uBADjBF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAbrBE,4DAAA,aAA2B;UACzBA,uDAAA,oBAA+B;UACjCA,0DAAA,EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN+C;AACqB;AACJ;AACT;AAEjE;AAC4D;AACF;AACJ;AACE;AACS;AACP;AACI;AACR;AACe;AACb;AACA;AACF;AACI;AACK;AACH;AACN;AACS;AACP;AACqB;AAEtB;AACT;AACwB;;AA0ChE,MAAO8B,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFR1C,wDAAY;IAAA;EAAA;;;iBAPb,CACT;QACE2C,OAAO,EAAEzB,mEAAiB;QAC1B0B,QAAQ,EAAEJ,gFAAe;QACzBK,KAAK,EAAE;OACR,CACF;MAAAhD,OAAA,GAlCCkB,oEAAa,EACbC,yFAAuB,EACvBC,kEAAgB,EAChBE,+DAAmB,EACnBC,uDAAW,EACXzB,iEAAgB;MAEhB;MACA0B,uEAAgB,EAChBC,qEAAe,EACfC,kEAAa,EACbC,oEAAc,EACdC,6EAAkB,EAClBC,sEAAe,EACfC,0EAAiB,EACjBC,kEAAa,EACbC,iFAAoB,EACpBC,oEAAc,EACdC,oEAAc,EACdC,kEAAa,EACbC,sEAAe,EACfC,2EAAiB,EACjBC,wEAAgB,EAChBC,kEAAa,EACbC,2EAAiB,EACjBC,oEAAc,EACdC,yFAAwB;IAAA;EAAA;;;sHAWfE,SAAS;IAAAK,YAAA,GAvCL9C,wDAAY;IAAAH,OAAA,GAEzBkB,oEAAa,EACbC,yFAAuB,EACvBC,kEAAgB,EAChBE,+DAAmB,EACnBC,uDAAW,EACXzB,iEAAgB;IAEhB;IACA0B,uEAAgB,EAChBC,qEAAe,EACfC,kEAAa,EACbC,oEAAc,EACdC,6EAAkB,EAClBC,sEAAe,EACfC,0EAAiB,EACjBC,kEAAa,EACbC,iFAAoB,EACpBC,oEAAc,EACdC,oEAAc,EACdC,kEAAa,EACbC,sEAAe,EACfC,2EAAiB,EACjBC,wEAAgB,EAChBC,kEAAa,EACbC,2EAAiB,EACjBC,oEAAc,EACdC,yFAAwB;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;ACrDtB,MAAO5D,SAAS;EACpBsB,YACU8C,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEH5D,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC2D,WAAW,CAACE,eAAe,EAAE,EAAE;MACtC,OAAO,IAAI;KACZ,MAAM;MACL,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC,OAAO,KAAK;;EAEhB;;;uBAbWvE,SAAS,EAAAgC,sDAAA,CAAAb,+DAAA,GAAAa,sDAAA,CAAA0C,mDAAA;IAAA;EAAA;;;aAAT1E,SAAS;MAAA4E,OAAA,EAAT5E,SAAS,CAAA6E,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;;;;;;;;;;;;;;;;;;ACAd,MAAOjB,eAAe;EAC1BvC,YAAoB8C,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CW,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,MAAMC,KAAK,GAAG,IAAI,CAACd,WAAW,CAACe,QAAQ,EAAE;IAEzC,IAAID,KAAK,EAAE;MACT,MAAME,OAAO,GAAGJ,GAAG,CAACK,KAAK,CAAC;QACxBC,OAAO,EAAEN,GAAG,CAACM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUL,KAAK,EAAE;OAC5D,CAAC;MACF,OAAOD,IAAI,CAACO,MAAM,CAACJ,OAAO,CAAC;;IAG7B,OAAOH,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;EACzB;;;uBAdWnB,eAAe,EAAA7B,sDAAA,CAAAb,+DAAA;IAAA;EAAA;;;aAAf0C,eAAe;MAAAe,OAAA,EAAff,eAAe,CAAAgB;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;ACH2B;AAEQ;;;;AAMzD,MAAOJ,WAAW;EAKtBnD,YACUsE,IAAgB,EAChBvB,MAAc;IADd,KAAAuB,IAAI,GAAJA,IAAI;IACJ,KAAAvB,MAAM,GAANA,MAAM;IANR,KAAAwB,kBAAkB,GAAG,IAAIJ,iDAAe,CAAc,IAAI,CAAC;IAC5D,KAAAK,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IACpD,KAAAC,MAAM,GAAG,GAAGL,kEAAW,CAACM,MAAM,MAAM;IAM1C,MAAMf,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,MAAMe,IAAI,GAAG,IAAI,CAACC,WAAW,EAAE;IAC/B,IAAIjB,KAAK,IAAIgB,IAAI,EAAE;MACjB;MACA,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACnB,KAAK,CAAC;MACxC,IAAIkB,QAAQ,EAAE;QACZ,IAAI,CAACP,kBAAkB,CAACZ,IAAI,CAAC;UAC3BqB,EAAE,EAAEF,QAAQ,CAACE,EAAE,IAAI,CAAC;UACpBC,KAAK,EAAEH,QAAQ,CAACG,KAAK,IAAI,EAAE;UAC3BC,GAAG,EAAEJ,QAAQ,CAACI,GAAG,IAAI,EAAE;UACvBC,MAAM,EAAEL,QAAQ,CAACK,MAAM,IAAI,EAAE;UAC7BP,IAAI,EAAEA;SACP,CAAC;;;EAGR;EAEAQ,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAe,GAAG,IAAI,CAACZ,MAAM,QAAQ,EAAEW,WAAW,CAAC,CAACE,IAAI,CAC3EnB,yCAAG,CAAEoB,QAAQ,IAAI;MACfC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC5B,KAAK,CAAC;MAC7C6B,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAACZ,IAAI,CAAC;MAE/C;MACA,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACS,QAAQ,CAAC5B,KAAK,CAAC;MACjD,IAAIkB,QAAQ,EAAE;QACZ,IAAI,CAACP,kBAAkB,CAACZ,IAAI,CAAC;UAC3BqB,EAAE,EAAEF,QAAQ,CAACE,EAAE,IAAI,CAAC;UACpBC,KAAK,EAAEH,QAAQ,CAACG,KAAK,IAAII,WAAW,CAACM,KAAK;UAC1CT,GAAG,EAAEJ,QAAQ,CAACI,GAAG,IAAI,EAAE;UACvBC,MAAM,EAAEL,QAAQ,CAACK,MAAM,IAAI,EAAE;UAC7BP,IAAI,EAAEY,QAAQ,CAACZ;SAChB,CAAC;;IAEN,CAAC,CAAC,CACH;EACH;EAEA;EACAgB,QAAQA,CAACC,QAAa;IACpB,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,WAAW,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ;IAE9E;IACA,MAAMC,gBAAgB,GAAG;MACvBf,KAAK,EAAEY,QAAQ,CAACZ,KAAK;MACrBC,GAAG,EAAEW,QAAQ,CAACI,QAAQ;MACtBd,MAAM,EAAEU,QAAQ,CAACK,SAAS;MAC1BtB,IAAI,EAAEiB,QAAQ,CAACE,WAAW,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ;MACnE;MACA,IAAIF,QAAQ,CAACE,WAAW,KAAK,QAAQ,IAAI;QACvCI,SAAS,EAAE,EAAE;QACbC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE;OAChB,CAAC;MACF,IAAIR,QAAQ,CAACE,WAAW,KAAK,WAAW,IAAI;QAC1CO,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;OACZ;KACF;IAEDC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAET,gBAAgB,CAAC;IACjE,OAAO,IAAI,CAAC1B,IAAI,CAACgB,IAAI,CAAC,GAAGjB,kEAAW,CAACM,MAAM,GAAGmB,QAAQ,EAAE,EAAEE,gBAAgB,CAAC;EAC7E;EAEA;EACQjB,WAAWA,CAACnB,KAAa;IAC/B,IAAI;MACF,MAAM8C,OAAO,GAAG9C,KAAK,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnC,MAAMC,OAAO,GAAGC,IAAI,CAACH,OAAO,CAAC;MAC7B,OAAOI,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;KAC3B,CAAC,OAAOI,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;;EAEf;EAEAC,MAAMA,CAAA;IACJxB,YAAY,CAACyB,UAAU,CAAC,OAAO,CAAC;IAChCzB,YAAY,CAACyB,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC3C,kBAAkB,CAACZ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACZ,MAAM,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEAY,QAAQA,CAAA;IACN,OAAO4B,YAAY,CAAC0B,OAAO,CAAC,OAAO,CAAC;EACtC;EAEAtC,WAAWA,CAAA;IACT,OAAOY,YAAY,CAAC0B,OAAO,CAAC,UAAU,CAAC;EACzC;EAEAnE,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACa,QAAQ,EAAE;EAC1B;EAEAuD,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC7C,kBAAkB,CAAC8C,KAAK;EACtC;;;uBA1GWlE,WAAW,EAAAzC,sDAAA,CAAAb,4DAAA,GAAAa,sDAAA,CAAA0C,mDAAA;IAAA;EAAA;;;aAAXD,WAAW;MAAAG,OAAA,EAAXH,WAAW,CAAAI,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;ACRb,MAAMa,WAAW,GAAG;EACzBkD,UAAU,EAAE,KAAK;EACjB7C,MAAM,EAAE,4BAA4B;EACpCC,MAAM,EAAE;CACT,C;;;;;;;;;;;;;;ACF4C;AAE7C6C,sEAAA,EAAwB,CAACE,eAAe,CAAClF,sDAAS,CAAC,CAChDmF,KAAK,CAACC,GAAG,IAAIpB,OAAO,CAACQ,KAAK,CAACY,GAAG,CAAC,CAAC,C", "sources": ["./src/app/app-routing.module.ts", "./src/app/app.component.ts", "./src/app/app.module.ts", "./src/app/core/guards/auth.guard.ts", "./src/app/core/interceptors/auth.interceptor.ts", "./src/app/core/services/auth.service.ts", "./src/environments/environment.ts", "./src/main.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { RouterModule, type Routes } from \"@angular/router\"\nimport { AuthGuard } from \"./core/guards/auth.guard\"\n\nconst routes: Routes = [\n  {\n    path: \"\",\n    loadChildren: () => import(\"./features/home/<USER>\").then((m) => m.HomeModule),\n  },\n  {\n    path: \"auth\",\n    loadChildren: () => import(\"./features/auth/auth.module\").then((m) => m.AuthModule),\n  },\n  {\n    path: \"dashboard\",\n    loadChildren: () => import(\"./features/dashboard/dashboard.module\").then((m) => m.DashboardModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"courses\",\n    loadChildren: () => import(\"./features/courses/courses.module\").then((m) => m.CoursesModule),\n  },\n  {\n    path: \"quiz\",\n    loadChildren: () => import(\"./features/quiz/quiz.module\").then((m) => m.QuizModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"payment\",\n    loadChildren: () => import(\"./features/payment/payment.module\").then((m) => m.PaymentModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"messages\",\n    loadChildren: () => import(\"./features/messages/messages.module\").then((m) => m.MessagesModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"certificates\",\n    loadChildren: () => import(\"./features/certificates/certificates.module\").then((m) => m.CertificatesModule),\n    canActivate: [AuthGuard],\n  },\n  {\n    path: \"**\",\n    redirectTo: \"\",\n  },\n]\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule],\n})\nexport class AppRoutingModule {}\n", "import { Component } from \"@angular/core\"\n\n@Component({\n  selector: \"app-root\",\n  template: `\n    <div class=\"app-container\">\n      <router-outlet></router-outlet>\n    </div>\n  `,\n  styles: [\n    `\n    .app-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n  `,\n  ],\n})\nexport class AppComponent {\n  title = \"elearning-platform\"\n}\n", "import { NgModule } from \"@angular/core\"\nimport { BrowserModule } from \"@angular/platform-browser\"\nimport { BrowserAnimationsModule } from \"@angular/platform-browser/animations\"\nimport { HttpClientModule, HTTP_INTERCEPTORS } from \"@angular/common/http\"\nimport { ReactiveFormsModule, FormsModule } from \"@angular/forms\"\n\n// Angular Material\nimport { MatToolbarModule } from \"@angular/material/toolbar\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatInputModule } from \"@angular/material/input\"\nimport { MatFormFieldModule } from \"@angular/material/form-field\"\nimport { MatSelectModule } from \"@angular/material/select\"\nimport { MatCheckboxModule } from \"@angular/material/checkbox\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatProgressBarModule } from \"@angular/material/progress-bar\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatBadgeModule } from \"@angular/material/badge\"\nimport { MatTabsModule } from \"@angular/material/tabs\"\nimport { MatDialogModule } from \"@angular/material/dialog\"\nimport { MatSnackBarModule } from \"@angular/material/snack-bar\"\nimport { MatSidenavModule } from \"@angular/material/sidenav\"\nimport { MatListModule } from \"@angular/material/list\"\nimport { MatGridListModule } from \"@angular/material/grid-list\"\nimport { MatRadioModule } from \"@angular/material/radio\"\nimport { MatProgressSpinnerModule } from \"@angular/material/progress-spinner\"\n\nimport { AppRoutingModule } from \"./app-routing.module\"\nimport { AppComponent } from \"./app.component\"\nimport { AuthInterceptor } from \"./core/interceptors/auth.interceptor\"\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n    FormsModule,\n    AppRoutingModule,\n\n    // Angular Material\n    MatToolbarModule,\n    MatButtonModule,\n    MatCardModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatIconModule,\n    MatProgressBarModule,\n    MatChipsModule,\n    MatBadgeModule,\n    MatTabsModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatGridListModule,\n    MatRadioModule,\n    MatProgressSpinnerModule,\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true,\n    },\n  ],\n  bootstrap: [AppComponent],\n})\nexport class AppModule {}\n", "import { Injectable } from \"@angular/core\"\nimport { CanActivate, Router } from \"@angular/router\"\nimport { AuthService } from \"../services/auth.service\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AuthGuard implements CanActivate {\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n  ) {}\n\n  canActivate(): boolean {\n    if (this.authService.isAuthenticated()) {\n      return true\n    } else {\n      this.router.navigate([\"/auth/login\"])\n      return false\n    }\n  }\n}\n", "import { Injectable } from \"@angular/core\"\nimport { HttpInterceptor, HttpRequest, HttpHandler } from \"@angular/common/http\"\nimport { AuthService } from \"../services/auth.service\"\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  constructor(private authService: AuthService) {}\n\n  intercept(req: HttpRequest<any>, next: HttpHandler) {\n    const token = this.authService.getToken()\n\n    if (token) {\n      const authReq = req.clone({\n        headers: req.headers.set(\"Authorization\", `Bearer ${token}`),\n      })\n      return next.handle(authReq)\n    }\n\n    return next.handle(req)\n  }\n}\n", "import { Injectable } from \"@angular/core\"\nimport { HttpClient, HttpHeaders } from \"@angular/common/http\"\nimport { BehaviorSubject, Observable, tap } from \"rxjs\"\nimport { Router } from \"@angular/router\"\nimport { environment } from \"../../../environments/environment\"\nimport { User, LoginRequest, AuthResponse } from \"../models/user.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AuthService {\n  private currentUserSubject = new BehaviorSubject<User | null>(null)\n  public currentUser$ = this.currentUserSubject.asObservable()\n  private apiUrl = `${environment.urlApi}auth`\n\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n  ) {\n    const token = this.getToken()\n    const role = this.getUserRole()\n    if (token && role) {\n      // Décoder le token pour récupérer les infos utilisateur\n      const userInfo = this.decodeToken(token)\n      if (userInfo) {\n        this.currentUserSubject.next({\n          id: userInfo.id || 0,\n          email: userInfo.email || '',\n          nom: userInfo.nom || '',\n          prenom: userInfo.prenom || '',\n          role: role as \"Client\" | \"Formateur\" | \"Admin\",\n        })\n      }\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, credentials).pipe(\n      tap((response) => {\n        localStorage.setItem(\"token\", response.token)\n        localStorage.setItem(\"userRole\", response.role)\n\n        // Décoder le token pour récupérer les infos utilisateur\n        const userInfo = this.decodeToken(response.token)\n        if (userInfo) {\n          this.currentUserSubject.next({\n            id: userInfo.id || 0,\n            email: userInfo.email || credentials.Email,\n            nom: userInfo.nom || '',\n            prenom: userInfo.prenom || '',\n            role: response.role as \"Client\" | \"Formateur\" | \"Admin\",\n          })\n        }\n      }),\n    )\n  }\n\n  // Note: Votre backend n'a pas d'endpoint register, utilisez les contrôleurs spécifiques\n  register(userData: any): Observable<any> {\n    const endpoint = userData.accountType === 'formateur' ? 'formateur' : 'client'\n\n    // ✅ Préparer les données selon le modèle .NET\n    const registrationData = {\n      email: userData.email,\n      nom: userData.lastName,\n      prenom: userData.firstName,\n      role: userData.accountType === 'formateur' ? 'Formateur' : 'Client',\n      // Initialiser les collections pour éviter les erreurs\n      ...(userData.accountType === 'client' && {\n        paiements: [],\n        coursConsultes: [],\n        resultatsQuiz: []\n      }),\n      ...(userData.accountType === 'formateur' && {\n        coursCree: [],\n        estValide: false\n      })\n    }\n\n    console.log('Données d\\'inscription envoyées:', registrationData)\n    return this.http.post(`${environment.urlApi}${endpoint}`, registrationData)\n  }\n\n  // Décoder le token JWT pour extraire les informations utilisateur\n  private decodeToken(token: string): any {\n    try {\n      const payload = token.split('.')[1]\n      const decoded = atob(payload)\n      return JSON.parse(decoded)\n    } catch (error) {\n      console.error('Erreur lors du décodage du token:', error)\n      return null\n    }\n  }\n\n  logout(): void {\n    localStorage.removeItem(\"token\")\n    localStorage.removeItem(\"userRole\")\n    this.currentUserSubject.next(null)\n    this.router.navigate([\"/\"])\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem(\"token\")\n  }\n\n  getUserRole(): string | null {\n    return localStorage.getItem(\"userRole\")\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getToken()\n  }\n\n  getCurrentUserValue(): User | null {\n    return this.currentUserSubject.value\n  }\n}\n", "export const environment = {\n  production: false,\n  apiUrl: \"http://localhost:5169/api/\",\n  urlApi: \"http://localhost:5169/api/\",\n}\n", "import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch(err => console.error(err));\n"], "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "loadChildren", "then", "m", "HomeModule", "AuthModule", "DashboardModule", "canActivate", "CoursesModule", "QuizModule", "PaymentModule", "MessagesModule", "CertificatesModule", "redirectTo", "AppRoutingModule", "forRoot", "imports", "i1", "exports", "AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "ReactiveFormsModule", "FormsModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatCheckboxModule", "MatIconModule", "MatProgressBarModule", "MatChipsModule", "MatBadgeModule", "MatTabsModule", "MatDialogModule", "MatSnackBarModule", "MatSidenavModule", "MatListModule", "MatGridListModule", "MatRadioModule", "MatProgressSpinnerModule", "AuthInterceptor", "AppModule", "bootstrap", "provide", "useClass", "multi", "declarations", "authService", "router", "isAuthenticated", "navigate", "ɵɵinject", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn", "intercept", "req", "next", "token", "getToken", "authReq", "clone", "headers", "set", "handle", "BehaviorSubject", "tap", "environment", "http", "currentUserSubject", "currentUser$", "asObservable", "apiUrl", "urlApi", "role", "getUserRole", "userInfo", "decodeToken", "id", "email", "nom", "prenom", "login", "credentials", "post", "pipe", "response", "localStorage", "setItem", "Email", "register", "userData", "endpoint", "accountType", "registrationData", "lastName", "firstName", "paiements", "coursConsultes", "resultatsQuiz", "coursCree", "estValide", "console", "log", "payload", "split", "decoded", "atob", "JSON", "parse", "error", "logout", "removeItem", "getItem", "getCurrentUserValue", "value", "HttpClient", "production", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}