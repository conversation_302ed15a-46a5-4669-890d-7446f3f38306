import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Client } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class ClientService {
  private apiUrl = `${environment.urlApi}client`

  constructor(private http: HttpClient) {}

  // GET: Tous les clients (correspond à GET /api/client)
  getAllClients(): Observable<Client[]> {
    return this.http.get<Client[]>(this.apiUrl)
  }

  // GET: Un client par ID (correspond à GET /api/client/{id})
  getClient(id: number): Observable<Client> {
    return this.http.get<Client>(`${this.apiUrl}/${id}`)
  }

  // POST: Créer un client (correspond à POST /api/client)
  createClient(client: any): Observable<Client> {
    // ✅ Préparer les données avec TOUTES les propriétés requises par le modèle .NET
    const clientData = {
      email: client.email,
      nom: client.nom,
      prenom: client.prenom,
      // ✅ Toutes les propriétés requises selon les erreurs de validation
      compteBancaire: "",
      paiements: [],
      coursConsultes: [],
      resultatsQuiz: []
    };

    console.log('Données envoyées au serveur:', clientData); // ✅ Debug
    return this.http.post<Client>(this.apiUrl, clientData)
  }

  // PUT: Modifier un client (correspond à PUT /api/client/{id})
  updateClient(id: number, client: Client): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, client)
  }

  // DELETE: Supprimer un client (correspond à DELETE /api/client/{id})
  deleteClient(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`)
  }

  // POST: S'inscrire à un cours gratuit (correspond à POST /api/client/{clientId}/inscrire/{coursId})
  sInscrire(clientId: number, coursId: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${clientId}/inscrire/${coursId}`, {})
  }

  // POST: Acheter un cours payant (correspond à POST /api/client/{clientId}/acheter/{coursId})
  acheterContenu(clientId: number, coursId: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${clientId}/acheter/${coursId}`, {})
  }
}
