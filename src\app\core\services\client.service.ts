import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Client } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class ClientService {
  constructor(private http: HttpClient) {}

  // GET: Tous les clients
  getClients(): Observable<Client[]> {
    return this.http.get<Client[]>(`${environment.urlApi}client`)
  }

  // GET: Un client par ID
  getClient(id: number): Observable<Client> {
    return this.http.get<Client>(`${environment.urlApi}client/${id}`)
  }

  // POST: Créer un client
  createClient(client: Client): Observable<Client> {
    return this.http.post<Client>(`${environment.urlApi}client`, client)
  }

  // PUT: Modifier un client
  updateClient(id: number, client: Client): Observable<any> {
    return this.http.put(`${environment.urlApi}client/${id}`, client)
  }

  // DELETE: Supprimer un client
  deleteClient(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}client/${id}`)
  }

  // POST: S'inscrire à un cours
  sInscrire(clientId: number, coursId: number): Observable<any> {
    return this.http.post(`${environment.urlApi}client/${clientId}/inscrire/${coursId}`, {})
  }

  // POST: Acheter un cours
  acheterContenu(clientId: number, coursId: number): Observable<any> {
    return this.http.post(`${environment.urlApi}client/${clientId}/acheter/${coursId}`, {})
  }
}
