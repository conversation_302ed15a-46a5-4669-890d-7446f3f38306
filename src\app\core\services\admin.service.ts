import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Certificat } from "../models/certificate.model"
import type { Formateur } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class AdminService {
  constructor(private http: HttpClient) {}

  // POST: Générer un certificat
  genererCertificat(nomClient: string): Observable<Certificat> {
    return this.http.post<Certificat>(`${environment.urlApi}admin/generer-certificat`, nomClient)
  }

  // POST: Valider un formateur
  validerFormateur(formateur: Formateur): Observable<any> {
    return this.http.post(`${environment.urlApi}admin/valider-formateur`, formateur)
  }

  // GET: Voir les statistiques
  voirStatistiques(): Observable<string> {
    return this.http.get<string>(`${environment.urlApi}admin/statistiques`)
  }
}
