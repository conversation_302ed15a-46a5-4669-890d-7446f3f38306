import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Certificat } from "../models/certificate.model"
import { Formateur } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class AdminService {
  private apiUrl = `${environment.urlApi}admin`

  constructor(private http: HttpClient) {}

  // POST: Générer un certificat (correspond à POST /api/admin/generer-certificat)
  genererCertificat(nomClient: string): Observable<Certificat> {
    return this.http.post<Certificat>(`${this.apiUrl}/generer-certificat`, nomClient, {
      headers: { 'Content-Type': 'application/json' }
    })
  }

  // POST: Valider un formateur (correspond à POST /api/admin/valider-formateur)
  validerFormateur(formateur: Formateur): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/valider-formateur`, formateur)
  }

  // GET: Voir les statistiques (correspond à GET /api/admin/statistiques)
  voirStatistiques(): Observable<string> {
    return this.http.get<string>(`${this.apiUrl}/statistiques`)
  }

  // Méthode utilitaire pour parser les statistiques en objet
  getStatistiquesObject(): Observable<{
    totalUtilisateurs: number;
    totalFormateurs: number;
    totalClients: number;
    totalCours: number;
  }> {
    return new Observable(observer => {
      this.voirStatistiques().subscribe({
        next: (statsString) => {
          try {
            // Parser la chaîne "Utilisateurs: 100, Formateurs: 20, Clients: 80, Cours: 50"
            const regex = /Utilisateurs: (\d+), Formateurs: (\d+), Clients: (\d+), Cours: (\d+)/
            const match = statsString.match(regex)

            if (match) {
              observer.next({
                totalUtilisateurs: parseInt(match[1]),
                totalFormateurs: parseInt(match[2]),
                totalClients: parseInt(match[3]),
                totalCours: parseInt(match[4])
              })
            } else {
              observer.error('Format de statistiques non reconnu')
            }
          } catch (error) {
            observer.error('Erreur lors du parsing des statistiques')
          }
          observer.complete()
        },
        error: (error) => observer.error(error)
      })
    })
  }
}
