{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from \"@angular/core\";\nimport { Validators } from \"@angular/forms\";\nexport let PaymentComponent = class PaymentComponent {\n  constructor(route, router, fb, courseService, paymentService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.fb = fb;\n    this.courseService = courseService;\n    this.paymentService = paymentService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.isProcessing = false;\n    this.paymentSuccess = false;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.route.paramMap.subscribe(params => {\n      this.courseId = Number(params.get(\"courseId\"));\n      this.loadCourseDetails();\n    });\n    this.initPaymentForm();\n  }\n  initPaymentForm() {\n    this.paymentForm = this.fb.group({\n      email: [this.currentUser?.email || \"\", [Validators.required, Validators.email]],\n      cardNumber: [\"\", [Validators.required, Validators.pattern(/^\\d{4}\\s\\d{4}\\s\\d{4}\\s\\d{4}$/)]],\n      expiryDate: [\"\", [Validators.required, Validators.pattern(/^(0[1-9]|1[0-2])\\/\\d{2}$/)]],\n      cvv: [\"\", [Validators.required, Validators.pattern(/^\\d{3}$/)]],\n      cardName: [\"\", Validators.required]\n    });\n  }\n  loadCourseDetails() {\n    // Mock data for demonstration\n    this.course = {\n      id: this.courseId,\n      titre: \"React Fundamentals\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\"\n      },\n      contenus: [],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false\n    };\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(this.courseId).subscribe({\n      next: (data) => {\n        this.course = data;\n        this.paymentForm.patchValue({ email: this.currentUser?.email || '' });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  formatCardNumber(event) {\n    const input = event.target;\n    let value = input.value.replace(/\\s/g, \"\");\n    if (value.length > 0) {\n      value = value.match(/.{1,4}/g)?.join(\" \") || \"\";\n    }\n    this.paymentForm.get(\"cardNumber\")?.setValue(value, {\n      emitEvent: false\n    });\n  }\n  formatExpiryDate(event) {\n    const input = event.target;\n    let value = input.value.replace(/\\D/g, \"\");\n    if (value.length > 2) {\n      value = value.substring(0, 2) + \"/\" + value.substring(2, 4);\n    }\n    this.paymentForm.get(\"expiryDate\")?.setValue(value, {\n      emitEvent: false\n    });\n  }\n  formatCvv(event) {\n    const input = event.target;\n    const value = input.value.replace(/\\D/g, \"\").substring(0, 3);\n    this.paymentForm.get(\"cvv\")?.setValue(value, {\n      emitEvent: false\n    });\n  }\n  onSubmit() {\n    if (this.paymentForm.valid && this.currentUser && this.course) {\n      this.isProcessing = true;\n      const paiementData = {\n        clientId: this.currentUser.id,\n        coursId: this.course.id,\n        montant: this.course.prix\n      };\n      // Mock payment processing\n      setTimeout(() => {\n        this.isProcessing = false;\n        this.paymentSuccess = true;\n        this.snackBar.open(\"Paiement réussi !\", \"Fermer\", {\n          duration: 3000\n        });\n        setTimeout(() => {\n          this.router.navigate([\"/courses\", this.course.id]);\n        }, 3000);\n      }, 2000);\n      // Uncomment to use API\n      /*\n      this.paymentService.effectuerPaiement(paiementData).subscribe({\n        next: (response) => {\n          this.isProcessing = false;\n          this.paymentSuccess = true;\n          this.snackBar.open('Paiement réussi !', 'Fermer', { duration: 3000 });\n          setTimeout(() => {\n            this.router.navigate(['/courses', this.course.id]);\n          }, 3000);\n        },\n        error: (err) => {\n          this.isProcessing = false;\n          this.snackBar.open('Erreur lors du paiement. Veuillez réessayer.', 'Fermer', { duration: 5000 });\n          console.error(err);\n        }\n      });\n      */\n    } else {\n      this.snackBar.open(\"Veuillez remplir correctement toutes les informations de paiement.\", \"Fermer\", {\n        duration: 5000\n      });\n      this.paymentForm.markAllAsTouched();\n    }\n  }\n  get trainerEarnings() {\n    return this.course ? this.course.prix * 0.7 : 0;\n  }\n  get platformFee() {\n    return this.course ? this.course.prix * 0.3 : 0;\n  }\n};\nPaymentComponent = __decorate([Component({\n  selector: \"app-payment\",\n  template: `\n    <div class=\"payment-container\">\n      <div class=\"content-wrapper\">\n        <!-- Payment Form -->\n        <div class=\"payment-form-section\">\n          <mat-card class=\"payment-card\">\n            <mat-card-header>\n              <mat-card-title class=\"card-title-with-icon\">\n                <mat-icon>credit_card</mat-icon>\n                Informations de paiement\n              </mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <form [formGroup]=\"paymentForm\" (ngSubmit)=\"onSubmit()\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Adresse e-mail</mat-label>\n                  <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                  <mat-error *ngIf=\"paymentForm.get('email')?.hasError('required')\">L'email est requis</mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('email')?.hasError('email')\">Format d'email invalide</mat-error>\n                </mat-form-field>\n\n                <mat-divider class=\"form-divider\"></mat-divider>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Numéro de carte</mat-label>\n                  <input matInput formControlName=\"cardNumber\" placeholder=\"1234 5678 9012 3456\"\n                         (input)=\"formatCardNumber($event)\">\n                  <mat-error *ngIf=\"paymentForm.get('cardNumber')?.hasError('required')\">Le numéro de carte est requis</mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('cardNumber')?.hasError('pattern')\">Numéro de carte invalide</mat-error>\n                </mat-form-field>\n\n                <div class=\"row-fields\">\n                  <mat-form-field appearance=\"outline\" class=\"half-width\">\n                    <mat-label>Date d'expiration</mat-label>\n                    <input matInput formControlName=\"expiryDate\" placeholder=\"MM/AA\"\n                           (input)=\"formatExpiryDate($event)\">\n                    <mat-error *ngIf=\"paymentForm.get('expiryDate')?.hasError('required')\">Date requise</mat-error>\n                    <mat-error *ngIf=\"paymentForm.get('expiryDate')?.hasError('pattern')\">Format MM/AA invalide</mat-error>\n                  </mat-form-field>\n                  <mat-form-field appearance=\"outline\" class=\"half-width\">\n                    <mat-label>CVV</mat-label>\n                    <input matInput formControlName=\"cvv\" placeholder=\"123\"\n                           (input)=\"formatCvv($event)\">\n                    <mat-error *ngIf=\"paymentForm.get('cvv')?.hasError('required')\">CVV requis</mat-error>\n                    <mat-error *ngIf=\"paymentForm.get('cvv')?.hasError('pattern')\">CVV invalide</mat-error>\n                  </mat-form-field>\n                </div>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Nom sur la carte</mat-label>\n                  <input matInput formControlName=\"cardName\" placeholder=\"Jean Dupont\">\n                  <mat-error *ngIf=\"paymentForm.get('cardName')?.hasError('required')\">Le nom est requis</mat-error>\n                </mat-form-field>\n\n                <div class=\"secure-payment-info\">\n                  <mat-icon>lock</mat-icon>\n                  <span class=\"secure-text\">Paiement sécurisé</span>\n                  <p class=\"secure-description\">Vos informations sont protégées par un cryptage SSL 256 bits.</p>\n                </div>\n\n                <button mat-raised-button color=\"primary\" type=\"submit\" \n                        [disabled]=\"paymentForm.invalid || isProcessing\" class=\"full-width-btn submit-btn\">\n                  <mat-spinner diameter=\"20\" *ngIf=\"isProcessing\"></mat-spinner>\n                  <span *ngIf=\"!isProcessing\">\n                    <mat-icon>euro_symbol</mat-icon>\n                    Payer {{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}\n                  </span>\n                </button>\n              </form>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Order Summary & Details -->\n        <div class=\"summary-section\">\n          <mat-card class=\"summary-card\">\n            <mat-card-header>\n              <mat-card-title>Résumé de la commande</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"course-summary-item\">\n                <div class=\"course-image-placeholder\">\n                  <mat-icon>credit_card</mat-icon>\n                </div>\n                <div class=\"course-details\">\n                  <h3>{{ course.titre }}</h3>\n                  <p class=\"course-instructor\">Par {{ course.formateur.prenom }} {{ course.formateur.nom }}</p>\n                  <div class=\"course-meta-summary\">\n                    <div class=\"meta-item\">\n                      <mat-icon>schedule</mat-icon>\n                      <span>{{ course.duree }} min</span>\n                    </div>\n                    <div class=\"meta-item\">\n                      <mat-icon>group</mat-icon>\n                      <span>{{ course.nombreEtudiants }} étudiants</span>\n                    </div>\n                    <div class=\"meta-item\">\n                      <mat-icon class=\"star-icon\">star</mat-icon>\n                      <span>{{ course.note }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <mat-divider class=\"summary-divider\"></mat-divider>\n\n              <div class=\"price-breakdown\">\n                <div class=\"price-item\">\n                  <span>Prix du cours</span>\n                  <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n                </div>\n                <div class=\"price-item sub-item\">\n                  <span>TVA incluse</span>\n                  <span>0€</span>\n                </div>\n              </div>\n\n              <mat-divider class=\"summary-divider\"></mat-divider>\n\n              <div class=\"total-price\">\n                <span>Total</span>\n                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Earnings Distribution -->\n          <mat-card class=\"earnings-card\">\n            <mat-card-header>\n              <mat-card-title>Répartition des gains</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"earnings-item\">\n                <span>Formateur (70%)</span>\n                <span class=\"trainer-earnings\">{{ trainerEarnings | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <div class=\"earnings-item\">\n                <span>Plateforme (30%)</span>\n                <span class=\"platform-fee\">{{ platformFee | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <mat-divider class=\"summary-divider\"></mat-divider>\n              <div class=\"earnings-item total-earnings\">\n                <span>Total</span>\n                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Guarantee -->\n          <mat-card class=\"guarantee-card\">\n            <mat-card-content>\n              <div class=\"guarantee-header\">\n                <mat-icon>check_circle</mat-icon>\n                <span>Garantie 30 jours</span>\n              </div>\n              <p class=\"guarantee-text\">\n                Si vous n'êtes pas satisfait du cours, nous vous remboursons intégralement sous 30 jours.\n              </p>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"payment-success-overlay\" *ngIf=\"paymentSuccess\">\n      <mat-card class=\"success-card\">\n        <mat-card-content>\n          <div class=\"success-icon-wrapper\">\n            <mat-icon>check_circle</mat-icon>\n          </div>\n          <h2>Paiement réussi !</h2>\n          <p>Votre achat a été traité avec succès. Vous allez être redirigé vers le cours.</p>\n          <div class=\"purchased-course-info\">\n            <p class=\"course-title\">{{ course.titre }}</p>\n            <p class=\"course-price\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .payment-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      padding: 2rem;\n      display: flex;\n      justify-content: center;\n      align-items: flex-start;\n    }\n\n    .content-wrapper {\n      display: grid;\n      grid-template-columns: 1.5fr 1fr;\n      gap: 2rem;\n      max-width: 1200px;\n      width: 100%;\n    }\n\n    @media (max-width: 960px) {\n      .content-wrapper {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    .payment-card, .summary-card, .earnings-card, .guarantee-card {\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .card-title-with-icon {\n      display: flex;\n      align-items: center;\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .card-title-with-icon mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .row-fields {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n    }\n\n    .form-divider {\n      margin: 1.5rem 0;\n    }\n\n    .secure-payment-info {\n      background-color: #e3f2fd; /* Light blue */\n      border: 1px solid #bbdefb; /* Lighter blue */\n      border-radius: 8px;\n      padding: 1rem;\n      margin-bottom: 1.5rem;\n      color: #1565c0; /* Darker blue */\n    }\n\n    .secure-payment-info mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n    }\n\n    .secure-text {\n      font-weight: 500;\n      font-size: 0.95rem;\n    }\n\n    .secure-description {\n      font-size: 0.85rem;\n      margin-top: 0.5rem;\n      line-height: 1.4;\n    }\n\n    .full-width-btn {\n      width: 100%;\n      padding: 0.8rem 1rem;\n      font-size: 1.1rem;\n      height: 48px;\n    }\n\n    .full-width-btn mat-icon {\n      margin-right: 0.5rem;\n    }\n\n    .submit-btn mat-spinner {\n      margin-right: 0.5rem;\n    }\n\n    .summary-section {\n      display: flex;\n      flex-direction: column;\n      gap: 1.5rem;\n    }\n\n    .course-summary-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .course-image-placeholder {\n      width: 64px;\n      height: 64px;\n      background-color: #e1bee7; /* Light purple */\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-shrink: 0;\n    }\n\n    .course-image-placeholder mat-icon {\n      font-size: 2.5rem;\n      width: 2.5rem;\n      height: 2.5rem;\n      color: #8e24aa; /* Dark purple */\n    }\n\n    .course-details h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .course-instructor {\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-meta-summary {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.8rem;\n      font-size: 0.85rem;\n      color: #777;\n    }\n\n    .course-meta-summary .meta-item {\n      display: flex;\n      align-items: center;\n      gap: 0.2rem;\n    }\n\n    .course-meta-summary .meta-item mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      color: #999;\n    }\n\n    .course-meta-summary .meta-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .summary-divider {\n      margin: 1rem 0;\n    }\n\n    .price-breakdown, .earnings-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 0.5rem;\n      font-size: 0.95rem;\n      color: #444;\n    }\n\n    .price-breakdown .sub-item {\n      font-size: 0.85rem;\n      color: #777;\n    }\n\n    .total-price {\n      display: flex;\n      justify-content: space-between;\n      font-size: 1.3rem;\n      font-weight: bold;\n      margin-top: 1rem;\n    }\n\n    .trainer-earnings {\n      color: #388e3c; /* Green */\n      font-weight: 500;\n    }\n\n    .platform-fee {\n      color: #673ab7; /* Purple */\n      font-weight: 500;\n    }\n\n    .total-earnings {\n      font-size: 1.1rem;\n      font-weight: bold;\n    }\n\n    .guarantee-card {\n      background-color: #e8f5e9; /* Light green */\n      border: 1px solid #c8e6c9; /* Lighter green */\n      color: #388e3c; /* Dark green */\n    }\n\n    .guarantee-header {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 500;\n      margin-bottom: 0.5rem;\n    }\n\n    .guarantee-header mat-icon {\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .guarantee-text {\n      font-size: 0.85rem;\n      line-height: 1.4;\n    }\n\n    /* Payment Success Overlay */\n    .payment-success-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.6);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 1000;\n    }\n\n    .success-card {\n      text-align: center;\n      padding: 2rem;\n      max-width: 400px;\n      width: 100%;\n    }\n\n    .success-icon-wrapper {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background-color: #e8f5e9;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 1.5rem;\n    }\n\n    .success-icon-wrapper mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      color: #4caf50;\n    }\n\n    .success-card h2 {\n      font-size: 1.8rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .success-card p {\n      font-size: 1rem;\n      color: #666;\n      margin-bottom: 1.5rem;\n    }\n\n    .purchased-course-info {\n      background-color: #f5f5f5;\n      border-radius: 8px;\n      padding: 1rem;\n    }\n\n    .purchased-course-info .course-title {\n      font-weight: 500;\n      font-size: 1.1rem;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .purchased-course-info .course-price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #4caf50; /* Green */\n    }\n  `]\n})], PaymentComponent);", "map": {"version": 3, "names": ["Component", "Validators", "PaymentComponent", "constructor", "route", "router", "fb", "courseService", "paymentService", "authService", "snackBar", "isProcessing", "paymentSuccess", "ngOnInit", "currentUser$", "subscribe", "user", "currentUser", "paramMap", "params", "courseId", "Number", "get", "loadCourseDetails", "initPaymentForm", "paymentForm", "group", "email", "required", "cardNumber", "pattern", "expiryDate", "cvv", "cardName", "course", "id", "titre", "description", "prix", "duree", "niveau", "formateurId", "formateur", "nom", "prenom", "contenus", "nombreEtudiants", "note", "estGratuit", "formatCardNumber", "event", "input", "target", "value", "replace", "length", "match", "join", "setValue", "emitEvent", "formatExpiryDate", "substring", "formatCvv", "onSubmit", "valid", "paiementData", "clientId", "coursId", "montant", "setTimeout", "open", "duration", "navigate", "mark<PERSON>llAsTouched", "trainer<PERSON><PERSON><PERSON><PERSON>", "platformFee", "__decorate", "selector", "template", "styles"], "sources": ["C:\\e-learning\\src\\app\\features\\payment\\payment.component.ts"], "sourcesContent": ["import { Component, type OnInit } from \"@angular/core\"\nimport { type Form<PERSON>uilder, type FormGroup, Validators } from \"@angular/forms\"\nimport type { ActivatedRoute, Router } from \"@angular/router\"\nimport type { CourseService } from \"../../core/services/course.service\"\nimport type { PaymentService } from \"../../core/services/payment.service\"\nimport type { MatSnackBar } from \"@angular/material/snack-bar\"\nimport type { Course } from \"../../core/models/course.model\"\nimport type { AuthService } from \"../../core/services/auth.service\"\nimport type { User } from \"../../core/models/user.model\"\n\n@Component({\n  selector: \"app-payment\",\n  template: `\n    <div class=\"payment-container\">\n      <div class=\"content-wrapper\">\n        <!-- Payment Form -->\n        <div class=\"payment-form-section\">\n          <mat-card class=\"payment-card\">\n            <mat-card-header>\n              <mat-card-title class=\"card-title-with-icon\">\n                <mat-icon>credit_card</mat-icon>\n                Informations de paiement\n              </mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <form [formGroup]=\"paymentForm\" (ngSubmit)=\"onSubmit()\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Adresse e-mail</mat-label>\n                  <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                  <mat-error *ngIf=\"paymentForm.get('email')?.hasError('required')\">L'email est requis</mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('email')?.hasError('email')\">Format d'email invalide</mat-error>\n                </mat-form-field>\n\n                <mat-divider class=\"form-divider\"></mat-divider>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Numéro de carte</mat-label>\n                  <input matInput formControlName=\"cardNumber\" placeholder=\"1234 5678 9012 3456\"\n                         (input)=\"formatCardNumber($event)\">\n                  <mat-error *ngIf=\"paymentForm.get('cardNumber')?.hasError('required')\">Le numéro de carte est requis</mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('cardNumber')?.hasError('pattern')\">Numéro de carte invalide</mat-error>\n                </mat-form-field>\n\n                <div class=\"row-fields\">\n                  <mat-form-field appearance=\"outline\" class=\"half-width\">\n                    <mat-label>Date d'expiration</mat-label>\n                    <input matInput formControlName=\"expiryDate\" placeholder=\"MM/AA\"\n                           (input)=\"formatExpiryDate($event)\">\n                    <mat-error *ngIf=\"paymentForm.get('expiryDate')?.hasError('required')\">Date requise</mat-error>\n                    <mat-error *ngIf=\"paymentForm.get('expiryDate')?.hasError('pattern')\">Format MM/AA invalide</mat-error>\n                  </mat-form-field>\n                  <mat-form-field appearance=\"outline\" class=\"half-width\">\n                    <mat-label>CVV</mat-label>\n                    <input matInput formControlName=\"cvv\" placeholder=\"123\"\n                           (input)=\"formatCvv($event)\">\n                    <mat-error *ngIf=\"paymentForm.get('cvv')?.hasError('required')\">CVV requis</mat-error>\n                    <mat-error *ngIf=\"paymentForm.get('cvv')?.hasError('pattern')\">CVV invalide</mat-error>\n                  </mat-form-field>\n                </div>\n\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Nom sur la carte</mat-label>\n                  <input matInput formControlName=\"cardName\" placeholder=\"Jean Dupont\">\n                  <mat-error *ngIf=\"paymentForm.get('cardName')?.hasError('required')\">Le nom est requis</mat-error>\n                </mat-form-field>\n\n                <div class=\"secure-payment-info\">\n                  <mat-icon>lock</mat-icon>\n                  <span class=\"secure-text\">Paiement sécurisé</span>\n                  <p class=\"secure-description\">Vos informations sont protégées par un cryptage SSL 256 bits.</p>\n                </div>\n\n                <button mat-raised-button color=\"primary\" type=\"submit\" \n                        [disabled]=\"paymentForm.invalid || isProcessing\" class=\"full-width-btn submit-btn\">\n                  <mat-spinner diameter=\"20\" *ngIf=\"isProcessing\"></mat-spinner>\n                  <span *ngIf=\"!isProcessing\">\n                    <mat-icon>euro_symbol</mat-icon>\n                    Payer {{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}\n                  </span>\n                </button>\n              </form>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Order Summary & Details -->\n        <div class=\"summary-section\">\n          <mat-card class=\"summary-card\">\n            <mat-card-header>\n              <mat-card-title>Résumé de la commande</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"course-summary-item\">\n                <div class=\"course-image-placeholder\">\n                  <mat-icon>credit_card</mat-icon>\n                </div>\n                <div class=\"course-details\">\n                  <h3>{{ course.titre }}</h3>\n                  <p class=\"course-instructor\">Par {{ course.formateur.prenom }} {{ course.formateur.nom }}</p>\n                  <div class=\"course-meta-summary\">\n                    <div class=\"meta-item\">\n                      <mat-icon>schedule</mat-icon>\n                      <span>{{ course.duree }} min</span>\n                    </div>\n                    <div class=\"meta-item\">\n                      <mat-icon>group</mat-icon>\n                      <span>{{ course.nombreEtudiants }} étudiants</span>\n                    </div>\n                    <div class=\"meta-item\">\n                      <mat-icon class=\"star-icon\">star</mat-icon>\n                      <span>{{ course.note }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <mat-divider class=\"summary-divider\"></mat-divider>\n\n              <div class=\"price-breakdown\">\n                <div class=\"price-item\">\n                  <span>Prix du cours</span>\n                  <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n                </div>\n                <div class=\"price-item sub-item\">\n                  <span>TVA incluse</span>\n                  <span>0€</span>\n                </div>\n              </div>\n\n              <mat-divider class=\"summary-divider\"></mat-divider>\n\n              <div class=\"total-price\">\n                <span>Total</span>\n                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Earnings Distribution -->\n          <mat-card class=\"earnings-card\">\n            <mat-card-header>\n              <mat-card-title>Répartition des gains</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"earnings-item\">\n                <span>Formateur (70%)</span>\n                <span class=\"trainer-earnings\">{{ trainerEarnings | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <div class=\"earnings-item\">\n                <span>Plateforme (30%)</span>\n                <span class=\"platform-fee\">{{ platformFee | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n              <mat-divider class=\"summary-divider\"></mat-divider>\n              <div class=\"earnings-item total-earnings\">\n                <span>Total</span>\n                <span>{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</span>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Guarantee -->\n          <mat-card class=\"guarantee-card\">\n            <mat-card-content>\n              <div class=\"guarantee-header\">\n                <mat-icon>check_circle</mat-icon>\n                <span>Garantie 30 jours</span>\n              </div>\n              <p class=\"guarantee-text\">\n                Si vous n'êtes pas satisfait du cours, nous vous remboursons intégralement sous 30 jours.\n              </p>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"payment-success-overlay\" *ngIf=\"paymentSuccess\">\n      <mat-card class=\"success-card\">\n        <mat-card-content>\n          <div class=\"success-icon-wrapper\">\n            <mat-icon>check_circle</mat-icon>\n          </div>\n          <h2>Paiement réussi !</h2>\n          <p>Votre achat a été traité avec succès. Vous allez être redirigé vers le cours.</p>\n          <div class=\"purchased-course-info\">\n            <p class=\"course-title\">{{ course.titre }}</p>\n            <p class=\"course-price\">{{ course.prix | currency:'EUR':'symbol':'1.2-2':'fr' }}</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [\n    `\n    .payment-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      padding: 2rem;\n      display: flex;\n      justify-content: center;\n      align-items: flex-start;\n    }\n\n    .content-wrapper {\n      display: grid;\n      grid-template-columns: 1.5fr 1fr;\n      gap: 2rem;\n      max-width: 1200px;\n      width: 100%;\n    }\n\n    @media (max-width: 960px) {\n      .content-wrapper {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    .payment-card, .summary-card, .earnings-card, .guarantee-card {\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .card-title-with-icon {\n      display: flex;\n      align-items: center;\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .card-title-with-icon mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .row-fields {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n    }\n\n    .form-divider {\n      margin: 1.5rem 0;\n    }\n\n    .secure-payment-info {\n      background-color: #e3f2fd; /* Light blue */\n      border: 1px solid #bbdefb; /* Lighter blue */\n      border-radius: 8px;\n      padding: 1rem;\n      margin-bottom: 1.5rem;\n      color: #1565c0; /* Darker blue */\n    }\n\n    .secure-payment-info mat-icon {\n      margin-right: 0.5rem;\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n    }\n\n    .secure-text {\n      font-weight: 500;\n      font-size: 0.95rem;\n    }\n\n    .secure-description {\n      font-size: 0.85rem;\n      margin-top: 0.5rem;\n      line-height: 1.4;\n    }\n\n    .full-width-btn {\n      width: 100%;\n      padding: 0.8rem 1rem;\n      font-size: 1.1rem;\n      height: 48px;\n    }\n\n    .full-width-btn mat-icon {\n      margin-right: 0.5rem;\n    }\n\n    .submit-btn mat-spinner {\n      margin-right: 0.5rem;\n    }\n\n    .summary-section {\n      display: flex;\n      flex-direction: column;\n      gap: 1.5rem;\n    }\n\n    .course-summary-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .course-image-placeholder {\n      width: 64px;\n      height: 64px;\n      background-color: #e1bee7; /* Light purple */\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-shrink: 0;\n    }\n\n    .course-image-placeholder mat-icon {\n      font-size: 2.5rem;\n      width: 2.5rem;\n      height: 2.5rem;\n      color: #8e24aa; /* Dark purple */\n    }\n\n    .course-details h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .course-instructor {\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.5rem;\n    }\n\n    .course-meta-summary {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.8rem;\n      font-size: 0.85rem;\n      color: #777;\n    }\n\n    .course-meta-summary .meta-item {\n      display: flex;\n      align-items: center;\n      gap: 0.2rem;\n    }\n\n    .course-meta-summary .meta-item mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      color: #999;\n    }\n\n    .course-meta-summary .meta-item .star-icon {\n      color: #ffc107; /* Yellow */\n    }\n\n    .summary-divider {\n      margin: 1rem 0;\n    }\n\n    .price-breakdown, .earnings-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 0.5rem;\n      font-size: 0.95rem;\n      color: #444;\n    }\n\n    .price-breakdown .sub-item {\n      font-size: 0.85rem;\n      color: #777;\n    }\n\n    .total-price {\n      display: flex;\n      justify-content: space-between;\n      font-size: 1.3rem;\n      font-weight: bold;\n      margin-top: 1rem;\n    }\n\n    .trainer-earnings {\n      color: #388e3c; /* Green */\n      font-weight: 500;\n    }\n\n    .platform-fee {\n      color: #673ab7; /* Purple */\n      font-weight: 500;\n    }\n\n    .total-earnings {\n      font-size: 1.1rem;\n      font-weight: bold;\n    }\n\n    .guarantee-card {\n      background-color: #e8f5e9; /* Light green */\n      border: 1px solid #c8e6c9; /* Lighter green */\n      color: #388e3c; /* Dark green */\n    }\n\n    .guarantee-header {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 500;\n      margin-bottom: 0.5rem;\n    }\n\n    .guarantee-header mat-icon {\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .guarantee-text {\n      font-size: 0.85rem;\n      line-height: 1.4;\n    }\n\n    /* Payment Success Overlay */\n    .payment-success-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.6);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 1000;\n    }\n\n    .success-card {\n      text-align: center;\n      padding: 2rem;\n      max-width: 400px;\n      width: 100%;\n    }\n\n    .success-icon-wrapper {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      background-color: #e8f5e9;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 1.5rem;\n    }\n\n    .success-icon-wrapper mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      color: #4caf50;\n    }\n\n    .success-card h2 {\n      font-size: 1.8rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .success-card p {\n      font-size: 1rem;\n      color: #666;\n      margin-bottom: 1.5rem;\n    }\n\n    .purchased-course-info {\n      background-color: #f5f5f5;\n      border-radius: 8px;\n      padding: 1rem;\n    }\n\n    .purchased-course-info .course-title {\n      font-weight: 500;\n      font-size: 1.1rem;\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .purchased-course-info .course-price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #4caf50; /* Green */\n    }\n  `,\n  ],\n})\nexport class PaymentComponent implements OnInit {\n  courseId!: number\n  course!: Course\n  paymentForm!: FormGroup\n  isProcessing = false\n  paymentSuccess = false\n  currentUser!: User | null\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private fb: FormBuilder,\n    private courseService: CourseService,\n    private paymentService: PaymentService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n    })\n\n    this.route.paramMap.subscribe((params) => {\n      this.courseId = Number(params.get(\"courseId\"))\n      this.loadCourseDetails()\n    })\n\n    this.initPaymentForm()\n  }\n\n  initPaymentForm(): void {\n    this.paymentForm = this.fb.group({\n      email: [this.currentUser?.email || \"\", [Validators.required, Validators.email]],\n      cardNumber: [\"\", [Validators.required, Validators.pattern(/^\\d{4}\\s\\d{4}\\s\\d{4}\\s\\d{4}$/)]],\n      expiryDate: [\"\", [Validators.required, Validators.pattern(/^(0[1-9]|1[0-2])\\/\\d{2}$/)]],\n      cvv: [\"\", [Validators.required, Validators.pattern(/^\\d{3}$/)]],\n      cardName: [\"\", Validators.required],\n    })\n  }\n\n  loadCourseDetails(): void {\n    // Mock data for demonstration\n    this.course = {\n      id: this.courseId,\n      titre: \"React Fundamentals\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\" },\n      contenus: [],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n    }\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(this.courseId).subscribe({\n      next: (data) => {\n        this.course = data;\n        this.paymentForm.patchValue({ email: this.currentUser?.email || '' });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  formatCardNumber(event: Event): void {\n    const input = event.target as HTMLInputElement\n    let value = input.value.replace(/\\s/g, \"\")\n    if (value.length > 0) {\n      value = value.match(/.{1,4}/g)?.join(\" \") || \"\"\n    }\n    this.paymentForm.get(\"cardNumber\")?.setValue(value, { emitEvent: false })\n  }\n\n  formatExpiryDate(event: Event): void {\n    const input = event.target as HTMLInputElement\n    let value = input.value.replace(/\\D/g, \"\")\n    if (value.length > 2) {\n      value = value.substring(0, 2) + \"/\" + value.substring(2, 4)\n    }\n    this.paymentForm.get(\"expiryDate\")?.setValue(value, { emitEvent: false })\n  }\n\n  formatCvv(event: Event): void {\n    const input = event.target as HTMLInputElement\n    const value = input.value.replace(/\\D/g, \"\").substring(0, 3)\n    this.paymentForm.get(\"cvv\")?.setValue(value, { emitEvent: false })\n  }\n\n  onSubmit(): void {\n    if (this.paymentForm.valid && this.currentUser && this.course) {\n      this.isProcessing = true\n      const paiementData = {\n        clientId: this.currentUser.id,\n        coursId: this.course.id,\n        montant: this.course.prix,\n      }\n\n      // Mock payment processing\n      setTimeout(() => {\n        this.isProcessing = false\n        this.paymentSuccess = true\n        this.snackBar.open(\"Paiement réussi !\", \"Fermer\", { duration: 3000 })\n        setTimeout(() => {\n          this.router.navigate([\"/courses\", this.course.id])\n        }, 3000)\n      }, 2000)\n\n      // Uncomment to use API\n      /*\n      this.paymentService.effectuerPaiement(paiementData).subscribe({\n        next: (response) => {\n          this.isProcessing = false;\n          this.paymentSuccess = true;\n          this.snackBar.open('Paiement réussi !', 'Fermer', { duration: 3000 });\n          setTimeout(() => {\n            this.router.navigate(['/courses', this.course.id]);\n          }, 3000);\n        },\n        error: (err) => {\n          this.isProcessing = false;\n          this.snackBar.open('Erreur lors du paiement. Veuillez réessayer.', 'Fermer', { duration: 5000 });\n          console.error(err);\n        }\n      });\n      */\n    } else {\n      this.snackBar.open(\"Veuillez remplir correctement toutes les informations de paiement.\", \"Fermer\", {\n        duration: 5000,\n      })\n      this.paymentForm.markAllAsTouched()\n    }\n  }\n\n  get trainerEarnings(): number {\n    return this.course ? this.course.prix * 0.7 : 0\n  }\n\n  get platformFee(): number {\n    return this.course ? this.course.prix * 0.3 : 0\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAqB,eAAe;AACtD,SAA2CC,UAAU,QAAQ,gBAAgB;AAuftE,WAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAQ3BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,EAAe,EACfC,aAA4B,EAC5BC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IANrB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAXlB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,cAAc,GAAG,KAAK;EAWnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACC,WAAW,GAAGD,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACZ,KAAK,CAACc,QAAQ,CAACH,SAAS,CAAEI,MAAM,IAAI;MACvC,IAAI,CAACC,QAAQ,GAAGC,MAAM,CAACF,MAAM,CAACG,GAAG,CAAC,UAAU,CAAC,CAAC;MAC9C,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACC,WAAW,GAAG,IAAI,CAACnB,EAAE,CAACoB,KAAK,CAAC;MAC/BC,KAAK,EAAE,CAAC,IAAI,CAACV,WAAW,EAAEU,KAAK,IAAI,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC0B,KAAK,CAAC,CAAC;MAC/EE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC6B,OAAO,CAAC,8BAA8B,CAAC,CAAC,CAAC;MAC3FC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC6B,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;MACvFE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC6B,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;MAC/DG,QAAQ,EAAE,CAAC,EAAE,EAAEhC,UAAU,CAAC2B,QAAQ;KACnC,CAAC;EACJ;EAEAL,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACW,MAAM,GAAG;MACZC,EAAE,EAAE,IAAI,CAACf,QAAQ;MACjBgB,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,kFAAkF;MAC/FC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,UAAU;MAClBC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAEP,EAAE,EAAE,CAAC;QAAEQ,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAM,CAAE;MACnDC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTC,UAAU,EAAE;KACb;IAED;IACA;;;;;;;;;;;;;EAaF;;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC1C,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MACpBF,KAAK,GAAGA,KAAK,CAACG,KAAK,CAAC,SAAS,CAAC,EAAEC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;;IAEjD,IAAI,CAAChC,WAAW,CAACH,GAAG,CAAC,YAAY,CAAC,EAAEoC,QAAQ,CAACL,KAAK,EAAE;MAAEM,SAAS,EAAE;IAAK,CAAE,CAAC;EAC3E;EAEAC,gBAAgBA,CAACV,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC1C,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MACpBF,KAAK,GAAGA,KAAK,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGR,KAAK,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE7D,IAAI,CAACpC,WAAW,CAACH,GAAG,CAAC,YAAY,CAAC,EAAEoC,QAAQ,CAACL,KAAK,EAAE;MAAEM,SAAS,EAAE;IAAK,CAAE,CAAC;EAC3E;EAEAG,SAASA,CAACZ,KAAY;IACpB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,MAAMC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACO,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACpC,WAAW,CAACH,GAAG,CAAC,KAAK,CAAC,EAAEoC,QAAQ,CAACL,KAAK,EAAE;MAAEM,SAAS,EAAE;IAAK,CAAE,CAAC;EACpE;EAEAI,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtC,WAAW,CAACuC,KAAK,IAAI,IAAI,CAAC/C,WAAW,IAAI,IAAI,CAACiB,MAAM,EAAE;MAC7D,IAAI,CAACvB,YAAY,GAAG,IAAI;MACxB,MAAMsD,YAAY,GAAG;QACnBC,QAAQ,EAAE,IAAI,CAACjD,WAAW,CAACkB,EAAE;QAC7BgC,OAAO,EAAE,IAAI,CAACjC,MAAM,CAACC,EAAE;QACvBiC,OAAO,EAAE,IAAI,CAAClC,MAAM,CAACI;OACtB;MAED;MACA+B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACF,QAAQ,CAAC4D,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACrEF,UAAU,CAAC,MAAK;UACd,IAAI,CAAChE,MAAM,CAACmE,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACtC,MAAM,CAACC,EAAE,CAAC,CAAC;QACpD,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,IAAI,CAAC;MAER;MACA;;;;;;;;;;;;;;;;;KAiBD,MAAM;MACL,IAAI,CAACzB,QAAQ,CAAC4D,IAAI,CAAC,oEAAoE,EAAE,QAAQ,EAAE;QACjGC,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAAC9C,WAAW,CAACgD,gBAAgB,EAAE;;EAEvC;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACxC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACI,IAAI,GAAG,GAAG,GAAG,CAAC;EACjD;EAEA,IAAIqC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACzC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACI,IAAI,GAAG,GAAG,GAAG,CAAC;EACjD;CACD;AAtJYpC,gBAAgB,GAAA0E,UAAA,EA9e5B5E,SAAS,CAAC;EACT6E,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmLT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoTD;CAEF,CAAC,C,EACW7E,gBAAgB,CAsJ5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}