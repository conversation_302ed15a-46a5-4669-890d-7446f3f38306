{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/** Configuration for opening a modal dialog. */\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nclass DialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    this.backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    this.disableClose = false;\n    /** Width of the dialog. */\n    this.width = '';\n    /** Height of the dialog. */\n    this.height = '';\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    this.ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    this.restoreFocus = true;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    this.closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    this.closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    this.closeOnOverlayDetachments = true;\n  }\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n  constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n    super();\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    this._config = _config;\n    this._interactivityChecker = _interactivityChecker;\n    this._ngZone = _ngZone;\n    this._overlayRef = _overlayRef;\n    this._focusMonitor = _focusMonitor;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    this._elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    this._closeInteractionType = null;\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._contentAttached();\n      return result;\n    };\n    this._ariaLabelledBy = this._config.ariaLabelledBy || null;\n    this._document = _document;\n  }\n  _contentAttached() {\n    this._initializeFocusTrap();\n    this._handleBackdropClicks();\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n  ngOnDestroy() {\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  _trapFocus() {\n    const element = this._elementRef.nativeElement;\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n    // dialog. If the element inside the dialog can't be focused, then the container is focused\n    // so the user can't tab into other elements behind it.\n    switch (this._config.autoFocus) {\n      case false:\n      case 'dialog':\n        // Ensure that focus is on the dialog container. It's possible that a different\n        // component tried to move focus while the open animation was running. See:\n        // https://github.com/angular/components/issues/16215. Note that we only want to do this\n        // if the focus isn't inside the dialog already, because it's possible that the consumer\n        // turned off `autoFocus` in order to move focus themselves.\n        if (!this._containsFocus()) {\n          element.focus();\n        }\n        break;\n      case true:\n      case 'first-tabbable':\n        this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer();\n          }\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this._config.autoFocus);\n        break;\n    }\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n  _focusDialogContainer() {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n  /** Returns whether focus is inside the dialog. */\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n  _initializeFocusTrap() {\n    this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n    // Save the previously focused element. This element will be re-focused\n    // when the dialog closes.\n    if (this._document) {\n      this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n    }\n  }\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n  _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function CdkDialogContainer_Factory(t) {\n      return new (t || CdkDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(DialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkDialogContainer,\n      selectors: [[\"cdk-dialog-container\"]],\n      viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n      hostVars: 6,\n      hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledBy)(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkPortalOutlet\", \"\"]],\n      template: function CdkDialogContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        }\n      },\n      dependencies: [i3.CdkPortalOutlet],\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet></ng-template>\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DialogConfig]\n      }]\n    }, {\n      type: i1.InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.OverlayRef\n    }, {\n      type: i1.FocusMonitor\n    }];\n  }, {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    /** Emits when the dialog has been closed. */\n    this.closed = new Subject();\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy');\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/** @docs-private */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nclass Dialog {\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n    this._overlay = _overlay;\n    this._injector = _injector;\n    this._defaultOptions = _defaultOptions;\n    this._parentDialog = _parentDialog;\n    this._overlayContainer = _overlayContainer;\n    this._openDialogsAtThisLevel = [];\n    this._afterAllClosedAtThisLevel = new Subject();\n    this._afterOpenedAtThisLevel = new Subject();\n    this._ariaHiddenElements = new Map();\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    this._scrollStrategy = scrollStrategy;\n  }\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = {\n      ...defaults,\n      ...config\n    };\n    config.id = config.id || `cdk-dialog-${uniqueId++}`;\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = this._overlay.create(overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n    dialogRef.containerInstance = dialogContainer;\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation\n    });\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  _attachContainer(overlay, dialogRef, config) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }), config.componentFactoryResolver);\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n      if (config.templateContext) {\n        context = {\n          ...context,\n          ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n        };\n      }\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n      dialogRef.componentRef = contentRef;\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n      optional: true\n    }))) {\n      providers.push({\n        provide: Directionality,\n        useValue: {\n          value: config.direction,\n          change: of()\n        }\n      });\n    }\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n    if (index > -1) {\n      this.openDialogs.splice(index, 1);\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n        this._ariaHiddenElements.clear();\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement();\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  static {\n    this.ɵfac = function Dialog_Factory(t) {\n      return new (t || Dialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(DEFAULT_DIALOG_CONFIG, 8), i0.ɵɵinject(Dialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(DIALOG_SCROLL_STRATEGY));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Dialog,\n      factory: Dialog.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.Injector\n    }, {\n      type: DialogConfig,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DEFAULT_DIALOG_CONFIG]\n      }]\n    }, {\n      type: Dialog,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: i1$1.OverlayContainer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DIALOG_SCROLL_STRATEGY]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nclass DialogModule {\n  static {\n    this.ɵfac = function DialogModule_Factory(t) {\n      return new (t || DialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DialogModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER],\n      imports: [OverlayModule, PortalModule, A11yModule,\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule],\n      exports: [\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      declarations: [CdkDialogContainer],\n      providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };", "map": {"version": 3, "names": ["i1", "A11yModule", "i1$1", "Overlay", "OverlayConfig", "OverlayRef", "OverlayModule", "_getFocusedElementPierceShadowDom", "i3", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "DOCUMENT", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "ViewChild", "InjectionToken", "Injector", "TemplateRef", "Injectable", "SkipSelf", "NgModule", "ESCAPE", "hasModifierKey", "Subject", "defer", "of", "Directionality", "startWith", "CdkDialogContainer_ng_template_0_Template", "rf", "ctx", "DialogConfig", "constructor", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "width", "height", "data", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "closeOnNavigation", "closeOnDestroy", "closeOnOverlayDetachments", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "_elementRef", "_focusTrapFactory", "_document", "_config", "_interactivityC<PERSON>cker", "_ngZone", "_overlayRef", "_focusMonitor", "_elementFocusedBeforeDialogWasOpened", "_closeInteractionType", "attachDomPortal", "portal", "_portalOutlet", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "_contentAttached", "_ariaLabelledBy", "_initializeFocusTrap", "_handleBackdropClicks", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_restoreFocus", "attachComponentPortal", "attachTemplatePortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "runOutsideAngular", "callback", "removeEventListener", "removeAttribute", "addEventListener", "focus", "_focusByCssSelector", "selector", "elementToFocus", "nativeElement", "querySelector", "_focusTrap", "focusInitialElementWhenReady", "then", "focusedSuccessfully", "_focusDialogContainer", "focusConfig", "focusTargetElement", "activeElement", "body", "contains", "focusVia", "destroy", "create", "backdropClick", "subscribe", "ɵfac", "CdkDialogContainer_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "FocusTrapFactory", "InteractivityChecker", "NgZone", "FocusMonitor", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "CdkDialogContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkDialogContainer_HostBindings", "ɵɵattribute", "id", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "CdkDialogContainer_Template", "ɵɵtemplate", "dependencies", "styles", "encapsulation", "ɵsetClassMetadata", "args", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "host", "undefined", "decorators", "static", "DialogRef", "overlayRef", "config", "closed", "keydownEvents", "outsidePointerEvents", "event", "keyCode", "preventDefault", "close", "<PERSON><PERSON><PERSON><PERSON>", "_detachSubscription", "detachments", "containerInstance", "closedSubject", "unsubscribe", "dispose", "next", "complete", "componentInstance", "updatePosition", "updateSize", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "block", "DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "uniqueId", "Dialog", "openDialogs", "_parentDialog", "_openDialogsAtThisLevel", "afterOpened", "_afterOpenedAtThisLevel", "_overlay", "_injector", "_defaultOptions", "_overlayContainer", "scrollStrategy", "_afterAllClosedAtThisLevel", "_ariaHiddenElements", "Map", "afterAllClosed", "length", "_getAfterAllClosed", "pipe", "_scrollStrategy", "open", "componentOrTemplateRef", "defaults", "getDialogById", "overlayConfig", "_getOverlayConfig", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_hideNonDialogContentFromAssistiveTechnology", "push", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "state", "positionStrategy", "position", "global", "centerHorizontally", "centerVertically", "direction", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "disposeOnNavigation", "userInjector", "injector", "viewContainerRef", "providers", "useValue", "containerType", "container", "containerPortal", "parent", "componentFactoryResolver", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "templateContext", "contentRef", "componentRef", "fallbackInjector", "get", "optional", "value", "change", "emitEvent", "index", "indexOf", "splice", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "overlayContainer", "getContainerElement", "parentElement", "siblings", "children", "i", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "Dialog_Factory", "ɵɵinject", "OverlayContainer", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "items", "DialogModule", "DialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/e-learning/node_modules/@angular/cdk/fesm2022/dialog.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    constructor() {\n        /** The ARIA role of the dialog element. */\n        this.role = 'dialog';\n        /** Optional CSS class or classes applied to the overlay panel. */\n        this.panelClass = '';\n        /** Whether the dialog has a backdrop. */\n        this.hasBackdrop = true;\n        /** Optional CSS class or classes applied to the overlay backdrop. */\n        this.backdropClass = '';\n        /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n        this.disableClose = false;\n        /** Width of the dialog. */\n        this.width = '';\n        /** Height of the dialog. */\n        this.height = '';\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** ID of the element that describes the dialog. */\n        this.ariaDescribedBy = null;\n        /** ID of the element that labels the dialog. */\n        this.ariaLabelledBy = null;\n        /** Dialog label applied via `aria-label` */\n        this.ariaLabel = null;\n        /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n        this.ariaModal = true;\n        /**\n         * Where the dialog should focus on open.\n         * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n         * AutoFocusTarget instead.\n         */\n        this.autoFocus = 'first-tabbable';\n        /**\n         * Whether the dialog should restore focus to the previously-focused element upon closing.\n         * Has the following behavior based on the type that is passed in:\n         * - `boolean` - when true, will return focus to the element that was focused before the dialog\n         *    was opened, otherwise won't restore focus at all.\n         * - `string` - focus will be restored to the first element that matches the CSS selector.\n         * - `HTMLElement` - focus will be restored to the specific element.\n         */\n        this.restoreFocus = true;\n        /**\n         * Whether the dialog should close when the user navigates backwards or forwards through browser\n         * history. This does not apply to navigation via anchor element unless using URL-hash based\n         * routing (`HashLocationStrategy` in the Angular router).\n         */\n        this.closeOnNavigation = true;\n        /**\n         * Whether the dialog should close when the dialog service is destroyed. This is useful if\n         * another service is wrapping the dialog and is managing the destruction instead.\n         */\n        this.closeOnDestroy = true;\n        /**\n         * Whether the dialog should close when the underlying overlay is detached. This is useful if\n         * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n         * external detachment can happen as a result of a scroll strategy triggering it or when the\n         * browser location changes.\n         */\n        this.closeOnOverlayDetachments = true;\n    }\n}\n\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n        super();\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        this._config = _config;\n        this._interactivityChecker = _interactivityChecker;\n        this._ngZone = _ngZone;\n        this._overlayRef = _overlayRef;\n        this._focusMonitor = _focusMonitor;\n        /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n        this._elementFocusedBeforeDialogWasOpened = null;\n        /**\n         * Type of interaction that led to the dialog being closed. This is used to determine\n         * whether the focus style will be applied when returning focus to its original location\n         * after the dialog is closed.\n         */\n        this._closeInteractionType = null;\n        /**\n         * Attaches a DOM portal to the dialog container.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwDialogContentAlreadyAttachedError();\n            }\n            const result = this._portalOutlet.attachDomPortal(portal);\n            this._contentAttached();\n            return result;\n        };\n        this._ariaLabelledBy = this._config.ariaLabelledBy || null;\n        this._document = _document;\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._handleBackdropClicks();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    element.removeEventListener('blur', callback);\n                    element.removeEventListener('mousedown', callback);\n                    element.removeAttribute('tabindex');\n                };\n                element.addEventListener('blur', callback);\n                element.addEventListener('mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus() {\n        const element = this._elementRef.nativeElement;\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n        // dialog. If the element inside the dialog can't be focused, then the container is focused\n        // so the user can't tab into other elements behind it.\n        switch (this._config.autoFocus) {\n            case false:\n            case 'dialog':\n                // Ensure that focus is on the dialog container. It's possible that a different\n                // component tried to move focus while the open animation was running. See:\n                // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                // if the focus isn't inside the dialog already, because it's possible that the consumer\n                // turned off `autoFocus` in order to move focus themselves.\n                if (!this._containsFocus()) {\n                    element.focus();\n                }\n                break;\n            case true:\n            case 'first-tabbable':\n                this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer();\n                    }\n                });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this._config.autoFocus);\n                break;\n        }\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer() {\n        // Note that there is no focus method when rendering on the server.\n        if (this._elementRef.nativeElement.focus) {\n            this._elementRef.nativeElement.focus();\n        }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        // Save the previously focused element. This element will be re-focused\n        // when the dialog closes.\n        if (this._document) {\n            this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n        }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this._overlayRef.backdropClick().subscribe(() => {\n            if (this._config.disableClose) {\n                this._recaptureFocus();\n            }\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDialogContainer, deps: [{ token: i0.ElementRef }, { token: i1.FocusTrapFactory }, { token: DOCUMENT, optional: true }, { token: DialogConfig }, { token: i1.InteractivityChecker }, { token: i0.NgZone }, { token: i1$1.OverlayRef }, { token: i1.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkDialogContainer, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledBy\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet></ng-template>\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"], dependencies: [{ kind: \"directive\", type: i3.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet></ng-template>\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DialogConfig]\n                }] }, { type: i1.InteractivityChecker }, { type: i0.NgZone }, { type: i1$1.OverlayRef }, { type: i1.FocusMonitor }]; }, propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        /** Emits when the dialog has been closed. */\n        this.closed = new Subject();\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n        });\n        this._detachSubscription = overlayRef.detachments().subscribe(() => {\n            // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n            if (config.closeOnOverlayDetachments !== false) {\n                this.close();\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this.containerInstance) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            // Drop the detach subscription first since it can be triggered by the\n            // `dispose` call and override the result of this closing sequence.\n            this._detachSubscription.unsubscribe();\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy');\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/** @docs-private */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nclass Dialog {\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n        this._overlay = _overlay;\n        this._injector = _injector;\n        this._defaultOptions = _defaultOptions;\n        this._parentDialog = _parentDialog;\n        this._overlayContainer = _overlayContainer;\n        this._openDialogsAtThisLevel = [];\n        this._afterAllClosedAtThisLevel = new Subject();\n        this._afterOpenedAtThisLevel = new Subject();\n        this._ariaHiddenElements = new Map();\n        /**\n         * Stream that emits when all open dialog have finished closing.\n         * Will emit on subscribe if there are no open dialogs to begin with.\n         */\n        this.afterAllClosed = defer(() => this.openDialogs.length\n            ? this._getAfterAllClosed()\n            : this._getAfterAllClosed().pipe(startWith(undefined)));\n        this._scrollStrategy = scrollStrategy;\n    }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || `cdk-dialog-${uniqueId++}`;\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = this._overlay.create(overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            this._hideNonDialogContentFromAssistiveTechnology();\n        }\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                this._overlay.position().global().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }), config.componentFactoryResolver);\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n            dialogRef.componentRef = contentRef;\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, { optional: true }))) {\n            providers.push({\n                provide: Directionality,\n                useValue: { value: config.direction, change: of() },\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Dialog, deps: [{ token: i1$1.Overlay }, { token: i0.Injector }, { token: DEFAULT_DIALOG_CONFIG, optional: true }, { token: Dialog, optional: true, skipSelf: true }, { token: i1$1.OverlayContainer }, { token: DIALOG_SCROLL_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Dialog }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.Injector }, { type: DialogConfig, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DEFAULT_DIALOG_CONFIG]\n                }] }, { type: Dialog, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: i1$1.OverlayContainer }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DIALOG_SCROLL_STRATEGY]\n                }] }]; } });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\nclass DialogModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: DialogModule, declarations: [CdkDialogContainer], imports: [OverlayModule, PortalModule, A11yModule], exports: [\n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule,\n            CdkDialogContainer] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DialogModule, providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER], imports: [OverlayModule, PortalModule, A11yModule, \n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    declarations: [CdkDialogContainer],\n                    providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,aAAa,QAAQ,sBAAsB;AACxF,SAASC,iCAAiC,QAAQ,uBAAuB;AACzE,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AACzL,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,OAAO,EAAEC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AAAA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AACA,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;IACA,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,gBAAgB;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,yBAAyB,GAAG,IAAI;EACzC;AACJ;AAEA,SAASC,sCAAsCA,CAAA,EAAG;EAC9C,MAAMC,KAAK,CAAC,uEAAuE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASlD,gBAAgB,CAAC;EAC9C8B,WAAWA,CAACqB,WAAW,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,WAAW,EAAEC,aAAa,EAAE;IACxH,KAAK,CAAC,CAAC;IACP,IAAI,CAACP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC;IACA,IAAI,CAACC,oCAAoC,GAAG,IAAI;IAChD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAIC,MAAM,IAAK;MAC/B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrFjB,sCAAsC,CAAC,CAAC;MAC5C;MACA,MAAMkB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACF,eAAe,CAACC,MAAM,CAAC;MACzD,IAAI,CAACK,gBAAgB,CAAC,CAAC;MACvB,OAAOD,MAAM;IACjB,CAAC;IACD,IAAI,CAACE,eAAe,GAAG,IAAI,CAACd,OAAO,CAACd,cAAc,IAAI,IAAI;IAC1D,IAAI,CAACa,SAAS,GAAGA,SAAS;EAC9B;EACAc,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACE,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAACb,MAAM,EAAE;IAC1B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFjB,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMkB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACY,qBAAqB,CAACb,MAAM,CAAC;IAC/D,IAAI,CAACK,gBAAgB,CAAC,CAAC;IACvB,OAAOD,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIU,oBAAoBA,CAACd,MAAM,EAAE;IACzB,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFjB,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMkB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACa,oBAAoB,CAACd,MAAM,CAAC;IAC9D,IAAI,CAACK,gBAAgB,CAAC,CAAC;IACvB,OAAOD,MAAM;EACjB;EACA;EACA;EACAW,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;MACxB,IAAI,CAACN,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC1B,qBAAqB,CAAC2B,WAAW,CAACF,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAAC3B,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;UACnBL,OAAO,CAACM,mBAAmB,CAAC,MAAM,EAAED,QAAQ,CAAC;UAC7CL,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAED,QAAQ,CAAC;UAClDL,OAAO,CAACO,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACDP,OAAO,CAACQ,gBAAgB,CAAC,MAAM,EAAEH,QAAQ,CAAC;QAC1CL,OAAO,CAACQ,gBAAgB,CAAC,WAAW,EAAEH,QAAQ,CAAC;MACnD,CAAC,CAAC;IACN;IACAL,OAAO,CAACS,KAAK,CAACR,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIS,mBAAmBA,CAACC,QAAQ,EAAEV,OAAO,EAAE;IACnC,IAAIW,cAAc,GAAG,IAAI,CAACzC,WAAW,CAAC0C,aAAa,CAACC,aAAa,CAACH,QAAQ,CAAC;IAC3E,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACb,WAAW,CAACa,cAAc,EAAEX,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIT,UAAUA,CAAA,EAAG;IACT,MAAMQ,OAAO,GAAG,IAAI,CAAC7B,WAAW,CAAC0C,aAAa;IAC9C;IACA;IACA;IACA;IACA;IACA,QAAQ,IAAI,CAACvC,OAAO,CAACX,SAAS;MAC1B,KAAK,KAAK;MACV,KAAK,QAAQ;QACT;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC,IAAI,CAACmC,cAAc,CAAC,CAAC,EAAE;UACxBE,OAAO,CAACS,KAAK,CAAC,CAAC;QACnB;QACA;MACJ,KAAK,IAAI;MACT,KAAK,gBAAgB;QACjB,IAAI,CAACM,UAAU,CAACC,4BAA4B,CAAC,CAAC,CAACC,IAAI,CAACC,mBAAmB,IAAI;UACvE;UACA;UACA,IAAI,CAACA,mBAAmB,EAAE;YACtB,IAAI,CAACC,qBAAqB,CAAC,CAAC;UAChC;QACJ,CAAC,CAAC;QACF;MACJ,KAAK,eAAe;QAChB,IAAI,CAACT,mBAAmB,CAAC,0CAA0C,CAAC;QACpE;MACJ;QACI,IAAI,CAACA,mBAAmB,CAAC,IAAI,CAACpC,OAAO,CAACX,SAAS,CAAC;QAChD;IACR;EACJ;EACA;EACA+B,aAAaA,CAAA,EAAG;IACZ,MAAM0B,WAAW,GAAG,IAAI,CAAC9C,OAAO,CAACV,YAAY;IAC7C,IAAIyD,kBAAkB,GAAG,IAAI;IAC7B,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;MACjCC,kBAAkB,GAAG,IAAI,CAAChD,SAAS,CAACyC,aAAa,CAACM,WAAW,CAAC;IAClE,CAAC,MACI,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;MACvCC,kBAAkB,GAAGD,WAAW,GAAG,IAAI,CAACzC,oCAAoC,GAAG,IAAI;IACvF,CAAC,MACI,IAAIyC,WAAW,EAAE;MAClBC,kBAAkB,GAAGD,WAAW;IACpC;IACA;IACA,IAAI,IAAI,CAAC9C,OAAO,CAACV,YAAY,IACzByD,kBAAkB,IAClB,OAAOA,kBAAkB,CAACZ,KAAK,KAAK,UAAU,EAAE;MAChD,MAAMa,aAAa,GAAGxG,iCAAiC,CAAC,CAAC;MACzD,MAAMkF,OAAO,GAAG,IAAI,CAAC7B,WAAW,CAAC0C,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI,CAACS,aAAa,IACdA,aAAa,KAAK,IAAI,CAACjD,SAAS,CAACkD,IAAI,IACrCD,aAAa,KAAKtB,OAAO,IACzBA,OAAO,CAACwB,QAAQ,CAACF,aAAa,CAAC,EAAE;QACjC,IAAI,IAAI,CAAC5C,aAAa,EAAE;UACpB,IAAI,CAACA,aAAa,CAAC+C,QAAQ,CAACJ,kBAAkB,EAAE,IAAI,CAACzC,qBAAqB,CAAC;UAC3E,IAAI,CAACA,qBAAqB,GAAG,IAAI;QACrC,CAAC,MACI;UACDyC,kBAAkB,CAACZ,KAAK,CAAC,CAAC;QAC9B;MACJ;IACJ;IACA,IAAI,IAAI,CAACM,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACW,OAAO,CAAC,CAAC;IAC7B;EACJ;EACA;EACAP,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAAChD,WAAW,CAAC0C,aAAa,CAACJ,KAAK,EAAE;MACtC,IAAI,CAACtC,WAAW,CAAC0C,aAAa,CAACJ,KAAK,CAAC,CAAC;IAC1C;EACJ;EACA;EACAX,cAAcA,CAAA,EAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAAC7B,WAAW,CAAC0C,aAAa;IAC9C,MAAMS,aAAa,GAAGxG,iCAAiC,CAAC,CAAC;IACzD,OAAOkF,OAAO,KAAKsB,aAAa,IAAItB,OAAO,CAACwB,QAAQ,CAACF,aAAa,CAAC;EACvE;EACA;EACAjC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC0B,UAAU,GAAG,IAAI,CAAC3C,iBAAiB,CAACuD,MAAM,CAAC,IAAI,CAACxD,WAAW,CAAC0C,aAAa,CAAC;IAC/E;IACA;IACA,IAAI,IAAI,CAACxC,SAAS,EAAE;MAChB,IAAI,CAACM,oCAAoC,GAAG7D,iCAAiC,CAAC,CAAC;IACnF;EACJ;EACA;EACAwE,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,CAACb,WAAW,CAACmD,aAAa,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;MAC7C,IAAI,IAAI,CAACvD,OAAO,CAACnB,YAAY,EAAE;QAC3B,IAAI,CAAC0C,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACiC,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwF9D,kBAAkB,EAA5B5C,EAAE,CAAA2G,iBAAA,CAA4C3G,EAAE,CAAC4G,UAAU,GAA3D5G,EAAE,CAAA2G,iBAAA,CAAsE1H,EAAE,CAAC4H,gBAAgB,GAA3F7G,EAAE,CAAA2G,iBAAA,CAAsG5G,QAAQ,MAAhHC,EAAE,CAAA2G,iBAAA,CAA2IpF,YAAY,GAAzJvB,EAAE,CAAA2G,iBAAA,CAAoK1H,EAAE,CAAC6H,oBAAoB,GAA7L9G,EAAE,CAAA2G,iBAAA,CAAwM3G,EAAE,CAAC+G,MAAM,GAAnN/G,EAAE,CAAA2G,iBAAA,CAA8NxH,IAAI,CAACG,UAAU,GAA/OU,EAAE,CAAA2G,iBAAA,CAA0P1H,EAAE,CAAC+H,YAAY;IAAA,CAA4C;EAAE;EACzZ;IAAS,IAAI,CAACC,IAAI,kBAD8EjH,EAAE,CAAAkH,iBAAA;MAAAC,IAAA,EACJvE,kBAAkB;MAAAwE,SAAA;MAAAC,SAAA,WAAAC,yBAAAjG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADhBrB,EAAE,CAAAuH,WAAA,CAC6e5H,eAAe;QAAA;QAAA,IAAA0B,EAAA;UAAA,IAAAmG,EAAA;UAD9fxH,EAAE,CAAAyH,cAAA,CAAAD,EAAA,GAAFxH,EAAE,CAAA0H,WAAA,QAAApG,GAAA,CAAAmC,aAAA,GAAA+D,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,eACoF,IAAI;MAAAC,QAAA;MAAAC,YAAA,WAAAC,gCAAA1G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD1FrB,EAAE,CAAAgI,WAAA,OAAA1G,GAAA,CAAA0B,OAAA,CAAAiF,EAAA,kBAAA3G,GAAA,CAAA0B,OAAA,CAAAvB,IAAA,gBAAAH,GAAA,CAAA0B,OAAA,CAAAZ,SAAA,qBAAAd,GAAA,CAAA0B,OAAA,CAAAb,SAAA,UAAAb,GAAA,CAAAwC,eAAA,gBAAAxC,GAAA,CAAA0B,OAAA,CAAAb,SAAA,sBAAAb,GAAA,CAAA0B,OAAA,CAAAf,eAAA;QAAA;MAAA;MAAAiG,QAAA,GAAFlI,EAAE,CAAAmI,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAnH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrB,EAAE,CAAAyI,UAAA,IAAArH,yCAAA,wBAC4nB,CAAC;QAAA;MAAA;MAAAsH,YAAA,GAA8JjJ,EAAE,CAACE,eAAe;MAAAgJ,MAAA;MAAAC,aAAA;IAAA,EAAuN;EAAE;AAC5mC;AACA;EAAA,QAAAjF,SAAA,oBAAAA,SAAA,KAHoG3D,EAAE,CAAA6I,iBAAA,CAGXjG,kBAAkB,EAAc,CAAC;IAChHuE,IAAI,EAAElH,SAAS;IACf6I,IAAI,EAAE,CAAC;MAAEzD,QAAQ,EAAE,sBAAsB;MAAEuD,aAAa,EAAE1I,iBAAiB,CAAC6I,IAAI;MAAEC,eAAe,EAAE7I,uBAAuB,CAAC8I,OAAO;MAAEC,IAAI,EAAE;QAC9H,OAAO,EAAE,sBAAsB;QAC/B,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,oBAAoB;QACjC,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,mBAAmB;QACxC,wBAAwB,EAAE,4CAA4C;QACtE,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE;MAC/B,CAAC;MAAEX,QAAQ,EAAE,+CAA+C;MAAEI,MAAM,EAAE,CAAC,mGAAmG;IAAE,CAAC;EACzL,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExB,IAAI,EAAEnH,EAAE,CAAC4G;IAAW,CAAC,EAAE;MAAEO,IAAI,EAAElI,EAAE,CAAC4H;IAAiB,CAAC,EAAE;MAAEM,IAAI,EAAEgC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACtHjC,IAAI,EAAE/G;MACV,CAAC,EAAE;QACC+G,IAAI,EAAE9G,MAAM;QACZyI,IAAI,EAAE,CAAC/I,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEoH,IAAI,EAAEgC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCjC,IAAI,EAAE9G,MAAM;QACZyI,IAAI,EAAE,CAACvH,YAAY;MACvB,CAAC;IAAE,CAAC,EAAE;MAAE4F,IAAI,EAAElI,EAAE,CAAC6H;IAAqB,CAAC,EAAE;MAAEK,IAAI,EAAEnH,EAAE,CAAC+G;IAAO,CAAC,EAAE;MAAEI,IAAI,EAAEhI,IAAI,CAACG;IAAW,CAAC,EAAE;MAAE6H,IAAI,EAAElI,EAAE,CAAC+H;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEvD,aAAa,EAAE,CAAC;MAC1J0D,IAAI,EAAE7G,SAAS;MACfwI,IAAI,EAAE,CAACnJ,eAAe,EAAE;QAAE0J,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ9H,WAAWA,CAAC+H,UAAU,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB;IACA,IAAI,CAACC,MAAM,GAAG,IAAI1I,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACc,YAAY,GAAG2H,MAAM,CAAC3H,YAAY;IACvC,IAAI,CAACyE,aAAa,GAAGiD,UAAU,CAACjD,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACoD,aAAa,GAAGH,UAAU,CAACG,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,oBAAoB,GAAGJ,UAAU,CAACI,oBAAoB,CAAC,CAAC;IAC7D,IAAI,CAAC1B,EAAE,GAAGuB,MAAM,CAACvB,EAAE,CAAC,CAAC;IACrB,IAAI,CAACyB,aAAa,CAACnD,SAAS,CAACqD,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAO,KAAKhJ,MAAM,IAAI,CAAC,IAAI,CAACgB,YAAY,IAAI,CAACf,cAAc,CAAC8I,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,KAAK,CAACZ,SAAS,EAAE;UAAEa,WAAW,EAAE;QAAW,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC;IACF,IAAI,CAAC1D,aAAa,CAACC,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAAC1E,YAAY,EAAE;QACpB,IAAI,CAACkI,KAAK,CAACZ,SAAS,EAAE;UAAEa,WAAW,EAAE;QAAQ,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,mBAAmB,GAAGV,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC3D,SAAS,CAAC,MAAM;MAChE;MACA,IAAIiD,MAAM,CAAC/G,yBAAyB,KAAK,KAAK,EAAE;QAC5C,IAAI,CAACsH,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAACnG,MAAM,EAAEe,OAAO,EAAE;IACnB,IAAI,IAAI,CAACwF,iBAAiB,EAAE;MACxB,MAAMC,aAAa,GAAG,IAAI,CAACX,MAAM;MACjC,IAAI,CAACU,iBAAiB,CAAC7G,qBAAqB,GAAGqB,OAAO,EAAEqF,WAAW,IAAI,SAAS;MAChF;MACA;MACA,IAAI,CAACC,mBAAmB,CAACI,WAAW,CAAC,CAAC;MACtC,IAAI,CAACd,UAAU,CAACe,OAAO,CAAC,CAAC;MACzBF,aAAa,CAACG,IAAI,CAAC3G,MAAM,CAAC;MAC1BwG,aAAa,CAACI,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACN,iBAAiB,GAAG,IAAI;IAC1D;EACJ;EACA;EACAO,cAAcA,CAAA,EAAG;IACb,IAAI,CAACnB,UAAU,CAACmB,cAAc,CAAC,CAAC;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAAC7I,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAACwH,UAAU,CAACoB,UAAU,CAAC;MAAE7I,KAAK;MAAEC;IAAO,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;EACA6I,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACtB,UAAU,CAACqB,aAAa,CAACC,OAAO,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAACtB,UAAU,CAACuB,gBAAgB,CAACD,OAAO,CAAC;IACzC,OAAO,IAAI;EACf;AACJ;;AAEA;AACA,MAAME,sBAAsB,GAAG,IAAIxK,cAAc,CAAC,sBAAsB,CAAC;AACzE;AACA,MAAMyK,WAAW,GAAG,IAAIzK,cAAc,CAAC,YAAY,CAAC;AACpD;AACA,MAAM0K,qBAAqB,GAAG,IAAI1K,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA,SAAS2K,uCAAuCA,CAACC,OAAO,EAAE;EACtD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA,MAAMC,+BAA+B,GAAG;EACpCC,OAAO,EAAER,sBAAsB;EAC/BS,IAAI,EAAE,CAACpM,OAAO,CAAC;EACfqM,UAAU,EAAEP;AAChB,CAAC;;AAED;AACA,IAAIQ,QAAQ,GAAG,CAAC;AAChB,MAAMC,MAAM,CAAC;EACT;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,WAAW,GAAG,IAAI,CAACE,uBAAuB;EAC7F;EACA;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,aAAa,GAAG,IAAI,CAACA,aAAa,CAACE,WAAW,GAAG,IAAI,CAACC,uBAAuB;EAC7F;EACAxK,WAAWA,CAACyK,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEN,aAAa,EAAEO,iBAAiB,EAAEC,cAAc,EAAE;IAChG,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACO,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACN,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACQ,0BAA0B,GAAG,IAAIvL,OAAO,CAAC,CAAC;IAC/C,IAAI,CAACiL,uBAAuB,GAAG,IAAIjL,OAAO,CAAC,CAAC;IAC5C,IAAI,CAACwL,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAGzL,KAAK,CAAC,MAAM,IAAI,CAAC4K,WAAW,CAACc,MAAM,GACnD,IAAI,CAACC,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAACzL,SAAS,CAACgI,SAAS,CAAC,CAAC,CAAC;IAC3D,IAAI,CAAC0D,eAAe,GAAGR,cAAc;EACzC;EACAS,IAAIA,CAACC,sBAAsB,EAAEvD,MAAM,EAAE;IACjC,MAAMwD,QAAQ,GAAI,IAAI,CAACb,eAAe,IAAI,IAAI5K,YAAY,CAAC,CAAE;IAC7DiI,MAAM,GAAG;MAAE,GAAGwD,QAAQ;MAAE,GAAGxD;IAAO,CAAC;IACnCA,MAAM,CAACvB,EAAE,GAAGuB,MAAM,CAACvB,EAAE,IAAK,cAAayD,QAAQ,EAAG,EAAC;IACnD,IAAIlC,MAAM,CAACvB,EAAE,IACT,IAAI,CAACgF,aAAa,CAACzD,MAAM,CAACvB,EAAE,CAAC,KAC5B,OAAOtE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMhB,KAAK,CAAE,mBAAkB6G,MAAM,CAACvB,EAAG,iDAAgD,CAAC;IAC9F;IACA,MAAMiF,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAAC3D,MAAM,CAAC;IACpD,MAAMD,UAAU,GAAG,IAAI,CAAC0C,QAAQ,CAAC5F,MAAM,CAAC6G,aAAa,CAAC;IACtD,MAAME,SAAS,GAAG,IAAI9D,SAAS,CAACC,UAAU,EAAEC,MAAM,CAAC;IACnD,MAAM6D,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAAC/D,UAAU,EAAE6D,SAAS,EAAE5D,MAAM,CAAC;IAC5E4D,SAAS,CAACjD,iBAAiB,GAAGkD,eAAe;IAC7C,IAAI,CAACE,oBAAoB,CAACR,sBAAsB,EAAEK,SAAS,EAAEC,eAAe,EAAE7D,MAAM,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACoC,WAAW,CAACc,MAAM,EAAE;MAC1B,IAAI,CAACc,4CAA4C,CAAC,CAAC;IACvD;IACA,IAAI,CAAC5B,WAAW,CAAC6B,IAAI,CAACL,SAAS,CAAC;IAChCA,SAAS,CAAC3D,MAAM,CAAClD,SAAS,CAAC,MAAM,IAAI,CAACmH,iBAAiB,CAACN,SAAS,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAACrB,WAAW,CAACxB,IAAI,CAAC6C,SAAS,CAAC;IAChC,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;EACIO,QAAQA,CAAA,EAAG;IACPC,cAAc,CAAC,IAAI,CAAChC,WAAW,EAAEiC,MAAM,IAAIA,MAAM,CAAC9D,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACIkD,aAAaA,CAAChF,EAAE,EAAE;IACd,OAAO,IAAI,CAAC2D,WAAW,CAACkC,IAAI,CAACD,MAAM,IAAIA,MAAM,CAAC5F,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACA9D,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACAyJ,cAAc,CAAC,IAAI,CAAC9B,uBAAuB,EAAE+B,MAAM,IAAI;MACnD;MACA,IAAIA,MAAM,CAACrE,MAAM,CAAChH,cAAc,KAAK,KAAK,EAAE;QACxC,IAAI,CAACkL,iBAAiB,CAACG,MAAM,EAAE,KAAK,CAAC;MACzC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAD,cAAc,CAAC,IAAI,CAAC9B,uBAAuB,EAAE+B,MAAM,IAAIA,MAAM,CAAC9D,KAAK,CAAC,CAAC,CAAC;IACtE,IAAI,CAACuC,0BAA0B,CAAC9B,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACwB,uBAAuB,CAACxB,QAAQ,CAAC,CAAC;IACvC,IAAI,CAACsB,uBAAuB,GAAG,EAAE;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIqB,iBAAiBA,CAAC3D,MAAM,EAAE;IACtB,MAAMuE,KAAK,GAAG,IAAI1O,aAAa,CAAC;MAC5B2O,gBAAgB,EAAExE,MAAM,CAACwE,gBAAgB,IACrC,IAAI,CAAC/B,QAAQ,CAACgC,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC;MAC7E/B,cAAc,EAAE7C,MAAM,CAAC6C,cAAc,IAAI,IAAI,CAACQ,eAAe,CAAC,CAAC;MAC/DnL,UAAU,EAAE8H,MAAM,CAAC9H,UAAU;MAC7BC,WAAW,EAAE6H,MAAM,CAAC7H,WAAW;MAC/B0M,SAAS,EAAE7E,MAAM,CAAC6E,SAAS;MAC3BC,QAAQ,EAAE9E,MAAM,CAAC8E,QAAQ;MACzBC,SAAS,EAAE/E,MAAM,CAAC+E,SAAS;MAC3BC,QAAQ,EAAEhF,MAAM,CAACgF,QAAQ;MACzBC,SAAS,EAAEjF,MAAM,CAACiF,SAAS;MAC3B3M,KAAK,EAAE0H,MAAM,CAAC1H,KAAK;MACnBC,MAAM,EAAEyH,MAAM,CAACzH,MAAM;MACrB2M,mBAAmB,EAAElF,MAAM,CAACjH;IAChC,CAAC,CAAC;IACF,IAAIiH,MAAM,CAAC5H,aAAa,EAAE;MACtBmM,KAAK,CAACnM,aAAa,GAAG4H,MAAM,CAAC5H,aAAa;IAC9C;IACA,OAAOmM,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIT,gBAAgBA,CAACnC,OAAO,EAAEiC,SAAS,EAAE5D,MAAM,EAAE;IACzC,MAAMmF,YAAY,GAAGnF,MAAM,CAACoF,QAAQ,IAAIpF,MAAM,CAACqF,gBAAgB,EAAED,QAAQ;IACzE,MAAME,SAAS,GAAG,CACd;MAAEvD,OAAO,EAAEhK,YAAY;MAAEwN,QAAQ,EAAEvF;IAAO,CAAC,EAC3C;MAAE+B,OAAO,EAAEjC,SAAS;MAAEyF,QAAQ,EAAE3B;IAAU,CAAC,EAC3C;MAAE7B,OAAO,EAAEjM,UAAU;MAAEyP,QAAQ,EAAE5D;IAAQ,CAAC,CAC7C;IACD,IAAI6D,aAAa;IACjB,IAAIxF,MAAM,CAACyF,SAAS,EAAE;MAClB,IAAI,OAAOzF,MAAM,CAACyF,SAAS,KAAK,UAAU,EAAE;QACxCD,aAAa,GAAGxF,MAAM,CAACyF,SAAS;MACpC,CAAC,MACI;QACDD,aAAa,GAAGxF,MAAM,CAACyF,SAAS,CAAC9H,IAAI;QACrC2H,SAAS,CAACrB,IAAI,CAAC,GAAGjE,MAAM,CAACyF,SAAS,CAACH,SAAS,CAACtF,MAAM,CAAC,CAAC;MACzD;IACJ,CAAC,MACI;MACDwF,aAAa,GAAGpM,kBAAkB;IACtC;IACA,MAAMsM,eAAe,GAAG,IAAItP,eAAe,CAACoP,aAAa,EAAExF,MAAM,CAACqF,gBAAgB,EAAErO,QAAQ,CAAC6F,MAAM,CAAC;MAAE8I,MAAM,EAAER,YAAY,IAAI,IAAI,CAACzC,SAAS;MAAE4C;IAAU,CAAC,CAAC,EAAEtF,MAAM,CAAC4F,wBAAwB,CAAC;IAC5L,MAAMC,YAAY,GAAGlE,OAAO,CAACmE,MAAM,CAACJ,eAAe,CAAC;IACpD,OAAOG,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIhC,oBAAoBA,CAACR,sBAAsB,EAAEK,SAAS,EAAEC,eAAe,EAAE7D,MAAM,EAAE;IAC7E,IAAIuD,sBAAsB,YAAYtM,WAAW,EAAE;MAC/C,MAAMmO,QAAQ,GAAG,IAAI,CAACY,eAAe,CAAChG,MAAM,EAAE4D,SAAS,EAAEC,eAAe,EAAElE,SAAS,CAAC;MACpF,IAAIsG,OAAO,GAAG;QAAEC,SAAS,EAAElG,MAAM,CAACxH,IAAI;QAAEoL;MAAU,CAAC;MACnD,IAAI5D,MAAM,CAACmG,eAAe,EAAE;QACxBF,OAAO,GAAG;UACN,GAAGA,OAAO;UACV,IAAI,OAAOjG,MAAM,CAACmG,eAAe,KAAK,UAAU,GAC1CnG,MAAM,CAACmG,eAAe,CAAC,CAAC,GACxBnG,MAAM,CAACmG,eAAe;QAChC,CAAC;MACL;MACAtC,eAAe,CAAC/I,oBAAoB,CAAC,IAAIzE,cAAc,CAACkN,sBAAsB,EAAE,IAAI,EAAE0C,OAAO,EAAEb,QAAQ,CAAC,CAAC;IAC7G,CAAC,MACI;MACD,MAAMA,QAAQ,GAAG,IAAI,CAACY,eAAe,CAAChG,MAAM,EAAE4D,SAAS,EAAEC,eAAe,EAAE,IAAI,CAACnB,SAAS,CAAC;MACzF,MAAM0D,UAAU,GAAGvC,eAAe,CAAChJ,qBAAqB,CAAC,IAAIzE,eAAe,CAACmN,sBAAsB,EAAEvD,MAAM,CAACqF,gBAAgB,EAAED,QAAQ,EAAEpF,MAAM,CAAC4F,wBAAwB,CAAC,CAAC;MACzKhC,SAAS,CAACyC,YAAY,GAAGD,UAAU;MACnCxC,SAAS,CAAC3C,iBAAiB,GAAGmF,UAAU,CAACL,QAAQ;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAAChG,MAAM,EAAE4D,SAAS,EAAEC,eAAe,EAAEyC,gBAAgB,EAAE;IAClE,MAAMnB,YAAY,GAAGnF,MAAM,CAACoF,QAAQ,IAAIpF,MAAM,CAACqF,gBAAgB,EAAED,QAAQ;IACzE,MAAME,SAAS,GAAG,CACd;MAAEvD,OAAO,EAAEP,WAAW;MAAE+D,QAAQ,EAAEvF,MAAM,CAACxH;IAAK,CAAC,EAC/C;MAAEuJ,OAAO,EAAEjC,SAAS;MAAEyF,QAAQ,EAAE3B;IAAU,CAAC,CAC9C;IACD,IAAI5D,MAAM,CAACsF,SAAS,EAAE;MAClB,IAAI,OAAOtF,MAAM,CAACsF,SAAS,KAAK,UAAU,EAAE;QACxCA,SAAS,CAACrB,IAAI,CAAC,GAAGjE,MAAM,CAACsF,SAAS,CAAC1B,SAAS,EAAE5D,MAAM,EAAE6D,eAAe,CAAC,CAAC;MAC3E,CAAC,MACI;QACDyB,SAAS,CAACrB,IAAI,CAAC,GAAGjE,MAAM,CAACsF,SAAS,CAAC;MACvC;IACJ;IACA,IAAItF,MAAM,CAAC6E,SAAS,KACf,CAACM,YAAY,IACV,CAACA,YAAY,CAACoB,GAAG,CAAC7O,cAAc,EAAE,IAAI,EAAE;MAAE8O,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,EAAE;MAClElB,SAAS,CAACrB,IAAI,CAAC;QACXlC,OAAO,EAAErK,cAAc;QACvB6N,QAAQ,EAAE;UAAEkB,KAAK,EAAEzG,MAAM,CAAC6E,SAAS;UAAE6B,MAAM,EAAEjP,EAAE,CAAC;QAAE;MACtD,CAAC,CAAC;IACN;IACA,OAAOT,QAAQ,CAAC6F,MAAM,CAAC;MAAE8I,MAAM,EAAER,YAAY,IAAImB,gBAAgB;MAAEhB;IAAU,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACIpB,iBAAiBA,CAACN,SAAS,EAAE+C,SAAS,EAAE;IACpC,MAAMC,KAAK,GAAG,IAAI,CAACxE,WAAW,CAACyE,OAAO,CAACjD,SAAS,CAAC;IACjD,IAAIgD,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACxE,WAAW,CAAC0E,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;MACA;MACA,IAAI,CAAC,IAAI,CAACxE,WAAW,CAACc,MAAM,EAAE;QAC1B,IAAI,CAACH,mBAAmB,CAACgE,OAAO,CAAC,CAACC,aAAa,EAAE9L,OAAO,KAAK;UACzD,IAAI8L,aAAa,EAAE;YACf9L,OAAO,CAAC+L,YAAY,CAAC,aAAa,EAAED,aAAa,CAAC;UACtD,CAAC,MACI;YACD9L,OAAO,CAACO,eAAe,CAAC,aAAa,CAAC;UAC1C;QACJ,CAAC,CAAC;QACF,IAAI,CAACsH,mBAAmB,CAACmE,KAAK,CAAC,CAAC;QAChC,IAAIP,SAAS,EAAE;UACX,IAAI,CAACxD,kBAAkB,CAAC,CAAC,CAACpC,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ;EACJ;EACA;EACAiD,4CAA4CA,CAAA,EAAG;IAC3C,MAAMmD,gBAAgB,GAAG,IAAI,CAACvE,iBAAiB,CAACwE,mBAAmB,CAAC,CAAC;IACrE;IACA,IAAID,gBAAgB,CAACE,aAAa,EAAE;MAChC,MAAMC,QAAQ,GAAGH,gBAAgB,CAACE,aAAa,CAACE,QAAQ;MACxD,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAACpE,MAAM,GAAG,CAAC,EAAEsE,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMC,OAAO,GAAGH,QAAQ,CAACE,CAAC,CAAC;QAC3B,IAAIC,OAAO,KAAKN,gBAAgB,IAC5BM,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7BD,OAAO,CAACC,QAAQ,KAAK,OAAO,IAC5B,CAACD,OAAO,CAACE,YAAY,CAAC,WAAW,CAAC,EAAE;UACpC,IAAI,CAAC5E,mBAAmB,CAAC6E,GAAG,CAACH,OAAO,EAAEA,OAAO,CAACI,YAAY,CAAC,aAAa,CAAC,CAAC;UAC1EJ,OAAO,CAACR,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA9D,kBAAkBA,CAAA,EAAG;IACjB,MAAMwC,MAAM,GAAG,IAAI,CAACtD,aAAa;IACjC,OAAOsD,MAAM,GAAGA,MAAM,CAACxC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACL,0BAA0B;EACjF;EACA;IAAS,IAAI,CAAC9F,IAAI,YAAA8K,eAAA5K,CAAA;MAAA,YAAAA,CAAA,IAAwFiF,MAAM,EAtXhB3L,EAAE,CAAAuR,QAAA,CAsXgCpS,IAAI,CAACC,OAAO,GAtX9CY,EAAE,CAAAuR,QAAA,CAsXyDvR,EAAE,CAACQ,QAAQ,GAtXtER,EAAE,CAAAuR,QAAA,CAsXiFtG,qBAAqB,MAtXxGjL,EAAE,CAAAuR,QAAA,CAsXmI5F,MAAM,OAtX3I3L,EAAE,CAAAuR,QAAA,CAsXsLpS,IAAI,CAACqS,gBAAgB,GAtX7MxR,EAAE,CAAAuR,QAAA,CAsXwNxG,sBAAsB;IAAA,CAA6C;EAAE;EAC/X;IAAS,IAAI,CAAC0G,KAAK,kBAvX6EzR,EAAE,CAAA0R,kBAAA;MAAAC,KAAA,EAuXYhG,MAAM;MAAAiG,OAAA,EAANjG,MAAM,CAAAnF;IAAA,EAAG;EAAE;AAC7H;AACA;EAAA,QAAA7C,SAAA,oBAAAA,SAAA,KAzXoG3D,EAAE,CAAA6I,iBAAA,CAyXX8C,MAAM,EAAc,CAAC;IACpGxE,IAAI,EAAEzG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEyG,IAAI,EAAEhI,IAAI,CAACC;IAAQ,CAAC,EAAE;MAAE+H,IAAI,EAAEnH,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAE2G,IAAI,EAAE5F,YAAY;MAAE6H,UAAU,EAAE,CAAC;QAChHjC,IAAI,EAAE/G;MACV,CAAC,EAAE;QACC+G,IAAI,EAAE9G,MAAM;QACZyI,IAAI,EAAE,CAACmC,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAE9D,IAAI,EAAEwE,MAAM;MAAEvC,UAAU,EAAE,CAAC;QAC/BjC,IAAI,EAAE/G;MACV,CAAC,EAAE;QACC+G,IAAI,EAAExG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEwG,IAAI,EAAEhI,IAAI,CAACqS;IAAiB,CAAC,EAAE;MAAErK,IAAI,EAAEgC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACnEjC,IAAI,EAAE9G,MAAM;QACZyI,IAAI,EAAE,CAACiC,sBAAsB;MACjC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA,SAAS6C,cAAcA,CAACiE,KAAK,EAAE9M,QAAQ,EAAE;EACrC,IAAIiM,CAAC,GAAGa,KAAK,CAACnF,MAAM;EACpB,OAAOsE,CAAC,EAAE,EAAE;IACRjM,QAAQ,CAAC8M,KAAK,CAACb,CAAC,CAAC,CAAC;EACtB;AACJ;AAEA,MAAMc,YAAY,CAAC;EACf;IAAS,IAAI,CAACtL,IAAI,YAAAuL,qBAAArL,CAAA;MAAA,YAAAA,CAAA,IAAwFoL,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAACE,IAAI,kBArZ8EhS,EAAE,CAAAiS,gBAAA;MAAA9K,IAAA,EAqZS2K;IAAY,EAIzF;EAAE;EAChC;IAAS,IAAI,CAACI,IAAI,kBA1Z8ElS,EAAE,CAAAmS,gBAAA;MAAArD,SAAA,EA0ZkC,CAACnD,MAAM,EAAEL,+BAA+B,CAAC;MAAA8G,OAAA,GAAY7S,aAAa,EAAEO,YAAY,EAAEZ,UAAU;MACxN;MACA;MACAY,YAAY;IAAA,EAAI;EAAE;AAC9B;AACA;EAAA,QAAA6D,SAAA,oBAAAA,SAAA,KA/ZoG3D,EAAE,CAAA6I,iBAAA,CA+ZXiJ,YAAY,EAAc,CAAC;IAC1G3K,IAAI,EAAEvG,QAAQ;IACdkI,IAAI,EAAE,CAAC;MACCsJ,OAAO,EAAE,CAAC7S,aAAa,EAAEO,YAAY,EAAEZ,UAAU,CAAC;MAClDmT,OAAO,EAAE;MACL;MACA;MACAvS,YAAY,EACZ8C,kBAAkB,CACrB;MACD0P,YAAY,EAAE,CAAC1P,kBAAkB,CAAC;MAClCkM,SAAS,EAAE,CAACnD,MAAM,EAAEL,+BAA+B;IACvD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS1I,kBAAkB,EAAEqI,qBAAqB,EAAED,WAAW,EAAED,sBAAsB,EAAEO,+BAA+B,EAAEJ,uCAAuC,EAAES,MAAM,EAAEpK,YAAY,EAAEuQ,YAAY,EAAExI,SAAS,EAAE5G,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}