import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Resume } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class ResumeService {
  constructor(private http: HttpClient) {}

  // GET: Tous les résumés
  getResumes(): Observable<Resume[]> {
    return this.http.get<Resume[]>(`${environment.urlApi}resume`)
  }

  // GET: Un résumé par ID
  getResume(id: number): Observable<Resume> {
    return this.http.get<Resume>(`${environment.urlApi}resume/${id}`)
  }

  // POST: Créer un résumé
  createResume(resume: Resume): Observable<Resume> {
    return this.http.post<Resume>(`${environment.urlApi}resume`, resume)
  }

  // PUT: Modifier un résumé
  updateResume(id: number, resume: Resume): Observable<any> {
    return this.http.put(`${environment.urlApi}resume/${id}`, resume)
  }

  // DELETE: Supprimer un résumé
  deleteResume(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}resume/${id}`)
  }
}
