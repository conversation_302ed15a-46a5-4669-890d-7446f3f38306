"use strict";
(self["webpackChunkelearning_platform"] = self["webpackChunkelearning_platform"] || []).push([["src_app_features_certificates_certificates_module_ts"],{

/***/ 4759:
/*!******************************************************!*\
  !*** ./src/app/core/services/certificate.service.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CertificateService: () => (/* binding */ CertificateService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);




class CertificateService {
  constructor(http) {
    this.http = http;
    this.apiUrl = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlApi}certificats`;
  }
  // GET: Télécharger un certificat PDF (correspond à GET /api/certificats/telecharger)
  telechargerCertificat(nomClient, prenomClient, titreCours, score) {
    const params = new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpParams().set('nomClient', nomClient).set('prenomClient', prenomClient).set('titreCours', titreCours).set('score', score.toString());
    return this.http.get(`${this.apiUrl}/telecharger`, {
      params,
      responseType: "blob"
    });
  }
  // Méthode utilitaire pour télécharger et sauvegarder le PDF
  downloadAndSaveCertificate(nomClient, prenomClient, titreCours, score, filename = 'certificat.pdf') {
    this.telechargerCertificat(nomClient, prenomClient, titreCours, score).subscribe({
      next: blob => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: error => {
        console.error('Erreur lors du téléchargement du certificat:', error);
      }
    });
  }
  static {
    this.ɵfac = function CertificateService_Factory(t) {
      return new (t || CertificateService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
      token: CertificateService,
      factory: CertificateService.ɵfac,
      providedIn: "root"
    });
  }
}

/***/ }),

/***/ 5188:
/*!*****************************************************************!*\
  !*** ./src/app/features/certificates/certificates.component.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CertificatesComponent: () => (/* binding */ CertificatesComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _core_services_certificate_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../core/services/certificate.service */ 4759);
/* harmony import */ var _core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core/services/auth.service */ 8010);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/snack-bar */ 3347);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/chips */ 2772);









function CertificatesComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 4)(1, "mat-card")(2, "mat-card-content", 5)(3, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "emoji_events");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6, "Aucun certificat disponible");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.getNoCertificatesMessage());
  }
}
function CertificatesComponent_ng_template_7_mat_card_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-card", 9)(1, "mat-card-header", 10)(2, "div", 11)(3, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "emoji_events");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div")(6, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "mat-card-subtitle");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "mat-card-content", 12)(11, "div", 13)(12, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](13, "person");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](15, "\u00C9tudiant: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](16, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](17);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "div", 13)(19, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](20, "calendar_today");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "div", 13)(24, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](25, "book");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, "Score: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "strong", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](29);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](30);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](31, "div", 13)(32, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](33, "N\u00B0 de s\u00E9rie: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](34, "mat-chip-listbox")(35, "mat-chip", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](36);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](37, "div", 16)(38, "button", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_38_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r7);
      const cert_r5 = restoredCtx.$implicit;
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r6.previewCertificate(cert_r5.id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](39, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](40, "visibility");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](41, " Aper\u00E7u ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](42, "button", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_42_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r7);
      const cert_r5 = restoredCtx.$implicit;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r8.downloadCertificate(cert_r5));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](43, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](44, "download");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](45, " PDF ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](46, "div", 19)(47, "mat-chip-listbox")(48, "mat-chip", 20)(49, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](50, "check_circle");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](51, " Certificat Valid\u00E9 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const cert_r5 = ctx.$implicit;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](cert_r5.cours == null ? null : cert_r5.cours.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"](" Par ", cert_r5.cours == null ? null : cert_r5.cours.formateur == null ? null : cert_r5.cours.formateur.prenom, " ", cert_r5.cours == null ? null : cert_r5.cours.formateur == null ? null : cert_r5.cours.formateur.nom, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"]("", cert_r5.client == null ? null : cert_r5.client.prenom, " ", cert_r5.client == null ? null : cert_r5.client.nom, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("Date d'obtention: ", cert_r5.dateGeneration ? ctx_r3.formatDate(cert_r5.dateGeneration) : "N/A", "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", ctx_r3.getScoreColorClass(cert_r5.scoreQuiz || 0, cert_r5.seuilReussite || 0));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", cert_r5.scoreQuiz, "% ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" (seuil: ", cert_r5.seuilReussite, "%) ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](cert_r5.numeroSerie);
  }
}
function CertificatesComponent_ng_template_7_mat_card_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-card", 21)(1, "mat-card-header")(2, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Statistiques des Certificats");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "mat-card-content", 22)(5, "div", 23)(6, "div", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, "Total \u00E9mis");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "div", 23)(11, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "Score moyen");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "div", 23)(16, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](17);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, "Excellents r\u00E9sultats");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "div", 23)(21, "div", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](24, "\u00C9tudiants certifi\u00E9s");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r4.certificates.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r4.averageScore(), "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r4.excellentResultsCount());
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r4.uniqueStudentsCount());
  }
}
function CertificatesComponent_ng_template_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, CertificatesComponent_ng_template_7_mat_card_1_Template, 52, 10, "mat-card", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, CertificatesComponent_ng_template_7_mat_card_2_Template, 25, 4, "mat-card", 8);
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r2.certificates);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.role) === "Formateur" || (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.role) === "Admin");
  }
}
class CertificatesComponent {
  constructor(certificateService, authService, snackBar) {
    this.certificateService = certificateService;
    this.authService = authService;
    this.snackBar = snackBar;
    this.certificates = [];
    this.currentUser = null;
  }
  ngOnInit() {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      if (this.currentUser) {
        this.loadCertificates();
      }
    });
  }
  loadCertificates() {
    // Mock data for demonstration
    this.certificates = [{
      id: 1,
      nomClient: "Pierre Martin",
      dateObtention: new Date("2024-01-15T14:30:00Z"),
      coursId: 1,
      cours: {
        id: 1,
        titre: "React Fundamentals",
        formateur: {
          id: 1,
          nom: "Dupont",
          prenom: "Jean"
        }
      },
      clientId: 1,
      client: {
        id: 1,
        nom: "Martin",
        prenom: "Pierre"
      },
      adminId: 1,
      admin: {
        id: 1,
        nom: "Admin",
        prenom: "System"
      },
      dateGeneration: new Date("2024-01-15T14:30:00Z"),
      scoreQuiz: 85,
      seuilReussite: 70,
      numeroSerie: "CERT-2024-001"
    }, {
      id: 2,
      nomClient: "Pierre Martin",
      dateObtention: new Date("2024-01-10T16:45:00Z"),
      coursId: 2,
      cours: {
        id: 2,
        titre: "JavaScript Avancé",
        formateur: {
          id: 2,
          nom: "Bernard",
          prenom: "Sophie"
        }
      },
      clientId: 1,
      client: {
        id: 1,
        nom: "Martin",
        prenom: "Pierre"
      },
      adminId: 1,
      admin: {
        id: 1,
        nom: "Admin",
        prenom: "System"
      },
      dateGeneration: new Date("2024-01-10T16:45:00Z"),
      scoreQuiz: 92,
      seuilReussite: 75,
      numeroSerie: "CERT-2024-002"
    }];
    // Uncomment to fetch from API if you have an endpoint for listing certificates
    /*
    // Example: this.certificateService.getCertificatesForUser(this.currentUser.id).subscribe(...)
    */
  }

  getPageTitle() {
    switch (this.currentUser?.role) {
      case "Client":
        return "Mes Certificats";
      case "Formateur":
        return "Certificats Émis";
      case "Admin":
        return "Gestion des Certificats";
      default:
        return "Certificats";
    }
  }
  getPageSubtitle() {
    switch (this.currentUser?.role) {
      case "Client":
        return "Vos certificats de réussite aux cours";
      case "Formateur":
        return "Certificats émis pour vos cours";
      case "Admin":
        return "Tous les certificats générés sur la plateforme";
      default:
        return "Consultez les certificats de la plateforme";
    }
  }
  getNoCertificatesMessage() {
    switch (this.currentUser?.role) {
      case "Client":
        return "Terminez un cours avec succès pour obtenir votre premier certificat.";
      case "Formateur":
        return "Aucun certificat n'a encore été généré pour vos cours.";
      case "Admin":
        return "Aucun certificat n'a encore été généré sur la plateforme.";
      default:
        return "Aucun certificat n'est disponible pour le moment.";
    }
  }
  formatDate(date) {
    return new Date(date).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "2-digit"
    });
  }
  getScoreColorClass(score, seuil) {
    if (score >= seuil + 20) return "score-green";
    if (score >= seuil + 10) return "score-blue";
    if (score >= seuil) return "score-yellow";
    return "score-red";
  }
  downloadCertificate(cert) {
    if (!cert.cours || !cert.client) {
      this.snackBar.open("Informations du certificat incomplètes.", "Fermer", {
        duration: 3000
      });
      return;
    }
    this.certificateService.telechargerCertificat(cert.client.nom, cert.client.prenom, cert.cours.titre, cert.scoreQuiz || 0).subscribe({
      next: blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `certificat-${cert.numeroSerie}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        this.snackBar.open("Certificat téléchargé !", "Fermer", {
          duration: 3000
        });
      },
      error: err => {
        this.snackBar.open("Erreur lors du téléchargement du certificat.", "Fermer", {
          duration: 3000
        });
        console.error(err);
      }
    });
  }
  previewCertificate(certificateId) {
    this.snackBar.open("Fonctionnalité d'aperçu non implémentée pour le moment.", "Fermer", {
      duration: 3000
    });
    // You would typically open a new tab/window with a route that renders the PDF
    // Example: window.open(`/certificates/${certificateId}/preview`, '_blank');
  }

  averageScore() {
    if (this.certificates.length === 0) return 0;
    const totalScore = this.certificates.reduce((sum, cert) => sum + (cert.scoreQuiz || 0), 0);
    return Math.round(totalScore / this.certificates.length);
  }
  excellentResultsCount() {
    return this.certificates.filter(cert => (cert.scoreQuiz || 0) >= (cert.seuilReussite || 0) + 20).length;
  }
  uniqueStudentsCount() {
    const uniqueClientIds = new Set(this.certificates.map(cert => cert.clientId));
    return uniqueClientIds.size;
  }
  static {
    this.ɵfac = function CertificatesComponent_Factory(t) {
      return new (t || CertificatesComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_certificate_service__WEBPACK_IMPORTED_MODULE_0__.CertificateService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_3__.MatSnackBar));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: CertificatesComponent,
      selectors: [["app-certificates"]],
      decls: 9,
      vars: 4,
      consts: [[1, "certificates-container"], [1, "header-section"], ["class", "no-certificates-card", 4, "ngIf", "ngIfElse"], ["certificatesList", ""], [1, "no-certificates-card"], [1, "no-certificates-content"], [1, "certificates-grid"], ["class", "certificate-card", 4, "ngFor", "ngForOf"], ["class", "stats-card", 4, "ngIf"], [1, "certificate-card"], [1, "certificate-header"], [1, "icon-wrapper"], [1, "certificate-details"], [1, "detail-item"], [3, "ngClass"], [1, "serial-number-chip"], [1, "card-actions"], ["mat-stroked-button", "", "color", "primary", 3, "click"], ["mat-raised-button", "", "color", "accent", 3, "click"], [1, "validation-badge"], [1, "validated-chip"], [1, "stats-card"], [1, "stats-grid"], [1, "stat-item"], [1, "stat-value", "blue"], [1, "stat-label"], [1, "stat-value", "green"], [1, "stat-value", "yellow"], [1, "stat-value", "purple"]],
      template: function CertificatesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](6, CertificatesComponent_div_6_Template, 9, 1, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](7, CertificatesComponent_ng_template_7_Template, 3, 2, "ng-template", null, 3, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.getPageTitle());
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.getPageSubtitle());
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.certificates.length === 0)("ngIfElse", _r1);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_material_card__WEBPACK_IMPORTED_MODULE_5__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_5__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_5__.MatCardHeader, _angular_material_card__WEBPACK_IMPORTED_MODULE_5__.MatCardSubtitle, _angular_material_card__WEBPACK_IMPORTED_MODULE_5__.MatCardTitle, _angular_material_button__WEBPACK_IMPORTED_MODULE_6__.MatButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__.MatIcon, _angular_material_chips__WEBPACK_IMPORTED_MODULE_8__.MatChip, _angular_material_chips__WEBPACK_IMPORTED_MODULE_8__.MatChipListbox],
      styles: [".certificates-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header-section[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n\n.header-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 0.5rem;\n}\n\n.header-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  color: #666;\n}\n\n.no-certificates-card[_ngcontent-%COMP%] {\n  max-width: 600px;\n  margin: 0 auto;\n  text-align: center;\n  padding: 2rem;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n}\n\n.no-certificates-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #aaa;\n}\n\n.no-certificates-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n}\n\n.no-certificates-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #777;\n}\n\n.certificates-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.certificate-card[_ngcontent-%COMP%] {\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\n}\n\n.certificate-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n}\n\n.certificate-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding-bottom: 0.5rem;\n}\n\n.icon-wrapper[_ngcontent-%COMP%] {\n  width: 56px;\n  height: 56px;\n  border-radius: 50%;\n  background-color: #fff3e0; \n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 1rem;\n  flex-shrink: 0;\n}\n\n.icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n  color: #ffb300; \n\n}\n\n.certificate-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 0.2rem;\n}\n\n.certificate-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: #666;\n}\n\n.certificate-details[_ngcontent-%COMP%] {\n  padding-top: 1rem;\n  border-top: 1px solid #eee;\n}\n\n.detail-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.8rem;\n  font-size: 0.95rem;\n  color: #555;\n}\n\n.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  width: 1.2rem;\n  height: 1.2rem;\n  margin-right: 0.8rem;\n  color: #999;\n}\n\n.serial-number-chip[_ngcontent-%COMP%] {\n  font-family: \"monospace\";\n  font-size: 0.8rem;\n  padding: 0.2rem 0.6rem;\n  height: auto;\n  background-color: #f0f0f0;\n  color: #555;\n}\n\n.score-green[_ngcontent-%COMP%] {\n  color: #4caf50;\n}\n\n.score-blue[_ngcontent-%COMP%] {\n  color: #2196f3;\n}\n\n.score-yellow[_ngcontent-%COMP%] {\n  color: #ffc107;\n}\n\n.score-red[_ngcontent-%COMP%] {\n  color: #f44336;\n}\n\n.card-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.8rem;\n  margin-top: 1.5rem;\n  padding-top: 1rem;\n  border-top: 1px solid #eee;\n}\n\n.card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.9rem;\n  padding: 0.6rem 1rem;\n}\n\n.card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  width: 1rem;\n  height: 1rem;\n  margin-right: 0.3rem;\n}\n\n.validation-badge[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 1.5rem;\n}\n\n.validated-chip[_ngcontent-%COMP%] {\n  background-color: #e8f5e9; \n\n  color: #388e3c; \n\n  font-size: 0.9rem;\n  padding: 0.4rem 0.8rem;\n  height: auto;\n}\n\n.validated-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  width: 1rem;\n  height: 1rem;\n  margin-right: 0.3rem;\n}\n\n.stats-card[_ngcontent-%COMP%] {\n  margin-top: 2rem;\n  padding: 1.5rem;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n}\n\n.stats-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.4rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n}\n\n.stats-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 1.5rem;\n  text-align: center;\n}\n\n.stat-item[_ngcontent-%COMP%] {\n  padding: 1rem;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  border: 1px solid #eee;\n}\n\n.stat-value[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  font-weight: bold;\n  margin-bottom: 0.3rem;\n}\n\n.stat-value.blue[_ngcontent-%COMP%] {\n  color: #2196f3;\n}\n\n.stat-value.green[_ngcontent-%COMP%] {\n  color: #4caf50;\n}\n\n.stat-value.yellow[_ngcontent-%COMP%] {\n  color: #ffc107;\n}\n\n.stat-value.purple[_ngcontent-%COMP%] {\n  color: #9c27b0;\n}\n\n.stat-label[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: #666;\n}\n\n@media (max-width: 768px) {\n  .certificates-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .stats-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 6547:
/*!**************************************************************!*\
  !*** ./src/app/features/certificates/certificates.module.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CertificatesModule: () => (/* binding */ CertificatesModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/chips */ 2772);
/* harmony import */ var _certificates_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./certificates.component */ 5188);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


// Angular Material







class CertificatesModule {
  static {
    this.ɵfac = function CertificatesModule_Factory(t) {
      return new (t || CertificatesModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: CertificatesModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_3__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_4__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_5__.MatIconModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_6__.MatChipsModule, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule.forChild([{
        path: "",
        component: _certificates_component__WEBPACK_IMPORTED_MODULE_0__.CertificatesComponent
      }])]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](CertificatesModule, {
    declarations: [_certificates_component__WEBPACK_IMPORTED_MODULE_0__.CertificatesComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_3__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_4__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_5__.MatIconModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_6__.MatChipsModule, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_features_certificates_certificates_module_ts.js.map