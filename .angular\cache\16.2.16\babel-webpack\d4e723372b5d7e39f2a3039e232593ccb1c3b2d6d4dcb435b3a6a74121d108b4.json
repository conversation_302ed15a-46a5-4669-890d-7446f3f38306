{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\nexport function distinct(keySelector, flushes) {\n  return operate((source, subscriber) => {\n    const distinctKeys = new Set();\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const key = keySelector ? keySelector(value) : value;\n      if (!distinctKeys.has(key)) {\n        distinctKeys.add(key);\n        subscriber.next(value);\n      }\n    }));\n    flushes && innerFrom(flushes).subscribe(createOperatorSubscriber(subscriber, () => distinctKeys.clear(), noop));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "noop", "innerFrom", "distinct", "keySelector", "flushes", "source", "subscriber", "distinctKeys", "Set", "subscribe", "value", "key", "has", "add", "next", "clear"], "sources": ["C:/e-learning/node_modules/rxjs/dist/esm/internal/operators/distinct.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\nexport function distinct(keySelector, flushes) {\n    return operate((source, subscriber) => {\n        const distinctKeys = new Set();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const key = keySelector ? keySelector(value) : value;\n            if (!distinctKeys.has(key)) {\n                distinctKeys.add(key);\n                subscriber.next(value);\n            }\n        }));\n        flushes && innerFrom(flushes).subscribe(createOperatorSubscriber(subscriber, () => distinctKeys.clear(), noop));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC3C,OAAON,OAAO,CAAC,CAACO,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9BH,MAAM,CAACI,SAAS,CAACV,wBAAwB,CAACO,UAAU,EAAGI,KAAK,IAAK;MAC7D,MAAMC,GAAG,GAAGR,WAAW,GAAGA,WAAW,CAACO,KAAK,CAAC,GAAGA,KAAK;MACpD,IAAI,CAACH,YAAY,CAACK,GAAG,CAACD,GAAG,CAAC,EAAE;QACxBJ,YAAY,CAACM,GAAG,CAACF,GAAG,CAAC;QACrBL,UAAU,CAACQ,IAAI,CAACJ,KAAK,CAAC;MAC1B;IACJ,CAAC,CAAC,CAAC;IACHN,OAAO,IAAIH,SAAS,CAACG,OAAO,CAAC,CAACK,SAAS,CAACV,wBAAwB,CAACO,UAAU,EAAE,MAAMC,YAAY,CAACQ,KAAK,CAAC,CAAC,EAAEf,IAAI,CAAC,CAAC;EACnH,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}