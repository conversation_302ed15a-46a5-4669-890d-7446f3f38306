import { Component } from "@angular/core"

@Component({
  selector: "app-home",
  template: `
    <div class="home-container">
      <div class="container">
        <!-- Header -->
        <div class="header">
          <div class="logo">
            <mat-icon class="logo-icon">school</mat-icon>
            <h1>Training Platform</h1>
          </div>
          <p class="subtitle">Plateforme de formation en ligne connectant formateurs et apprenants</p>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <button mat-raised-button color="accent" routerLink="/auth/login">
              🔑 Se connecter
            </button>
            <button mat-raised-button color="primary" routerLink="/auth/register">
              📝 S'inscrire
            </button>
          </div>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
          <mat-card class="feature-card">
            <mat-card-content>
              <mat-icon class="feature-icon green">book</mat-icon>
              <h3>Cours en ligne</h3>
              <p><PERSON><PERSON><PERSON> et suivez des cours interactifs</p>
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-content>
              <mat-icon class="feature-icon yellow">emoji_events</mat-icon>
              <h3>Certificats</h3>
              <p>Obtenez des certificats personnalisés</p>
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-content>
              <mat-icon class="feature-icon orange">payment</mat-icon>
              <h3>Paiements sécurisés</h3>
              <p>Système de paiement intégré</p>
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-content>
              <mat-icon class="feature-icon blue">message</mat-icon>
              <h3>Messagerie</h3>
              <p>Communication directe</p>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
    .home-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 2rem;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .header {
      text-align: center;
      margin-bottom: 4rem;
      color: white;
    }

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
    }

    .logo-icon {
      font-size: 3rem;
      margin-right: 1rem;
    }

    .logo h1 {
      font-size: 3rem;
      margin: 0;
      font-weight: bold;
    }

    .subtitle {
      font-size: 1.25rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 4rem;
    }

    .action-buttons button {
      padding: 0.75rem 2rem;
      font-size: 1.1rem;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 2rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .feature-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      text-align: center;
      padding: 2rem;
    }

    .feature-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .feature-icon.green { color: #4caf50; }
    .feature-icon.yellow { color: #ffc107; }
    .feature-icon.orange { color: #ff9800; }
    .feature-icon.blue { color: #2196f3; }

    .feature-card h3 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .feature-card p {
      opacity: 0.8;
    }
  `,
  ],
})
export class HomeComponent {}
