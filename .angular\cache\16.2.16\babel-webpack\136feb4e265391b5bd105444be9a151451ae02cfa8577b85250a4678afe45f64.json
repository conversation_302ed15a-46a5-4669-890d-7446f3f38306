{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nimport { BehaviorSubject, tap } from \"rxjs\";\nimport { environment } from \"../../../environments/environment\";\nexport let AuthService = class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    const token = this.getToken();\n    if (token) {\n      // TODO: Validate token and get user info if needed\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${environment.urlApi}auth/login`, credentials).pipe(tap(response => {\n      localStorage.setItem(\"token\", response.token);\n      localStorage.setItem(\"userRole\", response.role);\n      // You might want to get user details after login\n    }));\n  }\n\n  register(userData) {\n    // Note: Your backend doesn't have register endpoint, you might need to add it\n    return this.http.post(`${environment.urlApi}auth/register`, userData);\n  }\n  getCurrentUser() {\n    return this.http.get(`${environment.urlApi}auth/me`).pipe(tap(user => this.currentUserSubject.next(user)));\n  }\n  logout() {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"userRole\");\n    this.currentUserSubject.next(null);\n    this.router.navigate([\"/\"]);\n  }\n  getToken() {\n    return localStorage.getItem(\"token\");\n  }\n  getUserRole() {\n    return localStorage.getItem(\"userRole\");\n  }\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  getCurrentUserValue() {\n    return this.currentUserSubject.value;\n  }\n};\nAuthService = __decorate([Injectable({\n  providedIn: \"root\"\n})], AuthService);", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "tap", "environment", "AuthService", "constructor", "http", "router", "currentUserSubject", "currentUser$", "asObservable", "token", "getToken", "login", "credentials", "post", "urlApi", "pipe", "response", "localStorage", "setItem", "role", "register", "userData", "getCurrentUser", "get", "user", "next", "logout", "removeItem", "navigate", "getItem", "getUserRole", "isAuthenticated", "getCurrentUserValue", "value", "__decorate", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport type { HttpClient } from \"@angular/common/http\"\nimport { BehaviorSubject, type Observable, tap } from \"rxjs\"\nimport type { Router } from \"@angular/router\"\nimport { environment } from \"../../../environments/environment\"\nimport type { User, LoginRequest, RegisterRequest, AuthResponse } from \"../models/user.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AuthService {\n  private currentUserSubject = new BehaviorSubject<User | null>(null)\n  public currentUser$ = this.currentUserSubject.asObservable()\n\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n  ) {\n    const token = this.getToken()\n    if (token) {\n      // TODO: Validate token and get user info if needed\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${environment.urlApi}auth/login`, credentials).pipe(\n      tap((response) => {\n        localStorage.setItem(\"token\", response.token)\n        localStorage.setItem(\"userRole\", response.role)\n        // You might want to get user details after login\n      }),\n    )\n  }\n\n  register(userData: RegisterRequest): Observable<any> {\n    // Note: Your backend doesn't have register endpoint, you might need to add it\n    return this.http.post(`${environment.urlApi}auth/register`, userData)\n  }\n\n  getCurrentUser(): Observable<User> {\n    return this.http.get<User>(`${environment.urlApi}auth/me`).pipe(tap((user) => this.currentUserSubject.next(user)))\n  }\n\n  logout(): void {\n    localStorage.removeItem(\"token\")\n    localStorage.removeItem(\"userRole\")\n    this.currentUserSubject.next(null)\n    this.router.navigate([\"/\"])\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem(\"token\")\n  }\n\n  getUserRole(): string | null {\n    return localStorage.getItem(\"userRole\")\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getToken()\n  }\n\n  getCurrentUserValue(): User | null {\n    return this.currentUserSubject.value\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,eAAe,EAAmBC,GAAG,QAAQ,MAAM;AAE5D,SAASC,WAAW,QAAQ,mCAAmC;AAMxD,WAAMC,WAAW,GAAjB,MAAMA,WAAW;EAItBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IALR,KAAAC,kBAAkB,GAAG,IAAIP,eAAe,CAAc,IAAI,CAAC;IAC5D,KAAAQ,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAM1D,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAID,KAAK,EAAE;MACT;IAAA;EAEJ;EAEAE,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAe,GAAGZ,WAAW,CAACa,MAAM,YAAY,EAAEF,WAAW,CAAC,CAACG,IAAI,CACtFf,GAAG,CAAEgB,QAAQ,IAAI;MACfC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAACP,KAAK,CAAC;MAC7CQ,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAACG,IAAI,CAAC;MAC/C;IACF,CAAC,CAAC,CACH;EACH;;EAEAC,QAAQA,CAACC,QAAyB;IAChC;IACA,OAAO,IAAI,CAACjB,IAAI,CAACS,IAAI,CAAC,GAAGZ,WAAW,CAACa,MAAM,eAAe,EAAEO,QAAQ,CAAC;EACvE;EAEAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClB,IAAI,CAACmB,GAAG,CAAO,GAAGtB,WAAW,CAACa,MAAM,SAAS,CAAC,CAACC,IAAI,CAACf,GAAG,CAAEwB,IAAI,IAAK,IAAI,CAAClB,kBAAkB,CAACmB,IAAI,CAACD,IAAI,CAAC,CAAC,CAAC;EACpH;EAEAE,MAAMA,CAAA;IACJT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChCV,YAAY,CAACU,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAACrB,kBAAkB,CAACmB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACpB,MAAM,CAACuB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEAlB,QAAQA,CAAA;IACN,OAAOO,YAAY,CAACY,OAAO,CAAC,OAAO,CAAC;EACtC;EAEAC,WAAWA,CAAA;IACT,OAAOb,YAAY,CAACY,OAAO,CAAC,UAAU,CAAC;EACzC;EAEAE,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACrB,QAAQ,EAAE;EAC1B;EAEAsB,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC1B,kBAAkB,CAAC2B,KAAK;EACtC;CACD;AAvDY/B,WAAW,GAAAgC,UAAA,EAHvBpC,UAAU,CAAC;EACVqC,UAAU,EAAE;CACb,CAAC,C,EACWjC,WAAW,CAuDvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}