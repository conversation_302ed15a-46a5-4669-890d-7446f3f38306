{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { HomeComponent } from \"./home.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class HomeModule {\n  static {\n    this.ɵfac = function HomeModule_Factory(t) {\n      return new (t || HomeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, MatButtonModule, MatCardModule, MatIconModule, RouterModule.forChild([{\n        path: \"\",\n        component: HomeComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeModule, {\n    declarations: [HomeComponent],\n    imports: [CommonModule, MatButtonModule, MatCardModule, MatIconModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatButtonModule", "MatCardModule", "MatIconModule", "HomeComponent", "HomeModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatIconModule } from \"@angular/material/icon\"\n\nimport { HomeComponent } from \"./home.component\"\n\n@NgModule({\n  declarations: [HomeComponent],\n  imports: [\n    CommonModule,\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    RouterModule.forChild([{ path: \"\", component: HomeComponent }]),\n  ],\n})\nexport class HomeModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,aAAa,QAAQ,kBAAkB;;;AAYhD,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAPnBN,YAAY,EACZE,eAAe,EACfC,aAAa,EACbC,aAAa,EACbH,YAAY,CAACM,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAa,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAGtDC,UAAU;IAAAI,YAAA,GATNL,aAAa;IAAAM,OAAA,GAE1BX,YAAY,EACZE,eAAe,EACfC,aAAa,EACbC,aAAa,EAAAQ,EAAA,CAAAX,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}