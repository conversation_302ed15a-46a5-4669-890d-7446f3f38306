import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Message } from "../models/message.model"

@Injectable({
  providedIn: "root",
})
export class MessageService {
  private apiUrl = `${environment.urlApi}messages`

  constructor(private http: HttpClient) {}

  // POST: Envoyer un message (correspond à POST /api/messages)
  envoyerMessage(message: Message): Observable<string> {
    return this.http.post<string>(this.apiUrl, message)
  }

  // GET: Messages entre deux utilisateurs (correspond à GET /api/messages/entre/{id1}/{id2})
  getMessages(id1: number, id2: number): Observable<Message[]> {
    return this.http.get<Message[]>(`${this.apiUrl}/entre/${id1}/${id2}`)
  }
}
