import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Message } from "../models/message.model"

@Injectable({
  providedIn: "root",
})
export class MessageService {
  constructor(private http: HttpClient) {}

  // POST: Envoyer un message
  envoyerMessage(message: Message): Observable<any> {
    return this.http.post(`${environment.urlApi}messages`, message)
  }

  // GET: Messages entre deux utilisateurs
  getMessages(id1: number, id2: number): Observable<Message[]> {
    return this.http.get<Message[]>(`${environment.urlApi}messages/entre/${id1}/${id2}`)
  }
}
