{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/button\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/router\";\nexport class HomeComponent {\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 48,\n      vars: 0,\n      consts: [[1, \"home-container\"], [1, \"container\"], [1, \"header\"], [1, \"logo\"], [1, \"logo-icon\"], [1, \"subtitle\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/auth/login\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/auth/register\"], [1, \"features-grid\"], [1, \"feature-card\"], [1, \"feature-icon\", \"green\"], [1, \"feature-icon\", \"yellow\"], [1, \"feature-icon\", \"orange\"], [1, \"feature-icon\", \"blue\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"school\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"Training Platform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\", 5);\n          i0.ɵɵtext(9, \"Plateforme de formation en ligne connectant formateurs et apprenants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"button\", 7);\n          i0.ɵɵtext(12, \" \\uD83D\\uDD11 Se connecter \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 8);\n          i0.ɵɵtext(14, \" \\uD83D\\uDCDD S'inscrire \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"mat-card\", 10)(17, \"mat-card-content\")(18, \"mat-icon\", 11);\n          i0.ɵɵtext(19, \"book\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"h3\");\n          i0.ɵɵtext(21, \"Cours en ligne\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"Cr\\u00E9ez et suivez des cours interactifs\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"mat-card\", 10)(25, \"mat-card-content\")(26, \"mat-icon\", 12);\n          i0.ɵɵtext(27, \"emoji_events\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"h3\");\n          i0.ɵɵtext(29, \"Certificats\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \"Obtenez des certificats personnalis\\u00E9s\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"mat-card\", 10)(33, \"mat-card-content\")(34, \"mat-icon\", 13);\n          i0.ɵɵtext(35, \"payment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"h3\");\n          i0.ɵɵtext(37, \"Paiements s\\u00E9curis\\u00E9s\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\");\n          i0.ɵɵtext(39, \"Syst\\u00E8me de paiement int\\u00E9gr\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"mat-card\", 10)(41, \"mat-card-content\")(42, \"mat-icon\", 14);\n          i0.ɵɵtext(43, \"message\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"h3\");\n          i0.ɵɵtext(45, \"Messagerie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\");\n          i0.ɵɵtext(47, \"Communication directe\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n      },\n      dependencies: [i1.MatButton, i2.MatCard, i2.MatCardContent, i3.MatIcon, i4.RouterLink],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 2rem;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 4rem;\\n  color: white;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 1rem;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-right: 1rem;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin: 0;\\n  font-weight: bold;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  margin-bottom: 2rem;\\n  opacity: 0.9;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-bottom: 4rem;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.75rem 2rem;\\n  font-size: 1.1rem;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 2rem;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  text-align: center;\\n  padding: 2rem;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.feature-icon.green[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.feature-icon.yellow[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n.feature-icon.orange[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n.feature-icon.blue[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-bottom: 1rem;\\n  font-weight: 600;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HomeComponent", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\e-learning\\src\\app\\features\\home\\home.component.ts"], "sourcesContent": ["import { Component } from \"@angular/core\"\n\n@Component({\n  selector: \"app-home\",\n  template: `\n    <div class=\"home-container\">\n      <div class=\"container\">\n        <!-- Header -->\n        <div class=\"header\">\n          <div class=\"logo\">\n            <mat-icon class=\"logo-icon\">school</mat-icon>\n            <h1>Training Platform</h1>\n          </div>\n          <p class=\"subtitle\">Plateforme de formation en ligne connectant formateurs et apprenants</p>\n\n          <!-- Action Buttons -->\n          <div class=\"action-buttons\">\n            <button mat-raised-button color=\"accent\" routerLink=\"/auth/login\">\n              🔑 Se connecter\n            </button>\n            <button mat-raised-button color=\"primary\" routerLink=\"/auth/register\">\n              📝 S'inscrire\n            </button>\n          </div>\n        </div>\n\n        <!-- Features Grid -->\n        <div class=\"features-grid\">\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon green\">book</mat-icon>\n              <h3>Cours en ligne</h3>\n              <p><PERSON><PERSON><PERSON> et suivez des cours interactifs</p>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon yellow\">emoji_events</mat-icon>\n              <h3>Certificats</h3>\n              <p>Obtenez des certificats personnalisés</p>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon orange\">payment</mat-icon>\n              <h3>Paiements sécurisés</h3>\n              <p>Système de paiement intégré</p>\n            </mat-card-content>\n          </mat-card>\n\n          <mat-card class=\"feature-card\">\n            <mat-card-content>\n              <mat-icon class=\"feature-icon blue\">message</mat-icon>\n              <h3>Messagerie</h3>\n              <p>Communication directe</p>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .home-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .header {\n      text-align: center;\n      margin-bottom: 4rem;\n      color: white;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 1rem;\n    }\n\n    .logo-icon {\n      font-size: 3rem;\n      margin-right: 1rem;\n    }\n\n    .logo h1 {\n      font-size: 3rem;\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .subtitle {\n      font-size: 1.25rem;\n      margin-bottom: 2rem;\n      opacity: 0.9;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 4rem;\n    }\n\n    .action-buttons button {\n      padding: 0.75rem 2rem;\n      font-size: 1.1rem;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n      gap: 2rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .feature-card {\n      background: rgba(255, 255, 255, 0.1);\n      backdrop-filter: blur(10px);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      color: white;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .feature-icon {\n      font-size: 4rem;\n      margin-bottom: 1rem;\n    }\n\n    .feature-icon.green { color: #4caf50; }\n    .feature-icon.yellow { color: #ffc107; }\n    .feature-icon.orange { color: #ff9800; }\n    .feature-icon.blue { color: #2196f3; }\n\n    .feature-card h3 {\n      font-size: 1.5rem;\n      margin-bottom: 1rem;\n      font-weight: 600;\n    }\n\n    .feature-card p {\n      opacity: 0.8;\n    }\n  `,\n  ],\n})\nexport class HomeComponent {}\n"], "mappings": ";;;;;AA6JA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxJtBE,EAAA,CAAAC,cAAA,aAA4B;UAKQD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,2EAAoE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG5FH,EAAA,CAAAC,cAAA,cAA4B;UAExBD,EAAA,CAAAE,MAAA,mCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAAsE;UACpED,EAAA,CAAAE,MAAA,iCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,cAA2B;UAGgBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpDH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kDAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIhDH,EAAA,CAAAC,cAAA,oBAA+B;UAEWD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7DH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kDAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIhDH,EAAA,CAAAC,cAAA,oBAA+B;UAEWD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,qCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kDAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAItCH,EAAA,CAAAC,cAAA,oBAA+B;UAESD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}