import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { Video } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class VideoService {
  constructor(private http: HttpClient) {}

  // GET: Toutes les vidéos
  getVideos(): Observable<Video[]> {
    return this.http.get<Video[]>(`${environment.urlApi}video`)
  }

  // GET: Une vidéo par ID
  getVideo(id: number): Observable<Video> {
    return this.http.get<Video>(`${environment.urlApi}video/${id}`)
  }

  // POST: Créer une vidéo
  createVideo(video: Video): Observable<Video> {
    return this.http.post<Video>(`${environment.urlApi}video`, video)
  }

  // PUT: Modifier une vidéo
  updateVideo(id: number, video: Video): Observable<any> {
    return this.http.put(`${environment.urlApi}video/${id}`, video)
  }

  // DELETE: Supprimer une vidéo
  deleteVideo(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}video/${id}`)
  }
}
