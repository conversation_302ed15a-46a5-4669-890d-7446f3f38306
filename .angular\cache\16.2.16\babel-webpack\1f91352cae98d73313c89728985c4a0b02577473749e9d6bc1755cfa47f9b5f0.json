{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { CertificatesComponent } from \"./certificates.component\";\nexport let CertificatesModule = class CertificatesModule {};\nCertificatesModule = __decorate([NgModule({\n  declarations: [CertificatesComponent],\n  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, RouterModule.forChild([{\n    path: \"\",\n    component: CertificatesComponent\n  }])]\n})], CertificatesModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "CertificatesComponent", "CertificatesModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component"], "sources": ["C:\\e-learning\\src\\app\\features\\certificates\\certificates.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatChipsModule } from \"@angular/material/chips\"\n\nimport { CertificatesComponent } from \"./certificates.component\"\n\n@NgModule({\n  declarations: [CertificatesComponent],\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    RouterModule.forChild([{ path: \"\", component: CertificatesComponent }]),\n  ],\n})\nexport class CertificatesModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,qBAAqB,QAAQ,0BAA0B;AAazD,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB,GAAG;AAArBA,kBAAkB,GAAAC,UAAA,EAX9BT,QAAQ,CAAC;EACRU,YAAY,EAAE,CAACH,qBAAqB,CAAC;EACrCI,OAAO,EAAE,CACPV,YAAY,EACZE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdJ,YAAY,CAACU,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEP;EAAqB,CAAE,CAAC,CAAC;CAE1E,CAAC,C,EACWC,kBAAkB,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}