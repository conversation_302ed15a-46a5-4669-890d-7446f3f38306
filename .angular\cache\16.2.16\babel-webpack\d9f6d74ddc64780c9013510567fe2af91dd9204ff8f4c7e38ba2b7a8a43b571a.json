{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/certificate.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/chips\";\nfunction CertificatesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"mat-card\")(2, \"mat-card-content\", 5)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Aucun certificat disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getNoCertificatesMessage());\n  }\n}\nfunction CertificatesComponent_ng_template_7_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 9)(1, \"mat-card-header\", 10)(2, \"div\", 11)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"emoji_events\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\")(6, \"mat-card-title\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"mat-card-content\", 12)(11, \"div\", 13)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u00C9tudiant: \");\n    i0.ɵɵelementStart(16, \"strong\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 13)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 13)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"book\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \"Score: \");\n    i0.ɵɵelementStart(28, \"strong\", 14);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 13)(32, \"span\");\n    i0.ɵɵtext(33, \"N\\u00B0 de s\\u00E9rie: \");\n    i0.ɵɵelementStart(34, \"mat-chip-listbox\")(35, \"mat-chip\", 15);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 16)(38, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_38_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const cert_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.previewCertificate(cert_r5.id));\n    });\n    i0.ɵɵelementStart(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" Aper\\u00E7u \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_42_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const cert_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.downloadCertificate(cert_r5));\n    });\n    i0.ɵɵelementStart(43, \"mat-icon\");\n    i0.ɵɵtext(44, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(45, \" PDF \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 19)(47, \"mat-chip-listbox\")(48, \"mat-chip\", 20)(49, \"mat-icon\");\n    i0.ɵɵtext(50, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \" Certificat Valid\\u00E9 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const cert_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(cert_r5.cours.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Par \", cert_r5.cours.formateur.prenom, \" \", cert_r5.cours.formateur.nom, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\"\", cert_r5.client.prenom, \" \", cert_r5.client.nom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Date d'obtention: \", ctx_r3.formatDate(cert_r5.dateGeneration), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getScoreColorClass(cert_r5.scoreQuiz, cert_r5.seuilReussite));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", cert_r5.scoreQuiz, \"% \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (seuil: \", cert_r5.seuilReussite, \"%) \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(cert_r5.numeroSerie);\n  }\n}\nfunction CertificatesComponent_ng_template_7_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 21)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Statistiques des Certificats\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\", 22)(5, \"div\", 23)(6, \"div\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 25);\n    i0.ɵɵtext(9, \"Total \\u00E9mis\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 23)(11, \"div\", 26);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 25);\n    i0.ɵɵtext(14, \"Score moyen\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 23)(16, \"div\", 27);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 25);\n    i0.ɵɵtext(19, \"Excellents r\\u00E9sultats\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 23)(21, \"div\", 28);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 25);\n    i0.ɵɵtext(24, \"\\u00C9tudiants certifi\\u00E9s\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.certificates.length);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r4.averageScore(), \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.excellentResultsCount());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.uniqueStudentsCount());\n  }\n}\nfunction CertificatesComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, CertificatesComponent_ng_template_7_mat_card_1_Template, 52, 10, \"mat-card\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, CertificatesComponent_ng_template_7_mat_card_2_Template, 25, 4, \"mat-card\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.certificates);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.role) === \"Formateur\" || (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.role) === \"Admin\");\n  }\n}\nexport class CertificatesComponent {\n  constructor(certificateService, authService, snackBar) {\n    this.certificateService = certificateService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.certificates = [];\n    this.currentUser = null;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (this.currentUser) {\n        this.loadCertificates();\n      }\n    });\n  }\n  loadCertificates() {\n    // Mock data for demonstration\n    this.certificates = [{\n      id: 1,\n      nomClient: \"Pierre Martin\",\n      dateObtention: new Date(\"2024-01-15T14:30:00Z\"),\n      coursId: 1,\n      cours: {\n        id: 1,\n        titre: \"React Fundamentals\",\n        formateur: {\n          id: 1,\n          nom: \"Dupont\",\n          prenom: \"Jean\"\n        }\n      },\n      clientId: 1,\n      client: {\n        id: 1,\n        nom: \"Martin\",\n        prenom: \"Pierre\"\n      },\n      adminId: 1,\n      admin: {\n        id: 1,\n        nom: \"Admin\",\n        prenom: \"System\"\n      },\n      dateGeneration: new Date(\"2024-01-15T14:30:00Z\"),\n      scoreQuiz: 85,\n      seuilReussite: 70,\n      numeroSerie: \"CERT-2024-001\"\n    }, {\n      id: 2,\n      coursId: 2,\n      cours: {\n        id: 2,\n        titre: \"JavaScript Avancé\",\n        formateur: {\n          id: 2,\n          nom: \"Bernard\",\n          prenom: \"Sophie\"\n        }\n      },\n      clientId: 1,\n      client: {\n        id: 1,\n        nom: \"Martin\",\n        prenom: \"Pierre\"\n      },\n      adminId: 1,\n      admin: {\n        id: 1,\n        nom: \"Admin\",\n        prenom: \"System\"\n      },\n      dateGeneration: new Date(\"2024-01-10T16:45:00Z\"),\n      scoreQuiz: 92,\n      seuilReussite: 75,\n      numeroSerie: \"CERT-2024-002\"\n    }];\n    // Uncomment to fetch from API if you have an endpoint for listing certificates\n    /*\n    // Example: this.certificateService.getCertificatesForUser(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  getPageTitle() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Mes Certificats\";\n      case \"Formateur\":\n        return \"Certificats Émis\";\n      case \"Admin\":\n        return \"Gestion des Certificats\";\n      default:\n        return \"Certificats\";\n    }\n  }\n  getPageSubtitle() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Vos certificats de réussite aux cours\";\n      case \"Formateur\":\n        return \"Certificats émis pour vos cours\";\n      case \"Admin\":\n        return \"Tous les certificats générés sur la plateforme\";\n      default:\n        return \"Consultez les certificats de la plateforme\";\n    }\n  }\n  getNoCertificatesMessage() {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Terminez un cours avec succès pour obtenir votre premier certificat.\";\n      case \"Formateur\":\n        return \"Aucun certificat n'a encore été généré pour vos cours.\";\n      case \"Admin\":\n        return \"Aucun certificat n'a encore été généré sur la plateforme.\";\n      default:\n        return \"Aucun certificat n'est disponible pour le moment.\";\n    }\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString(\"fr-FR\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"2-digit\"\n    });\n  }\n  getScoreColorClass(score, seuil) {\n    if (score >= seuil + 20) return \"score-green\";\n    if (score >= seuil + 10) return \"score-blue\";\n    if (score >= seuil) return \"score-yellow\";\n    return \"score-red\";\n  }\n  downloadCertificate(cert) {\n    if (!cert.cours || !cert.client) {\n      this.snackBar.open(\"Informations du certificat incomplètes.\", \"Fermer\", {\n        duration: 3000\n      });\n      return;\n    }\n    this.certificateService.telechargerCertificat(cert.client.nom, cert.client.prenom, cert.cours.titre, cert.scoreQuiz).subscribe({\n      next: blob => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = `certificat-${cert.numeroSerie}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n        this.snackBar.open(\"Certificat téléchargé !\", \"Fermer\", {\n          duration: 3000\n        });\n      },\n      error: err => {\n        this.snackBar.open(\"Erreur lors du téléchargement du certificat.\", \"Fermer\", {\n          duration: 3000\n        });\n        console.error(err);\n      }\n    });\n  }\n  previewCertificate(certificateId) {\n    this.snackBar.open(\"Fonctionnalité d'aperçu non implémentée pour le moment.\", \"Fermer\", {\n      duration: 3000\n    });\n    // You would typically open a new tab/window with a route that renders the PDF\n    // Example: window.open(`/certificates/${certificateId}/preview`, '_blank');\n  }\n\n  averageScore() {\n    if (this.certificates.length === 0) return 0;\n    const totalScore = this.certificates.reduce((sum, cert) => sum + cert.scoreQuiz, 0);\n    return Math.round(totalScore / this.certificates.length);\n  }\n  excellentResultsCount() {\n    return this.certificates.filter(cert => cert.scoreQuiz >= cert.seuilReussite + 20).length;\n  }\n  uniqueStudentsCount() {\n    const uniqueClientIds = new Set(this.certificates.map(cert => cert.clientId));\n    return uniqueClientIds.size;\n  }\n  static {\n    this.ɵfac = function CertificatesComponent_Factory(t) {\n      return new (t || CertificatesComponent)(i0.ɵɵdirectiveInject(i1.CertificateService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CertificatesComponent,\n      selectors: [[\"app-certificates\"]],\n      decls: 9,\n      vars: 4,\n      consts: [[1, \"certificates-container\"], [1, \"header-section\"], [\"class\", \"no-certificates-card\", 4, \"ngIf\", \"ngIfElse\"], [\"certificatesList\", \"\"], [1, \"no-certificates-card\"], [1, \"no-certificates-content\"], [1, \"certificates-grid\"], [\"class\", \"certificate-card\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"stats-card\", 4, \"ngIf\"], [1, \"certificate-card\"], [1, \"certificate-header\"], [1, \"icon-wrapper\"], [1, \"certificate-details\"], [1, \"detail-item\"], [3, \"ngClass\"], [1, \"serial-number-chip\"], [1, \"card-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [1, \"validation-badge\"], [1, \"validated-chip\"], [1, \"stats-card\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-value\", \"blue\"], [1, \"stat-label\"], [1, \"stat-value\", \"green\"], [1, \"stat-value\", \"yellow\"], [1, \"stat-value\", \"purple\"]],\n      template: function CertificatesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, CertificatesComponent_div_6_Template, 9, 1, \"div\", 2);\n          i0.ɵɵtemplate(7, CertificatesComponent_ng_template_7_Template, 3, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(8);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.getPageTitle());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getPageSubtitle());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.certificates.length === 0)(\"ngIfElse\", _r1);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatButton, i7.MatIcon, i8.MatChip, i8.MatChipListbox],\n      styles: [\".certificates-container[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n\\n.header-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  text-align: center;\\n}\\n\\n.header-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: bold;\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.header-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n}\\n\\n.no-certificates-card[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n  text-align: center;\\n  padding: 2rem;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.no-certificates-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  margin-bottom: 1rem;\\n  color: #aaa;\\n}\\n\\n.no-certificates-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.no-certificates-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #777;\\n}\\n\\n.certificates-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 2rem;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.certificate-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\\n}\\n\\n.certificate-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.certificate-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding-bottom: 0.5rem;\\n}\\n\\n.icon-wrapper[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-color: #fff3e0; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 1rem;\\n  flex-shrink: 0;\\n}\\n\\n.icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: #ffb300; \\n\\n}\\n\\n.certificate-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 0.2rem;\\n}\\n\\n.certificate-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.certificate-details[_ngcontent-%COMP%] {\\n  padding-top: 1rem;\\n  border-top: 1px solid #eee;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 0.8rem;\\n  font-size: 0.95rem;\\n  color: #555;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  margin-right: 0.8rem;\\n  color: #999;\\n}\\n\\n.serial-number-chip[_ngcontent-%COMP%] {\\n  font-family: \\\"monospace\\\";\\n  font-size: 0.8rem;\\n  padding: 0.2rem 0.6rem;\\n  height: auto;\\n  background-color: #f0f0f0;\\n  color: #555;\\n}\\n\\n.score-green[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.score-blue[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.score-yellow[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n.score-red[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.8rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #eee;\\n}\\n\\n.card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 0.9rem;\\n  padding: 0.6rem 1rem;\\n}\\n\\n.card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  margin-right: 0.3rem;\\n}\\n\\n.validation-badge[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1.5rem;\\n}\\n\\n.validated-chip[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9; \\n\\n  color: #388e3c; \\n\\n  font-size: 0.9rem;\\n  padding: 0.4rem 0.8rem;\\n  height: auto;\\n}\\n\\n.validated-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  margin-right: 0.3rem;\\n}\\n\\n.stats-card[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1.5rem;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.stats-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 1.5rem;\\n  text-align: center;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background-color: #f9f9f9;\\n  border-radius: 8px;\\n  border: 1px solid #eee;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  margin-bottom: 0.3rem;\\n}\\n\\n.stat-value.blue[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.stat-value.green[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.stat-value.yellow[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n.stat-value.purple[_ngcontent-%COMP%] {\\n  color: #9c27b0;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .certificates-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getNoCertificatesMessage", "ɵɵlistener", "CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_38_listener", "restoredCtx", "ɵɵrestoreView", "_r7", "cert_r5", "$implicit", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "previewCertificate", "id", "CertificatesComponent_ng_template_7_mat_card_1_Template_button_click_42_listener", "ctx_r8", "downloadCertificate", "cours", "titre", "ɵɵtextInterpolate2", "formateur", "prenom", "nom", "client", "ɵɵtextInterpolate1", "ctx_r3", "formatDate", "dateGeneration", "ɵɵproperty", "getScoreColorClass", "scoreQuiz", "<PERSON>uil<PERSON><PERSON><PERSON>", "numeroSerie", "ctx_r4", "certificates", "length", "averageScore", "excellentResultsCount", "uniqueStudentsCount", "ɵɵtemplate", "CertificatesComponent_ng_template_7_mat_card_1_Template", "CertificatesComponent_ng_template_7_mat_card_2_Template", "ctx_r2", "currentUser", "role", "CertificatesComponent", "constructor", "certificateService", "authService", "snackBar", "ngOnInit", "currentUser$", "subscribe", "user", "loadCertificates", "nomClient", "dateObtention", "Date", "coursId", "clientId", "adminId", "admin", "getPageTitle", "getPageSubtitle", "date", "toLocaleDateString", "year", "month", "day", "score", "<PERSON>uil", "cert", "open", "duration", "telechargerCertificat", "next", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "error", "err", "console", "certificateId", "totalScore", "reduce", "sum", "Math", "round", "filter", "uniqueClientIds", "Set", "map", "size", "ɵɵdirectiveInject", "i1", "CertificateService", "i2", "AuthService", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "CertificatesComponent_Template", "rf", "ctx", "CertificatesComponent_div_6_Template", "CertificatesComponent_ng_template_7_Template", "ɵɵtemplateRefExtractor", "_r1"], "sources": ["C:\\e-learning\\src\\app\\features\\certificates\\certificates.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { CertificateService } from \"../../core/services/certificate.service\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { Certificat } from \"../../core/models/certificate.model\"\nimport { User } from \"../../core/models/user.model\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\n\n@Component({\n  selector: \"app-certificates\",\n  template: `\n    <div class=\"certificates-container\">\n      <div class=\"header-section\">\n        <h1>{{ getPageTitle() }}</h1>\n        <p>{{ getPageSubtitle() }}</p>\n      </div>\n\n      <div *ngIf=\"certificates.length === 0; else certificatesList\" class=\"no-certificates-card\">\n        <mat-card>\n          <mat-card-content class=\"no-certificates-content\">\n            <mat-icon>emoji_events</mat-icon>\n            <h3>Aucun certificat disponible</h3>\n            <p>{{ getNoCertificatesMessage() }}</p>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #certificatesList>\n        <div class=\"certificates-grid\">\n          <mat-card *ngFor=\"let cert of certificates\" class=\"certificate-card\">\n            <mat-card-header class=\"certificate-header\">\n              <div class=\"icon-wrapper\">\n                <mat-icon>emoji_events</mat-icon>\n              </div>\n              <div>\n                <mat-card-title>{{ cert.cours.titre }}</mat-card-title>\n                <mat-card-subtitle>\n                  Par {{ cert.cours.formateur.prenom }} {{ cert.cours.formateur.nom }}\n                </mat-card-subtitle>\n              </div>\n            </mat-card-header>\n\n            <mat-card-content class=\"certificate-details\">\n              <div class=\"detail-item\">\n                <mat-icon>person</mat-icon>\n                <span>Étudiant: <strong>{{ cert.client.prenom }} {{ cert.client.nom }}</strong></span>\n              </div>\n              <div class=\"detail-item\">\n                <mat-icon>calendar_today</mat-icon>\n                <span>Date d'obtention: {{ formatDate(cert.dateGeneration) }}</span>\n              </div>\n              <div class=\"detail-item\">\n                <mat-icon>book</mat-icon>\n                <span>Score: \n                  <strong [ngClass]=\"getScoreColorClass(cert.scoreQuiz, cert.seuilReussite)\">\n                    {{ cert.scoreQuiz }}%\n                  </strong>\n                  (seuil: {{ cert.seuilReussite }}%)\n                </span>\n              </div>\n              <div class=\"detail-item\">\n                <span>N° de série: \n                  <mat-chip-listbox>\n                    <mat-chip class=\"serial-number-chip\">{{ cert.numeroSerie }}</mat-chip>\n                  </mat-chip-listbox>\n                </span>\n              </div>\n\n              <div class=\"card-actions\">\n                <button mat-stroked-button color=\"primary\" (click)=\"previewCertificate(cert.id!)\">\n                  <mat-icon>visibility</mat-icon> Aperçu\n                </button>\n                <button mat-raised-button color=\"accent\" (click)=\"downloadCertificate(cert)\">\n                  <mat-icon>download</mat-icon> PDF\n                </button>\n              </div>\n\n              <div class=\"validation-badge\">\n                <mat-chip-listbox>\n                  <mat-chip class=\"validated-chip\">\n                    <mat-icon>check_circle</mat-icon> Certificat Validé\n                  </mat-chip>\n                </mat-chip-listbox>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Statistics for Formateurs and Admins -->\n        <mat-card *ngIf=\"currentUser?.role === 'Formateur' || currentUser?.role === 'Admin'\" class=\"stats-card\">\n          <mat-card-header>\n            <mat-card-title>Statistiques des Certificats</mat-card-title>\n          </mat-card-header>\n          <mat-card-content class=\"stats-grid\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value blue\">{{ certificates.length }}</div>\n              <div class=\"stat-label\">Total émis</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-value green\">{{ averageScore() }}%</div>\n              <div class=\"stat-label\">Score moyen</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-value yellow\">{{ excellentResultsCount() }}</div>\n              <div class=\"stat-label\">Excellents résultats</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-value purple\">{{ uniqueStudentsCount() }}</div>\n              <div class=\"stat-label\">Étudiants certifiés</div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </ng-template>\n    </div>\n  `,\n  styles: [\n    `\n    .certificates-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n    }\n\n    .header-section {\n      margin-bottom: 2rem;\n      text-align: center;\n    }\n\n    .header-section h1 {\n      font-size: 2.5rem;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 0.5rem;\n    }\n\n    .header-section p {\n      font-size: 1.1rem;\n      color: #666;\n    }\n\n    .no-certificates-card {\n      max-width: 600px;\n      margin: 0 auto;\n      text-align: center;\n      padding: 2rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .no-certificates-content mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #aaa;\n    }\n\n    .no-certificates-content h3 {\n      font-size: 1.5rem;\n      font-weight: 600;\n      margin-bottom: 0.5rem;\n    }\n\n    .no-certificates-content p {\n      color: #777;\n    }\n\n    .certificates-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n      gap: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .certificate-card {\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\n    }\n\n    .certificate-card:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n    }\n\n    .certificate-header {\n      display: flex;\n      align-items: center;\n      padding-bottom: 0.5rem;\n    }\n\n    .icon-wrapper {\n      width: 56px;\n      height: 56px;\n      border-radius: 50%;\n      background-color: #fff3e0; /* Light orange */\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 1rem;\n      flex-shrink: 0;\n    }\n\n    .icon-wrapper mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n      color: #ffb300; /* Orange */\n    }\n\n    .certificate-header mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 0.2rem;\n    }\n\n    .certificate-header mat-card-subtitle {\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    .certificate-details {\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .detail-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 0.8rem;\n      font-size: 0.95rem;\n      color: #555;\n    }\n\n    .detail-item mat-icon {\n      font-size: 1.2rem;\n      width: 1.2rem;\n      height: 1.2rem;\n      margin-right: 0.8rem;\n      color: #999;\n    }\n\n    .serial-number-chip {\n      font-family: 'monospace';\n      font-size: 0.8rem;\n      padding: 0.2rem 0.6rem;\n      height: auto;\n      background-color: #f0f0f0;\n      color: #555;\n    }\n\n    .score-green { color: #4caf50; }\n    .score-blue { color: #2196f3; }\n    .score-yellow { color: #ffc107; }\n    .score-red { color: #f44336; }\n\n    .card-actions {\n      display: flex;\n      gap: 0.8rem;\n      margin-top: 1.5rem;\n      padding-top: 1rem;\n      border-top: 1px solid #eee;\n    }\n\n    .card-actions button {\n      flex: 1;\n      font-size: 0.9rem;\n      padding: 0.6rem 1rem;\n    }\n\n    .card-actions button mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.3rem;\n    }\n\n    .validation-badge {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .validated-chip {\n      background-color: #e8f5e9; /* Light green */\n      color: #388e3c; /* Dark green */\n      font-size: 0.9rem;\n      padding: 0.4rem 0.8rem;\n      height: auto;\n    }\n\n    .validated-chip mat-icon {\n      font-size: 1rem;\n      width: 1rem;\n      height: 1rem;\n      margin-right: 0.3rem;\n    }\n\n    .stats-card {\n      margin-top: 2rem;\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .stats-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      gap: 1.5rem;\n      text-align: center;\n    }\n\n    .stat-item {\n      padding: 1rem;\n      background-color: #f9f9f9;\n      border-radius: 8px;\n      border: 1px solid #eee;\n    }\n\n    .stat-value {\n      font-size: 2rem;\n      font-weight: bold;\n      margin-bottom: 0.3rem;\n    }\n\n    .stat-value.blue { color: #2196f3; }\n    .stat-value.green { color: #4caf50; }\n    .stat-value.yellow { color: #ffc107; }\n    .stat-value.purple { color: #9c27b0; }\n\n    .stat-label {\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .certificates-grid {\n        grid-template-columns: 1fr;\n      }\n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `,\n  ],\n})\nexport class CertificatesComponent implements OnInit {\n  certificates: Certificat[] = []\n  currentUser: User | null = null\n\n  constructor(\n    private certificateService: CertificateService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n      if (this.currentUser) {\n        this.loadCertificates()\n      }\n    })\n  }\n\n  loadCertificates(): void {\n    // Mock data for demonstration\n    this.certificates = [\n      {\n        id: 1,\n        nomClient: \"Pierre Martin\",\n        dateObtention: new Date(\"2024-01-15T14:30:00Z\"),\n        coursId: 1,\n        cours: {\n          id: 1,\n          titre: \"React Fundamentals\",\n          formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\" },\n        },\n        clientId: 1,\n        client: { id: 1, nom: \"Martin\", prenom: \"Pierre\" },\n        adminId: 1,\n        admin: { id: 1, nom: \"Admin\", prenom: \"System\" },\n        dateGeneration: new Date(\"2024-01-15T14:30:00Z\"),\n        scoreQuiz: 85,\n        seuilReussite: 70,\n        numeroSerie: \"CERT-2024-001\",\n      },\n      {\n        id: 2,\n        coursId: 2,\n        cours: {\n          id: 2,\n          titre: \"JavaScript Avancé\",\n          formateur: { id: 2, nom: \"Bernard\", prenom: \"Sophie\" },\n        },\n        clientId: 1,\n        client: { id: 1, nom: \"Martin\", prenom: \"Pierre\" },\n        adminId: 1,\n        admin: { id: 1, nom: \"Admin\", prenom: \"System\" },\n        dateGeneration: new Date(\"2024-01-10T16:45:00Z\"),\n        scoreQuiz: 92,\n        seuilReussite: 75,\n        numeroSerie: \"CERT-2024-002\",\n      },\n    ]\n\n    // Uncomment to fetch from API if you have an endpoint for listing certificates\n    /*\n    // Example: this.certificateService.getCertificatesForUser(this.currentUser.id).subscribe(...)\n    */\n  }\n\n  getPageTitle(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Mes Certificats\"\n      case \"Formateur\":\n        return \"Certificats Émis\"\n      case \"Admin\":\n        return \"Gestion des Certificats\"\n      default:\n        return \"Certificats\"\n    }\n  }\n\n  getPageSubtitle(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Vos certificats de réussite aux cours\"\n      case \"Formateur\":\n        return \"Certificats émis pour vos cours\"\n      case \"Admin\":\n        return \"Tous les certificats générés sur la plateforme\"\n      default:\n        return \"Consultez les certificats de la plateforme\"\n    }\n  }\n\n  getNoCertificatesMessage(): string {\n    switch (this.currentUser?.role) {\n      case \"Client\":\n        return \"Terminez un cours avec succès pour obtenir votre premier certificat.\"\n      case \"Formateur\":\n        return \"Aucun certificat n'a encore été généré pour vos cours.\"\n      case \"Admin\":\n        return \"Aucun certificat n'a encore été généré sur la plateforme.\"\n      default:\n        return \"Aucun certificat n'est disponible pour le moment.\"\n    }\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString(\"fr-FR\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"2-digit\",\n    })\n  }\n\n  getScoreColorClass(score: number, seuil: number): string {\n    if (score >= seuil + 20) return \"score-green\"\n    if (score >= seuil + 10) return \"score-blue\"\n    if (score >= seuil) return \"score-yellow\"\n    return \"score-red\"\n  }\n\n  downloadCertificate(cert: Certificat): void {\n    if (!cert.cours || !cert.client) {\n      this.snackBar.open(\"Informations du certificat incomplètes.\", \"Fermer\", { duration: 3000 })\n      return\n    }\n\n    this.certificateService\n      .telechargerCertificat(cert.client.nom, cert.client.prenom, cert.cours.titre, cert.scoreQuiz)\n      .subscribe({\n        next: (blob) => {\n          const url = window.URL.createObjectURL(blob)\n          const a = document.createElement(\"a\")\n          a.href = url\n          a.download = `certificat-${cert.numeroSerie}.pdf`\n          document.body.appendChild(a)\n          a.click()\n          document.body.removeChild(a)\n          window.URL.revokeObjectURL(url)\n          this.snackBar.open(\"Certificat téléchargé !\", \"Fermer\", { duration: 3000 })\n        },\n        error: (err) => {\n          this.snackBar.open(\"Erreur lors du téléchargement du certificat.\", \"Fermer\", { duration: 3000 })\n          console.error(err)\n        },\n      })\n  }\n\n  previewCertificate(certificateId: number): void {\n    this.snackBar.open(\"Fonctionnalité d'aperçu non implémentée pour le moment.\", \"Fermer\", { duration: 3000 })\n    // You would typically open a new tab/window with a route that renders the PDF\n    // Example: window.open(`/certificates/${certificateId}/preview`, '_blank');\n  }\n\n  averageScore(): number {\n    if (this.certificates.length === 0) return 0\n    const totalScore = this.certificates.reduce((sum, cert) => sum + cert.scoreQuiz, 0)\n    return Math.round(totalScore / this.certificates.length)\n  }\n\n  excellentResultsCount(): number {\n    return this.certificates.filter((cert) => cert.scoreQuiz >= cert.seuilReussite + 20).length\n  }\n\n  uniqueStudentsCount(): number {\n    const uniqueClientIds = new Set(this.certificates.map((cert) => cert.clientId))\n    return uniqueClientIds.size\n  }\n}\n"], "mappings": ";;;;;;;;;;;IAgBMA,EAAA,CAAAC,cAAA,aAA2F;IAG3ED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,wBAAA,GAAgC;;;;;;IAOrCP,EAAA,CAAAC,cAAA,kBAAqE;IAGrDD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnCH,EAAA,CAAAC,cAAA,UAAK;IACaD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACvDH,EAAA,CAAAC,cAAA,wBAAmB;IACjBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAIxBH,EAAA,CAAAC,cAAA,4BAA8C;IAEhCD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,uBAAU;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEjFH,EAAA,CAAAC,cAAA,eAAyB;IACbD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtEH,EAAA,CAAAC,cAAA,eAAyB;IACbD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,eACJ;IAAAF,EAAA,CAAAC,cAAA,kBAA2E;IACzED,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAyB;IACjBD,EAAA,CAAAE,MAAA,+BACJ;IAAAF,EAAA,CAAAC,cAAA,wBAAkB;IACqBD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAK5EH,EAAA,CAAAC,cAAA,eAA0B;IACmBD,EAAA,CAAAQ,UAAA,mBAAAC,iFAAA;MAAA,MAAAC,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAF,MAAA,CAAAG,kBAAA,CAAAL,OAAA,CAAAM,EAAA,CAA4B;IAAA,EAAC;IAC/EnB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,qBAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA6E;IAApCD,EAAA,CAAAQ,UAAA,mBAAAY,iFAAA;MAAA,MAAAV,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAO,MAAA,GAAArB,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAI,MAAA,CAAAC,mBAAA,CAAAT,OAAA,CAAyB;IAAA,EAAC;IAC1Eb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,aAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAA8B;IAGdD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,gCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IA9CGH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAQ,OAAA,CAAAU,KAAA,CAAAC,KAAA,CAAsB;IAEpCxB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAyB,kBAAA,UAAAZ,OAAA,CAAAU,KAAA,CAAAG,SAAA,CAAAC,MAAA,OAAAd,OAAA,CAAAU,KAAA,CAAAG,SAAA,CAAAE,GAAA,MACF;IAOwB5B,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAyB,kBAAA,KAAAZ,OAAA,CAAAgB,MAAA,CAAAF,MAAA,OAAAd,OAAA,CAAAgB,MAAA,CAAAD,GAAA,KAA8C;IAIhE5B,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAA8B,kBAAA,uBAAAC,MAAA,CAAAC,UAAA,CAAAnB,OAAA,CAAAoB,cAAA,MAAuD;IAKnDjC,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAkC,UAAA,YAAAH,MAAA,CAAAI,kBAAA,CAAAtB,OAAA,CAAAuB,SAAA,EAAAvB,OAAA,CAAAwB,aAAA,EAAkE;IACxErC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA8B,kBAAA,MAAAjB,OAAA,CAAAuB,SAAA,OACF;IACApC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA8B,kBAAA,cAAAjB,OAAA,CAAAwB,aAAA,QACF;IAKyCrC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAQ,OAAA,CAAAyB,WAAA,CAAsB;;;;;IA0BvEtC,EAAA,CAAAC,cAAA,mBAAwG;IAEpFD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAE/DH,EAAA,CAAAC,cAAA,2BAAqC;IAEJD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,sBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE1CH,EAAA,CAAAC,cAAA,eAAuB;IACSD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE3CH,EAAA,CAAAC,cAAA,eAAuB;IACUD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,iCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEpDH,EAAA,CAAAC,cAAA,eAAuB;IACUD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,qCAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAbpBH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAkC,MAAA,CAAAC,YAAA,CAAAC,MAAA,CAAyB;IAIxBzC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAA8B,kBAAA,KAAAS,MAAA,CAAAG,YAAA,QAAqB;IAIpB1C,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAkC,MAAA,CAAAI,qBAAA,GAA6B;IAI7B3C,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAkC,MAAA,CAAAK,mBAAA,GAA2B;;;;;IA/EhE5C,EAAA,CAAAC,cAAA,aAA+B;IAC7BD,EAAA,CAAA6C,UAAA,IAAAC,uDAAA,wBAwDW;IACb9C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA6C,UAAA,IAAAE,uDAAA,uBAsBW;;;;IAlFkB/C,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAkC,UAAA,YAAAc,MAAA,CAAAR,YAAA,CAAe;IA4DjCxC,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAkC,UAAA,UAAAc,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,IAAA,sBAAAF,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,IAAA,cAAwE;;;AAoQ3F,OAAM,MAAOC,qBAAqB;EAIhCC,YACUC,kBAAsC,EACtCC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IANlB,KAAAf,YAAY,GAAiB,EAAE;IAC/B,KAAAS,WAAW,GAAgB,IAAI;EAM5B;EAEHO,QAAQA,CAAA;IACN,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACV,WAAW,GAAGU,IAAI;MACvB,IAAI,IAAI,CAACV,WAAW,EAAE;QACpB,IAAI,CAACW,gBAAgB,EAAE;;IAE3B,CAAC,CAAC;EACJ;EAEAA,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACpB,YAAY,GAAG,CAClB;MACErB,EAAE,EAAE,CAAC;MACL0C,SAAS,EAAE,eAAe;MAC1BC,aAAa,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;MAC/CC,OAAO,EAAE,CAAC;MACVzC,KAAK,EAAE;QACLJ,EAAE,EAAE,CAAC;QACLK,KAAK,EAAE,oBAAoB;QAC3BE,SAAS,EAAE;UAAEP,EAAE,EAAE,CAAC;UAAES,GAAG,EAAE,QAAQ;UAAED,MAAM,EAAE;QAAM;OAClD;MACDsC,QAAQ,EAAE,CAAC;MACXpC,MAAM,EAAE;QAAEV,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,QAAQ;QAAED,MAAM,EAAE;MAAQ,CAAE;MAClDuC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE;QAAEhD,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,OAAO;QAAED,MAAM,EAAE;MAAQ,CAAE;MAChDM,cAAc,EAAE,IAAI8B,IAAI,CAAC,sBAAsB,CAAC;MAChD3B,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;KACd,EACD;MACEnB,EAAE,EAAE,CAAC;MACL6C,OAAO,EAAE,CAAC;MACVzC,KAAK,EAAE;QACLJ,EAAE,EAAE,CAAC;QACLK,KAAK,EAAE,mBAAmB;QAC1BE,SAAS,EAAE;UAAEP,EAAE,EAAE,CAAC;UAAES,GAAG,EAAE,SAAS;UAAED,MAAM,EAAE;QAAQ;OACrD;MACDsC,QAAQ,EAAE,CAAC;MACXpC,MAAM,EAAE;QAAEV,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,QAAQ;QAAED,MAAM,EAAE;MAAQ,CAAE;MAClDuC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE;QAAEhD,EAAE,EAAE,CAAC;QAAES,GAAG,EAAE,OAAO;QAAED,MAAM,EAAE;MAAQ,CAAE;MAChDM,cAAc,EAAE,IAAI8B,IAAI,CAAC,sBAAsB,CAAC;MAChD3B,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;KACd,CACF;IAED;IACA;;;EAGF;;EAEA8B,YAAYA,CAAA;IACV,QAAQ,IAAI,CAACnB,WAAW,EAAEC,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,iBAAiB;MAC1B,KAAK,WAAW;QACd,OAAO,kBAAkB;MAC3B,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC;QACE,OAAO,aAAa;;EAE1B;EAEAmB,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACpB,WAAW,EAAEC,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,uCAAuC;MAChD,KAAK,WAAW;QACd,OAAO,iCAAiC;MAC1C,KAAK,OAAO;QACV,OAAO,gDAAgD;MACzD;QACE,OAAO,4CAA4C;;EAEzD;EAEA3C,wBAAwBA,CAAA;IACtB,QAAQ,IAAI,CAAC0C,WAAW,EAAEC,IAAI;MAC5B,KAAK,QAAQ;QACX,OAAO,sEAAsE;MAC/E,KAAK,WAAW;QACd,OAAO,wDAAwD;MACjE,KAAK,OAAO;QACV,OAAO,2DAA2D;MACpE;QACE,OAAO,mDAAmD;;EAEhE;EAEAlB,UAAUA,CAACsC,IAAU;IACnB,OAAO,IAAIP,IAAI,CAACO,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAvC,kBAAkBA,CAACwC,KAAa,EAAEC,KAAa;IAC7C,IAAID,KAAK,IAAIC,KAAK,GAAG,EAAE,EAAE,OAAO,aAAa;IAC7C,IAAID,KAAK,IAAIC,KAAK,GAAG,EAAE,EAAE,OAAO,YAAY;IAC5C,IAAID,KAAK,IAAIC,KAAK,EAAE,OAAO,cAAc;IACzC,OAAO,WAAW;EACpB;EAEAtD,mBAAmBA,CAACuD,IAAgB;IAClC,IAAI,CAACA,IAAI,CAACtD,KAAK,IAAI,CAACsD,IAAI,CAAChD,MAAM,EAAE;MAC/B,IAAI,CAAC0B,QAAQ,CAACuB,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC3F;;IAGF,IAAI,CAAC1B,kBAAkB,CACpB2B,qBAAqB,CAACH,IAAI,CAAChD,MAAM,CAACD,GAAG,EAAEiD,IAAI,CAAChD,MAAM,CAACF,MAAM,EAAEkD,IAAI,CAACtD,KAAK,CAACC,KAAK,EAAEqD,IAAI,CAACzC,SAAS,CAAC,CAC5FsB,SAAS,CAAC;MACTuB,IAAI,EAAGC,IAAI,IAAI;QACb,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;QACZI,CAAC,CAACI,QAAQ,GAAG,cAAcd,IAAI,CAACvC,WAAW,MAAM;QACjDkD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;QAC5BA,CAAC,CAACO,KAAK,EAAE;QACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;QAC5BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;QAC/B,IAAI,CAAC5B,QAAQ,CAACuB,IAAI,CAAC,yBAAyB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC7E,CAAC;MACDkB,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAAC3C,QAAQ,CAACuB,IAAI,CAAC,8CAA8C,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAChGoB,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACN;EAEAhF,kBAAkBA,CAACkF,aAAqB;IACtC,IAAI,CAAC7C,QAAQ,CAACuB,IAAI,CAAC,yDAAyD,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAC3G;IACA;EACF;;EAEArC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACF,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC5C,MAAM4D,UAAU,GAAG,IAAI,CAAC7D,YAAY,CAAC8D,MAAM,CAAC,CAACC,GAAG,EAAE1B,IAAI,KAAK0B,GAAG,GAAG1B,IAAI,CAACzC,SAAS,EAAE,CAAC,CAAC;IACnF,OAAOoE,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAG,IAAI,CAAC7D,YAAY,CAACC,MAAM,CAAC;EAC1D;EAEAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACH,YAAY,CAACkE,MAAM,CAAE7B,IAAI,IAAKA,IAAI,CAACzC,SAAS,IAAIyC,IAAI,CAACxC,aAAa,GAAG,EAAE,CAAC,CAACI,MAAM;EAC7F;EAEAG,mBAAmBA,CAAA;IACjB,MAAM+D,eAAe,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACpE,YAAY,CAACqE,GAAG,CAAEhC,IAAI,IAAKA,IAAI,CAACZ,QAAQ,CAAC,CAAC;IAC/E,OAAO0C,eAAe,CAACG,IAAI;EAC7B;;;uBAtKW3D,qBAAqB,EAAAnD,EAAA,CAAA+G,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAjH,EAAA,CAAA+G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnH,EAAA,CAAA+G,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAArBlE,qBAAqB;MAAAmE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlV9B5H,EAAA,CAAAC,cAAA,aAAoC;UAE5BD,EAAA,CAAAE,MAAA,GAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,GAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGhCH,EAAA,CAAA6C,UAAA,IAAAiF,oCAAA,iBAQM;UAEN9H,EAAA,CAAA6C,UAAA,IAAAkF,4CAAA,gCAAA/H,EAAA,CAAAgI,sBAAA,CAqFc;UAChBhI,EAAA,CAAAG,YAAA,EAAM;;;;UApGEH,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAK,iBAAA,CAAAwH,GAAA,CAAAzD,YAAA,GAAoB;UACrBpE,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAK,iBAAA,CAAAwH,GAAA,CAAAxD,eAAA,GAAuB;UAGtBrE,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAkC,UAAA,SAAA2F,GAAA,CAAArF,YAAA,CAAAC,MAAA,OAAiC,aAAAwF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}