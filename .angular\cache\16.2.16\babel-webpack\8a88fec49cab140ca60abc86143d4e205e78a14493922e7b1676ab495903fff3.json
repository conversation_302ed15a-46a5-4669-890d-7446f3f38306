{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatToolbarModule } from \"@angular/material/toolbar\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { MatGridListModule } from \"@angular/material/grid-list\";\nimport { DashboardComponent } from \"./dashboard.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardModule {\n  static {\n    this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatToolbarModule, MatChipsModule, MatGridListModule, RouterModule.forChild([{\n        path: \"\",\n        component: DashboardComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    declarations: [DashboardComponent],\n    imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatToolbarModule, MatChipsModule, MatGridListModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatToolbarModule", "MatChipsModule", "MatGridListModule", "DashboardComponent", "DashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatToolbarModule } from \"@angular/material/toolbar\"\nimport { MatChipsModule } from \"@angular/material/chips\"\nimport { MatGridListModule } from \"@angular/material/grid-list\"\n\nimport { DashboardComponent } from \"./dashboard.component\"\n\n@NgModule({\n  declarations: [DashboardComponent],\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatToolbarModule,\n    MatChipsModule,\n    MatGridListModule,\n    RouterModule.forChild([{ path: \"\", component: DashboardComponent }]),\n  ],\n})\nexport class DashboardModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D,SAASC,kBAAkB,QAAQ,uBAAuB;;;AAe1D,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAVxBT,YAAY,EACZE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,iBAAiB,EACjBN,YAAY,CAACS,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAkB,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAG3DC,eAAe;IAAAI,YAAA,GAZXL,kBAAkB;IAAAM,OAAA,GAE/Bd,YAAY,EACZE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,iBAAiB,EAAAQ,EAAA,CAAAd,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}