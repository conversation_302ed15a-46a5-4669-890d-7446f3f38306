import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Course, Contenu } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class CourseService {
  private apiUrl = `${environment.urlApi}cours`

  constructor(private http: HttpClient) {}

  // GET: Tous les cours (correspond à GET /api/cours)
  getAllCours(): Observable<Course[]> {
    return this.http.get<Course[]>(this.apiUrl)
  }

  // GET: Un cours par ID (correspond à GET /api/cours/{id})
  getCours(id: number): Observable<Course> {
    return this.http.get<Course>(`${this.apiUrl}/${id}`)
  }

  // PUT: Modifier un cours (correspond à PUT /api/cours/{id})
  modifierCours(id: number, cours: Partial<Course>): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, cours)
  }

  // DELETE: Supprimer un cours (correspond à DELETE /api/cours/{id})
  supprimerCours(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`)
  }

  // POST: Ajouter un contenu à un cours (correspond à POST /api/cours/{id}/ajouter-contenu)
  ajouterContenu(coursId: number, contenu: Contenu): Observable<any> {
    return this.http.post(`${this.apiUrl}/${coursId}/ajouter-contenu`, contenu)
  }
}
