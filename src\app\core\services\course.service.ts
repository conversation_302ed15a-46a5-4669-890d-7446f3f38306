import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { Course, Contenu } from "../models/course.model"

@Injectable({
  providedIn: "root",
})
export class CourseService {
  constructor(private http: HttpClient) {}

  // GET: Tous les cours
  getAllCours(): Observable<Course[]> {
    return this.http.get<Course[]>(`${environment.urlApi}cours`)
  }

  // GET: Un cours par ID
  getCours(id: number): Observable<Course> {
    return this.http.get<Course>(`${environment.urlApi}cours/${id}`)
  }

  // PUT: Modifier un cours
  modifierCours(id: number, cours: Partial<Course>): Observable<any> {
    return this.http.put(`${environment.urlApi}cours/${id}`, cours)
  }

  // DELETE: Supprimer un cours
  supprimerCours(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}cours/${id}`)
  }

  // POST: Ajouter un contenu
  ajouterContenu(coursId: number, contenu: Contenu): Observable<any> {
    return this.http.post(`${environment.urlApi}cours/${coursId}/ajouter-contenu`, contenu)
  }
}
