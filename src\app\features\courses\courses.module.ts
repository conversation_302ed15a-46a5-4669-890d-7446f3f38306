import { NgModule } from "@angular/core"
import { CommonModule } from "@angular/common"
import { RouterModule } from "@angular/router"
import { FormsModule } from "@angular/forms" // Pour ngModel dans les filtres

// Angular Material
import { MatCardModule } from "@angular/material/card"
import { MatButtonModule } from "@angular/material/button"
import { MatIconModule } from "@angular/material/icon"
import { MatInputModule } from "@angular/material/input"
import { MatFormFieldModule } from "@angular/material/form-field"
import { MatSelectModule } from "@angular/material/select"
import { MatChipsModule } from "@angular/material/chips"
import { MatProgressBarModule } from "@angular/material/progress-bar"

import { CourseListComponent } from "./course-list/course-list.component"
import { CourseDetailComponent } from "./course-detail/course-detail.component"
import { CourseCreateEditComponent } from "./course-create-edit/course-create-edit.component"
import { ReactiveFormsModule } from "@angular/forms"

@NgModule({
  declarations: [CourseListComponent, CourseDetailComponent, CourseCreateEditComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatChipsModule,
    MatProgressBarModule,
    RouterModule.forChild([
      { path: "", component: CourseListComponent },
      { path: "create", component: CourseCreateEditComponent },
      { path: "edit/:id", component: CourseCreateEditComponent },
      { path: ":id", component: CourseDetailComponent },
    ]),
  ],
})
export class CoursesModule {}
