{"version": 3, "file": "common.js", "mappings": ";;;;;;;;;;;;;;;;AAG+D;;;AAMzD,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,kEAAW,CAACK,MAAM,OAAO;EAEN;EAEvC;EACAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAW,IAAI,CAACH,MAAM,CAAC;EAC7C;EAEA;EACAI,QAAQA,CAACC,EAAU;IACjB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,IAAIK,EAAE,EAAE,CAAC;EACtD;EAEA;EACAC,aAAaA,CAACD,EAAU,EAAEE,KAAsB;IAC9C,OAAO,IAAI,CAACR,IAAI,CAACS,GAAG,CAAC,GAAG,IAAI,CAACR,MAAM,IAAIK,EAAE,EAAE,EAAEE,KAAK,CAAC;EACrD;EAEA;EACAE,cAAcA,CAACJ,EAAU;IACvB,OAAO,IAAI,CAACN,IAAI,CAACW,MAAM,CAAC,GAAG,IAAI,CAACV,MAAM,IAAIK,EAAE,EAAE,CAAC;EACjD;EAEA;EACAM,cAAcA,CAACC,OAAe,EAAEC,OAAgB;IAC9C,OAAO,IAAI,CAACd,IAAI,CAACe,IAAI,CAAC,GAAG,IAAI,CAACd,MAAM,IAAIY,OAAO,kBAAkB,EAAEC,OAAO,CAAC;EAC7E;;;uBA5BWhB,aAAa,EAAAkB,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAbpB,aAAa;MAAAsB,OAAA,EAAbtB,aAAa,CAAAuB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "sources": ["./src/app/core/services/course.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Course, Contenu } from \"../models/course.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class CourseService {\n  private apiUrl = `${environment.urlApi}cours`\n\n  constructor(private http: HttpClient) {}\n\n  // GET: Tous les cours (correspond à GET /api/cours)\n  getAllCours(): Observable<Course[]> {\n    return this.http.get<Course[]>(this.apiUrl)\n  }\n\n  // GET: Un cours par ID (correspond à GET /api/cours/{id})\n  getCours(id: number): Observable<Course> {\n    return this.http.get<Course>(`${this.apiUrl}/${id}`)\n  }\n\n  // PUT: Modifier un cours (correspond à PUT /api/cours/{id})\n  modifierCours(id: number, cours: Partial<Course>): Observable<any> {\n    return this.http.put(`${this.apiUrl}/${id}`, cours)\n  }\n\n  // DELETE: Supprimer un cours (correspond à DELETE /api/cours/{id})\n  supprimerCours(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/${id}`)\n  }\n\n  // POST: Ajouter un contenu à un cours (correspond à POST /api/cours/{id}/ajouter-contenu)\n  ajouterContenu(coursId: number, contenu: Contenu): Observable<any> {\n    return this.http.post(`${this.apiUrl}/${coursId}/ajouter-contenu`, contenu)\n  }\n}\n"], "names": ["environment", "CourseService", "constructor", "http", "apiUrl", "urlApi", "getAllCours", "get", "getCours", "id", "modifierCours", "cours", "put", "supprimerCours", "delete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coursId", "contenu", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}