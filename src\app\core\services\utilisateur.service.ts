import { Injectable } from "@angular/core"
import type { HttpClient } from "@angular/common/http"
import type { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import type { User } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class UtilisateurService {
  constructor(private http: HttpClient) {}

  // GET: Tous les utilisateurs
  getUtilisateurs(): Observable<User[]> {
    return this.http.get<User[]>(`${environment.urlApi}utilisateur`)
  }

  // GET: Un utilisateur par ID
  getUtilisateur(id: number): Observable<User> {
    return this.http.get<User>(`${environment.urlApi}utilisateur/${id}`)
  }

  // POST: Créer un utilisateur
  createUtilisateur(utilisateur: User): Observable<User> {
    return this.http.post<User>(`${environment.urlApi}utilisateur`, utilisateur)
  }

  // PUT: Modifier un utilisateur
  updateUtilisateur(id: number, utilisateur: User): Observable<any> {
    return this.http.put(`${environment.urlApi}utilisateur/${id}`, utilisateur)
  }

  // DELETE: Supprimer un utilisateur
  deleteUtilisateur(id: number): Observable<any> {
    return this.http.delete(`${environment.urlApi}utilisateur/${id}`)
  }

  // GET: Utilisateurs par rôle
  getUsersByRole(role: string): Observable<User[]> {
    return this.http.get<User[]>(`${environment.urlApi}utilisateur/role/${role}`)
  }
}
