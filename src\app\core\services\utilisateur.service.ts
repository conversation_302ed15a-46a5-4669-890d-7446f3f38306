import { Injectable } from "@angular/core"
import { HttpClient } from "@angular/common/http"
import { Observable } from "rxjs"
import { environment } from "../../../environments/environment"
import { User } from "../models/user.model"

@Injectable({
  providedIn: "root",
})
export class UtilisateurService {
  private apiUrl = `${environment.urlApi}utilisateur`

  constructor(private http: HttpClient) {}

  // GET: Tous les utilisateurs (correspond à GET /api/utilisateur)
  getUtilisateurs(): Observable<User[]> {
    return this.http.get<User[]>(this.apiUrl)
  }

  // GET: Un utilisateur par ID (correspond à GET /api/utilisateur/{id})
  getUtilisateur(id: number): Observable<User> {
    return this.http.get<User>(`${this.apiUrl}/${id}`)
  }

  // POST: Créer un utilisateur (correspond à POST /api/utilisateur)
  createUtilisateur(utilisateur: User): Observable<User> {
    return this.http.post<User>(this.apiUrl, utilisateur)
  }

  // PUT: Modifier un utilisateur (correspond à PUT /api/utilisateur/{id})
  updateUtilisateur(id: number, utilisateur: User): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, utilisateur)
  }

  // DELETE: Supprimer un utilisateur (correspond à DELETE /api/utilisateur/{id})
  deleteUtilisateur(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`)
  }

  // GET: Utilisateurs par rôle (correspond à GET /api/utilisateur/role/{role})
  getUsersByRole(role: string): Observable<User[]> {
    return this.http.get<User[]>(`${this.apiUrl}/role/${role}`)
  }
}
