{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" L'email est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Format d'email invalide \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le mot de passe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 17);\n  }\n}\nfunction LoginComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(fb, authService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.loginForm = this.fb.group({\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", Validators.required],\n      rememberMe: [false]\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const {\n        email,\n        password\n      } = this.loginForm.value;\n      this.authService.login({\n        email,\n        password\n      }).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open(\"Connexion réussie !\", \"Fermer\", {\n            duration: 3000\n          });\n          this.router.navigate([\"/dashboard\"]);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(\"Erreur de connexion. Vérifiez vos identifiants.\", \"Fermer\", {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 41,\n      vars: 9,\n      consts: [[1, \"auth-container\"], [1, \"auth-wrapper\"], [1, \"auth-header\"], [1, \"logo\"], [1, \"auth-card\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"checkbox-container\"], [\"formControlName\", \"rememberMe\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"submit-btn\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"auth-links\"], [\"routerLink\", \"/auth/register\", 1, \"link\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"school\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"Training Platform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"Plateforme de formation en ligne\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-card\", 4)(11, \"mat-card-header\")(12, \"mat-card-title\");\n          i0.ɵɵtext(13, \"Connexion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_15_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(16, \"mat-form-field\", 6)(17, \"mat-label\");\n          i0.ɵɵtext(18, \"E-mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 7);\n          i0.ɵɵtemplate(20, LoginComponent_mat_error_20_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(21, LoginComponent_mat_error_21_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 6)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 9);\n          i0.ɵɵelementStart(26, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_26_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(27, \"mat-icon\");\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(29, LoginComponent_mat_error_29_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 11)(31, \"mat-checkbox\", 12);\n          i0.ɵɵtext(32, \"Se souvenir de moi\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"button\", 13);\n          i0.ɵɵtemplate(34, LoginComponent_mat_spinner_34_Template, 1, 0, \"mat-spinner\", 14);\n          i0.ɵɵtemplate(35, LoginComponent_span_35_Template, 2, 0, \"span\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 15)(37, \"p\");\n          i0.ɵɵtext(38, \" Pas encore de compte ? \");\n          i0.ɵɵelementStart(39, \"a\", 16);\n          i0.ɵɵtext(40, \"S'inscrire\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_5_0;\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.hasError(\"email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_5_0.hasError(\"required\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatCheckbox, i11.MatIcon, i12.MatProgressSpinner, i3.RouterLink],\n      styles: [\".auth-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1rem;\\n}\\n\\n.auth-wrapper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n.auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n  color: white;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.logo[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-right: 0.5rem;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin: 0;\\n  font-weight: bold;\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1.1rem;\\n}\\n\\n.auth-links[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1.5rem;\\n}\\n\\n.link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "LoginComponent", "constructor", "fb", "authService", "router", "snackBar", "hidePassword", "isLoading", "ngOnInit", "loginForm", "group", "email", "required", "password", "rememberMe", "onSubmit", "valid", "value", "login", "subscribe", "next", "response", "open", "duration", "navigate", "error", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_15_listener", "ɵɵtemplate", "LoginComponent_mat_error_20_Template", "LoginComponent_mat_error_21_Template", "LoginComponent_Template_button_click_26_listener", "LoginComponent_mat_error_29_Template", "LoginComponent_mat_spinner_34_Template", "LoginComponent_span_35_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "ɵɵtextInterpolate", "tmp_5_0", "invalid"], "sources": ["C:\\e-learning\\src\\app\\features\\auth\\login\\login.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { Form<PERSON>uilder, FormGroup, Validators } from \"@angular/forms\"\nimport { Router } from \"@angular/router\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { AuthService } from \"../../../core/services/auth.service\"\n\n@Component({\n  selector: \"app-login\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <!-- Header -->\n        <div class=\"auth-header\">\n          <div class=\"logo\">\n            <mat-icon>school</mat-icon>\n            <h1>Training Platform</h1>\n          </div>\n          <p>Plateforme de formation en ligne</p>\n        </div>\n\n        <!-- Login Form -->\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Connexion</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"loginForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"rememberMe\">Se souvenir de moi</mat-checkbox>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"loginForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">Se connecter</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Pas encore de compte ? \n                <a routerLink=\"/auth/register\" class=\"link\">S'inscrire</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .auth-header {\n      text-align: center;\n      margin-bottom: 2rem;\n      color: white;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0.5rem;\n    }\n\n    .logo mat-icon {\n      font-size: 2.5rem;\n      margin-right: 0.5rem;\n    }\n\n    .logo h1 {\n      font-size: 2rem;\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `,\n  ],\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup\n  hidePassword = true\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.loginForm = this.fb.group({\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", Validators.required],\n      rememberMe: [false],\n    })\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true\n      const { email, password } = this.loginForm.value\n\n      this.authService.login({ email, password }).subscribe({\n        next: (response) => {\n          this.isLoading = false\n          this.snackBar.open(\"Connexion réussie !\", \"Fermer\", { duration: 3000 })\n          this.router.navigate([\"/dashboard\"])\n        },\n        error: (error) => {\n          this.isLoading = false\n          this.snackBar.open(\"Erreur de connexion. Vérifiez vos identifiants.\", \"Fermer\", { duration: 5000 })\n        },\n      })\n    }\n  }\n}\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;IA8BnDC,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AA0F5D,OAAM,MAAOE,cAAc;EAKzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,KAAK;EAOd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACiB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAEnB,UAAU,CAACkB,QAAQ,CAAC;MACnCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,SAAS,CAACO,KAAK,EAAE;MACxB,IAAI,CAACT,SAAS,GAAG,IAAI;MACrB,MAAM;QAAEI,KAAK;QAAEE;MAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACQ,KAAK;MAEhD,IAAI,CAACd,WAAW,CAACe,KAAK,CAAC;QAAEP,KAAK;QAAEE;MAAQ,CAAE,CAAC,CAACM,SAAS,CAAC;QACpDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACd,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACiB,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACvE,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACF,QAAQ,CAACiB,IAAI,CAAC,iDAAiD,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACrG;OACD,CAAC;;EAEN;;;uBArCWvB,cAAc,EAAAL,EAAA,CAAA+B,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjC,EAAA,CAAA+B,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnC,EAAA,CAAA+B,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAArC,EAAA,CAAA+B,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdlC,cAAc;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1IvB9C,EAAA,CAAAC,cAAA,aAA4B;UAKVD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,uCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIzCH,EAAA,CAAAC,cAAA,mBAA4B;UAERD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAG5CH,EAAA,CAAAC,cAAA,wBAAkB;UACcD,EAAA,CAAAgD,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAA3B,QAAA,EAAU;UAAA,EAAC;UACnDpB,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAI,SAAA,gBAAmF;UACnFJ,EAAA,CAAAkD,UAAA,KAAAC,oCAAA,uBAEY;UACZnD,EAAA,CAAAkD,UAAA,KAAAE,oCAAA,uBAEY;UACdpD,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAI,SAAA,gBAAuF;UACvFJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAgD,UAAA,mBAAAK,iDAAA;YAAA,OAAAN,GAAA,CAAApC,YAAA,IAAAoC,GAAA,CAAApC,YAAA;UAAA,EAAsC;UACtEX,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEzEH,EAAA,CAAAkD,UAAA,KAAAI,oCAAA,uBAEY;UACdtD,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,eAAgC;UACaD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAG9EH,EAAA,CAAAC,cAAA,kBACkF;UAChFD,EAAA,CAAAkD,UAAA,KAAAK,sCAAA,0BAA2D;UAC3DvD,EAAA,CAAAkD,UAAA,KAAAM,+BAAA,kBAA4C;UAC9CxD,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,eAAwB;UAEpBD,EAAA,CAAAE,MAAA,gCACA;UAAAF,EAAA,CAAAC,cAAA,aAA4C;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;UArCxDH,EAAA,CAAAyD,SAAA,IAAuB;UAAvBzD,EAAA,CAAA0D,UAAA,cAAAX,GAAA,CAAAjC,SAAA,CAAuB;UAIbd,EAAA,CAAAyD,SAAA,GAAkD;UAAlDzD,EAAA,CAAA0D,UAAA,UAAAC,OAAA,GAAAZ,GAAA,CAAAjC,SAAA,CAAA8C,GAAA,4BAAAD,OAAA,CAAAE,QAAA,aAAkD;UAGlD7D,EAAA,CAAAyD,SAAA,GAA+C;UAA/CzD,EAAA,CAAA0D,UAAA,UAAAI,OAAA,GAAAf,GAAA,CAAAjC,SAAA,CAAA8C,GAAA,4BAAAE,OAAA,CAAAD,QAAA,UAA+C;UAO3C7D,EAAA,CAAAyD,SAAA,GAA2C;UAA3CzD,EAAA,CAAA0D,UAAA,SAAAX,GAAA,CAAApC,YAAA,uBAA2C;UAE/CX,EAAA,CAAAyD,SAAA,GAAkD;UAAlDzD,EAAA,CAAA+D,iBAAA,CAAAhB,GAAA,CAAApC,YAAA,mCAAkD;UAElDX,EAAA,CAAAyD,SAAA,GAAqD;UAArDzD,EAAA,CAAA0D,UAAA,UAAAM,OAAA,GAAAjB,GAAA,CAAAjC,SAAA,CAAA8C,GAAA,+BAAAI,OAAA,CAAAH,QAAA,aAAqD;UAU3D7D,EAAA,CAAAyD,SAAA,GAA2C;UAA3CzD,EAAA,CAAA0D,UAAA,aAAAX,GAAA,CAAAjC,SAAA,CAAAmD,OAAA,IAAAlB,GAAA,CAAAnC,SAAA,CAA2C;UACrBZ,EAAA,CAAAyD,SAAA,GAAe;UAAfzD,EAAA,CAAA0D,UAAA,SAAAX,GAAA,CAAAnC,SAAA,CAAe;UACpCZ,EAAA,CAAAyD,SAAA,GAAgB;UAAhBzD,EAAA,CAAA0D,UAAA,UAAAX,GAAA,CAAAnC,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}