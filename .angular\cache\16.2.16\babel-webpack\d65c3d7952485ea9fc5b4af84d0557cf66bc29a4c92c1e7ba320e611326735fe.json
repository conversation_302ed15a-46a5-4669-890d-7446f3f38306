{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PaymentService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlApi}paiement`;\n  }\n  // POST: Effectuer un paiement (correspond à POST /api/paiement/effectuer)\n  effectuerPaiement(paiement) {\n    return this.http.post(`${this.apiUrl}/effectuer`, paiement);\n  }\n  static {\n    this.ɵfac = function PaymentService_Factory(t) {\n      return new (t || PaymentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PaymentService,\n      factory: PaymentService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "PaymentService", "constructor", "http", "apiUrl", "urlApi", "effectuerPaiement", "paiement", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\payment.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Paiement, PaymentResponse } from \"../models/payment.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class PaymentService {\n  private apiUrl = `${environment.urlApi}paiement`\n\n  constructor(private http: HttpClient) {}\n\n  // POST: Effectuer un paiement (correspond à POST /api/paiement/effectuer)\n  effectuerPaiement(paiement: Paiement): Observable<PaymentResponse> {\n    return this.http.post<PaymentResponse>(`${this.apiUrl}/effectuer`, paiement)\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,MAAM,UAAU;EAET;EAEvC;EACAC,iBAAiBA,CAACC,QAAkB;IAClC,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAkB,GAAG,IAAI,CAACJ,MAAM,YAAY,EAAEG,QAAQ,CAAC;EAC9E;;;uBARWN,cAAc,EAAAQ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdX,cAAc;MAAAY,OAAA,EAAdZ,cAAc,CAAAa,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}