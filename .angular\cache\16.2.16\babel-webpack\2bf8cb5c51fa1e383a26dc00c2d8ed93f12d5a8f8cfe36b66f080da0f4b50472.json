{"ast": null, "code": "import { interval } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../core/services/quiz.service\";\nimport * as i3 from \"../../core/services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-bar\";\nimport * as i11 from \"@angular/material/radio\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction QuizComponent_div_1_mat_radio_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    i0.ɵɵproperty(\"value\", i_r8);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r7, \" \");\n  }\n}\nfunction QuizComponent_div_1_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function QuizComponent_div_1_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.nextQuestion());\n    });\n    i0.ɵɵtext(1, \" Question suivante \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectedAnswers[ctx_r4.quiz.questions[ctx_r4.currentQuestionIndex].id] === undefined);\n  }\n}\nfunction QuizComponent_div_1_button_26_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 27);\n  }\n}\nfunction QuizComponent_div_1_button_26_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Terminer le quiz\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuizComponent_div_1_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function QuizComponent_div_1_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.submitQuiz());\n    });\n    i0.ɵɵtemplate(1, QuizComponent_div_1_button_26_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 25);\n    i0.ɵɵtemplate(2, QuizComponent_div_1_button_26_span_2_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.selectedAnswers[ctx_r5.quiz.questions[ctx_r5.currentQuestionIndex].id] === undefined || ctx_r5.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isLoading);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"current\": a0,\n    \"answered\": a1\n  };\n};\nfunction QuizComponent_div_1_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function QuizComponent_div_1_button_32_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const i_r16 = restoredCtx.index;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.goToQuestion(i_r16));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const q_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, i_r16 === ctx_r6.currentQuestionIndex, ctx_r6.selectedAnswers[q_r15.id] !== undefined && i_r16 !== ctx_r6.currentQuestionIndex));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i_r16 + 1, \" \");\n  }\n}\nfunction QuizComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h1\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"timer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 7);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 8)(11, \"span\", 9);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 10);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"mat-progress-bar\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-card\", 12)(17, \"mat-card-title\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-card-content\")(20, \"mat-radio-group\", 13);\n    i0.ɵɵlistener(\"ngModelChange\", function QuizComponent_div_1_Template_mat_radio_group_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.selectedAnswers[ctx_r19.quiz.questions[ctx_r19.currentQuestionIndex].id] = $event);\n    });\n    i0.ɵɵtemplate(21, QuizComponent_div_1_mat_radio_button_21_Template, 2, 2, \"mat-radio-button\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 15)(23, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function QuizComponent_div_1_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.previousQuestion());\n    });\n    i0.ɵɵtext(24, \" Question pr\\u00E9c\\u00E9dente \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, QuizComponent_div_1_button_25_Template, 2, 1, \"button\", 17);\n    i0.ɵɵtemplate(26, QuizComponent_div_1_button_26_Template, 3, 3, \"button\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-card\", 19)(28, \"mat-card-title\");\n    i0.ɵɵtext(29, \"Aper\\u00E7u des questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"mat-card-content\")(31, \"div\", 20);\n    i0.ɵɵtemplate(32, QuizComponent_div_1_button_32_Template, 2, 5, \"button\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.quiz.titre);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.formatTime(ctx_r0.timeRemaining));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"Question \", ctx_r0.currentQuestionIndex + 1, \" sur \", ctx_r0.quiz.questions.length, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Seuil de r\\u00E9ussite: \", ctx_r0.quiz.seuilReussite, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r0.progress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.quiz.questions[ctx_r0.currentQuestionIndex].texte);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.selectedAnswers[ctx_r0.quiz.questions[ctx_r0.currentQuestionIndex].id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.quiz.questions[ctx_r0.currentQuestionIndex].options);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.currentQuestionIndex === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentQuestionIndex < ctx_r0.quiz.questions.length - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentQuestionIndex === ctx_r0.quiz.questions.length - 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.quiz.questions);\n  }\n}\nfunction QuizComponent_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement du quiz...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Un certificat sera g\\u00E9n\\u00E9r\\u00E9 automatiquement !\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function QuizComponent_ng_template_2_div_1_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r29.restartQuiz());\n    });\n    i0.ɵɵtext(1, \" Recommencer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_div_25_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 56);\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_div_25_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 57);\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_div_25_div_9_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1, \"\\u2713 Bonne r\\u00E9ponse\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_div_25_div_9_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1, \"\\u2717 Votre r\\u00E9ponse\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"correct-option\": a0,\n    \"user-incorrect-option\": a1\n  };\n};\nfunction QuizComponent_ng_template_2_div_1_div_25_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, QuizComponent_ng_template_2_div_1_div_25_div_9_span_2_Template, 2, 0, \"span\", 59);\n    i0.ɵɵtemplate(3, QuizComponent_ng_template_2_div_1_div_25_div_9_span_3_Template, 2, 0, \"span\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r36 = ctx.$implicit;\n    const j_r37 = ctx.index;\n    const ctx_r40 = i0.ɵɵnextContext();\n    const question_r31 = ctx_r40.$implicit;\n    const i_r32 = ctx_r40.index;\n    const ctx_r35 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c1, j_r37 === question_r31.bonneReponse, j_r37 === (ctx_r35.results.reponses[i_r32] == null ? null : ctx_r35.results.reponses[i_r32].reponseChoisie) && !(ctx_r35.results.reponses[i_r32] == null ? null : ctx_r35.results.reponses[i_r32].estCorrecte)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r36, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", j_r37 === question_r31.bonneReponse);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", j_r37 === (ctx_r35.results.reponses[i_r32] == null ? null : ctx_r35.results.reponses[i_r32].reponseChoisie) && !(ctx_r35.results.reponses[i_r32] == null ? null : ctx_r35.results.reponses[i_r32].estCorrecte));\n  }\n}\nfunction QuizComponent_ng_template_2_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, QuizComponent_ng_template_2_div_1_div_25_mat_icon_4_Template, 2, 0, \"mat-icon\", 51);\n    i0.ɵɵtemplate(5, QuizComponent_ng_template_2_div_1_div_25_mat_icon_5_Template, 2, 0, \"mat-icon\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 54);\n    i0.ɵɵtemplate(9, QuizComponent_ng_template_2_div_1_div_25_div_9_Template, 4, 7, \"div\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const question_r31 = ctx.$implicit;\n    const i_r32 = ctx.index;\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Question \", i_r32 + 1, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.results.reponses[i_r32] == null ? null : ctx_r28.results.reponses[i_r32].estCorrecte);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.results.reponses[i_r32] && !(ctx_r28.results.reponses[i_r32] == null ? null : ctx_r28.results.reponses[i_r32].estCorrecte));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(question_r31.texte);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", question_r31.options);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"success\": a0,\n    \"fail\": a1\n  };\n};\nconst _c3 = function (a1) {\n  return [\"/courses\", a1];\n};\nfunction QuizComponent_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-card\", 33)(2, \"mat-card-header\", 34)(3, \"div\", 35);\n    i0.ɵɵtemplate(4, QuizComponent_ng_template_2_div_1_mat_icon_4_Template, 2, 0, \"mat-icon\", 26);\n    i0.ɵɵtemplate(5, QuizComponent_ng_template_2_div_1_mat_icon_5_Template, 2, 0, \"mat-icon\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-title\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-card-content\", 36)(11, \"div\", 37)(12, \"span\", 38);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 39);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"mat-progress-bar\", 40);\n    i0.ɵɵtemplate(17, QuizComponent_ng_template_2_div_1_div_17_Template, 5, 0, \"div\", 41);\n    i0.ɵɵelementStart(18, \"div\", 42)(19, \"button\", 43);\n    i0.ɵɵtext(20, \" Retour au cours \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, QuizComponent_ng_template_2_div_1_button_21_Template, 2, 0, \"button\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 45)(23, \"h3\");\n    i0.ɵɵtext(24, \"D\\u00E9tail des r\\u00E9ponses :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, QuizComponent_ng_template_2_div_1_div_25_Template, 10, 5, \"div\", 46);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(13, _c2, ctx_r23.results.reussi, !ctx_r23.results.reussi));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.results.reussi);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r23.results.reussi);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r23.results.reussi ? \"F\\u00E9licitations !\" : \"Quiz non r\\u00E9ussi\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.results.reussi ? \"Vous avez r\\u00E9ussi le quiz avec succ\\u00E8s !\" : \"Il vous faut \" + ctx_r23.quiz.seuilReussite + \"% pour r\\u00E9ussir ce quiz.\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r23.results.pourcentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r23.results.score, \" sur \", ctx_r23.results.totalQuestions, \" questions correctes\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r23.results.pourcentage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.results.reussi);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(16, _c3, ctx_r23.quiz.coursId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r23.results.reussi);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.quiz.questions);\n  }\n}\nfunction QuizComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, QuizComponent_ng_template_2_div_0_Template, 4, 0, \"div\", 29);\n    i0.ɵɵtemplate(1, QuizComponent_ng_template_2_div_1_Template, 26, 18, \"div\", 30);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading && ctx_r2.showResults && ctx_r2.results);\n  }\n}\nexport class QuizComponent {\n  constructor(route, router, quizService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.quizService = quizService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.currentQuestionIndex = 0;\n    this.selectedAnswers = {}; // { questionId: selectedOptionIndex }\n    this.showResults = false;\n    this.results = null;\n    this.isLoading = true;\n    this.timeRemaining = 0;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.route.paramMap.subscribe(params => {\n      this.quizId = Number(params.get(\"id\"));\n      this.loadQuiz();\n    });\n  }\n  ngOnDestroy() {\n    if (this.timerSubscription) {\n      this.timerSubscription.unsubscribe();\n    }\n  }\n  loadQuiz() {\n    this.isLoading = true;\n    // Mock data for demonstration\n    this.quiz = {\n      id: this.quizId,\n      titre: \"Quiz - Bases de React\",\n      description: \"Testez vos connaissances sur les concepts fondamentaux de React\",\n      seuilReussite: 70,\n      dureeEstimee: 15,\n      coursId: 1,\n      typeContenu: \"Quiz\",\n      estComplete: false,\n      estDebloque: true,\n      ordre: 3,\n      questions: [{\n        id: 1,\n        texte: \"Qu'est-ce que React ?\",\n        options: [\"Un framework CSS\", \"Une bibliothèque JavaScript pour créer des interfaces utilisateur\", \"Un serveur web\", \"Un langage de programmation\"],\n        bonneReponse: 1\n      }, {\n        id: 2,\n        texte: \"Que sont les props en React ?\",\n        options: [\"Des propriétés passées aux composants\", \"Des méthodes de classe\", \"Des variables globales\", \"Des styles CSS\"],\n        bonneReponse: 0\n      }, {\n        id: 3,\n        texte: \"Comment créer un composant fonctionnel en React ?\",\n        options: [\"class MyComponent extends React.Component\", \"function MyComponent() { return <div></div>; }\", \"const MyComponent = React.createClass()\", \"React.component('MyComponent')\"],\n        bonneReponse: 1\n      }, {\n        id: 4,\n        texte: \"Qu'est-ce que le JSX ?\",\n        options: [\"Un nouveau langage de programmation\", \"Une extension de syntaxe JavaScript\", \"Un framework CSS\", \"Une base de données\"],\n        bonneReponse: 1\n      }, {\n        id: 5,\n        texte: \"Comment gérer l'état dans un composant fonctionnel ?\",\n        options: [\"Avec this.state\", \"Avec le hook useState\", \"Avec des variables globales\", \"Avec localStorage\"],\n        bonneReponse: 1\n      }]\n    };\n    this.timeRemaining = this.quiz.dureeEstimee * 60; // Convert minutes to seconds\n    this.startTimer();\n    this.isLoading = false;\n    // Uncomment to fetch from API\n    /*\n    this.quizService.getQuizById(this.quizId).subscribe({\n      next: (data) => {\n        this.quiz = data;\n        this.timeRemaining = this.quiz.dureeEstimee * 60;\n        this.startTimer();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  startTimer() {\n    this.timerSubscription = interval(1000).subscribe(() => {\n      if (this.timeRemaining > 0 && !this.showResults) {\n        this.timeRemaining--;\n      } else if (this.timeRemaining === 0 && !this.showResults) {\n        this.submitQuiz();\n        this.timerSubscription.unsubscribe();\n      }\n    });\n  }\n  previousQuestion() {\n    if (this.currentQuestionIndex > 0) {\n      this.currentQuestionIndex--;\n    }\n  }\n  nextQuestion() {\n    if (this.currentQuestionIndex < this.quiz.questions.length - 1) {\n      this.currentQuestionIndex++;\n    }\n  }\n  goToQuestion(index) {\n    this.currentQuestionIndex = index;\n  }\n  submitQuiz() {\n    if (!this.quiz || !this.currentUser) return;\n    this.isLoading = true;\n    this.timerSubscription.unsubscribe();\n    let score = 0;\n    const reponses = [];\n    this.quiz.questions.forEach(question => {\n      const selectedAnswer = this.selectedAnswers[question.id];\n      const isCorrect = selectedAnswer === question.bonneReponse;\n      if (isCorrect) {\n        score++;\n      }\n      reponses.push({\n        questionId: question.id,\n        reponseChoisie: selectedAnswer ?? -1,\n        estCorrecte: isCorrect\n      });\n    });\n    const pourcentage = Math.round(score / this.quiz.questions.length * 100);\n    const reussi = pourcentage >= (this.quiz.seuilReussite || 0);\n    const resultatQuiz = {\n      clientId: this.currentUser.id,\n      quizId: this.quiz.id,\n      score: pourcentage,\n      dateSoumission: new Date()\n    };\n    // Mock submission\n    this.results = {\n      score: score,\n      totalQuestions: this.quiz.questions.length,\n      pourcentage: pourcentage,\n      reussi: reussi,\n      reponses: reponses\n    }; // Cast to any because ResultatQuiz doesn't have all these properties\n    this.showResults = true;\n    this.isLoading = false;\n    // Uncomment to submit to API\n    /*\n    this.quizService.soumettreResultat(this.quiz.id, resultatQuiz).subscribe({\n      next: (res) => {\n        this.results = {\n          score: score,\n          totalQuestions: this.quiz.questions.length,\n          pourcentage: pourcentage,\n          reussi: reussi,\n          reponses: reponses\n        } as any; // Cast to any because ResultatQuiz doesn't have all these properties\n        this.showResults = true;\n        this.isLoading = false;\n        this.snackBar.open('Résultat du quiz enregistré !', 'Fermer', { duration: 3000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de la soumission du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n      }\n    });\n    */\n  }\n\n  restartQuiz() {\n    this.currentQuestionIndex = 0;\n    this.selectedAnswers = {};\n    this.showResults = false;\n    this.results = null;\n    this.timeRemaining = this.quiz.dureeEstimee * 60;\n    this.startTimer();\n  }\n  formatTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n  }\n  get progress() {\n    return (this.currentQuestionIndex + 1) / this.quiz.questions.length * 100;\n  }\n  static {\n    this.ɵfac = function QuizComponent_Factory(t) {\n      return new (t || QuizComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.QuizService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuizComponent,\n      selectors: [[\"app-quiz\"]],\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"quiz-container\"], [\"class\", \"quiz-wrapper\", 4, \"ngIf\", \"ngIfElse\"], [\"resultsOrLoading\", \"\"], [1, \"quiz-wrapper\"], [1, \"quiz-header\"], [1, \"title-row\"], [1, \"timer\"], [1, \"time-display\"], [1, \"progress-row\"], [1, \"question-count\"], [1, \"threshold\"], [\"mode\", \"determinate\", 3, \"value\"], [1, \"question-card\"], [\"aria-label\", \"S\\u00E9lectionnez une option\", 1, \"options-group\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"option-item\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"navigation-buttons\"], [\"mat-stroked-button\", \"\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"overview-card\"], [1, \"question-dots\"], [\"mat-mini-fab\", \"\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"option-item\", 3, \"value\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [\"diameter\", \"20\"], [\"mat-mini-fab\", \"\", 3, \"ngClass\", \"click\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [\"class\", \"results-container\", 4, \"ngIf\"], [1, \"loading-spinner\"], [1, \"results-container\"], [1, \"results-card\"], [1, \"results-header\"], [1, \"result-icon-wrapper\", 3, \"ngClass\"], [1, \"results-content\"], [1, \"score-display\"], [1, \"percentage\"], [1, \"score-count\"], [\"mode\", \"determinate\", 1, \"results-progress\", 3, \"value\"], [\"class\", \"certificate-info\", 4, \"ngIf\"], [1, \"results-actions\"], [\"mat-stroked-button\", \"\", 3, \"routerLink\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [1, \"answers-detail\"], [\"class\", \"answer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"certificate-info\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"answer-item\"], [1, \"answer-header\"], [\"class\", \"correct-icon\", 4, \"ngIf\"], [\"class\", \"incorrect-icon\", 4, \"ngIf\"], [1, \"question-text\"], [1, \"options-detail\"], [\"class\", \"option-detail-item\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"correct-icon\"], [1, \"incorrect-icon\"], [1, \"option-detail-item\", 3, \"ngClass\"], [\"class\", \"correct-label\", 4, \"ngIf\"], [\"class\", \"incorrect-label\", 4, \"ngIf\"], [1, \"correct-label\"], [1, \"incorrect-label\"]],\n      template: function QuizComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, QuizComponent_div_1_Template, 33, 13, \"div\", 1);\n          i0.ɵɵtemplate(2, QuizComponent_ng_template_2_Template, 2, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.quiz && !ctx.showResults)(\"ngIfElse\", _r1);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.NgControlStatus, i6.NgModel, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatButton, i8.MatMiniFabButton, i9.MatIcon, i10.MatProgressBar, i11.MatRadioGroup, i11.MatRadioButton, i12.MatProgressSpinner, i1.RouterLink],\n      styles: [\".quiz-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n}\\n\\n.quiz-wrapper[_ngcontent-%COMP%], .results-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 800px;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem;\\n}\\n\\n.quiz-header[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.title-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n\\n.quiz-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: bold;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n.timer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #d32f2f; \\n\\n  font-weight: 500;\\n}\\n\\n.timer[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.3rem;\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.time-display[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-family: \\\"monospace\\\";\\n}\\n\\n.progress-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 0.9rem;\\n  color: #666;\\n  margin-bottom: 0.8rem;\\n}\\n\\nmat-progress-bar[_ngcontent-%COMP%] {\\n  height: 8px;\\n  border-radius: 4px;\\n}\\n\\n.question-card[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding: 1.5rem;\\n}\\n\\n.question-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  margin-bottom: 1.5rem;\\n  color: #333;\\n}\\n\\n.options-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.option-item[_ngcontent-%COMP%] {\\n  padding: 0.8rem 1rem;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\\n}\\n\\n.option-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #bbb;\\n}\\n\\n.option-item.mat-radio-checked[_ngcontent-%COMP%] {\\n  border-color: #673ab7; \\n\\n  background-color: #ede7f6; \\n\\n}\\n\\n.navigation-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 2rem;\\n}\\n\\n.navigation-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.8rem 1.5rem;\\n  font-size: 1rem;\\n}\\n\\n.overview-card[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n\\n.overview-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n}\\n\\n.question-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n\\n.question-dots[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #e0e0e0; \\n\\n  color: #666;\\n  font-weight: 500;\\n  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;\\n}\\n\\n.question-dots[_ngcontent-%COMP%]   button.current[_ngcontent-%COMP%] {\\n  background-color: #673ab7; \\n\\n  color: white;\\n}\\n\\n.question-dots[_ngcontent-%COMP%]   button.answered[_ngcontent-%COMP%] {\\n  background-color: #c8e6c9; \\n\\n  color: #388e3c; \\n\\n}\\n\\n\\n\\n.results-card[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  text-align: center;\\n}\\n\\n.results-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.result-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 1rem;\\n}\\n\\n.result-icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n.result-icon-wrapper.success[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9; \\n\\n}\\n\\n.result-icon-wrapper.success[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50; \\n\\n}\\n\\n.result-icon-wrapper.fail[_ngcontent-%COMP%] {\\n  background-color: #ffebee; \\n\\n}\\n\\n.result-icon-wrapper.fail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #f44336; \\n\\n}\\n\\n.results-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.results-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #666;\\n}\\n\\n.score-display[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.percentage[_ngcontent-%COMP%] {\\n  font-size: 3.5rem;\\n  font-weight: bold;\\n  color: #673ab7; \\n\\n}\\n\\n.score-count[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #666;\\n}\\n\\n.results-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.certificate-info[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 8px;\\n  padding: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.8rem;\\n  color: #388e3c;\\n  font-weight: 500;\\n  margin-bottom: 2rem;\\n}\\n\\n.certificate-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  width: 1.8rem;\\n  height: 1.8rem;\\n}\\n\\n.results-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.results-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.8rem 1.5rem;\\n  font-size: 1rem;\\n}\\n\\n.answers-detail[_ngcontent-%COMP%] {\\n  text-align: left;\\n  margin-top: 2rem;\\n  border-top: 1px solid #eee;\\n  padding-top: 2rem;\\n}\\n\\n.answers-detail[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 1.5rem;\\n  color: #333;\\n}\\n\\n.answer-item[_ngcontent-%COMP%] {\\n  border: 1px solid #eee;\\n  border-radius: 8px;\\n  padding: 1.2rem;\\n  margin-bottom: 1rem;\\n  background-color: #fff;\\n}\\n\\n.answer-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.8rem;\\n}\\n\\n.answer-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n  color: #444;\\n  margin: 0;\\n}\\n\\n.correct-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.incorrect-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.question-text[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #333;\\n  margin-bottom: 1rem;\\n}\\n\\n.options-detail[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.6rem;\\n}\\n\\n.option-detail-item[_ngcontent-%COMP%] {\\n  padding: 0.6rem 1rem;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n  color: #555;\\n  background-color: #f9f9f9;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.correct-option[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #388e3c;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.user-incorrect-option[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #d32f2f;\\n  border: 1px solid #ffcdd2;\\n}\\n\\n.correct-label[_ngcontent-%COMP%] {\\n  color: #388e3c;\\n  font-weight: 500;\\n}\\n\\n.incorrect-label[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-weight: 500;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["interval", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r8", "ɵɵadvance", "ɵɵtextInterpolate1", "option_r7", "ɵɵlistener", "QuizComponent_div_1_button_25_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "nextQuestion", "ctx_r4", "selectedAnswer<PERSON>", "quiz", "questions", "currentQuestionIndex", "id", "undefined", "ɵɵelement", "QuizComponent_div_1_button_26_Template_button_click_0_listener", "_r14", "ctx_r13", "submitQuiz", "ɵɵtemplate", "QuizComponent_div_1_button_26_mat_spinner_1_Template", "QuizComponent_div_1_button_26_span_2_Template", "ctx_r5", "isLoading", "QuizComponent_div_1_button_32_Template_button_click_0_listener", "restoredCtx", "_r18", "i_r16", "index", "ctx_r17", "goToQuestion", "ɵɵpureFunction2", "_c0", "ctx_r6", "q_r15", "QuizComponent_div_1_Template_mat_radio_group_ngModelChange_20_listener", "$event", "_r20", "ctx_r19", "QuizComponent_div_1_mat_radio_button_21_Template", "QuizComponent_div_1_Template_button_click_23_listener", "ctx_r21", "previousQuestion", "QuizComponent_div_1_button_25_Template", "QuizComponent_div_1_button_26_Template", "QuizComponent_div_1_button_32_Template", "ɵɵtextInterpolate", "ctx_r0", "titre", "formatTime", "timeRemaining", "ɵɵtextInterpolate2", "length", "<PERSON>uil<PERSON><PERSON><PERSON>", "progress", "texte", "options", "QuizComponent_ng_template_2_div_1_button_21_Template_button_click_0_listener", "_r30", "ctx_r29", "restartQuiz", "QuizComponent_ng_template_2_div_1_div_25_div_9_span_2_Template", "QuizComponent_ng_template_2_div_1_div_25_div_9_span_3_Template", "_c1", "j_r37", "question_r31", "bonneReponse", "ctx_r35", "results", "reponses", "i_r32", "reponseChoisie", "estCorrecte", "option_r36", "QuizComponent_ng_template_2_div_1_div_25_mat_icon_4_Template", "QuizComponent_ng_template_2_div_1_div_25_mat_icon_5_Template", "QuizComponent_ng_template_2_div_1_div_25_div_9_Template", "ctx_r28", "QuizComponent_ng_template_2_div_1_mat_icon_4_Template", "QuizComponent_ng_template_2_div_1_mat_icon_5_Template", "QuizComponent_ng_template_2_div_1_div_17_Template", "QuizComponent_ng_template_2_div_1_button_21_Template", "QuizComponent_ng_template_2_div_1_div_25_Template", "_c2", "ctx_r23", "<PERSON><PERSON><PERSON>", "pourcentage", "score", "totalQuestions", "ɵɵpureFunction1", "_c3", "coursId", "QuizComponent_ng_template_2_div_0_Template", "QuizComponent_ng_template_2_div_1_Template", "ctx_r2", "showResults", "QuizComponent", "constructor", "route", "router", "quizService", "authService", "snackBar", "ngOnInit", "currentUser$", "subscribe", "user", "currentUser", "paramMap", "params", "quizId", "Number", "get", "loadQuiz", "ngOnDestroy", "timerSubscription", "unsubscribe", "description", "du<PERSON><PERSON><PERSON><PERSON>", "typeContenu", "estComplete", "estDebloque", "ordre", "startTimer", "for<PERSON>ach", "question", "<PERSON><PERSON><PERSON><PERSON>", "isCorrect", "push", "questionId", "Math", "round", "resultatQuiz", "clientId", "dateSoumission", "Date", "seconds", "minutes", "floor", "remainingSeconds", "toString", "padStart", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "QuizService", "i3", "AuthService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "QuizComponent_Template", "rf", "ctx", "QuizComponent_div_1_Template", "QuizComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor", "_r1"], "sources": ["C:\\e-learning\\src\\app\\features\\quiz\\quiz.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@angular/core\"\nimport { ActivatedRoute, Router } from \"@angular/router\"\nimport { QuizService } from \"../../core/services/quiz.service\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Quiz } from \"../../core/models/course.model\"\nimport { ResultatQuiz } from \"../../core/models/resultat-quiz.model\"\nimport { AuthService } from \"../../core/services/auth.service\"\nimport { interval, Subscription } from \"rxjs\"\nimport { User } from \"../../core/models/user.model\" // Import User model\n\n@Component({\n  selector: \"app-quiz\",\n  template: `\n    <div class=\"quiz-container\">\n      <div class=\"quiz-wrapper\" *ngIf=\"quiz && !showResults; else resultsOrLoading\">\n        <!-- Header -->\n        <div class=\"quiz-header\">\n          <div class=\"title-row\">\n            <h1>{{ quiz.titre }}</h1>\n            <div class=\"timer\">\n              <mat-icon>timer</mat-icon>\n              <span class=\"time-display\">{{ formatTime(timeRemaining) }}</span>\n            </div>\n          </div>\n\n          <div class=\"progress-row\">\n            <span class=\"question-count\">Question {{ currentQuestionIndex + 1 }} sur {{ quiz.questions.length }}</span>\n            <span class=\"threshold\">Seuil de réussite: {{ quiz.seuilReussite }}%</span>\n          </div>\n          <mat-progress-bar mode=\"determinate\" [value]=\"progress\"></mat-progress-bar>\n        </div>\n\n        <!-- Question Card -->\n        <mat-card class=\"question-card\">\n          <mat-card-title>{{ quiz.questions[currentQuestionIndex].texte }}</mat-card-title>\n          <mat-card-content>\n            <mat-radio-group \n              aria-label=\"Sélectionnez une option\" \n              [(ngModel)]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id]\"\n              class=\"options-group\">\n              <mat-radio-button \n                *ngFor=\"let option of quiz.questions[currentQuestionIndex].options; let i = index\" \n                [value]=\"i\" \n                class=\"option-item\">\n                {{ option }}\n              </mat-radio-button>\n            </mat-radio-group>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Navigation Buttons -->\n        <div class=\"navigation-buttons\">\n          <button mat-stroked-button (click)=\"previousQuestion()\" [disabled]=\"currentQuestionIndex === 0\">\n            Question précédente\n          </button>\n\n          <button mat-raised-button color=\"primary\" \n                  (click)=\"nextQuestion()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined\"\n                  *ngIf=\"currentQuestionIndex < quiz.questions.length - 1\">\n            Question suivante\n          </button>\n\n          <button mat-raised-button color=\"accent\" \n                  (click)=\"submitQuiz()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined || isLoading\"\n                  *ngIf=\"currentQuestionIndex === quiz.questions.length - 1\">\n            <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n            <span *ngIf=\"!isLoading\">Terminer le quiz</span>\n          </button>\n        </div>\n\n        <!-- Questions Overview -->\n        <mat-card class=\"overview-card\">\n          <mat-card-title>Aperçu des questions</mat-card-title>\n          <mat-card-content>\n            <div class=\"question-dots\">\n              <button mat-mini-fab *ngFor=\"let q of quiz.questions; let i = index\" \n                      [ngClass]=\"{\n                        'current': i === currentQuestionIndex,\n                        'answered': selectedAnswers[q.id] !== undefined && i !== currentQuestionIndex\n                      }\"\n                      (click)=\"goToQuestion(i)\">\n                {{ i + 1 }}\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #resultsOrLoading>\n        <div *ngIf=\"isLoading\" class=\"loading-spinner\">\n          <mat-spinner></mat-spinner>\n          <p>Chargement du quiz...</p>\n        </div>\n        <div *ngIf=\"!isLoading && showResults && results\" class=\"results-container\">\n          <mat-card class=\"results-card\">\n            <mat-card-header class=\"results-header\">\n              <div class=\"result-icon-wrapper\" [ngClass]=\"{'success': results.reussi, 'fail': !results.reussi}\">\n                <mat-icon *ngIf=\"results.reussi\">check_circle</mat-icon>\n                <mat-icon *ngIf=\"!results.reussi\">cancel</mat-icon>\n              </div>\n              <mat-card-title>{{ results.reussi ? 'Félicitations !' : 'Quiz non réussi' }}</mat-card-title>\n              <mat-card-subtitle>\n                {{ results.reussi ? 'Vous avez réussi le quiz avec succès !' : 'Il vous faut ' + quiz.seuilReussite + '% pour réussir ce quiz.' }}\n              </mat-card-subtitle>\n            </mat-card-header>\n\n            <mat-card-content class=\"results-content\">\n              <div class=\"score-display\">\n                <span class=\"percentage\">{{ results.pourcentage }}%</span>\n                <p class=\"score-count\">{{ results.score }} sur {{ results.totalQuestions }} questions correctes</p>\n              </div>\n\n              <mat-progress-bar mode=\"determinate\" [value]=\"results.pourcentage\" class=\"results-progress\"></mat-progress-bar>\n\n              <div class=\"certificate-info\" *ngIf=\"results.reussi\">\n                <mat-icon>emoji_events</mat-icon>\n                <p>Un certificat sera généré automatiquement !</p>\n              </div>\n\n              <div class=\"results-actions\">\n                <button mat-stroked-button [routerLink]=\"['/courses', quiz.coursId]\">\n                  Retour au cours\n                </button>\n                <button mat-raised-button color=\"primary\" *ngIf=\"!results.reussi\" (click)=\"restartQuiz()\">\n                  Recommencer\n                </button>\n              </div>\n\n              <div class=\"answers-detail\">\n                <h3>Détail des réponses :</h3>\n                <div *ngFor=\"let question of quiz.questions; let i = index\" class=\"answer-item\">\n                  <div class=\"answer-header\">\n                    <h4>Question {{ i + 1 }}</h4>\n                    <mat-icon *ngIf=\"results.reponses[i]?.estCorrecte\" class=\"correct-icon\">check_circle</mat-icon>\n                    <mat-icon *ngIf=\"results.reponses[i] && !results.reponses[i]?.estCorrecte\" class=\"incorrect-icon\">cancel</mat-icon>\n                  </div>\n                  <p class=\"question-text\">{{ question.texte }}</p>\n                  <div class=\"options-detail\">\n                    <div *ngFor=\"let option of question.options; let j = index\" \n                         [ngClass]=\"{\n                           'correct-option': j === question.bonneReponse,\n                           'user-incorrect-option': j === results.reponses[i]?.reponseChoisie && !results.reponses[i]?.estCorrecte\n                         }\"\n                         class=\"option-detail-item\">\n                      {{ option }}\n                      <span *ngIf=\"j === question.bonneReponse\" class=\"correct-label\">✓ Bonne réponse</span>\n                      <span *ngIf=\"j === results.reponses[i]?.reponseChoisie && !results.reponses[i]?.estCorrecte\" class=\"incorrect-label\">✗ Votre réponse</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [\n    `\n    .quiz-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .quiz-wrapper, .results-container {\n      width: 100%;\n      max-width: 800px;\n    }\n\n    .loading-spinner {\n      text-align: center;\n      padding: 4rem;\n    }\n\n    .quiz-header {\n      margin-bottom: 2rem;\n      background-color: #fff;\n      padding: 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n    }\n\n    .title-row {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .quiz-header h1 {\n      font-size: 1.8rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .timer {\n      display: flex;\n      align-items: center;\n      color: #d32f2f; /* Red */\n      font-weight: 500;\n    }\n\n    .timer mat-icon {\n      margin-right: 0.3rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .time-display {\n      font-size: 1.5rem;\n      font-family: 'monospace';\n    }\n\n    .progress-row {\n      display: flex;\n      justify-content: space-between;\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.8rem;\n    }\n\n    mat-progress-bar {\n      height: 8px;\n      border-radius: 4px;\n    }\n\n    .question-card {\n      margin-bottom: 2rem;\n      padding: 1.5rem;\n    }\n\n    .question-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .options-group {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .option-item {\n      padding: 0.8rem 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n    }\n\n    .option-item:hover {\n      background-color: #f5f5f5;\n      border-color: #bbb;\n    }\n\n    .option-item.mat-radio-checked {\n      border-color: #673ab7; /* Purple */\n      background-color: #ede7f6; /* Light purple */\n    }\n\n    .navigation-buttons {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 2rem;\n    }\n\n    .navigation-buttons button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .overview-card {\n      padding: 1.5rem;\n    }\n\n    .overview-card mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .question-dots {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n    }\n\n    .question-dots button {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #e0e0e0; /* Light gray */\n      color: #666;\n      font-weight: 500;\n      transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;\n    }\n\n    .question-dots button.current {\n      background-color: #673ab7; /* Purple */\n      color: white;\n    }\n\n    .question-dots button.answered {\n      background-color: #c8e6c9; /* Light green */\n      color: #388e3c; /* Dark green */\n    }\n\n    /* Results styles */\n    .results-card {\n      padding: 2rem;\n      text-align: center;\n    }\n\n    .results-header {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .result-icon-wrapper {\n      width: 64px;\n      height: 64px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 1rem;\n    }\n\n    .result-icon-wrapper mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n    }\n\n    .result-icon-wrapper.success {\n      background-color: #e8f5e9; /* Light green */\n    }\n    .result-icon-wrapper.success mat-icon {\n      color: #4caf50; /* Green */\n    }\n\n    .result-icon-wrapper.fail {\n      background-color: #ffebee; /* Light red */\n    }\n    .result-icon-wrapper.fail mat-icon {\n      color: #f44336; /* Red */\n    }\n\n    .results-card mat-card-title {\n      font-size: 2rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n    }\n\n    .results-card mat-card-subtitle {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .score-display {\n      margin-bottom: 1.5rem;\n    }\n\n    .percentage {\n      font-size: 3.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .score-count {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .results-progress {\n      margin-bottom: 1.5rem;\n    }\n\n    .certificate-info {\n      background-color: #e8f5e9;\n      border: 1px solid #c8e6c9;\n      border-radius: 8px;\n      padding: 1rem;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 0.8rem;\n      color: #388e3c;\n      font-weight: 500;\n      margin-bottom: 2rem;\n    }\n\n    .certificate-info mat-icon {\n      font-size: 1.8rem;\n      width: 1.8rem;\n      height: 1.8rem;\n    }\n\n    .results-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 2rem;\n    }\n\n    .results-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .answers-detail {\n      text-align: left;\n      margin-top: 2rem;\n      border-top: 1px solid #eee;\n      padding-top: 2rem;\n    }\n\n    .answers-detail h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .answer-item {\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 1.2rem;\n      margin-bottom: 1rem;\n      background-color: #fff;\n    }\n\n    .answer-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 0.8rem;\n    }\n\n    .answer-header h4 {\n      font-size: 1.1rem;\n      font-weight: 500;\n      color: #444;\n      margin: 0;\n    }\n\n    .correct-icon { color: #4caf50; }\n    .incorrect-icon { color: #f44336; }\n\n    .question-text {\n      font-size: 1rem;\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    .options-detail {\n      display: flex;\n      flex-direction: column;\n      gap: 0.6rem;\n    }\n\n    .option-detail-item {\n      padding: 0.6rem 1rem;\n      border-radius: 6px;\n      font-size: 0.9rem;\n      color: #555;\n      background-color: #f9f9f9;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .correct-option {\n      background-color: #e8f5e9;\n      color: #388e3c;\n      border: 1px solid #c8e6c9;\n    }\n\n    .user-incorrect-option {\n      background-color: #ffebee;\n      color: #d32f2f;\n      border: 1px solid #ffcdd2;\n    }\n\n    .correct-label {\n      color: #388e3c;\n      font-weight: 500;\n    }\n\n    .incorrect-label {\n      color: #d32f2f;\n      font-weight: 500;\n    }\n  `,\n  ],\n})\nexport class QuizComponent implements OnInit, OnDestroy {\n  quizId!: number\n  quiz!: Quiz\n  currentQuestionIndex = 0\n  selectedAnswers: { [key: number]: number } = {} // { questionId: selectedOptionIndex }\n  showResults = false\n  results: ResultatQuiz | null = null\n  isLoading = true\n  timeRemaining = 0\n  timerSubscription!: Subscription\n  currentUser!: User | null\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private quizService: QuizService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n    })\n\n    this.route.paramMap.subscribe((params) => {\n      this.quizId = Number(params.get(\"id\"))\n      this.loadQuiz()\n    })\n  }\n\n  ngOnDestroy(): void {\n    if (this.timerSubscription) {\n      this.timerSubscription.unsubscribe()\n    }\n  }\n\n  loadQuiz(): void {\n    this.isLoading = true\n    // Mock data for demonstration\n    this.quiz = {\n      id: this.quizId,\n      titre: \"Quiz - Bases de React\",\n      description: \"Testez vos connaissances sur les concepts fondamentaux de React\",\n      seuilReussite: 70,\n      dureeEstimee: 15, // in minutes\n      coursId: 1,\n      typeContenu: \"Quiz\",\n      estComplete: false,\n      estDebloque: true,\n      ordre: 3,\n      questions: [\n        {\n          id: 1,\n          texte: \"Qu'est-ce que React ?\",\n          options: [\n            \"Un framework CSS\",\n            \"Une bibliothèque JavaScript pour créer des interfaces utilisateur\",\n            \"Un serveur web\",\n            \"Un langage de programmation\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 2,\n          texte: \"Que sont les props en React ?\",\n          options: [\n            \"Des propriétés passées aux composants\",\n            \"Des méthodes de classe\",\n            \"Des variables globales\",\n            \"Des styles CSS\",\n          ],\n          bonneReponse: 0,\n        },\n        {\n          id: 3,\n          texte: \"Comment créer un composant fonctionnel en React ?\",\n          options: [\n            \"class MyComponent extends React.Component\",\n            \"function MyComponent() { return <div></div>; }\",\n            \"const MyComponent = React.createClass()\",\n            \"React.component('MyComponent')\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 4,\n          texte: \"Qu'est-ce que le JSX ?\",\n          options: [\n            \"Un nouveau langage de programmation\",\n            \"Une extension de syntaxe JavaScript\",\n            \"Un framework CSS\",\n            \"Une base de données\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 5,\n          texte: \"Comment gérer l'état dans un composant fonctionnel ?\",\n          options: [\"Avec this.state\", \"Avec le hook useState\", \"Avec des variables globales\", \"Avec localStorage\"],\n          bonneReponse: 1,\n        },\n      ],\n    }\n    this.timeRemaining = this.quiz.dureeEstimee * 60 // Convert minutes to seconds\n    this.startTimer()\n    this.isLoading = false\n\n    // Uncomment to fetch from API\n    /*\n    this.quizService.getQuizById(this.quizId).subscribe({\n      next: (data) => {\n        this.quiz = data;\n        this.timeRemaining = this.quiz.dureeEstimee * 60;\n        this.startTimer();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  startTimer(): void {\n    this.timerSubscription = interval(1000).subscribe(() => {\n      if (this.timeRemaining > 0 && !this.showResults) {\n        this.timeRemaining--\n      } else if (this.timeRemaining === 0 && !this.showResults) {\n        this.submitQuiz()\n        this.timerSubscription.unsubscribe()\n      }\n    })\n  }\n\n  previousQuestion(): void {\n    if (this.currentQuestionIndex > 0) {\n      this.currentQuestionIndex--\n    }\n  }\n\n  nextQuestion(): void {\n    if (this.currentQuestionIndex < this.quiz.questions.length - 1) {\n      this.currentQuestionIndex++\n    }\n  }\n\n  goToQuestion(index: number): void {\n    this.currentQuestionIndex = index\n  }\n\n  submitQuiz(): void {\n    if (!this.quiz || !this.currentUser) return\n\n    this.isLoading = true\n    this.timerSubscription.unsubscribe()\n\n    let score = 0\n    const reponses: { questionId: number; reponseChoisie: number; estCorrecte: boolean }[] = []\n\n    this.quiz.questions.forEach((question) => {\n      const selectedAnswer = this.selectedAnswers[question.id]\n      const isCorrect = selectedAnswer === question.bonneReponse\n      if (isCorrect) {\n        score++\n      }\n      reponses.push({\n        questionId: question.id,\n        reponseChoisie: selectedAnswer ?? -1,\n        estCorrecte: isCorrect,\n      })\n    })\n\n    const pourcentage = Math.round((score / this.quiz.questions.length) * 100)\n    const reussi = pourcentage >= (this.quiz.seuilReussite || 0)\n\n    const resultatQuiz: ResultatQuiz = {\n      clientId: this.currentUser.id,\n      quizId: this.quiz.id,\n      score: pourcentage, // Store percentage as score\n      dateSoumission: new Date(),\n    }\n\n    // Mock submission\n    this.results = {\n      score: score,\n      totalQuestions: this.quiz.questions.length,\n      pourcentage: pourcentage,\n      reussi: reussi,\n      reponses: reponses,\n    } as any // Cast to any because ResultatQuiz doesn't have all these properties\n    this.showResults = true\n    this.isLoading = false\n\n    // Uncomment to submit to API\n    /*\n    this.quizService.soumettreResultat(this.quiz.id, resultatQuiz).subscribe({\n      next: (res) => {\n        this.results = {\n          score: score,\n          totalQuestions: this.quiz.questions.length,\n          pourcentage: pourcentage,\n          reussi: reussi,\n          reponses: reponses\n        } as any; // Cast to any because ResultatQuiz doesn't have all these properties\n        this.showResults = true;\n        this.isLoading = false;\n        this.snackBar.open('Résultat du quiz enregistré !', 'Fermer', { duration: 3000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de la soumission du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n      }\n    });\n    */\n  }\n\n  restartQuiz(): void {\n    this.currentQuestionIndex = 0\n    this.selectedAnswers = {}\n    this.showResults = false\n    this.results = null\n    this.timeRemaining = this.quiz.dureeEstimee * 60\n    this.startTimer()\n  }\n\n  formatTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60)\n    const remainingSeconds = seconds % 60\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n  }\n\n  get progress(): number {\n    return ((this.currentQuestionIndex + 1) / this.quiz.questions.length) * 100\n  }\n}\n"], "mappings": "AAOA,SAASA,QAAQ,QAAsB,MAAM;;;;;;;;;;;;;;;;IAiC/BC,EAAA,CAAAC,cAAA,2BAGsB;IACpBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAmB;;;;;IAHjBH,EAAA,CAAAI,UAAA,UAAAC,IAAA,CAAW;IAEXL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,SAAA,MACF;;;;;;IAWJR,EAAA,CAAAC,cAAA,iBAGiE;IAFzDD,EAAA,CAAAS,UAAA,mBAAAC,+DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAG9BhB,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHDH,EAAA,CAAAI,UAAA,aAAAa,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,IAAA,CAAAC,SAAA,CAAAH,MAAA,CAAAI,oBAAA,EAAAC,EAAA,MAAAC,SAAA,CAAmF;;;;;IASzFvB,EAAA,CAAAwB,SAAA,sBAA2D;;;;;IAC3DxB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IALlDH,EAAA,CAAAC,cAAA,iBAGmE;IAF3DD,EAAA,CAAAS,UAAA,mBAAAgB,+DAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAY,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAG5B5B,EAAA,CAAA6B,UAAA,IAAAC,oDAAA,0BAA2D;IAC3D9B,EAAA,CAAA6B,UAAA,IAAAE,6CAAA,mBAAgD;IAClD/B,EAAA,CAAAG,YAAA,EAAS;;;;IAJDH,EAAA,CAAAI,UAAA,aAAA4B,MAAA,CAAAd,eAAA,CAAAc,MAAA,CAAAb,IAAA,CAAAC,SAAA,CAAAY,MAAA,CAAAX,oBAAA,EAAAC,EAAA,MAAAC,SAAA,IAAAS,MAAA,CAAAC,SAAA,CAAgG;IAE1EjC,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAI,UAAA,SAAA4B,MAAA,CAAAC,SAAA,CAAe;IACpCjC,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAI,UAAA,UAAA4B,MAAA,CAAAC,SAAA,CAAgB;;;;;;;;;;;;IASrBjC,EAAA,CAAAC,cAAA,iBAKkC;IAA1BD,EAAA,CAAAS,UAAA,mBAAAyB,+DAAA;MAAA,MAAAC,WAAA,GAAAnC,EAAA,CAAAW,aAAA,CAAAyB,IAAA;MAAA,MAAAC,KAAA,GAAAF,WAAA,CAAAG,KAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAwB,OAAA,CAAAC,YAAA,CAAAH,KAAA,CAAe;IAAA,EAAC;IAC/BrC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IANDH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAyC,eAAA,IAAAC,GAAA,EAAAL,KAAA,KAAAM,MAAA,CAAAtB,oBAAA,EAAAsB,MAAA,CAAAzB,eAAA,CAAA0B,KAAA,CAAAtB,EAAA,MAAAC,SAAA,IAAAc,KAAA,KAAAM,MAAA,CAAAtB,oBAAA,EAGE;IAERrB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAA8B,KAAA,UACF;;;;;;IAtERrC,EAAA,CAAAC,cAAA,aAA8E;IAIpED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,aAAmB;IACPD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrEH,EAAA,CAAAC,cAAA,cAA0B;IACKD,EAAA,CAAAE,MAAA,IAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3GH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7EH,EAAA,CAAAwB,SAAA,4BAA2E;IAC7ExB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,oBAAgC;IACdD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjFH,EAAA,CAAAC,cAAA,wBAAkB;IAGdD,EAAA,CAAAS,UAAA,2BAAAoC,uEAAAC,MAAA;MAAA9C,EAAA,CAAAW,aAAA,CAAAoC,IAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAiC,OAAA,CAAA9B,eAAA,CAAA8B,OAAA,CAAA7B,IAAA,CAAAC,SAAA,CAAA4B,OAAA,CAAA3B,oBAAA,EAAAC,EAAA,IAAAwB,MAAA;IAAA,EAAsE;IAEtE9C,EAAA,CAAA6B,UAAA,KAAAoB,gDAAA,+BAKmB;IACrBjD,EAAA,CAAAG,YAAA,EAAkB;IAKtBH,EAAA,CAAAC,cAAA,eAAgC;IACHD,EAAA,CAAAS,UAAA,mBAAAyC,sDAAA;MAAAlD,EAAA,CAAAW,aAAA,CAAAoC,IAAA;MAAA,MAAAI,OAAA,GAAAnD,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAoC,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IACrDpD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAA6B,UAAA,KAAAwB,sCAAA,qBAKS;IAETrD,EAAA,CAAA6B,UAAA,KAAAyB,sCAAA,qBAMS;IACXtD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,oBAAgC;IACdD,EAAA,CAAAE,MAAA,iCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACrDH,EAAA,CAAAC,cAAA,wBAAkB;IAEdD,EAAA,CAAA6B,UAAA,KAAA0B,sCAAA,qBAOS;IACXvD,EAAA,CAAAG,YAAA,EAAM;;;;IAnEFH,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAwD,iBAAA,CAAAC,MAAA,CAAAtC,IAAA,CAAAuC,KAAA,CAAgB;IAGS1D,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAwD,iBAAA,CAAAC,MAAA,CAAAE,UAAA,CAAAF,MAAA,CAAAG,aAAA,EAA+B;IAK/B5D,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAA6D,kBAAA,cAAAJ,MAAA,CAAApC,oBAAA,eAAAoC,MAAA,CAAAtC,IAAA,CAAAC,SAAA,CAAA0C,MAAA,KAAuE;IAC5E9D,EAAA,CAAAM,SAAA,GAA4C;IAA5CN,EAAA,CAAAO,kBAAA,6BAAAkD,MAAA,CAAAtC,IAAA,CAAA4C,aAAA,MAA4C;IAEjC/D,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,UAAAqD,MAAA,CAAAO,QAAA,CAAkB;IAKvChE,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAwD,iBAAA,CAAAC,MAAA,CAAAtC,IAAA,CAAAC,SAAA,CAAAqC,MAAA,CAAApC,oBAAA,EAAA4C,KAAA,CAAgD;IAI5DjE,EAAA,CAAAM,SAAA,GAAsE;IAAtEN,EAAA,CAAAI,UAAA,YAAAqD,MAAA,CAAAvC,eAAA,CAAAuC,MAAA,CAAAtC,IAAA,CAAAC,SAAA,CAAAqC,MAAA,CAAApC,oBAAA,EAAAC,EAAA,EAAsE;IAGjDtB,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAI,UAAA,YAAAqD,MAAA,CAAAtC,IAAA,CAAAC,SAAA,CAAAqC,MAAA,CAAApC,oBAAA,EAAA6C,OAAA,CAAiD;IAWlBlE,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAqD,MAAA,CAAApC,oBAAA,OAAuC;IAOtFrB,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAI,UAAA,SAAAqD,MAAA,CAAApC,oBAAA,GAAAoC,MAAA,CAAAtC,IAAA,CAAAC,SAAA,CAAA0C,MAAA,KAAsD;IAOtD9D,EAAA,CAAAM,SAAA,GAAwD;IAAxDN,EAAA,CAAAI,UAAA,SAAAqD,MAAA,CAAApC,oBAAA,KAAAoC,MAAA,CAAAtC,IAAA,CAAAC,SAAA,CAAA0C,MAAA,KAAwD;IAW1B9D,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAqD,MAAA,CAAAtC,IAAA,CAAAC,SAAA,CAAmB;;;;;IAc5DpB,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAwB,SAAA,kBAA2B;IAC3BxB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMtBH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAgBrDH,EAAA,CAAAC,cAAA,cAAqD;IACzCD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iEAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAOlDH,EAAA,CAAAC,cAAA,iBAA0F;IAAxBD,EAAA,CAAAS,UAAA,mBAAA0D,6EAAA;MAAAnE,EAAA,CAAAW,aAAA,CAAAyD,IAAA;MAAA,MAAAC,OAAA,GAAArE,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAsD,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACvFtE,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAQLH,EAAA,CAAAC,cAAA,mBAAwE;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/FH,EAAA,CAAAC,cAAA,mBAAkG;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAWjHH,EAAA,CAAAC,cAAA,eAAgE;IAAAD,EAAA,CAAAE,MAAA,gCAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtFH,EAAA,CAAAC,cAAA,eAAqH;IAAAD,EAAA,CAAAE,MAAA,gCAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;IAR7IH,EAAA,CAAAC,cAAA,cAKgC;IAC9BD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAA6B,UAAA,IAAA0C,8DAAA,mBAAsF;IACtFvE,EAAA,CAAA6B,UAAA,IAAA2C,8DAAA,mBAA2I;IAC7IxE,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;IARDH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAyC,eAAA,IAAAgC,GAAA,EAAAC,KAAA,KAAAC,YAAA,CAAAC,YAAA,EAAAF,KAAA,MAAAG,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,mBAAAH,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,EAAAC,cAAA,OAAAJ,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,mBAAAH,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,EAAAE,WAAA,GAGE;IAELlF,EAAA,CAAAM,SAAA,GACA;IADAN,EAAA,CAAAO,kBAAA,MAAA4E,UAAA,MACA;IAAOnF,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAsE,KAAA,KAAAC,YAAA,CAAAC,YAAA,CAAiC;IACjC5E,EAAA,CAAAM,SAAA,GAAoF;IAApFN,EAAA,CAAAI,UAAA,SAAAsE,KAAA,MAAAG,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,mBAAAH,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,EAAAC,cAAA,OAAAJ,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,mBAAAH,OAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,EAAAE,WAAA,EAAoF;;;;;IAhBjGlF,EAAA,CAAAC,cAAA,cAAgF;IAExED,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAA6B,UAAA,IAAAuD,4DAAA,uBAA+F;IAC/FpF,EAAA,CAAA6B,UAAA,IAAAwD,4DAAA,uBAAmH;IACrHrF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjDH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAA6B,UAAA,IAAAyD,uDAAA,kBASM;IACRtF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAhBAH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAO,kBAAA,cAAAyE,KAAA,SAAoB;IACbhF,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAI,UAAA,SAAAmF,OAAA,CAAAT,OAAA,CAAAC,QAAA,CAAAC,KAAA,mBAAAO,OAAA,CAAAT,OAAA,CAAAC,QAAA,CAAAC,KAAA,EAAAE,WAAA,CAAsC;IACtClF,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAAI,UAAA,SAAAmF,OAAA,CAAAT,OAAA,CAAAC,QAAA,CAAAC,KAAA,OAAAO,OAAA,CAAAT,OAAA,CAAAC,QAAA,CAAAC,KAAA,mBAAAO,OAAA,CAAAT,OAAA,CAAAC,QAAA,CAAAC,KAAA,EAAAE,WAAA,EAA8D;IAElDlF,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAwD,iBAAA,CAAAmB,YAAA,CAAAV,KAAA,CAAoB;IAEnBjE,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAI,UAAA,YAAAuE,YAAA,CAAAT,OAAA,CAAqB;;;;;;;;;;;;;;IA7CzDlE,EAAA,CAAAC,cAAA,cAA4E;IAIpED,EAAA,CAAA6B,UAAA,IAAA2D,qDAAA,uBAAwD;IACxDxF,EAAA,CAAA6B,UAAA,IAAA4D,qDAAA,uBAAmD;IACrDzF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAC7FH,EAAA,CAAAC,cAAA,wBAAmB;IACjBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAGtBH,EAAA,CAAAC,cAAA,4BAA0C;IAEbD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAwE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGrGH,EAAA,CAAAwB,SAAA,4BAA+G;IAE/GxB,EAAA,CAAA6B,UAAA,KAAA6D,iDAAA,kBAGM;IAEN1F,EAAA,CAAAC,cAAA,eAA6B;IAEzBD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAA6B,UAAA,KAAA8D,oDAAA,qBAES;IACX3F,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA4B;IACtBD,EAAA,CAAAE,MAAA,uCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAA6B,UAAA,KAAA+D,iDAAA,mBAmBM;IACR5F,EAAA,CAAAG,YAAA,EAAM;;;;IAtD2BH,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAyC,eAAA,KAAAoD,GAAA,EAAAC,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,GAAAD,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,EAAgE;IACpF/F,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAA0F,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,CAAoB;IACpB/F,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAI,UAAA,UAAA0F,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,CAAqB;IAElB/F,EAAA,CAAAM,SAAA,GAA4D;IAA5DN,EAAA,CAAAwD,iBAAA,CAAAsC,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,mDAA4D;IAE1E/F,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAuF,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,0EAAAD,OAAA,CAAA3E,IAAA,CAAA4C,aAAA,uCACF;IAK2B/D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAO,kBAAA,KAAAuF,OAAA,CAAAhB,OAAA,CAAAkB,WAAA,MAA0B;IAC5BhG,EAAA,CAAAM,SAAA,GAAwE;IAAxEN,EAAA,CAAA6D,kBAAA,KAAAiC,OAAA,CAAAhB,OAAA,CAAAmB,KAAA,WAAAH,OAAA,CAAAhB,OAAA,CAAAoB,cAAA,yBAAwE;IAG5DlG,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAA0F,OAAA,CAAAhB,OAAA,CAAAkB,WAAA,CAA6B;IAEnChG,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAA0F,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,CAAoB;IAMtB/F,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAmG,eAAA,KAAAC,GAAA,EAAAN,OAAA,CAAA3E,IAAA,CAAAkF,OAAA,EAAyC;IAGzBrG,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAI,UAAA,UAAA0F,OAAA,CAAAhB,OAAA,CAAAiB,MAAA,CAAqB;IAOtC/F,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAA0F,OAAA,CAAA3E,IAAA,CAAAC,SAAA,CAAmB;;;;;IAzCrDpB,EAAA,CAAA6B,UAAA,IAAAyE,0CAAA,kBAGM;IACNtG,EAAA,CAAA6B,UAAA,IAAA0E,0CAAA,oBA4DM;;;;IAhEAvG,EAAA,CAAAI,UAAA,SAAAoG,MAAA,CAAAvE,SAAA,CAAe;IAIfjC,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAI,UAAA,UAAAoG,MAAA,CAAAvE,SAAA,IAAAuE,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAA1B,OAAA,CAA0C;;;AA6ZxD,OAAM,MAAO4B,aAAa;EAYxBC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAdlB,KAAA3F,oBAAoB,GAAG,CAAC;IACxB,KAAAH,eAAe,GAA8B,EAAE,EAAC;IAChD,KAAAuF,WAAW,GAAG,KAAK;IACnB,KAAA3B,OAAO,GAAwB,IAAI;IACnC,KAAA7C,SAAS,GAAG,IAAI;IAChB,KAAA2B,aAAa,GAAG,CAAC;EAUd;EAEHqD,QAAQA,CAAA;IACN,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACC,WAAW,GAAGD,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACR,KAAK,CAACU,QAAQ,CAACH,SAAS,CAAEI,MAAM,IAAI;MACvC,IAAI,CAACC,MAAM,GAAGC,MAAM,CAACF,MAAM,CAACG,GAAG,CAAC,IAAI,CAAC,CAAC;MACtC,IAAI,CAACC,QAAQ,EAAE;IACjB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACC,WAAW,EAAE;;EAExC;EAEAH,QAAQA,CAAA;IACN,IAAI,CAAC1F,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACd,IAAI,GAAG;MACVG,EAAE,EAAE,IAAI,CAACkG,MAAM;MACf9D,KAAK,EAAE,uBAAuB;MAC9BqE,WAAW,EAAE,iEAAiE;MAC9EhE,aAAa,EAAE,EAAE;MACjBiE,YAAY,EAAE,EAAE;MAChB3B,OAAO,EAAE,CAAC;MACV4B,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,CAAC;MACRhH,SAAS,EAAE,CACT;QACEE,EAAE,EAAE,CAAC;QACL2C,KAAK,EAAE,uBAAuB;QAC9BC,OAAO,EAAE,CACP,kBAAkB,EAClB,mEAAmE,EACnE,gBAAgB,EAChB,6BAA6B,CAC9B;QACDU,YAAY,EAAE;OACf,EACD;QACEtD,EAAE,EAAE,CAAC;QACL2C,KAAK,EAAE,+BAA+B;QACtCC,OAAO,EAAE,CACP,uCAAuC,EACvC,wBAAwB,EACxB,wBAAwB,EACxB,gBAAgB,CACjB;QACDU,YAAY,EAAE;OACf,EACD;QACEtD,EAAE,EAAE,CAAC;QACL2C,KAAK,EAAE,mDAAmD;QAC1DC,OAAO,EAAE,CACP,2CAA2C,EAC3C,gDAAgD,EAChD,yCAAyC,EACzC,gCAAgC,CACjC;QACDU,YAAY,EAAE;OACf,EACD;QACEtD,EAAE,EAAE,CAAC;QACL2C,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE,CACP,qCAAqC,EACrC,qCAAqC,EACrC,kBAAkB,EAClB,qBAAqB,CACtB;QACDU,YAAY,EAAE;OACf,EACD;QACEtD,EAAE,EAAE,CAAC;QACL2C,KAAK,EAAE,sDAAsD;QAC7DC,OAAO,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,6BAA6B,EAAE,mBAAmB,CAAC;QACzGU,YAAY,EAAE;OACf;KAEJ;IACD,IAAI,CAAChB,aAAa,GAAG,IAAI,CAACzC,IAAI,CAAC6G,YAAY,GAAG,EAAE,EAAC;IACjD,IAAI,CAACK,UAAU,EAAE;IACjB,IAAI,CAACpG,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;EAgBF;;EAEAoG,UAAUA,CAAA;IACR,IAAI,CAACR,iBAAiB,GAAG9H,QAAQ,CAAC,IAAI,CAAC,CAACoH,SAAS,CAAC,MAAK;MACrD,IAAI,IAAI,CAACvD,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC6C,WAAW,EAAE;QAC/C,IAAI,CAAC7C,aAAa,EAAE;OACrB,MAAM,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC6C,WAAW,EAAE;QACxD,IAAI,CAAC7E,UAAU,EAAE;QACjB,IAAI,CAACiG,iBAAiB,CAACC,WAAW,EAAE;;IAExC,CAAC,CAAC;EACJ;EAEA1E,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC/B,oBAAoB,GAAG,CAAC,EAAE;MACjC,IAAI,CAACA,oBAAoB,EAAE;;EAE/B;EAEAL,YAAYA,CAAA;IACV,IAAI,IAAI,CAACK,oBAAoB,GAAG,IAAI,CAACF,IAAI,CAACC,SAAS,CAAC0C,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACzC,oBAAoB,EAAE;;EAE/B;EAEAmB,YAAYA,CAACF,KAAa;IACxB,IAAI,CAACjB,oBAAoB,GAAGiB,KAAK;EACnC;EAEAV,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACT,IAAI,IAAI,CAAC,IAAI,CAACkG,WAAW,EAAE;IAErC,IAAI,CAACpF,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC4F,iBAAiB,CAACC,WAAW,EAAE;IAEpC,IAAI7B,KAAK,GAAG,CAAC;IACb,MAAMlB,QAAQ,GAA2E,EAAE;IAE3F,IAAI,CAAC5D,IAAI,CAACC,SAAS,CAACkH,OAAO,CAAEC,QAAQ,IAAI;MACvC,MAAMC,cAAc,GAAG,IAAI,CAACtH,eAAe,CAACqH,QAAQ,CAACjH,EAAE,CAAC;MACxD,MAAMmH,SAAS,GAAGD,cAAc,KAAKD,QAAQ,CAAC3D,YAAY;MAC1D,IAAI6D,SAAS,EAAE;QACbxC,KAAK,EAAE;;MAETlB,QAAQ,CAAC2D,IAAI,CAAC;QACZC,UAAU,EAAEJ,QAAQ,CAACjH,EAAE;QACvB2D,cAAc,EAAEuD,cAAc,IAAI,CAAC,CAAC;QACpCtD,WAAW,EAAEuD;OACd,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMzC,WAAW,GAAG4C,IAAI,CAACC,KAAK,CAAE5C,KAAK,GAAG,IAAI,CAAC9E,IAAI,CAACC,SAAS,CAAC0C,MAAM,GAAI,GAAG,CAAC;IAC1E,MAAMiC,MAAM,GAAGC,WAAW,KAAK,IAAI,CAAC7E,IAAI,CAAC4C,aAAa,IAAI,CAAC,CAAC;IAE5D,MAAM+E,YAAY,GAAiB;MACjCC,QAAQ,EAAE,IAAI,CAAC1B,WAAW,CAAC/F,EAAE;MAC7BkG,MAAM,EAAE,IAAI,CAACrG,IAAI,CAACG,EAAE;MACpB2E,KAAK,EAAED,WAAW;MAClBgD,cAAc,EAAE,IAAIC,IAAI;KACzB;IAED;IACA,IAAI,CAACnE,OAAO,GAAG;MACbmB,KAAK,EAAEA,KAAK;MACZC,cAAc,EAAE,IAAI,CAAC/E,IAAI,CAACC,SAAS,CAAC0C,MAAM;MAC1CkC,WAAW,EAAEA,WAAW;MACxBD,MAAM,EAAEA,MAAM;MACdhB,QAAQ,EAAEA;KACJ,EAAC;IACT,IAAI,CAAC0B,WAAW,GAAG,IAAI;IACvB,IAAI,CAACxE,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;;;;;;EAqBF;;EAEAqC,WAAWA,CAAA;IACT,IAAI,CAACjD,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACH,eAAe,GAAG,EAAE;IACzB,IAAI,CAACuF,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC3B,OAAO,GAAG,IAAI;IACnB,IAAI,CAAClB,aAAa,GAAG,IAAI,CAACzC,IAAI,CAAC6G,YAAY,GAAG,EAAE;IAChD,IAAI,CAACK,UAAU,EAAE;EACnB;EAEA1E,UAAUA,CAACuF,OAAe;IACxB,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGH,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA,IAAIvF,QAAQA,CAAA;IACV,OAAQ,CAAC,IAAI,CAAC3C,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACF,IAAI,CAACC,SAAS,CAAC0C,MAAM,GAAI,GAAG;EAC7E;;;uBA9OW4C,aAAa,EAAA1G,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA3J,EAAA,CAAAwJ,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA7J,EAAA,CAAAwJ,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA/J,EAAA,CAAAwJ,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAbvD,aAAa;MAAAwD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/etBxK,EAAA,CAAAC,cAAA,aAA4B;UAC1BD,EAAA,CAAA6B,UAAA,IAAA6I,4BAAA,mBA0EM;UAEN1K,EAAA,CAAA6B,UAAA,IAAA8I,oCAAA,gCAAA3K,EAAA,CAAA4K,sBAAA,CAkEc;UAChB5K,EAAA,CAAAG,YAAA,EAAM;;;;UA/IuBH,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,SAAAqK,GAAA,CAAAtJ,IAAA,KAAAsJ,GAAA,CAAAhE,WAAA,CAA4B,aAAAoE,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}