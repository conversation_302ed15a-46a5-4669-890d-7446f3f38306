{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PaymentService {\n  constructor(http) {\n    this.http = http;\n  }\n  // POST: Effectuer un paiement\n  effectuerPaiement(paiement) {\n    return this.http.post(`${environment.urlApi}paiement/effectuer`, paiement);\n  }\n  static {\n    this.ɵfac = function PaymentService_Factory(t) {\n      return new (t || PaymentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PaymentService,\n      factory: PaymentService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "PaymentService", "constructor", "http", "effectuerPaiement", "paiement", "post", "urlApi", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\payment.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Paiement, PaymentResponse } from \"../models/payment.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class PaymentService {\n  constructor(private http: HttpClient) {}\n\n  // POST: Effectuer un paiement\n  effectuerPaiement(paiement: Paiement): Observable<PaymentResponse> {\n    return this.http.post<PaymentResponse>(`${environment.urlApi}paiement/effectuer`, paiement)\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,cAAc;EACzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvC;EACAC,iBAAiBA,CAACC,QAAkB;IAClC,OAAO,IAAI,CAACF,IAAI,CAACG,IAAI,CAAkB,GAAGN,WAAW,CAACO,MAAM,oBAAoB,EAAEF,QAAQ,CAAC;EAC7F;;;uBANWJ,cAAc,EAAAO,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdV,cAAc;MAAAW,OAAA,EAAdX,cAAc,CAAAY,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}