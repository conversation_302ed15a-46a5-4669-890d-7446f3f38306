{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from \"@angular/core\";\nimport { interval } from \"rxjs\";\nexport let QuizComponent = class QuizComponent {\n  constructor(route, router, quizService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.quizService = quizService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.currentQuestionIndex = 0;\n    this.selectedAnswers = {}; // { questionId: selectedOptionIndex }\n    this.showResults = false;\n    this.results = null;\n    this.isLoading = true;\n    this.timeRemaining = 0;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.route.paramMap.subscribe(params => {\n      this.quizId = Number(params.get(\"id\"));\n      this.loadQuiz();\n    });\n  }\n  ngOnDestroy() {\n    if (this.timerSubscription) {\n      this.timerSubscription.unsubscribe();\n    }\n  }\n  loadQuiz() {\n    this.isLoading = true;\n    // Mock data for demonstration\n    this.quiz = {\n      id: this.quizId,\n      titre: \"Quiz - Bases de React\",\n      description: \"Testez vos connaissances sur les concepts fondamentaux de React\",\n      seuilReussite: 70,\n      dureeEstimee: 15,\n      coursId: 1,\n      typeContenu: \"Quiz\",\n      estComplete: false,\n      estDebloque: true,\n      ordre: 3,\n      questions: [{\n        id: 1,\n        texte: \"Qu'est-ce que React ?\",\n        options: [\"Un framework CSS\", \"Une bibliothèque JavaScript pour créer des interfaces utilisateur\", \"Un serveur web\", \"Un langage de programmation\"],\n        bonneReponse: 1\n      }, {\n        id: 2,\n        texte: \"Que sont les props en React ?\",\n        options: [\"Des propriétés passées aux composants\", \"Des méthodes de classe\", \"Des variables globales\", \"Des styles CSS\"],\n        bonneReponse: 0\n      }, {\n        id: 3,\n        texte: \"Comment créer un composant fonctionnel en React ?\",\n        options: [\"class MyComponent extends React.Component\", \"function MyComponent() { return <div></div>; }\", \"const MyComponent = React.createClass()\", \"React.component('MyComponent')\"],\n        bonneReponse: 1\n      }, {\n        id: 4,\n        texte: \"Qu'est-ce que le JSX ?\",\n        options: [\"Un nouveau langage de programmation\", \"Une extension de syntaxe JavaScript\", \"Un framework CSS\", \"Une base de données\"],\n        bonneReponse: 1\n      }, {\n        id: 5,\n        texte: \"Comment gérer l'état dans un composant fonctionnel ?\",\n        options: [\"Avec this.state\", \"Avec le hook useState\", \"Avec des variables globales\", \"Avec localStorage\"],\n        bonneReponse: 1\n      }]\n    };\n    this.timeRemaining = this.quiz.dureeEstimee * 60; // Convert minutes to seconds\n    this.startTimer();\n    this.isLoading = false;\n    // Uncomment to fetch from API\n    /*\n    this.quizService.getQuizById(this.quizId).subscribe({\n      next: (data) => {\n        this.quiz = data;\n        this.timeRemaining = this.quiz.dureeEstimee * 60;\n        this.startTimer();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  startTimer() {\n    this.timerSubscription = interval(1000).subscribe(() => {\n      if (this.timeRemaining > 0 && !this.showResults) {\n        this.timeRemaining--;\n      } else if (this.timeRemaining === 0 && !this.showResults) {\n        this.submitQuiz();\n        this.timerSubscription.unsubscribe();\n      }\n    });\n  }\n  previousQuestion() {\n    if (this.currentQuestionIndex > 0) {\n      this.currentQuestionIndex--;\n    }\n  }\n  nextQuestion() {\n    if (this.currentQuestionIndex < this.quiz.questions.length - 1) {\n      this.currentQuestionIndex++;\n    }\n  }\n  goToQuestion(index) {\n    this.currentQuestionIndex = index;\n  }\n  submitQuiz() {\n    if (!this.quiz || !this.currentUser) return;\n    this.isLoading = true;\n    this.timerSubscription.unsubscribe();\n    let score = 0;\n    const reponses = [];\n    this.quiz.questions.forEach(question => {\n      const selectedAnswer = this.selectedAnswers[question.id];\n      const isCorrect = selectedAnswer === question.bonneReponse;\n      if (isCorrect) {\n        score++;\n      }\n      reponses.push({\n        questionId: question.id,\n        reponseChoisie: selectedAnswer ?? -1,\n        estCorrecte: isCorrect\n      });\n    });\n    const pourcentage = Math.round(score / this.quiz.questions.length * 100);\n    const reussi = pourcentage >= (this.quiz.seuilReussite || 0);\n    const resultatQuiz = {\n      clientId: this.currentUser.id,\n      quizId: this.quiz.id,\n      score: pourcentage,\n      dateSoumission: new Date()\n    };\n    // Mock submission\n    this.results = {\n      score: score,\n      totalQuestions: this.quiz.questions.length,\n      pourcentage: pourcentage,\n      reussi: reussi,\n      reponses: reponses\n    }; // Cast to any because ResultatQuiz doesn't have all these properties\n    this.showResults = true;\n    this.isLoading = false;\n    // Uncomment to submit to API\n    /*\n    this.quizService.soumettreResultat(this.quiz.id, resultatQuiz).subscribe({\n      next: (res) => {\n        this.results = {\n          score: score,\n          totalQuestions: this.quiz.questions.length,\n          pourcentage: pourcentage,\n          reussi: reussi,\n          reponses: reponses\n        } as any; // Cast to any because ResultatQuiz doesn't have all these properties\n        this.showResults = true;\n        this.isLoading = false;\n        this.snackBar.open('Résultat du quiz enregistré !', 'Fermer', { duration: 3000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de la soumission du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n      }\n    });\n    */\n  }\n\n  restartQuiz() {\n    this.currentQuestionIndex = 0;\n    this.selectedAnswers = {};\n    this.showResults = false;\n    this.results = null;\n    this.timeRemaining = this.quiz.dureeEstimee * 60;\n    this.startTimer();\n  }\n  formatTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n  }\n  get progress() {\n    return (this.currentQuestionIndex + 1) / this.quiz.questions.length * 100;\n  }\n};\nQuizComponent = __decorate([Component({\n  selector: \"app-quiz\",\n  template: `\n    <div class=\"quiz-container\">\n      <div class=\"quiz-wrapper\" *ngIf=\"quiz && !showResults; else resultsOrLoading\">\n        <!-- Header -->\n        <div class=\"quiz-header\">\n          <div class=\"title-row\">\n            <h1>{{ quiz.titre }}</h1>\n            <div class=\"timer\">\n              <mat-icon>timer</mat-icon>\n              <span class=\"time-display\">{{ formatTime(timeRemaining) }}</span>\n            </div>\n          </div>\n\n          <div class=\"progress-row\">\n            <span class=\"question-count\">Question {{ currentQuestionIndex + 1 }} sur {{ quiz.questions.length }}</span>\n            <span class=\"threshold\">Seuil de réussite: {{ quiz.seuilReussite }}%</span>\n          </div>\n          <mat-progress-bar mode=\"determinate\" [value]=\"progress\"></mat-progress-bar>\n        </div>\n\n        <!-- Question Card -->\n        <mat-card class=\"question-card\">\n          <mat-card-title>{{ quiz.questions[currentQuestionIndex].texte }}</mat-card-title>\n          <mat-card-content>\n            <mat-radio-group \n              aria-label=\"Sélectionnez une option\" \n              [(ngModel)]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id]\"\n              class=\"options-group\">\n              <mat-radio-button \n                *ngFor=\"let option of quiz.questions[currentQuestionIndex].options; let i = index\" \n                [value]=\"i\" \n                class=\"option-item\">\n                {{ option }}\n              </mat-radio-button>\n            </mat-radio-group>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Navigation Buttons -->\n        <div class=\"navigation-buttons\">\n          <button mat-stroked-button (click)=\"previousQuestion()\" [disabled]=\"currentQuestionIndex === 0\">\n            Question précédente\n          </button>\n\n          <button mat-raised-button color=\"primary\" \n                  (click)=\"nextQuestion()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined\"\n                  *ngIf=\"currentQuestionIndex < quiz.questions.length - 1\">\n            Question suivante\n          </button>\n\n          <button mat-raised-button color=\"accent\" \n                  (click)=\"submitQuiz()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined || isLoading\"\n                  *ngIf=\"currentQuestionIndex === quiz.questions.length - 1\">\n            <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n            <span *ngIf=\"!isLoading\">Terminer le quiz</span>\n          </button>\n        </div>\n\n        <!-- Questions Overview -->\n        <mat-card class=\"overview-card\">\n          <mat-card-title>Aperçu des questions</mat-card-title>\n          <mat-card-content>\n            <div class=\"question-dots\">\n              <button mat-mini-fab *ngFor=\"let q of quiz.questions; let i = index\" \n                      [ngClass]=\"{\n                        'current': i === currentQuestionIndex,\n                        'answered': selectedAnswers[q.id] !== undefined && i !== currentQuestionIndex\n                      }\"\n                      (click)=\"goToQuestion(i)\">\n                {{ i + 1 }}\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #resultsOrLoading>\n        <div *ngIf=\"isLoading\" class=\"loading-spinner\">\n          <mat-spinner></mat-spinner>\n          <p>Chargement du quiz...</p>\n        </div>\n        <div *ngIf=\"!isLoading && showResults && results\" class=\"results-container\">\n          <mat-card class=\"results-card\">\n            <mat-card-header class=\"results-header\">\n              <div class=\"result-icon-wrapper\" [ngClass]=\"{'success': results.reussi, 'fail': !results.reussi}\">\n                <mat-icon *ngIf=\"results.reussi\">check_circle</mat-icon>\n                <mat-icon *ngIf=\"!results.reussi\">cancel</mat-icon>\n              </div>\n              <mat-card-title>{{ results.reussi ? 'Félicitations !' : 'Quiz non réussi' }}</mat-card-title>\n              <mat-card-subtitle>\n                {{ results.reussi ? 'Vous avez réussi le quiz avec succès !' : 'Il vous faut ' + quiz.seuilReussite + '% pour réussir ce quiz.' }}\n              </mat-card-subtitle>\n            </mat-card-header>\n\n            <mat-card-content class=\"results-content\">\n              <div class=\"score-display\">\n                <span class=\"percentage\">{{ results.pourcentage }}%</span>\n                <p class=\"score-count\">{{ results.score }} sur {{ results.totalQuestions }} questions correctes</p>\n              </div>\n\n              <mat-progress-bar mode=\"determinate\" [value]=\"results.pourcentage\" class=\"results-progress\"></mat-progress-bar>\n\n              <div class=\"certificate-info\" *ngIf=\"results.reussi\">\n                <mat-icon>emoji_events</mat-icon>\n                <p>Un certificat sera généré automatiquement !</p>\n              </div>\n\n              <div class=\"results-actions\">\n                <button mat-stroked-button [routerLink]=\"['/courses', quiz.coursId]\">\n                  Retour au cours\n                </button>\n                <button mat-raised-button color=\"primary\" *ngIf=\"!results.reussi\" (click)=\"restartQuiz()\">\n                  Recommencer\n                </button>\n              </div>\n\n              <div class=\"answers-detail\">\n                <h3>Détail des réponses :</h3>\n                <div *ngFor=\"let question of quiz.questions; let i = index\" class=\"answer-item\">\n                  <div class=\"answer-header\">\n                    <h4>Question {{ i + 1 }}</h4>\n                    <mat-icon *ngIf=\"results.reponses[i].estCorrecte\" class=\"correct-icon\">check_circle</mat-icon>\n                    <mat-icon *ngIf=\"!results.reponses[i].estCorrecte\" class=\"incorrect-icon\">cancel</mat-icon>\n                  </div>\n                  <p class=\"question-text\">{{ question.texte }}</p>\n                  <div class=\"options-detail\">\n                    <div *ngFor=\"let option of question.options; let j = index\" \n                         [ngClass]=\"{\n                           'correct-option': j === question.bonneReponse,\n                           'user-incorrect-option': j === results.reponses[i].reponseChoisie && !results.reponses[i].estCorrecte\n                         }\"\n                         class=\"option-detail-item\">\n                      {{ option }}\n                      <span *ngIf=\"j === question.bonneReponse\" class=\"correct-label\">✓ Bonne réponse</span>\n                      <span *ngIf=\"j === results.reponses[i].reponseChoisie && !results.reponses[i].estCorrecte\" class=\"incorrect-label\">✗ Votre réponse</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [`\n    .quiz-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .quiz-wrapper, .results-container {\n      width: 100%;\n      max-width: 800px;\n    }\n\n    .loading-spinner {\n      text-align: center;\n      padding: 4rem;\n    }\n\n    .quiz-header {\n      margin-bottom: 2rem;\n      background-color: #fff;\n      padding: 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n    }\n\n    .title-row {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .quiz-header h1 {\n      font-size: 1.8rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .timer {\n      display: flex;\n      align-items: center;\n      color: #d32f2f; /* Red */\n      font-weight: 500;\n    }\n\n    .timer mat-icon {\n      margin-right: 0.3rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .time-display {\n      font-size: 1.5rem;\n      font-family: 'monospace';\n    }\n\n    .progress-row {\n      display: flex;\n      justify-content: space-between;\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.8rem;\n    }\n\n    mat-progress-bar {\n      height: 8px;\n      border-radius: 4px;\n    }\n\n    .question-card {\n      margin-bottom: 2rem;\n      padding: 1.5rem;\n    }\n\n    .question-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .options-group {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .option-item {\n      padding: 0.8rem 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n    }\n\n    .option-item:hover {\n      background-color: #f5f5f5;\n      border-color: #bbb;\n    }\n\n    .option-item.mat-radio-checked {\n      border-color: #673ab7; /* Purple */\n      background-color: #ede7f6; /* Light purple */\n    }\n\n    .navigation-buttons {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 2rem;\n    }\n\n    .navigation-buttons button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .overview-card {\n      padding: 1.5rem;\n    }\n\n    .overview-card mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .question-dots {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n    }\n\n    .question-dots button {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #e0e0e0; /* Light gray */\n      color: #666;\n      font-weight: 500;\n      transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;\n    }\n\n    .question-dots button.current {\n      background-color: #673ab7; /* Purple */\n      color: white;\n    }\n\n    .question-dots button.answered {\n      background-color: #c8e6c9; /* Light green */\n      color: #388e3c; /* Dark green */\n    }\n\n    /* Results styles */\n    .results-card {\n      padding: 2rem;\n      text-align: center;\n    }\n\n    .results-header {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .result-icon-wrapper {\n      width: 64px;\n      height: 64px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 1rem;\n    }\n\n    .result-icon-wrapper mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n    }\n\n    .result-icon-wrapper.success {\n      background-color: #e8f5e9; /* Light green */\n    }\n    .result-icon-wrapper.success mat-icon {\n      color: #4caf50; /* Green */\n    }\n\n    .result-icon-wrapper.fail {\n      background-color: #ffebee; /* Light red */\n    }\n    .result-icon-wrapper.fail mat-icon {\n      color: #f44336; /* Red */\n    }\n\n    .results-card mat-card-title {\n      font-size: 2rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n    }\n\n    .results-card mat-card-subtitle {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .score-display {\n      margin-bottom: 1.5rem;\n    }\n\n    .percentage {\n      font-size: 3.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .score-count {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .results-progress {\n      margin-bottom: 1.5rem;\n    }\n\n    .certificate-info {\n      background-color: #e8f5e9;\n      border: 1px solid #c8e6c9;\n      border-radius: 8px;\n      padding: 1rem;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 0.8rem;\n      color: #388e3c;\n      font-weight: 500;\n      margin-bottom: 2rem;\n    }\n\n    .certificate-info mat-icon {\n      font-size: 1.8rem;\n      width: 1.8rem;\n      height: 1.8rem;\n    }\n\n    .results-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 2rem;\n    }\n\n    .results-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .answers-detail {\n      text-align: left;\n      margin-top: 2rem;\n      border-top: 1px solid #eee;\n      padding-top: 2rem;\n    }\n\n    .answers-detail h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .answer-item {\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 1.2rem;\n      margin-bottom: 1rem;\n      background-color: #fff;\n    }\n\n    .answer-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 0.8rem;\n    }\n\n    .answer-header h4 {\n      font-size: 1.1rem;\n      font-weight: 500;\n      color: #444;\n      margin: 0;\n    }\n\n    .correct-icon { color: #4caf50; }\n    .incorrect-icon { color: #f44336; }\n\n    .question-text {\n      font-size: 1rem;\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    .options-detail {\n      display: flex;\n      flex-direction: column;\n      gap: 0.6rem;\n    }\n\n    .option-detail-item {\n      padding: 0.6rem 1rem;\n      border-radius: 6px;\n      font-size: 0.9rem;\n      color: #555;\n      background-color: #f9f9f9;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .correct-option {\n      background-color: #e8f5e9;\n      color: #388e3c;\n      border: 1px solid #c8e6c9;\n    }\n\n    .user-incorrect-option {\n      background-color: #ffebee;\n      color: #d32f2f;\n      border: 1px solid #ffcdd2;\n    }\n\n    .correct-label {\n      color: #388e3c;\n      font-weight: 500;\n    }\n\n    .incorrect-label {\n      color: #d32f2f;\n      font-weight: 500;\n    }\n  `]\n})], QuizComponent);", "map": {"version": 3, "names": ["Component", "interval", "QuizComponent", "constructor", "route", "router", "quizService", "authService", "snackBar", "currentQuestionIndex", "selectedAnswer<PERSON>", "showResults", "results", "isLoading", "timeRemaining", "ngOnInit", "currentUser$", "subscribe", "user", "currentUser", "paramMap", "params", "quizId", "Number", "get", "loadQuiz", "ngOnDestroy", "timerSubscription", "unsubscribe", "quiz", "id", "titre", "description", "<PERSON>uil<PERSON><PERSON><PERSON>", "du<PERSON><PERSON><PERSON><PERSON>", "coursId", "typeContenu", "estComplete", "estDebloque", "ordre", "questions", "texte", "options", "bonneReponse", "startTimer", "submitQuiz", "previousQuestion", "nextQuestion", "length", "goToQuestion", "index", "score", "reponses", "for<PERSON>ach", "question", "<PERSON><PERSON><PERSON><PERSON>", "isCorrect", "push", "questionId", "reponseChoisie", "estCorrecte", "pourcentage", "Math", "round", "<PERSON><PERSON><PERSON>", "resultatQuiz", "clientId", "dateSoumission", "Date", "totalQuestions", "restartQuiz", "formatTime", "seconds", "minutes", "floor", "remainingSeconds", "toString", "padStart", "progress", "__decorate", "selector", "template", "styles"], "sources": ["C:\\e-learning\\src\\app\\features\\quiz\\quiz.component.ts"], "sourcesContent": ["import { Component, type OnInit, type <PERSON><PERSON><PERSON><PERSON> } from \"@angular/core\"\nimport type { ActivatedRoute, Router } from \"@angular/router\"\nimport type { QuizService } from \"../../core/services/quiz.service\"\nimport type { MatSnackBar } from \"@angular/material/snack-bar\"\nimport type { Quiz } from \"../../core/models/course.model\"\nimport type { ResultatQuiz } from \"../../core/models/resultat-quiz.model\"\nimport type { AuthService } from \"../../core/services/auth.service\"\nimport { interval, type Subscription } from \"rxjs\"\nimport type { User } from \"../../core/models/user.model\" // Import User model\n\n@Component({\n  selector: \"app-quiz\",\n  template: `\n    <div class=\"quiz-container\">\n      <div class=\"quiz-wrapper\" *ngIf=\"quiz && !showResults; else resultsOrLoading\">\n        <!-- Header -->\n        <div class=\"quiz-header\">\n          <div class=\"title-row\">\n            <h1>{{ quiz.titre }}</h1>\n            <div class=\"timer\">\n              <mat-icon>timer</mat-icon>\n              <span class=\"time-display\">{{ formatTime(timeRemaining) }}</span>\n            </div>\n          </div>\n\n          <div class=\"progress-row\">\n            <span class=\"question-count\">Question {{ currentQuestionIndex + 1 }} sur {{ quiz.questions.length }}</span>\n            <span class=\"threshold\">Seuil de réussite: {{ quiz.seuilReussite }}%</span>\n          </div>\n          <mat-progress-bar mode=\"determinate\" [value]=\"progress\"></mat-progress-bar>\n        </div>\n\n        <!-- Question Card -->\n        <mat-card class=\"question-card\">\n          <mat-card-title>{{ quiz.questions[currentQuestionIndex].texte }}</mat-card-title>\n          <mat-card-content>\n            <mat-radio-group \n              aria-label=\"Sélectionnez une option\" \n              [(ngModel)]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id]\"\n              class=\"options-group\">\n              <mat-radio-button \n                *ngFor=\"let option of quiz.questions[currentQuestionIndex].options; let i = index\" \n                [value]=\"i\" \n                class=\"option-item\">\n                {{ option }}\n              </mat-radio-button>\n            </mat-radio-group>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Navigation Buttons -->\n        <div class=\"navigation-buttons\">\n          <button mat-stroked-button (click)=\"previousQuestion()\" [disabled]=\"currentQuestionIndex === 0\">\n            Question précédente\n          </button>\n\n          <button mat-raised-button color=\"primary\" \n                  (click)=\"nextQuestion()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined\"\n                  *ngIf=\"currentQuestionIndex < quiz.questions.length - 1\">\n            Question suivante\n          </button>\n\n          <button mat-raised-button color=\"accent\" \n                  (click)=\"submitQuiz()\" \n                  [disabled]=\"selectedAnswers[quiz.questions[currentQuestionIndex].id] === undefined || isLoading\"\n                  *ngIf=\"currentQuestionIndex === quiz.questions.length - 1\">\n            <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n            <span *ngIf=\"!isLoading\">Terminer le quiz</span>\n          </button>\n        </div>\n\n        <!-- Questions Overview -->\n        <mat-card class=\"overview-card\">\n          <mat-card-title>Aperçu des questions</mat-card-title>\n          <mat-card-content>\n            <div class=\"question-dots\">\n              <button mat-mini-fab *ngFor=\"let q of quiz.questions; let i = index\" \n                      [ngClass]=\"{\n                        'current': i === currentQuestionIndex,\n                        'answered': selectedAnswers[q.id] !== undefined && i !== currentQuestionIndex\n                      }\"\n                      (click)=\"goToQuestion(i)\">\n                {{ i + 1 }}\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <ng-template #resultsOrLoading>\n        <div *ngIf=\"isLoading\" class=\"loading-spinner\">\n          <mat-spinner></mat-spinner>\n          <p>Chargement du quiz...</p>\n        </div>\n        <div *ngIf=\"!isLoading && showResults && results\" class=\"results-container\">\n          <mat-card class=\"results-card\">\n            <mat-card-header class=\"results-header\">\n              <div class=\"result-icon-wrapper\" [ngClass]=\"{'success': results.reussi, 'fail': !results.reussi}\">\n                <mat-icon *ngIf=\"results.reussi\">check_circle</mat-icon>\n                <mat-icon *ngIf=\"!results.reussi\">cancel</mat-icon>\n              </div>\n              <mat-card-title>{{ results.reussi ? 'Félicitations !' : 'Quiz non réussi' }}</mat-card-title>\n              <mat-card-subtitle>\n                {{ results.reussi ? 'Vous avez réussi le quiz avec succès !' : 'Il vous faut ' + quiz.seuilReussite + '% pour réussir ce quiz.' }}\n              </mat-card-subtitle>\n            </mat-card-header>\n\n            <mat-card-content class=\"results-content\">\n              <div class=\"score-display\">\n                <span class=\"percentage\">{{ results.pourcentage }}%</span>\n                <p class=\"score-count\">{{ results.score }} sur {{ results.totalQuestions }} questions correctes</p>\n              </div>\n\n              <mat-progress-bar mode=\"determinate\" [value]=\"results.pourcentage\" class=\"results-progress\"></mat-progress-bar>\n\n              <div class=\"certificate-info\" *ngIf=\"results.reussi\">\n                <mat-icon>emoji_events</mat-icon>\n                <p>Un certificat sera généré automatiquement !</p>\n              </div>\n\n              <div class=\"results-actions\">\n                <button mat-stroked-button [routerLink]=\"['/courses', quiz.coursId]\">\n                  Retour au cours\n                </button>\n                <button mat-raised-button color=\"primary\" *ngIf=\"!results.reussi\" (click)=\"restartQuiz()\">\n                  Recommencer\n                </button>\n              </div>\n\n              <div class=\"answers-detail\">\n                <h3>Détail des réponses :</h3>\n                <div *ngFor=\"let question of quiz.questions; let i = index\" class=\"answer-item\">\n                  <div class=\"answer-header\">\n                    <h4>Question {{ i + 1 }}</h4>\n                    <mat-icon *ngIf=\"results.reponses[i].estCorrecte\" class=\"correct-icon\">check_circle</mat-icon>\n                    <mat-icon *ngIf=\"!results.reponses[i].estCorrecte\" class=\"incorrect-icon\">cancel</mat-icon>\n                  </div>\n                  <p class=\"question-text\">{{ question.texte }}</p>\n                  <div class=\"options-detail\">\n                    <div *ngFor=\"let option of question.options; let j = index\" \n                         [ngClass]=\"{\n                           'correct-option': j === question.bonneReponse,\n                           'user-incorrect-option': j === results.reponses[i].reponseChoisie && !results.reponses[i].estCorrecte\n                         }\"\n                         class=\"option-detail-item\">\n                      {{ option }}\n                      <span *ngIf=\"j === question.bonneReponse\" class=\"correct-label\">✓ Bonne réponse</span>\n                      <span *ngIf=\"j === results.reponses[i].reponseChoisie && !results.reponses[i].estCorrecte\" class=\"incorrect-label\">✗ Votre réponse</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </ng-template>\n    </div>\n  `,\n  styles: [\n    `\n    .quiz-container {\n      min-height: 100vh;\n      background-color: #f5f5f5;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .quiz-wrapper, .results-container {\n      width: 100%;\n      max-width: 800px;\n    }\n\n    .loading-spinner {\n      text-align: center;\n      padding: 4rem;\n    }\n\n    .quiz-header {\n      margin-bottom: 2rem;\n      background-color: #fff;\n      padding: 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n    }\n\n    .title-row {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .quiz-header h1 {\n      font-size: 1.8rem;\n      font-weight: bold;\n      color: #333;\n      margin: 0;\n    }\n\n    .timer {\n      display: flex;\n      align-items: center;\n      color: #d32f2f; /* Red */\n      font-weight: 500;\n    }\n\n    .timer mat-icon {\n      margin-right: 0.3rem;\n      font-size: 1.5rem;\n      width: 1.5rem;\n      height: 1.5rem;\n    }\n\n    .time-display {\n      font-size: 1.5rem;\n      font-family: 'monospace';\n    }\n\n    .progress-row {\n      display: flex;\n      justify-content: space-between;\n      font-size: 0.9rem;\n      color: #666;\n      margin-bottom: 0.8rem;\n    }\n\n    mat-progress-bar {\n      height: 8px;\n      border-radius: 4px;\n    }\n\n    .question-card {\n      margin-bottom: 2rem;\n      padding: 1.5rem;\n    }\n\n    .question-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .options-group {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .option-item {\n      padding: 0.8rem 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n    }\n\n    .option-item:hover {\n      background-color: #f5f5f5;\n      border-color: #bbb;\n    }\n\n    .option-item.mat-radio-checked {\n      border-color: #673ab7; /* Purple */\n      background-color: #ede7f6; /* Light purple */\n    }\n\n    .navigation-buttons {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 2rem;\n    }\n\n    .navigation-buttons button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .overview-card {\n      padding: 1.5rem;\n    }\n\n    .overview-card mat-card-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 1rem;\n    }\n\n    .question-dots {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n    }\n\n    .question-dots button {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #e0e0e0; /* Light gray */\n      color: #666;\n      font-weight: 500;\n      transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;\n    }\n\n    .question-dots button.current {\n      background-color: #673ab7; /* Purple */\n      color: white;\n    }\n\n    .question-dots button.answered {\n      background-color: #c8e6c9; /* Light green */\n      color: #388e3c; /* Dark green */\n    }\n\n    /* Results styles */\n    .results-card {\n      padding: 2rem;\n      text-align: center;\n    }\n\n    .results-header {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .result-icon-wrapper {\n      width: 64px;\n      height: 64px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 1rem;\n    }\n\n    .result-icon-wrapper mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n    }\n\n    .result-icon-wrapper.success {\n      background-color: #e8f5e9; /* Light green */\n    }\n    .result-icon-wrapper.success mat-icon {\n      color: #4caf50; /* Green */\n    }\n\n    .result-icon-wrapper.fail {\n      background-color: #ffebee; /* Light red */\n    }\n    .result-icon-wrapper.fail mat-icon {\n      color: #f44336; /* Red */\n    }\n\n    .results-card mat-card-title {\n      font-size: 2rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n    }\n\n    .results-card mat-card-subtitle {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .score-display {\n      margin-bottom: 1.5rem;\n    }\n\n    .percentage {\n      font-size: 3.5rem;\n      font-weight: bold;\n      color: #673ab7; /* Purple */\n    }\n\n    .score-count {\n      font-size: 1rem;\n      color: #666;\n    }\n\n    .results-progress {\n      margin-bottom: 1.5rem;\n    }\n\n    .certificate-info {\n      background-color: #e8f5e9;\n      border: 1px solid #c8e6c9;\n      border-radius: 8px;\n      padding: 1rem;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 0.8rem;\n      color: #388e3c;\n      font-weight: 500;\n      margin-bottom: 2rem;\n    }\n\n    .certificate-info mat-icon {\n      font-size: 1.8rem;\n      width: 1.8rem;\n      height: 1.8rem;\n    }\n\n    .results-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 2rem;\n    }\n\n    .results-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    .answers-detail {\n      text-align: left;\n      margin-top: 2rem;\n      border-top: 1px solid #eee;\n      padding-top: 2rem;\n    }\n\n    .answers-detail h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      color: #333;\n    }\n\n    .answer-item {\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 1.2rem;\n      margin-bottom: 1rem;\n      background-color: #fff;\n    }\n\n    .answer-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 0.8rem;\n    }\n\n    .answer-header h4 {\n      font-size: 1.1rem;\n      font-weight: 500;\n      color: #444;\n      margin: 0;\n    }\n\n    .correct-icon { color: #4caf50; }\n    .incorrect-icon { color: #f44336; }\n\n    .question-text {\n      font-size: 1rem;\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    .options-detail {\n      display: flex;\n      flex-direction: column;\n      gap: 0.6rem;\n    }\n\n    .option-detail-item {\n      padding: 0.6rem 1rem;\n      border-radius: 6px;\n      font-size: 0.9rem;\n      color: #555;\n      background-color: #f9f9f9;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .correct-option {\n      background-color: #e8f5e9;\n      color: #388e3c;\n      border: 1px solid #c8e6c9;\n    }\n\n    .user-incorrect-option {\n      background-color: #ffebee;\n      color: #d32f2f;\n      border: 1px solid #ffcdd2;\n    }\n\n    .correct-label {\n      color: #388e3c;\n      font-weight: 500;\n    }\n\n    .incorrect-label {\n      color: #d32f2f;\n      font-weight: 500;\n    }\n  `,\n  ],\n})\nexport class QuizComponent implements OnInit, OnDestroy {\n  quizId!: number\n  quiz!: Quiz\n  currentQuestionIndex = 0\n  selectedAnswers: { [key: number]: number } = {} // { questionId: selectedOptionIndex }\n  showResults = false\n  results: ResultatQuiz | null = null\n  isLoading = true\n  timeRemaining = 0\n  timerSubscription!: Subscription\n  currentUser!: User | null\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private quizService: QuizService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe((user) => {\n      this.currentUser = user\n    })\n\n    this.route.paramMap.subscribe((params) => {\n      this.quizId = Number(params.get(\"id\"))\n      this.loadQuiz()\n    })\n  }\n\n  ngOnDestroy(): void {\n    if (this.timerSubscription) {\n      this.timerSubscription.unsubscribe()\n    }\n  }\n\n  loadQuiz(): void {\n    this.isLoading = true\n    // Mock data for demonstration\n    this.quiz = {\n      id: this.quizId,\n      titre: \"Quiz - Bases de React\",\n      description: \"Testez vos connaissances sur les concepts fondamentaux de React\",\n      seuilReussite: 70,\n      dureeEstimee: 15, // in minutes\n      coursId: 1,\n      typeContenu: \"Quiz\",\n      estComplete: false,\n      estDebloque: true,\n      ordre: 3,\n      questions: [\n        {\n          id: 1,\n          texte: \"Qu'est-ce que React ?\",\n          options: [\n            \"Un framework CSS\",\n            \"Une bibliothèque JavaScript pour créer des interfaces utilisateur\",\n            \"Un serveur web\",\n            \"Un langage de programmation\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 2,\n          texte: \"Que sont les props en React ?\",\n          options: [\n            \"Des propriétés passées aux composants\",\n            \"Des méthodes de classe\",\n            \"Des variables globales\",\n            \"Des styles CSS\",\n          ],\n          bonneReponse: 0,\n        },\n        {\n          id: 3,\n          texte: \"Comment créer un composant fonctionnel en React ?\",\n          options: [\n            \"class MyComponent extends React.Component\",\n            \"function MyComponent() { return <div></div>; }\",\n            \"const MyComponent = React.createClass()\",\n            \"React.component('MyComponent')\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 4,\n          texte: \"Qu'est-ce que le JSX ?\",\n          options: [\n            \"Un nouveau langage de programmation\",\n            \"Une extension de syntaxe JavaScript\",\n            \"Un framework CSS\",\n            \"Une base de données\",\n          ],\n          bonneReponse: 1,\n        },\n        {\n          id: 5,\n          texte: \"Comment gérer l'état dans un composant fonctionnel ?\",\n          options: [\"Avec this.state\", \"Avec le hook useState\", \"Avec des variables globales\", \"Avec localStorage\"],\n          bonneReponse: 1,\n        },\n      ],\n    }\n    this.timeRemaining = this.quiz.dureeEstimee * 60 // Convert minutes to seconds\n    this.startTimer()\n    this.isLoading = false\n\n    // Uncomment to fetch from API\n    /*\n    this.quizService.getQuizById(this.quizId).subscribe({\n      next: (data) => {\n        this.quiz = data;\n        this.timeRemaining = this.quiz.dureeEstimee * 60;\n        this.startTimer();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  startTimer(): void {\n    this.timerSubscription = interval(1000).subscribe(() => {\n      if (this.timeRemaining > 0 && !this.showResults) {\n        this.timeRemaining--\n      } else if (this.timeRemaining === 0 && !this.showResults) {\n        this.submitQuiz()\n        this.timerSubscription.unsubscribe()\n      }\n    })\n  }\n\n  previousQuestion(): void {\n    if (this.currentQuestionIndex > 0) {\n      this.currentQuestionIndex--\n    }\n  }\n\n  nextQuestion(): void {\n    if (this.currentQuestionIndex < this.quiz.questions.length - 1) {\n      this.currentQuestionIndex++\n    }\n  }\n\n  goToQuestion(index: number): void {\n    this.currentQuestionIndex = index\n  }\n\n  submitQuiz(): void {\n    if (!this.quiz || !this.currentUser) return\n\n    this.isLoading = true\n    this.timerSubscription.unsubscribe()\n\n    let score = 0\n    const reponses: { questionId: number; reponseChoisie: number; estCorrecte: boolean }[] = []\n\n    this.quiz.questions.forEach((question) => {\n      const selectedAnswer = this.selectedAnswers[question.id]\n      const isCorrect = selectedAnswer === question.bonneReponse\n      if (isCorrect) {\n        score++\n      }\n      reponses.push({\n        questionId: question.id,\n        reponseChoisie: selectedAnswer ?? -1,\n        estCorrecte: isCorrect,\n      })\n    })\n\n    const pourcentage = Math.round((score / this.quiz.questions.length) * 100)\n    const reussi = pourcentage >= (this.quiz.seuilReussite || 0)\n\n    const resultatQuiz: ResultatQuiz = {\n      clientId: this.currentUser.id,\n      quizId: this.quiz.id,\n      score: pourcentage, // Store percentage as score\n      dateSoumission: new Date(),\n    }\n\n    // Mock submission\n    this.results = {\n      score: score,\n      totalQuestions: this.quiz.questions.length,\n      pourcentage: pourcentage,\n      reussi: reussi,\n      reponses: reponses,\n    } as any // Cast to any because ResultatQuiz doesn't have all these properties\n    this.showResults = true\n    this.isLoading = false\n\n    // Uncomment to submit to API\n    /*\n    this.quizService.soumettreResultat(this.quiz.id, resultatQuiz).subscribe({\n      next: (res) => {\n        this.results = {\n          score: score,\n          totalQuestions: this.quiz.questions.length,\n          pourcentage: pourcentage,\n          reussi: reussi,\n          reponses: reponses\n        } as any; // Cast to any because ResultatQuiz doesn't have all these properties\n        this.showResults = true;\n        this.isLoading = false;\n        this.snackBar.open('Résultat du quiz enregistré !', 'Fermer', { duration: 3000 });\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors de la soumission du quiz.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n      }\n    });\n    */\n  }\n\n  restartQuiz(): void {\n    this.currentQuestionIndex = 0\n    this.selectedAnswers = {}\n    this.showResults = false\n    this.results = null\n    this.timeRemaining = this.quiz.dureeEstimee * 60\n    this.startTimer()\n  }\n\n  formatTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60)\n    const remainingSeconds = seconds % 60\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n  }\n\n  get progress(): number {\n    return ((this.currentQuestionIndex + 1) / this.quiz.questions.length) * 100\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAqC,eAAe;AAOtE,SAASC,QAAQ,QAA2B,MAAM;AAqf3C,WAAMC,aAAa,GAAnB,MAAMA,aAAa;EAYxBC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAdlB,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,eAAe,GAA8B,EAAE,EAAC;IAChD,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,OAAO,GAAwB,IAAI;IACnC,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,aAAa,GAAG,CAAC;EAUd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACR,WAAW,CAACS,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC/C,IAAI,CAACC,WAAW,GAAGD,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACd,KAAK,CAACgB,QAAQ,CAACH,SAAS,CAAEI,MAAM,IAAI;MACvC,IAAI,CAACC,MAAM,GAAGC,MAAM,CAACF,MAAM,CAACG,GAAG,CAAC,IAAI,CAAC,CAAC;MACtC,IAAI,CAACC,QAAQ,EAAE;IACjB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACC,WAAW,EAAE;;EAExC;EAEAH,QAAQA,CAAA;IACN,IAAI,CAACZ,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACgB,IAAI,GAAG;MACVC,EAAE,EAAE,IAAI,CAACR,MAAM;MACfS,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,iEAAiE;MAC9EC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,CAAC;MACVC,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,CACT;QACEV,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,uBAAuB;QAC9BC,OAAO,EAAE,CACP,kBAAkB,EAClB,mEAAmE,EACnE,gBAAgB,EAChB,6BAA6B,CAC9B;QACDC,YAAY,EAAE;OACf,EACD;QACEb,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,+BAA+B;QACtCC,OAAO,EAAE,CACP,uCAAuC,EACvC,wBAAwB,EACxB,wBAAwB,EACxB,gBAAgB,CACjB;QACDC,YAAY,EAAE;OACf,EACD;QACEb,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,mDAAmD;QAC1DC,OAAO,EAAE,CACP,2CAA2C,EAC3C,gDAAgD,EAChD,yCAAyC,EACzC,gCAAgC,CACjC;QACDC,YAAY,EAAE;OACf,EACD;QACEb,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE,CACP,qCAAqC,EACrC,qCAAqC,EACrC,kBAAkB,EAClB,qBAAqB,CACtB;QACDC,YAAY,EAAE;OACf,EACD;QACEb,EAAE,EAAE,CAAC;QACLW,KAAK,EAAE,sDAAsD;QAC7DC,OAAO,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,6BAA6B,EAAE,mBAAmB,CAAC;QACzGC,YAAY,EAAE;OACf;KAEJ;IACD,IAAI,CAAC7B,aAAa,GAAG,IAAI,CAACe,IAAI,CAACK,YAAY,GAAG,EAAE,EAAC;IACjD,IAAI,CAACU,UAAU,EAAE;IACjB,IAAI,CAAC/B,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;EAgBF;;EAEA+B,UAAUA,CAAA;IACR,IAAI,CAACjB,iBAAiB,GAAG1B,QAAQ,CAAC,IAAI,CAAC,CAACgB,SAAS,CAAC,MAAK;MACrD,IAAI,IAAI,CAACH,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;QAC/C,IAAI,CAACG,aAAa,EAAE;OACrB,MAAM,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;QACxD,IAAI,CAACkC,UAAU,EAAE;QACjB,IAAI,CAAClB,iBAAiB,CAACC,WAAW,EAAE;;IAExC,CAAC,CAAC;EACJ;EAEAkB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACrC,oBAAoB,GAAG,CAAC,EAAE;MACjC,IAAI,CAACA,oBAAoB,EAAE;;EAE/B;EAEAsC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACtC,oBAAoB,GAAG,IAAI,CAACoB,IAAI,CAACW,SAAS,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACvC,oBAAoB,EAAE;;EAE/B;EAEAwC,YAAYA,CAACC,KAAa;IACxB,IAAI,CAACzC,oBAAoB,GAAGyC,KAAK;EACnC;EAEAL,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAChB,IAAI,IAAI,CAAC,IAAI,CAACV,WAAW,EAAE;IAErC,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACc,iBAAiB,CAACC,WAAW,EAAE;IAEpC,IAAIuB,KAAK,GAAG,CAAC;IACb,MAAMC,QAAQ,GAA2E,EAAE;IAE3F,IAAI,CAACvB,IAAI,CAACW,SAAS,CAACa,OAAO,CAAEC,QAAQ,IAAI;MACvC,MAAMC,cAAc,GAAG,IAAI,CAAC7C,eAAe,CAAC4C,QAAQ,CAACxB,EAAE,CAAC;MACxD,MAAM0B,SAAS,GAAGD,cAAc,KAAKD,QAAQ,CAACX,YAAY;MAC1D,IAAIa,SAAS,EAAE;QACbL,KAAK,EAAE;;MAETC,QAAQ,CAACK,IAAI,CAAC;QACZC,UAAU,EAAEJ,QAAQ,CAACxB,EAAE;QACvB6B,cAAc,EAAEJ,cAAc,IAAI,CAAC,CAAC;QACpCK,WAAW,EAAEJ;OACd,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMK,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAEZ,KAAK,GAAG,IAAI,CAACtB,IAAI,CAACW,SAAS,CAACQ,MAAM,GAAI,GAAG,CAAC;IAC1E,MAAMgB,MAAM,GAAGH,WAAW,KAAK,IAAI,CAAChC,IAAI,CAACI,aAAa,IAAI,CAAC,CAAC;IAE5D,MAAMgC,YAAY,GAAiB;MACjCC,QAAQ,EAAE,IAAI,CAAC/C,WAAW,CAACW,EAAE;MAC7BR,MAAM,EAAE,IAAI,CAACO,IAAI,CAACC,EAAE;MACpBqB,KAAK,EAAEU,WAAW;MAClBM,cAAc,EAAE,IAAIC,IAAI;KACzB;IAED;IACA,IAAI,CAACxD,OAAO,GAAG;MACbuC,KAAK,EAAEA,KAAK;MACZkB,cAAc,EAAE,IAAI,CAACxC,IAAI,CAACW,SAAS,CAACQ,MAAM;MAC1Ca,WAAW,EAAEA,WAAW;MACxBG,MAAM,EAAEA,MAAM;MACdZ,QAAQ,EAAEA;KACJ,EAAC;IACT,IAAI,CAACzC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;;;;;;EAqBF;;EAEAyD,WAAWA,CAAA;IACT,IAAI,CAAC7D,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACE,aAAa,GAAG,IAAI,CAACe,IAAI,CAACK,YAAY,GAAG,EAAE;IAChD,IAAI,CAACU,UAAU,EAAE;EACnB;EAEA2B,UAAUA,CAACC,OAAe;IACxB,MAAMC,OAAO,GAAGX,IAAI,CAACY,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGH,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAQ,CAAC,IAAI,CAACrE,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACoB,IAAI,CAACW,SAAS,CAACQ,MAAM,GAAI,GAAG;EAC7E;CACD;AA/OY9C,aAAa,GAAA6E,UAAA,EAlfzB/E,SAAS,CAAC;EACTgF,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkJT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyVD;CAEF,CAAC,C,EACWhF,aAAa,CA+OzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}