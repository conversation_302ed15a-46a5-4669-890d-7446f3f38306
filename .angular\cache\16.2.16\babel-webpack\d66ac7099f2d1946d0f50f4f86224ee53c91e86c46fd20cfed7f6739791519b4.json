{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class MessageService {\n  constructor(http) {\n    this.http = http;\n  }\n  // POST: Envoyer un message\n  envoyerMessage(message) {\n    return this.http.post(`${environment.urlApi}messages`, message);\n  }\n  // GET: Messages entre deux utilisateurs\n  getMessages(id1, id2) {\n    return this.http.get(`${environment.urlApi}messages/entre/${id1}/${id2}`);\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "MessageService", "constructor", "http", "envoyer<PERSON>essage", "message", "post", "urlApi", "getMessages", "id1", "id2", "get", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\e-learning\\src\\app\\core\\services\\message.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { Message } from \"../models/message.model\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class MessageService {\n  constructor(private http: HttpClient) {}\n\n  // POST: Envoyer un message\n  envoyerMessage(message: Message): Observable<any> {\n    return this.http.post(`${environment.urlApi}messages`, message)\n  }\n\n  // GET: Messages entre deux utilisateurs\n  getMessages(id1: number, id2: number): Observable<Message[]> {\n    return this.http.get<Message[]>(`${environment.urlApi}messages/entre/${id1}/${id2}`)\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,cAAc;EACzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvC;EACAC,cAAcA,CAACC,OAAgB;IAC7B,OAAO,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC,GAAGN,WAAW,CAACO,MAAM,UAAU,EAAEF,OAAO,CAAC;EACjE;EAEA;EACAG,WAAWA,CAACC,GAAW,EAAEC,GAAW;IAClC,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAY,GAAGX,WAAW,CAACO,MAAM,kBAAkBE,GAAG,IAAIC,GAAG,EAAE,CAAC;EACtF;;;uBAXWT,cAAc,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdd,cAAc;MAAAe,OAAA,EAAdf,cAAc,CAAAgB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}