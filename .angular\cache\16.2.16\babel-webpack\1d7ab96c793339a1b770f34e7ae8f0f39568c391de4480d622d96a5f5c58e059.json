{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nexport class AnimationFrameAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(() => scheduler.flush(undefined)));\n  }\n  recycleAsyncId(scheduler, id, delay = 0) {\n    var _a;\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n    const {\n      actions\n    } = scheduler;\n    if (id != null && id === scheduler._scheduled && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      animationFrameProvider.cancelAnimationFrame(id);\n      scheduler._scheduled = undefined;\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["AsyncAction", "animationFrameProvider", "AnimationFrameAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "_scheduled", "requestAnimationFrame", "flush", "undefined", "recycleAsyncId", "_a", "length", "cancelAnimationFrame"], "sources": ["C:/e-learning/node_modules/rxjs/dist/esm/internal/scheduler/AnimationFrameAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nexport class AnimationFrameAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(() => scheduler.flush(undefined)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        var _a;\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        const { actions } = scheduler;\n        if (id != null && id === scheduler._scheduled && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            animationFrameProvider.cancelAnimationFrame(id);\n            scheduler._scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,OAAO,MAAMC,oBAAoB,SAASF,WAAW,CAAC;EAClDG,WAAWA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACzB,KAAK,CAACD,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAC,cAAcA,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7B,OAAO,KAAK,CAACF,cAAc,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACAJ,SAAS,CAACK,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5B,OAAON,SAAS,CAACO,UAAU,KAAKP,SAAS,CAACO,UAAU,GAAGV,sBAAsB,CAACW,qBAAqB,CAAC,MAAMR,SAAS,CAACS,KAAK,CAACC,SAAS,CAAC,CAAC,CAAC;EAC1I;EACAC,cAAcA,CAACX,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIQ,EAAE;IACN,IAAIR,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE;MAC5C,OAAO,KAAK,CAACO,cAAc,CAACX,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACA,MAAM;MAAEC;IAAQ,CAAC,GAAGL,SAAS;IAC7B,IAAIG,EAAE,IAAI,IAAI,IAAIA,EAAE,KAAKH,SAAS,CAACO,UAAU,IAAI,CAAC,CAACK,EAAE,GAAGP,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACT,EAAE,MAAMA,EAAE,EAAE;MACrIN,sBAAsB,CAACiB,oBAAoB,CAACX,EAAE,CAAC;MAC/CH,SAAS,CAACO,UAAU,GAAGG,SAAS;IACpC;IACA,OAAOA,SAAS;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}