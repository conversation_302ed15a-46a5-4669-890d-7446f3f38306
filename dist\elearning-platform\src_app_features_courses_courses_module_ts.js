"use strict";
(self["webpackChunkelearning_platform"] = self["webpackChunkelearning_platform"] || []).push([["src_app_features_courses_courses_module_ts"],{

/***/ 6563:
/*!*************************************************************************************!*\
  !*** ./src/app/features/courses/course-create-edit/course-create-edit.component.ts ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CourseCreateEditComponent: () => (/* binding */ CourseCreateEditComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _core_services_course_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../core/services/course.service */ 8769);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/snack-bar */ 3347);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/select */ 5175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/core */ 4646);
/* harmony import */ var _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/progress-spinner */ 1134);















function CourseCreateEditComponent_mat_error_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Le titre est requis");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_mat_error_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "La description est requise");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_mat_error_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "La dur\u00E9e est requise");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_mat_error_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "La dur\u00E9e doit \u00EAtre positive");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_mat_error_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Le niveau est requis");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_mat_form_field_37_mat_error_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Le prix est requis");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_mat_form_field_37_mat_error_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Le prix doit \u00EAtre positif");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_mat_form_field_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-form-field", 3)(1, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Prix (\u20AC)");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "input", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](4, CourseCreateEditComponent_mat_form_field_37_mat_error_4_Template, 2, 0, "mat-error", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](5, CourseCreateEditComponent_mat_form_field_37_mat_error_5_Template, 2, 0, "mat-error", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    let tmp_0_0;
    let tmp_1_0;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (tmp_0_0 = ctx_r5.courseForm.get("prix")) == null ? null : tmp_0_0.hasError("required"));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (tmp_1_0 = ctx_r5.courseForm.get("prix")) == null ? null : tmp_1_0.hasError("min"));
  }
}
function CourseCreateEditComponent_div_43_mat_form_field_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-form-field", 35)(1, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Dur\u00E9e vid\u00E9o (min)");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "input", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_div_43_ng_container_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "mat-form-field", 36)(2, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "Seuil de r\u00E9ussite (%)");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](4, "input", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
}
function CourseCreateEditComponent_div_43_mat_form_field_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-form-field", 38)(1, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Contenu texte");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "textarea", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseCreateEditComponent_div_43_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 25)(1, "mat-form-field", 26)(2, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "Type de contenu");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "mat-select", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("selectionChange", function CourseCreateEditComponent_div_43_Template_mat_select_selectionChange_4_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r17);
      const i_r12 = restoredCtx.index;
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r16.onContentTypeChange(i_r12));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "mat-option", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "Vid\u00E9o");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "mat-option", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](8, "Quiz");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "mat-option", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10, "R\u00E9sum\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "mat-form-field", 31)(12, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](13, "Titre du contenu");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](14, "input", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](15, CourseCreateEditComponent_div_43_mat_form_field_15_Template, 4, 0, "mat-form-field", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](16, CourseCreateEditComponent_div_43_ng_container_16_Template, 5, 0, "ng-container", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](17, CourseCreateEditComponent_div_43_mat_form_field_17_Template, 4, 0, "mat-form-field", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](18, "button", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function CourseCreateEditComponent_div_43_Template_button_click_18_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r17);
      const i_r12 = restoredCtx.index;
      const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r18.removeContenu(i_r12));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](19, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](20, "delete");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const content_r11 = ctx.$implicit;
    const i_r12 = ctx.index;
    let tmp_1_0;
    let tmp_2_0;
    let tmp_3_0;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroupName", i_r12);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ((tmp_1_0 = content_r11.get("typeContenu")) == null ? null : tmp_1_0.value) === "Video");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ((tmp_2_0 = content_r11.get("typeContenu")) == null ? null : tmp_2_0.value) === "Quiz");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ((tmp_3_0 = content_r11.get("typeContenu")) == null ? null : tmp_3_0.value) === "Resume");
  }
}
function CourseCreateEditComponent_mat_spinner_50_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "mat-spinner", 40);
  }
}
function CourseCreateEditComponent_span_51_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r8.isEditMode ? "Mettre \u00E0 jour" : "Cr\u00E9er le cours");
  }
}
class CourseCreateEditComponent {
  constructor(fb, route, router, courseService, snackBar) {
    this.fb = fb;
    this.route = route;
    this.router = router;
    this.courseService = courseService;
    this.snackBar = snackBar;
    this.isEditMode = false;
    this.courseId = null;
    this.isLoading = false;
  }
  ngOnInit() {
    this.initForm();
    this.route.paramMap.subscribe(params => {
      const id = params.get("id");
      if (id) {
        this.courseId = Number(id);
        this.isEditMode = true;
        this.loadCourse(this.courseId);
      }
    });
  }
  initForm() {
    this.courseForm = this.fb.group({
      titre: ["", _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      description: ["", _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      duree: [null, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(1)]],
      niveau: ["", _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      estGratuit: [false],
      prix: [{
        value: null,
        disabled: false
      }, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(0)]],
      contenus: this.fb.array([])
    });
    // Disable price if estGratuit is true initially
    if (this.courseForm.get("estGratuit")?.value) {
      this.courseForm.get("prix")?.disable();
    }
  }
  loadCourse(id) {
    this.isLoading = true;
    // Mock data for demonstration
    const mockCourse = {
      id: id,
      titre: "React Fundamentals (Edit)",
      description: "Apprenez les bases de React avec des exemples pratiques et des projets concrets.",
      prix: 99.99,
      duree: 120,
      niveau: "Débutant",
      formateurId: 1,
      formateur: {
        id: 1,
        nom: "Dupont",
        prenom: "Jean",
        email: "<EMAIL>",
        role: "Formateur"
      },
      contenus: [{
        id: 1,
        titre: "Introduction à React",
        typeContenu: "Video",
        duree: 30,
        coursId: id,
        estComplete: false,
        estDebloque: true,
        ordre: 1
      }, {
        id: 3,
        titre: "Quiz - Bases de React",
        typeContenu: "Quiz",
        seuilReussite: 70,
        coursId: id,
        estComplete: false,
        estDebloque: true,
        ordre: 3
      }],
      nombreEtudiants: 245,
      note: 4.8,
      estGratuit: false
    };
    this.courseForm.patchValue(mockCourse);
    mockCourse.contenus?.forEach(content => {
      this.addContenu(content);
    });
    this.togglePriceField();
    this.isLoading = false;
    // Uncomment to fetch from API
    /*
    this.courseService.getCours(id).subscribe({
      next: (data) => {
        this.courseForm.patchValue(data);
        data.contenus.forEach(content => {
          this.addContenu(content);
        });
        this.togglePriceField();
        this.isLoading = false;
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.isLoading = false;
        this.router.navigate(['/courses']);
      }
    });
    */
  }

  get contenus() {
    return this.courseForm.get("contenus");
  }
  addContenu(content) {
    let contentGroup;
    if (content) {
      if (content.typeContenu === "Video") {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          typeContenu: [content.typeContenu, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          duree: [content.duree, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(1)]],
          coursId: [content.coursId]
        });
      } else if (content.typeContenu === "Quiz") {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          typeContenu: [content.typeContenu, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          seuilReussite: [content.seuilReussite, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(0), _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.max(100)]],
          coursId: [content.coursId]
        });
      } else if (content.typeContenu === "Resume") {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          typeContenu: [content.typeContenu, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          contenuTexte: [content.contenuTexte],
          coursId: [content.coursId]
        });
      } else {
        contentGroup = this.fb.group({
          id: [content.id],
          titre: [content.titre, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          typeContenu: [content.typeContenu, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
          coursId: [content.coursId]
        });
      }
    } else {
      contentGroup = this.fb.group({
        id: [0],
        titre: ["", _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
        typeContenu: ["Video", _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
        duree: [null, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(1)]],
        seuilReussite: [null, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(0), _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.max(100)]],
        contenuTexte: [""],
        coursId: [this.courseId]
      });
    }
    this.contenus.push(contentGroup);
  }
  removeContenu(index) {
    this.contenus.removeAt(index);
  }
  onContentTypeChange(index) {
    const contentGroup = this.contenus.at(index);
    const type = contentGroup.get("typeContenu")?.value;
    // Reset and re-apply validators based on type
    contentGroup.get("duree")?.clearValidators();
    contentGroup.get("duree")?.updateValueAndValidity();
    contentGroup.get("seuilReussite")?.clearValidators();
    contentGroup.get("seuilReussite")?.updateValueAndValidity();
    contentGroup.get("contenuTexte")?.clearValidators();
    contentGroup.get("contenuTexte")?.updateValueAndValidity();
    if (type === "Video") {
      contentGroup.get("duree")?.setValidators([_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(1)]);
    } else if (type === "Quiz") {
      contentGroup.get("seuilReussite")?.setValidators([_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(0), _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.max(100)]);
    }
    // No specific validators for Resume contentText, it's optional
  }

  togglePriceField() {
    const estGratuitControl = this.courseForm.get("estGratuit");
    const prixControl = this.courseForm.get("prix");
    if (estGratuitControl?.value) {
      prixControl?.disable();
      prixControl?.setValue(0);
    } else {
      prixControl?.enable();
      prixControl?.setValue(null); // Clear value when re-enabling
    }

    prixControl?.updateValueAndValidity();
  }
  onSubmit() {
    if (this.courseForm.valid) {
      this.isLoading = true;
      const courseData = this.courseForm.value;
      // Ensure prix is 0 if estGratuit is true, even if disabled field value is not picked up
      if (courseData.estGratuit) {
        courseData.prix = 0;
      }
      if (this.isEditMode && this.courseId) {
        // Update existing course
        this.courseService.modifierCours(this.courseId, courseData).subscribe({
          next: res => {
            this.snackBar.open("Cours mis à jour avec succès !", "Fermer", {
              duration: 3000
            });
            this.isLoading = false;
            this.router.navigate(["/courses", this.courseId]);
          },
          error: err => {
            this.snackBar.open("Erreur lors de la mise à jour du cours.", "Fermer", {
              duration: 3000
            });
            console.error(err);
            this.isLoading = false;
          }
        });
      } else {
        // Create new course
        // Note: Your backend's FormateurController has AjouterCours, but it expects FormateurId in URL.
        // You might need a dedicated POST /api/cours endpoint or adjust this.
        // For now, simulating success.
        this.snackBar.open("Cours créé avec succès (simulé) !", "Fermer", {
          duration: 3000
        });
        this.isLoading = false;
        this.router.navigate(["/courses"]);
        /*
        this.courseService.createCourse(courseData).subscribe({
          next: (res) => {
            this.snackBar.open('Cours créé avec succès !', 'Fermer', { duration: 3000 });
            this.isLoading = false;
            this.router.navigate(['/courses']);
          },
          error: (err) => {
            this.snackBar.open('Erreur lors de la création du cours.', 'Fermer', { duration: 3000 });
            console.error(err);
            this.isLoading = false;
          }
        });
        */
      }
    } else {
      this.snackBar.open("Veuillez remplir tous les champs requis.", "Fermer", {
        duration: 3000
      });
      this.courseForm.markAllAsTouched(); // Show validation errors
    }
  }

  onCancel() {
    this.router.navigate(["/courses"]);
  }
  static {
    this.ɵfac = function CourseCreateEditComponent_Factory(t) {
      return new (t || CourseCreateEditComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_core_services_course_service__WEBPACK_IMPORTED_MODULE_0__.CourseService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__.MatSnackBar));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: CourseCreateEditComponent,
      selectors: [["app-course-create-edit"]],
      decls: 54,
      vars: 12,
      consts: [[1, "course-form-container"], [1, "course-form-card"], [3, "formGroup", "ngSubmit"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "formControlName", "titre"], [4, "ngIf"], ["matInput", "", "formControlName", "description", "rows", "4"], [1, "row-fields"], ["appearance", "outline", 1, "half-width"], ["matInput", "", "type", "number", "formControlName", "duree"], ["formControlName", "niveau"], ["value", "D\u00E9butant"], ["value", "Interm\u00E9diaire"], ["value", "Avanc\u00E9"], ["formControlName", "estGratuit", 3, "change"], ["appearance", "outline", "class", "full-width", 4, "ngIf"], [1, "contents-card"], ["formArrayName", "contenus", 1, "content-list"], ["class", "content-item", 3, "formGroupName", 4, "ngFor", "ngForOf"], ["mat-raised-button", "", "color", "accent", 3, "click"], [1, "form-actions"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"], ["diameter", "20", 4, "ngIf"], ["mat-stroked-button", "", "color", "warn", "type", "button", 3, "click"], ["matInput", "", "type", "number", "formControlName", "prix"], [1, "content-item", 3, "formGroupName"], ["appearance", "outline", 1, "content-type-select"], ["formControlName", "typeContenu", 3, "selectionChange"], ["value", "Video"], ["value", "Quiz"], ["value", "Resume"], ["appearance", "outline", 1, "content-title-input"], ["appearance", "outline", "class", "content-duration-input", 4, "ngIf"], ["appearance", "outline", "class", "content-resume-text", 4, "ngIf"], ["mat-icon-button", "", "color", "warn", 3, "click"], ["appearance", "outline", 1, "content-duration-input"], ["appearance", "outline", 1, "content-quiz-threshold"], ["matInput", "", "type", "number", "formControlName", "seuilReussite"], ["appearance", "outline", 1, "content-resume-text"], ["matInput", "", "formControlName", "contenuTexte", "rows", "3"], ["diameter", "20"]],
      template: function CourseCreateEditComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "mat-card", 1)(2, "mat-card-header")(3, "mat-card-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "mat-card-content")(6, "form", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function CourseCreateEditComponent_Template_form_ngSubmit_6_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "mat-form-field", 3)(8, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9, "Titre du cours");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](10, "input", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](11, CourseCreateEditComponent_mat_error_11_Template, 2, 0, "mat-error", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](12, "mat-form-field", 3)(13, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](14, "Description");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](15, "textarea", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](16, CourseCreateEditComponent_mat_error_16_Template, 2, 0, "mat-error", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "div", 7)(18, "mat-form-field", 8)(19, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](20, "Dur\u00E9e (minutes)");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](21, "input", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](22, CourseCreateEditComponent_mat_error_22_Template, 2, 0, "mat-error", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](23, CourseCreateEditComponent_mat_error_23_Template, 2, 0, "mat-error", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](24, "mat-form-field", 8)(25, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](26, "Niveau");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](27, "mat-select", 10)(28, "mat-option", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](29, "D\u00E9butant");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](30, "mat-option", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](31, "Interm\u00E9diaire");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](32, "mat-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](33, "Avanc\u00E9");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](34, CourseCreateEditComponent_mat_error_34_Template, 2, 0, "mat-error", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](35, "mat-checkbox", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("change", function CourseCreateEditComponent_Template_mat_checkbox_change_35_listener() {
            return ctx.togglePriceField();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](36, "Cours gratuit");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](37, CourseCreateEditComponent_mat_form_field_37_Template, 6, 2, "mat-form-field", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](38, "mat-card", 16)(39, "mat-card-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](40, "Contenus du cours");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](41, "mat-card-content")(42, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](43, CourseCreateEditComponent_div_43_Template, 21, 4, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](44, "button", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function CourseCreateEditComponent_Template_button_click_44_listener() {
            return ctx.addContenu();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](45, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](46, "add");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](47, " Ajouter un contenu ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](48, "div", 20)(49, "button", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](50, CourseCreateEditComponent_mat_spinner_50_Template, 1, 0, "mat-spinner", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](51, CourseCreateEditComponent_span_51_Template, 2, 1, "span", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](52, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function CourseCreateEditComponent_Template_button_click_52_listener() {
            return ctx.onCancel();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](53, "Annuler");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          let tmp_2_0;
          let tmp_3_0;
          let tmp_4_0;
          let tmp_5_0;
          let tmp_6_0;
          let tmp_7_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx.isEditMode ? "Modifier le cours" : "Cr\u00E9er un nouveau cours");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.courseForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (tmp_2_0 = ctx.courseForm.get("titre")) == null ? null : tmp_2_0.hasError("required"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (tmp_3_0 = ctx.courseForm.get("description")) == null ? null : tmp_3_0.hasError("required"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (tmp_4_0 = ctx.courseForm.get("duree")) == null ? null : tmp_4_0.hasError("required"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (tmp_5_0 = ctx.courseForm.get("duree")) == null ? null : tmp_5_0.hasError("min"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (tmp_6_0 = ctx.courseForm.get("niveau")) == null ? null : tmp_6_0.hasError("required"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !((tmp_7_0 = ctx.courseForm.get("estGratuit")) == null ? null : tmp_7_0.value));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.contenus.controls);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("disabled", ctx.courseForm.invalid || ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.isLoading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupName, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormArrayName, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardHeader, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardTitle, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatIconButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__.MatIcon, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatError, _angular_material_select__WEBPACK_IMPORTED_MODULE_11__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_12__.MatOption, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_13__.MatProgressSpinner],
      styles: [".course-form-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: flex-start;\n}\n\n.course-form-card[_ngcontent-%COMP%] {\n  max-width: 800px;\n  width: 100%;\n  padding: 1.5rem;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n}\n\n.course-form-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.8rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.full-width[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-bottom: 1rem;\n}\n\n.row-fields[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n\n.half-width[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\nmat-checkbox[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n\n.contents-card[_ngcontent-%COMP%] {\n  margin-top: 2rem;\n  padding: 1.5rem;\n  background-color: #f9f9f9;\n  border: 1px solid #eee;\n}\n\n.contents-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.4rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n  text-align: left;\n}\n\n.content-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.content-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  align-items: center;\n  padding: 1rem;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);\n}\n\n.content-type-select[_ngcontent-%COMP%] {\n  flex: 1 1 180px;\n}\n\n.content-title-input[_ngcontent-%COMP%] {\n  flex: 2 1 250px;\n}\n\n.content-duration-input[_ngcontent-%COMP%], .content-quiz-threshold[_ngcontent-%COMP%] {\n  flex: 1 1 150px;\n}\n\n.content-resume-text[_ngcontent-%COMP%] {\n  flex: 3 1 300px;\n}\n\n.content-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n}\n\n.form-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n\n.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  padding: 0.8rem 1.5rem;\n  font-size: 1rem;\n}\n\nmat-spinner[_ngcontent-%COMP%] {\n  margin-right: 0.5rem;\n}\n\n@media (max-width: 768px) {\n  .row-fields[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .half-width[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n  .content-item[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .content-item[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n  .form-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 1003:
/*!***************************************************************************!*\
  !*** ./src/app/features/courses/course-detail/course-detail.component.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CourseDetailComponent: () => (/* binding */ CourseDetailComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _core_services_course_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../core/services/course.service */ 8769);
/* harmony import */ var _core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../core/services/auth.service */ 8010);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/snack-bar */ 3347);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/chips */ 2772);
/* harmony import */ var _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/progress-bar */ 6354);
/* harmony import */ var _angular_material_tabs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/tabs */ 8223);












function CourseDetailComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 32)(1, "div", 33)(2, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Progression du cours");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](6, "mat-progress-bar", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r0.course.progression, "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx_r0.course.progression);
  }
}
function CourseDetailComponent_div_57_mat_icon_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "check_circle");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function CourseDetailComponent_div_57_mat_icon_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const content_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]().$implicit;
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r11.getContentIcon(content_r8.typeContenu));
  }
}
function CourseDetailComponent_div_57_mat_icon_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "lock");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function CourseDetailComponent_div_57_span_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "span", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const content_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", content_r8.duree, " minutes");
  }
}
function CourseDetailComponent_div_57_button_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r19 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CourseDetailComponent_div_57_button_11_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r19);
      const content_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]().$implicit;
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r17.handleStartContent(content_r8.id, content_r8.typeContenu));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const content_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", content_r8.estComplete ? "Revoir" : "Commencer", " ");
  }
}
const _c0 = function (a0, a1, a2) {
  return {
    "completed": a0,
    "unlocked": a1,
    "locked": a2
  };
};
function CourseDetailComponent_div_57_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 35)(1, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, CourseDetailComponent_div_57_mat_icon_2_Template, 2, 0, "mat-icon", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](3, CourseDetailComponent_div_57_mat_icon_3_Template, 2, 1, "mat-icon", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, CourseDetailComponent_div_57_mat_icon_4_Template, 2, 0, "mat-icon", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 38)(6, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "p", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](10, CourseDetailComponent_div_57_span_10_Template, 2, 1, "span", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](11, CourseDetailComponent_div_57_button_11_Template, 2, 1, "button", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const content_r8 = ctx.$implicit;
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction3"](8, _c0, content_r8.estComplete, content_r8.estDebloque && !content_r8.estComplete, !content_r8.estDebloque));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", content_r8.estComplete);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !content_r8.estComplete && content_r8.estDebloque);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !content_r8.estDebloque);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](content_r8.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](content_r8.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", content_r8.duree);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r1.course.estAchete && content_r8.estDebloque);
  }
}
const _c1 = function (a0) {
  return [a0, "EUR", "symbol", "1.2-2", "fr"];
};
function CourseDetailComponent_div_75_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 44)(1, "span", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](3, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBindV"](3, 1, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction1"](7, _c1, ctx_r2.course.prix)));
  }
}
function CourseDetailComponent_div_76_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 44)(1, "mat-chip-listbox")(2, "mat-chip", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Cours Gratuit");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
}
function CourseDetailComponent_button_78_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CourseDetailComponent_button_78_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r22);
      const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r21.enrollInCourse(ctx_r21.course.id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Commencer le cours ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
const _c2 = function (a1) {
  return ["/payment", a1];
};
function CourseDetailComponent_button_79_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 48)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "euro_symbol");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, " Acheter le cours ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction1"](1, _c2, ctx_r5.course.id));
  }
}
function CourseDetailComponent_button_80_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CourseDetailComponent_button_80_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r24);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r23.selectedTabIndex = 1);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Continuer le cours ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function CourseDetailComponent_mat_chip_listbox_81_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-chip-listbox", 50)(1, "mat-chip", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "Cours achet\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
class CourseDetailComponent {
  constructor(route, router, courseService, authService, snackBar) {
    this.route = route;
    this.router = router;
    this.courseService = courseService;
    this.authService = authService;
    this.snackBar = snackBar;
    this.selectedTabIndex = 0;
  }
  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      this.courseId = Number(params.get("id"));
      this.loadCourseDetails();
    });
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }
  loadCourseDetails() {
    // Mock data for demonstration
    this.course = {
      id: this.courseId,
      titre: "React Fundamentals",
      description: "Apprenez les bases de React avec des exemples pratiques et des projets concrets. Ce cours couvre tous les concepts essentiels pour débuter avec React : composants, props, state, hooks, et bien plus encore.",
      prix: 99.99,
      duree: 120,
      niveau: "Débutant",
      formateurId: 1,
      formateur: {
        id: 1,
        nom: "Dupont",
        prenom: "Jean",
        email: "<EMAIL>",
        role: "Formateur",
        bio: "Développeur Full-Stack avec 8 ans d'expérience. Spécialisé en React et Node.js. Formateur passionné ayant formé plus de 1000 développeurs."
      },
      contenus: [{
        id: 1,
        titre: "Introduction à React",
        description: "Découvrez React et ses concepts de base",
        typeContenu: "Video",
        duree: 30,
        estComplete: true,
        estDebloque: true,
        ordre: 1,
        coursId: this.courseId
      }, {
        id: 2,
        titre: "Components et Props",
        description: "Apprenez à créer et utiliser des composants",
        typeContenu: "Video",
        duree: 45,
        estComplete: true,
        estDebloque: true,
        ordre: 2,
        coursId: this.courseId
      }, {
        id: 3,
        titre: "Quiz - Bases de React",
        description: "Testez vos connaissances sur les bases",
        typeContenu: "Quiz",
        estComplete: false,
        estDebloque: true,
        ordre: 3,
        coursId: this.courseId
      }, {
        id: 4,
        titre: "State et Hooks",
        description: "Gérez l'état de vos composants",
        typeContenu: "Video",
        duree: 50,
        estComplete: false,
        estDebloque: false,
        ordre: 4,
        coursId: this.courseId
      }, {
        id: 5,
        titre: "Résumé du chapitre",
        description: "Points clés à retenir",
        typeContenu: "Resume",
        estComplete: false,
        estDebloque: false,
        ordre: 5,
        coursId: this.courseId
      }],
      nombreEtudiants: 245,
      note: 4.8,
      estGratuit: false,
      estAchete: true,
      progression: 40 // Simulate progress
    };
    // Uncomment to fetch from API
    /*
    this.courseService.getCours(this.courseId).subscribe({
      next: (data) => {
        this.course = data;
        // TODO: Fetch user enrollment status and progress
        this.course.estAchete = true; // Example
        this.course.progression = 40; // Example
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });
        console.error(err);
        this.router.navigate(['/courses']);
      }
    });
    */
  }

  getLevelColor(niveau) {
    switch (niveau) {
      case "Débutant":
        return "bg-green-100";
      case "Intermédiaire":
        return "bg-yellow-100";
      case "Avancé":
        return "bg-red-100";
      default:
        return "bg-gray-100";
    }
  }
  getContentIcon(type) {
    switch (type) {
      case "Video":
        return "play_circle";
      case "Quiz":
        return "quiz";
      case "Resume":
        return "description";
      default:
        return "book";
    }
  }
  handleStartContent(contentId, typeContenu) {
    if (!this.course.estAchete) {
      this.snackBar.open("Veuillez acheter le cours pour accéder au contenu.", "Fermer", {
        duration: 3000
      });
      return;
    }
    const content = this.course.contenus?.find(c => c.id === contentId);
    if (!content?.estDebloque) {
      this.snackBar.open("Ce contenu n'est pas encore débloqué.", "Fermer", {
        duration: 3000
      });
      return;
    }
    switch (typeContenu) {
      case "Quiz":
        this.router.navigate(["/quiz", contentId]);
        break;
      case "Video":
        this.router.navigate(["/video", contentId]); // Assuming a video player component
        break;
      case "Resume":
        this.router.navigate(["/resume", contentId]); // Assuming a resume viewer component
        break;
      default:
        this.snackBar.open("Type de contenu non pris en charge.", "Fermer", {
          duration: 3000
        });
    }
  }
  enrollInCourse(courseId) {
    if (!this.currentUser) {
      this.snackBar.open("Veuillez vous connecter pour vous inscrire.", "Fermer", {
        duration: 3000
      });
      this.router.navigate(["/auth/login"]);
      return;
    }
    // Call the service to enroll in a free course
    // this.courseService.enrollInCourse(courseId).subscribe({
    //   next: (res) => {
    //     this.snackBar.open('Inscription réussie au cours gratuit !', 'Fermer', { duration: 3000 });
    //     this.course.estAchete = true; // Update UI
    //     this.course.progression = 0;
    //   },
    //   error: (err) => {
    //     this.snackBar.open('Erreur lors de l\'inscription.', 'Fermer', { duration: 3000 });
    //     console.error(err);
    //   }
    // });
    this.snackBar.open("Inscription réussie au cours gratuit (simulé) !", "Fermer", {
      duration: 3000
    });
    this.course.estAchete = true; // Simulate UI update
    this.course.progression = 0;
  }
  static {
    this.ɵfac = function CourseDetailComponent_Factory(t) {
      return new (t || CourseDetailComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_course_service__WEBPACK_IMPORTED_MODULE_0__.CourseService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_core_services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__.MatSnackBar));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: CourseDetailComponent,
      selectors: [["app-course-detail"]],
      decls: 106,
      vars: 26,
      consts: [[1, "course-detail-container"], [1, "content-wrapper"], [1, "main-content"], [1, "course-header"], [1, "description"], [1, "course-meta"], [1, "meta-item"], [1, "star-icon"], ["class", "progress-section", 4, "ngIf"], ["animationDuration", "0ms", 3, "selectedIndex", "selectedIndexChange"], ["label", "Aper\u00E7u"], [1, "tab-card"], [1, "description-full"], [1, "learning-outcomes"], ["label", "Contenu"], [1, "content-list"], ["class", "content-item", 4, "ngFor", "ngForOf"], ["label", "Formateur"], [1, "instructor-info"], [1, "instructor-avatar"], [1, "instructor-bio"], [1, "sidebar"], [1, "sticky-card"], ["class", "price-section", 4, "ngIf"], [1, "action-buttons"], ["mat-raised-button", "", "color", "accent", "class", "full-width-btn", 3, "click", 4, "ngIf"], ["mat-raised-button", "", "color", "primary", "class", "full-width-btn", 3, "routerLink", 4, "ngIf"], ["mat-raised-button", "", "color", "primary", "class", "full-width-btn", 3, "click", 4, "ngIf"], ["class", "purchased-chip", 4, "ngIf"], [1, "course-summary"], [1, "summary-item"], [1, "rating-display"], [1, "progress-section"], [1, "progress-label"], ["mode", "determinate", 3, "value"], [1, "content-item"], [1, "content-icon-wrapper", 3, "ngClass"], [4, "ngIf"], [1, "content-details"], [1, "content-description"], ["class", "content-duration", 4, "ngIf"], ["mat-stroked-button", "", "color", "primary", 3, "click", 4, "ngIf"], [1, "content-duration"], ["mat-stroked-button", "", "color", "primary", 3, "click"], [1, "price-section"], [1, "price-value"], [1, "free-chip-large"], ["mat-raised-button", "", "color", "accent", 1, "full-width-btn", 3, "click"], ["mat-raised-button", "", "color", "primary", 1, "full-width-btn", 3, "routerLink"], ["mat-raised-button", "", "color", "primary", 1, "full-width-btn", 3, "click"], [1, "purchased-chip"], [1, "purchased-chip-item"]],
      template: function CourseDetailComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "p", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "div", 5)(9, "mat-chip-listbox")(10, "mat-chip");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "div", 6)(13, "mat-icon", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "star");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "div", 6)(18, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, "schedule");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](21);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](22, CourseDetailComponent_div_22_Template, 7, 2, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "mat-tab-group", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("selectedIndexChange", function CourseDetailComponent_Template_mat_tab_group_selectedIndexChange_23_listener($event) {
            return ctx.selectedTabIndex = $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](24, "mat-tab", 10)(25, "mat-card", 11)(26, "mat-card-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, "Description du cours");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "mat-card-content")(29, "p", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](30);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](31, "div", 13)(32, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](33, "Ce que vous apprendrez :");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](34, "ul")(35, "li")(36, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](37, "check_circle");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](38, " Les concepts fondamentaux de React");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](39, "li")(40, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](41, "check_circle");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](42, " Cr\u00E9ation et utilisation de composants");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](43, "li")(44, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](45, "check_circle");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](46, " Gestion de l'\u00E9tat avec les hooks");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](47, "li")(48, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](49, "check_circle");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](50, " Bonnes pratiques de d\u00E9veloppement");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](51, "mat-tab", 14)(52, "mat-card", 11)(53, "mat-card-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](54, "Contenu du cours");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](55, "mat-card-content")(56, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](57, CourseDetailComponent_div_57_Template, 12, 12, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](58, "mat-tab", 17)(59, "mat-card", 11)(60, "mat-card-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](61, "\u00C0 propos du formateur");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](62, "mat-card-content")(63, "div", 18)(64, "div", 19)(65, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](66);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](67, "div")(68, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](69);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](70, "p", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](71, "D\u00E9veloppeur Full-Stack avec 8 ans d'exp\u00E9rience. Sp\u00E9cialis\u00E9 en React et Node.js. Formateur passionn\u00E9 ayant form\u00E9 plus de 1000 d\u00E9veloppeurs.");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](72, "div", 21)(73, "mat-card", 22)(74, "mat-card-content");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](75, CourseDetailComponent_div_75_Template, 4, 9, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](76, CourseDetailComponent_div_76_Template, 4, 0, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](77, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](78, CourseDetailComponent_button_78_Template, 2, 0, "button", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](79, CourseDetailComponent_button_79_Template, 4, 3, "button", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](80, CourseDetailComponent_button_80_Template, 2, 0, "button", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](81, CourseDetailComponent_mat_chip_listbox_81_Template, 3, 0, "mat-chip-listbox", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](82, "div", 29)(83, "div", 30)(84, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](85, "Dur\u00E9e totale:");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](86, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](87);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](88, "div", 30)(89, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](90, "Niveau:");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](91, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](92);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](93, "div", 30)(94, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](95, "\u00C9tudiants:");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](96, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](97);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](98, "div", 30)(99, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](100, "Note:");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](101, "div", 31)(102, "mat-icon", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](103, "star");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](104, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](105);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.course.titre);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.course.description);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](ctx.getLevelColor(ctx.course.niveau || "D\u00E9butant"));
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.course.niveau);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"]("", ctx.course.note, " (", ctx.course.nombreEtudiants, " \u00E9tudiants)");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx.course.duree, " minutes");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.course.estAchete);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("selectedIndex", ctx.selectedTabIndex);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.course.description);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.course.contenus);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"]("", ctx.course.formateur == null ? null : ctx.course.formateur.prenom == null ? null : ctx.course.formateur.prenom[0], "", ctx.course.formateur == null ? null : ctx.course.formateur.nom == null ? null : ctx.course.formateur.nom[0], "");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"]("", ctx.course.formateur == null ? null : ctx.course.formateur.prenom, " ", ctx.course.formateur == null ? null : ctx.course.formateur.nom, "");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.course.estGratuit);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.course.estGratuit);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.course.estAchete && ctx.course.estGratuit);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.course.estAchete && !ctx.course.estGratuit);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.course.estAchete);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.course.estAchete);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx.course.duree, " minutes");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.course.niveau);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.course.nombreEtudiants);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.course.note);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardTitle, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__.MatIcon, _angular_material_chips__WEBPACK_IMPORTED_MODULE_9__.MatChip, _angular_material_chips__WEBPACK_IMPORTED_MODULE_9__.MatChipListbox, _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_10__.MatProgressBar, _angular_material_tabs__WEBPACK_IMPORTED_MODULE_11__.MatTab, _angular_material_tabs__WEBPACK_IMPORTED_MODULE_11__.MatTabGroup, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink, _angular_common__WEBPACK_IMPORTED_MODULE_5__.CurrencyPipe],
      styles: [".course-detail-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.content-wrapper[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.main-content[_ngcontent-%COMP%] {\n  grid-column: span 2; \n\n}\n\n@media (min-width: 960px) {\n  .main-content[_ngcontent-%COMP%] {\n    grid-column: span 2/span 2;\n  }\n  .sidebar[_ngcontent-%COMP%] {\n    grid-column: span 1/span 1;\n  }\n}\n.course-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n\n.course-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 0.5rem;\n}\n\n.course-header[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  color: #666;\n  margin-bottom: 1.5rem;\n}\n\n.course-meta[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.course-meta[_ngcontent-%COMP%]   .mat-chip[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  padding: 0.4rem 0.8rem;\n  height: auto;\n}\n\n.course-meta[_ngcontent-%COMP%]   .mat-chip.bg-green-100[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.course-meta[_ngcontent-%COMP%]   .mat-chip.bg-yellow-100[_ngcontent-%COMP%] {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n.course-meta[_ngcontent-%COMP%]   .mat-chip.bg-red-100[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.meta-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.3rem;\n  font-size: 0.95rem;\n  color: #555;\n}\n\n.meta-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  width: 1.2rem;\n  height: 1.2rem;\n  color: #888;\n}\n\n.meta-item[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%] {\n  color: #ffc107; \n\n}\n\n.progress-section[_ngcontent-%COMP%] {\n  margin-top: 1.5rem;\n}\n\n.progress-label[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  font-size: 0.9rem;\n  font-weight: 500;\n  margin-bottom: 0.5rem;\n  color: #444;\n}\n\nmat-progress-bar[_ngcontent-%COMP%] {\n  height: 8px;\n  border-radius: 4px;\n}\n\n.tab-card[_ngcontent-%COMP%] {\n  margin-top: 1.5rem;\n  padding: 1.5rem;\n}\n\n.tab-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.4rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n}\n\n.description-full[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  line-height: 1.6;\n  color: #444;\n}\n\n.learning-outcomes[_ngcontent-%COMP%] {\n  margin-top: 2rem;\n}\n\n.learning-outcomes[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: #333;\n}\n\n.learning-outcomes[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.learning-outcomes[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.8rem;\n  font-size: 0.95rem;\n  color: #555;\n}\n\n.learning-outcomes[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #4caf50; \n\n  margin-right: 0.8rem;\n  font-size: 1.2rem;\n  width: 1.2rem;\n  height: 1.2rem;\n}\n\n.content-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.content-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  border: 1px solid #eee;\n  border-radius: 8px;\n  background-color: #fff;\n  transition: box-shadow 0.2s ease-in-out;\n}\n\n.content-item[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n}\n\n.content-icon-wrapper[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 1rem;\n  flex-shrink: 0;\n}\n\n.content-icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  width: 1.5rem;\n  height: 1.5rem;\n}\n\n.content-icon-wrapper.completed[_ngcontent-%COMP%] {\n  background-color: #e8f5e9; \n\n}\n\n.content-icon-wrapper.completed[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #4caf50; \n\n}\n\n.content-icon-wrapper.unlocked[_ngcontent-%COMP%] {\n  background-color: #e3f2fd; \n\n}\n\n.content-icon-wrapper.unlocked[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #2196f3; \n\n}\n\n.content-icon-wrapper.locked[_ngcontent-%COMP%] {\n  background-color: #f5f5f5; \n\n}\n\n.content-icon-wrapper.locked[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #9e9e9e; \n\n}\n\n.content-details[_ngcontent-%COMP%] {\n  flex-grow: 1;\n}\n\n.content-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  font-weight: 500;\n  margin-bottom: 0.2rem;\n  color: #333;\n}\n\n.content-description[_ngcontent-%COMP%] {\n  font-size: 0.85rem;\n  color: #777;\n  margin-bottom: 0.4rem;\n}\n\n.content-duration[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #888;\n}\n\n.content-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  margin-left: 1rem;\n  flex-shrink: 0;\n}\n\n.instructor-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: 1.5rem;\n  padding: 1rem 0;\n}\n\n.instructor-avatar[_ngcontent-%COMP%] {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  background-color: #e1bee7; \n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  font-weight: bold;\n  color: #8e24aa; \n\n  flex-shrink: 0;\n}\n\n.instructor-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n\n.instructor-bio[_ngcontent-%COMP%] {\n  font-size: 0.95rem;\n  line-height: 1.5;\n  color: #555;\n}\n\n.sidebar[_ngcontent-%COMP%] {\n  grid-column: span 1;\n}\n\n.sticky-card[_ngcontent-%COMP%] {\n  position: sticky;\n  top: 2rem; \n\n  padding: 1.5rem;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n}\n\n.price-section[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 1.5rem;\n}\n\n.price-value[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #673ab7; \n\n}\n\n.free-chip-large[_ngcontent-%COMP%] {\n  background-color: #e6ffed;\n  color: #28a745;\n  font-size: 1.2rem;\n  padding: 0.8rem 1.5rem;\n  height: auto;\n}\n\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.8rem;\n  margin-bottom: 1.5rem;\n}\n\n.full-width-btn[_ngcontent-%COMP%] {\n  width: 100%;\n  font-size: 1rem;\n  padding: 0.8rem 1rem;\n}\n\n.full-width-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  width: 1.2rem;\n  height: 1.2rem;\n  margin-right: 0.5rem;\n}\n\n.purchased-chip[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 1rem;\n}\n\n.purchased-chip-item[_ngcontent-%COMP%] {\n  background-color: #e8f5e9;\n  color: #4caf50;\n  font-size: 0.9rem;\n  padding: 0.4rem 0.8rem;\n  height: auto;\n}\n\n.course-summary[_ngcontent-%COMP%] {\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #eee;\n  display: flex;\n  flex-direction: column;\n  gap: 0.8rem;\n  font-size: 0.95rem;\n  color: #555;\n}\n\n.summary-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.rating-display[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.3rem;\n}\n\n.rating-display[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%] {\n  color: #ffc107; \n\n  font-size: 1.1rem;\n  width: 1.1rem;\n  height: 1.1rem;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 2835:
/*!***********************************************************************!*\
  !*** ./src/app/features/courses/course-list/course-list.component.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CourseListComponent: () => (/* binding */ CourseListComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _core_services_course_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../core/services/course.service */ 8769);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/material/snack-bar */ 3347);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/select */ 5175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/core */ 4646);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/chips */ 2772);














const _c0 = function (a0) {
  return [a0, "EUR", "symbol", "1.2-2", "fr"];
};
function CourseListComponent_div_33_mat_card_1_span_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipe"](2, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const course_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipeBindV"](2, 1, _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](7, _c0, course_r4.prix)));
  }
}
function CourseListComponent_div_33_mat_card_1_mat_chip_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-chip", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Gratuit");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function CourseListComponent_div_33_mat_card_1_div_37_span_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const content_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("(", content_r12.duree, " min)");
  }
}
function CourseListComponent_div_33_mat_card_1_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 35)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](5, CourseListComponent_div_33_mat_card_1_div_37_span_5_Template, 2, 1, "span", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const content_r12 = ctx.$implicit;
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r7.getContentIcon(content_r12.typeContenu));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](content_r12.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", content_r12.duree);
  }
}
function CourseListComponent_div_33_mat_card_1_p_39_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "p", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const course_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("+", course_r4.contenus.length - 3, " autres contenus");
  }
}
function CourseListComponent_div_33_mat_card_1_button_43_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "button", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Commencer");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
const _c1 = function (a1) {
  return ["/payment", a1];
};
function CourseListComponent_div_33_mat_card_1_button_44_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "button", 40)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "euro_symbol");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, " Acheter ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const course_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](1, _c1, course_r4.id));
  }
}
const _c2 = function (a1) {
  return ["/courses", a1];
};
function CourseListComponent_div_33_mat_card_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-card", 18)(1, "mat-card-header")(2, "div", 19)(3, "mat-chip-listbox")(4, "mat-chip");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](6, CourseListComponent_div_33_mat_card_1_span_6_Template, 3, 9, "span", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](7, CourseListComponent_div_33_mat_card_1_mat_chip_7_Template, 2, 0, "mat-chip", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](10, "mat-card-subtitle", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](12, "mat-card-content")(13, "div", 23)(14, "div", 24)(15, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](16, "person");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](19, "div", 24)(20, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](21, "schedule");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](22, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](23);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](24, "div", 24)(25, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](26, "group");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](27, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](28);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](29, "div", 24)(30, "mat-icon", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](31, "star");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](32, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](33);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](34, "div", 26)(35, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](36, "Contenu du cours:");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](37, CourseListComponent_div_33_mat_card_1_div_37_Template, 6, 3, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipe"](38, "slice");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](39, CourseListComponent_div_33_mat_card_1_p_39_Template, 2, 1, "p", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](40, "div", 29)(41, "button", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](42, " Voir d\u00E9tails ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](43, CourseListComponent_div_33_mat_card_1_button_43_Template, 2, 0, "button", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](44, CourseListComponent_div_33_mat_card_1_button_44_Template, 4, 3, "button", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const course_r4 = ctx.$implicit;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassMap"](ctx_r3.getLevelColor(course_r4.niveau || "D\u00E9butant"));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](course_r4.niveau);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !course_r4.estGratuit);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", course_r4.estGratuit);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](course_r4.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](course_r4.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate2"]("", course_r4.formateur == null ? null : course_r4.formateur.prenom, " ", course_r4.formateur == null ? null : course_r4.formateur.nom, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", course_r4.duree, " min");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", course_r4.nombreEtudiants, " \u00E9tudiants");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](course_r4.note);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipeBind3"](38, 17, course_r4.contenus, 0, 3));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", course_r4.contenus && course_r4.contenus.length > 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](21, _c2, course_r4.id));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", course_r4.estGratuit);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !course_r4.estGratuit);
  }
}
function CourseListComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, CourseListComponent_div_33_mat_card_1_Template, 45, 23, "mat-card", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx_r0.filteredCourses);
  }
}
function CourseListComponent_ng_template_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 41)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "book_off");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4, "Aucun cours trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "Essayez de modifier vos crit\u00E8res de recherche.");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
  }
}
class CourseListComponent {
  constructor(courseService, snackBar, router) {
    this.courseService = courseService;
    this.snackBar = snackBar;
    this.router = router;
    this.courses = [];
    this.filteredCourses = [];
    this.searchTerm = "";
    this.filterLevel = "all";
    this.filterPrice = "all";
  }
  ngOnInit() {
    this.loadCourses();
  }
  loadCourses() {
    // Mock data for demonstration
    this.courses = [{
      id: 1,
      titre: "React Fundamentals",
      description: "Apprenez les bases de React avec des exemples pratiques et des projets concrets.",
      prix: 99.99,
      duree: 120,
      niveau: "Débutant",
      formateurId: 1,
      formateur: {
        id: 1,
        nom: "Dupont",
        prenom: "Jean",
        email: "<EMAIL>",
        role: "Formateur"
      },
      contenus: [{
        id: 1,
        titre: "Introduction à React",
        typeContenu: "Video",
        coursId: 1,
        estComplete: false,
        estDebloque: true,
        ordre: 1
      }, {
        id: 2,
        titre: "Components et Props",
        typeContenu: "Video",
        coursId: 1,
        estComplete: false,
        estDebloque: true,
        ordre: 2
      }, {
        id: 3,
        titre: "Quiz - Bases de React",
        typeContenu: "Quiz",
        coursId: 1,
        estComplete: false,
        estDebloque: true,
        ordre: 3
      }, {
        id: 4,
        titre: "Résumé du chapitre",
        typeContenu: "Resume",
        coursId: 1,
        estComplete: false,
        estDebloque: true,
        ordre: 4
      }],
      nombreEtudiants: 245,
      note: 4.8,
      estGratuit: false
    }, {
      id: 2,
      titre: "JavaScript Avancé",
      description: "Maîtrisez les concepts avancés de JavaScript : closures, prototypes, async/await.",
      prix: 149.99,
      duree: 180,
      niveau: "Avancé",
      formateurId: 2,
      formateur: {
        id: 2,
        nom: "Martin",
        prenom: "Sophie",
        email: "<EMAIL>",
        role: "Formateur"
      },
      contenus: [{
        id: 5,
        titre: "Closures et Scope",
        typeContenu: "Video",
        coursId: 2,
        estComplete: false,
        estDebloque: true,
        ordre: 1
      }, {
        id: 6,
        titre: "Prototypes et Héritage",
        typeContenu: "Video",
        coursId: 2,
        estComplete: false,
        estDebloque: true,
        ordre: 2
      }, {
        id: 7,
        titre: "Quiz - Concepts avancés",
        typeContenu: "Quiz",
        coursId: 2,
        estComplete: false,
        estDebloque: true,
        ordre: 3
      }],
      nombreEtudiants: 156,
      note: 4.9,
      estGratuit: false
    }, {
      id: 3,
      titre: "Introduction au Web",
      description: "Cours gratuit pour débuter dans le développement web avec HTML, CSS et JavaScript.",
      prix: 0,
      duree: 60,
      niveau: "Débutant",
      formateurId: 3,
      formateur: {
        id: 3,
        nom: "Bernard",
        prenom: "Pierre",
        email: "<EMAIL>",
        role: "Formateur"
      },
      contenus: [{
        id: 8,
        titre: "HTML Basics",
        typeContenu: "Video",
        coursId: 3,
        estComplete: false,
        estDebloque: true,
        ordre: 1
      }, {
        id: 9,
        titre: "CSS Styling",
        typeContenu: "Video",
        coursId: 3,
        estComplete: false,
        estDebloque: true,
        ordre: 2
      }, {
        id: 10,
        titre: "Quiz final",
        typeContenu: "Quiz",
        coursId: 3,
        estComplete: false,
        estDebloque: true,
        ordre: 3
      }],
      nombreEtudiants: 892,
      note: 4.6,
      estGratuit: true
    }];
    this.applyFilters();
    // Uncomment to fetch from API
    /*
    this.courseService.getAllCours().subscribe({
      next: (data) => {
        this.courses = data;
        this.applyFilters();
      },
      error: (err) => {
        this.snackBar.open('Erreur lors du chargement des cours.', 'Fermer', { duration: 3000 });
        console.error(err);
      }
    });
    */
  }

  applyFilters() {
    this.filteredCourses = this.courses.filter(course => {
      const matchesSearch = course.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) || (course.description || '').toLowerCase().includes(this.searchTerm.toLowerCase());
      const matchesLevel = this.filterLevel === "all" || course.niveau === this.filterLevel;
      const matchesPrice = this.filterPrice === "all" || this.filterPrice === "free" && course.estGratuit || this.filterPrice === "paid" && !course.estGratuit;
      return matchesSearch && matchesLevel && matchesPrice;
    });
  }
  getContentIcon(type) {
    switch (type) {
      case "Video":
        return "play_circle";
      case "Quiz":
        return "quiz";
      case "Resume":
        return "description";
      default:
        return "book";
    }
  }
  getLevelColor(niveau) {
    switch (niveau) {
      case "Débutant":
        return "bg-green-100";
      case "Intermédiaire":
        return "bg-yellow-100";
      case "Avancé":
        return "bg-red-100";
      default:
        return "bg-gray-100";
    }
  }
  static {
    this.ɵfac = function CourseListComponent_Factory(t) {
      return new (t || CourseListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_core_services_course_service__WEBPACK_IMPORTED_MODULE_0__.CourseService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__.MatSnackBar), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: CourseListComponent,
      selectors: [["app-course-list"]],
      decls: 36,
      vars: 5,
      consts: [[1, "course-list-container"], [1, "header-section"], [1, "filters-row"], ["appearance", "outline", 1, "search-input"], ["matInput", "", 3, "ngModel", "ngModelChange", "input"], ["matSuffix", ""], ["appearance", "outline", 1, "filter-select"], [3, "ngModel", "ngModelChange", "selectionChange"], ["value", "all"], ["value", "D\u00E9butant"], ["value", "Interm\u00E9diaire"], ["value", "Avanc\u00E9"], ["value", "free"], ["value", "paid"], ["class", "courses-grid", 4, "ngIf", "ngIfElse"], ["noCourses", ""], [1, "courses-grid"], ["class", "course-card", 4, "ngFor", "ngForOf"], [1, "course-card"], [1, "card-header-top"], ["class", "price", 4, "ngIf"], ["class", "free-chip", 4, "ngIf"], [1, "description"], [1, "course-info"], [1, "info-item"], [1, "star-icon"], [1, "content-preview"], ["class", "content-item", 4, "ngFor", "ngForOf"], ["class", "more-content", 4, "ngIf"], [1, "card-actions"], ["mat-stroked-button", "", "color", "primary", 3, "routerLink"], ["mat-raised-button", "", "color", "accent", 4, "ngIf"], ["mat-raised-button", "", "color", "primary", 3, "routerLink", 4, "ngIf"], [1, "price"], [1, "free-chip"], [1, "content-item"], ["class", "content-duration", 4, "ngIf"], [1, "content-duration"], [1, "more-content"], ["mat-raised-button", "", "color", "accent"], ["mat-raised-button", "", "color", "primary", 3, "routerLink"], [1, "no-courses"]],
      template: function CourseListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "Catalogue des Cours");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 2)(5, "mat-form-field", 3)(6, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7, "Rechercher un cours...");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "input", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngModelChange", function CourseListComponent_Template_input_ngModelChange_8_listener($event) {
            return ctx.searchTerm = $event;
          })("input", function CourseListComponent_Template_input_input_8_listener() {
            return ctx.applyFilters();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "mat-icon", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10, "search");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "mat-form-field", 6)(12, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](13, "Niveau");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](14, "mat-select", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngModelChange", function CourseListComponent_Template_mat_select_ngModelChange_14_listener($event) {
            return ctx.filterLevel = $event;
          })("selectionChange", function CourseListComponent_Template_mat_select_selectionChange_14_listener() {
            return ctx.applyFilters();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](15, "mat-option", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](16, "Tous les niveaux");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "mat-option", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](18, "D\u00E9butant");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](19, "mat-option", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](20, "Interm\u00E9diaire");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](21, "mat-option", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](22, "Avanc\u00E9");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](23, "mat-form-field", 6)(24, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](25, "Prix");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](26, "mat-select", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngModelChange", function CourseListComponent_Template_mat_select_ngModelChange_26_listener($event) {
            return ctx.filterPrice = $event;
          })("selectionChange", function CourseListComponent_Template_mat_select_selectionChange_26_listener() {
            return ctx.applyFilters();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](27, "mat-option", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](28, "Tous les prix");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](29, "mat-option", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](30, "Gratuit");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](31, "mat-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](32, "Payant");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](33, CourseListComponent_div_33_Template, 2, 1, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](34, CourseListComponent_ng_template_34_Template, 7, 0, "ng-template", null, 15, _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵreference"](35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngModel", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngModel", ctx.filterLevel);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngModel", ctx.filterPrice);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.filteredCourses.length > 0)("ngIfElse", _r1);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgModel, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardHeader, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardSubtitle, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardTitle, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__.MatIcon, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatSuffix, _angular_material_select__WEBPACK_IMPORTED_MODULE_11__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_12__.MatOption, _angular_material_chips__WEBPACK_IMPORTED_MODULE_13__.MatChip, _angular_material_chips__WEBPACK_IMPORTED_MODULE_13__.MatChipListbox, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink, _angular_common__WEBPACK_IMPORTED_MODULE_4__.SlicePipe, _angular_common__WEBPACK_IMPORTED_MODULE_4__.CurrencyPipe],
      styles: [".course-list-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header-section[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n\n.header-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.filters-row[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  justify-content: center;\n  margin-bottom: 2rem;\n}\n\n.search-input[_ngcontent-%COMP%] {\n  flex-grow: 1;\n  max-width: 400px;\n}\n\n.filter-select[_ngcontent-%COMP%] {\n  width: 200px;\n}\n\n.courses-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.course-card[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  height: 100%;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;\n}\n\n.course-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n}\n\n.card-header-top[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.mat-chip[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  padding: 0.3rem 0.7rem;\n  height: auto;\n}\n\n.mat-chip.bg-green-100[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.mat-chip.bg-yellow-100[_ngcontent-%COMP%] {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n.mat-chip.bg-red-100[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.free-chip[_ngcontent-%COMP%] {\n  background-color: #e6ffed;\n  color: #28a745;\n}\n\n.price[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #673ab7; \n\n}\n\n.course-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n}\n\n.course-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: #666;\n  line-height: 1.4;\n  height: 3em; \n\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.course-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  margin-top: 1rem;\n  font-size: 0.9rem;\n  color: #555;\n}\n\n.info-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.3rem;\n}\n\n.info-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  width: 1.1rem;\n  height: 1.1rem;\n  color: #888;\n}\n\n.info-item[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%] {\n  color: #ffc107; \n\n}\n\n.content-preview[_ngcontent-%COMP%] {\n  margin-top: 1.5rem;\n  padding-top: 1rem;\n  border-top: 1px solid #eee;\n}\n\n.content-preview[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  margin-bottom: 0.8rem;\n  color: #444;\n}\n\n.content-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.4rem;\n}\n\n.content-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  width: 1rem;\n  height: 1rem;\n  color: #777;\n}\n\n.content-duration[_ngcontent-%COMP%] {\n  margin-left: auto;\n  font-size: 0.8rem;\n  color: #888;\n}\n\n.more-content[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #888;\n  margin-top: 0.5rem;\n}\n\n.card-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.8rem;\n  margin-top: 1.5rem;\n  padding-top: 1rem;\n  border-top: 1px solid #eee;\n}\n\n.card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.9rem;\n  padding: 0.6rem 1rem;\n}\n\n.card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  width: 1rem;\n  height: 1rem;\n  margin-right: 0.3rem;\n}\n\n.no-courses[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 4rem 0;\n  color: #777;\n}\n\n.no-courses[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #aaa;\n}\n\n.no-courses[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n}\n\n@media (max-width: 768px) {\n  .filters-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .search-input[_ngcontent-%COMP%], .filter-select[_ngcontent-%COMP%] {\n    max-width: 100%;\n    width: 100%;\n  }\n  .courses-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 6281:
/*!****************************************************!*\
  !*** ./src/app/features/courses/courses.module.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CoursesModule: () => (/* binding */ CoursesModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/select */ 5175);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/chips */ 2772);
/* harmony import */ var _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/progress-bar */ 6354);
/* harmony import */ var _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/progress-spinner */ 1134);
/* harmony import */ var _angular_material_tabs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/tabs */ 8223);
/* harmony import */ var _course_list_course_list_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./course-list/course-list.component */ 2835);
/* harmony import */ var _course_detail_course_detail_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./course-detail/course-detail.component */ 1003);
/* harmony import */ var _course_create_edit_course_create_edit_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./course-create-edit/course-create-edit.component */ 6563);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);


 // Pour ngModel dans les filtres
// Angular Material
















class CoursesModule {
  static {
    this.ɵfac = function CoursesModule_Factory(t) {
      return new (t || CoursesModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
      type: CoursesModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.ReactiveFormsModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInputModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormFieldModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_11__.MatSelectModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_12__.MatChipsModule, _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_13__.MatProgressBarModule, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_14__.MatProgressSpinnerModule, _angular_material_tabs__WEBPACK_IMPORTED_MODULE_15__.MatTabsModule, _angular_router__WEBPACK_IMPORTED_MODULE_16__.RouterModule.forChild([{
        path: "",
        component: _course_list_course_list_component__WEBPACK_IMPORTED_MODULE_0__.CourseListComponent
      }, {
        path: "create",
        component: _course_create_edit_course_create_edit_component__WEBPACK_IMPORTED_MODULE_2__.CourseCreateEditComponent
      }, {
        path: "edit/:id",
        component: _course_create_edit_course_create_edit_component__WEBPACK_IMPORTED_MODULE_2__.CourseCreateEditComponent
      }, {
        path: ":id",
        component: _course_detail_course_detail_component__WEBPACK_IMPORTED_MODULE_1__.CourseDetailComponent
      }])]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](CoursesModule, {
    declarations: [_course_list_course_list_component__WEBPACK_IMPORTED_MODULE_0__.CourseListComponent, _course_detail_course_detail_component__WEBPACK_IMPORTED_MODULE_1__.CourseDetailComponent, _course_create_edit_course_create_edit_component__WEBPACK_IMPORTED_MODULE_2__.CourseCreateEditComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.ReactiveFormsModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_6__.MatCardModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatButtonModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInputModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormFieldModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_11__.MatSelectModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_12__.MatChipsModule, _angular_material_progress_bar__WEBPACK_IMPORTED_MODULE_13__.MatProgressBarModule, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_14__.MatProgressSpinnerModule, _angular_material_tabs__WEBPACK_IMPORTED_MODULE_15__.MatTabsModule, _angular_router__WEBPACK_IMPORTED_MODULE_16__.RouterModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_features_courses_courses_module_ts.js.map