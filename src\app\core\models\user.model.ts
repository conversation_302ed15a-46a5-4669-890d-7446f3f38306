import type { Paiement } from "./payment.model"
import type { Course } from "./course.model"
import type { ResultatQuiz } from "./resultat-quiz.model"
import type { Certificat } from "./certificate.model"

export interface User {
  id: number
  email: string
  nom: string
  prenom: string
  role: "Client" | "Formateur" | "Admin"
}

export interface Client extends User {
  paiements?: Paiement[]
  coursConsultes?: Course[]
  resultatsQuiz?: ResultatQuiz[]
}

export interface Formateur extends User {
  bio?: string
  cv?: string
  diplome?: string
  estValide?: boolean
  compteBancaire?: string
  coursCree?: Course[]
}

export interface Admin extends User {
  compteBancaire?: string
  certificatsGeneres?: Certificat[]
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  nom: string
  prenom: string
  email: string
  password: string
  accountType: string
}

export interface AuthResponse {
  token: string
  role: string
}
