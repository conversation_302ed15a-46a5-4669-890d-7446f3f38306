import { Paiement } from "./payment.model"
import { Course } from "./course.model"
import { ResultatQuiz } from "./resultat-quiz.model"
import { Certificat } from "./certificate.model"

export interface User {
  id: number
  email: string
  nom: string
  prenom: string
  role: "Client" | "Formateur" | "Admin"
}

export interface Client extends User {
  role: "Client"
  compteBancaire?: string          // Correspond à CompteBancaire en .NET
  paiements?: Paiement[]           // Correspond à Paiements en .NET
  coursConsultes?: Course[]        // Correspond à CoursConsultes en .NET
  resultatsQuiz?: ResultatQuiz[]   // Correspond à ResultatsQuiz en .NET
}

export interface Formateur extends User {
  bio?: string
  cv?: string
  diplome?: string
  estValide?: boolean
  compteBancaire?: string
  coursCree?: Course[]
}

export interface Admin extends User {
  compteBancaire?: string
  certificatsGeneres?: Certificat[]
}

export interface LoginRequest {
  Email: string    // Correspond au modèle .NET (majuscule)
  Password: string // Correspond au modèle .NET (majuscule)
}

export interface RegisterRequest {
  nom: string
  prenom: string
  email: string
  password: string
  accountType: string
}

export interface AuthResponse {
  token: string
  role: string
}
