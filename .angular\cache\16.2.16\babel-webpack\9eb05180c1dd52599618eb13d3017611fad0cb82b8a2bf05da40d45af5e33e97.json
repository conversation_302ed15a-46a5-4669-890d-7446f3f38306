{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from \"@angular/core\";\nimport { Validators } from \"@angular/forms\";\nexport let CourseCreateEditComponent = class CourseCreateEditComponent {\n  constructor(fb, route, router, courseService, snackBar) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.courseService = courseService;\n    this.snackBar = snackBar;\n    this.isEditMode = false;\n    this.courseId = null;\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.initForm();\n    this.route.paramMap.subscribe(params => {\n      const id = params.get(\"id\");\n      if (id) {\n        this.courseId = Number(id);\n        this.isEditMode = true;\n        this.loadCourse(this.courseId);\n      }\n    });\n  }\n  initForm() {\n    this.courseForm = this.fb.group({\n      titre: [\"\", Validators.required],\n      description: [\"\", Validators.required],\n      duree: [null, [Validators.required, Validators.min(1)]],\n      niveau: [\"\", Validators.required],\n      estGratuit: [false],\n      prix: [{\n        value: null,\n        disabled: false\n      }, [Validators.required, Validators.min(0)]],\n      contenus: this.fb.array([])\n    });\n    // Disable price if estGratuit is true initially\n    if (this.courseForm.get(\"estGratuit\")?.value) {\n      this.courseForm.get(\"prix\")?.disable();\n    }\n  }\n  loadCourse(id) {\n    this.isLoading = true;\n    // Mock data for demonstration\n    const mockCourse = {\n      id: id,\n      titre: \"React Fundamentals (Edit)\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\"\n      },\n      contenus: [{\n        id: 1,\n        titre: \"Introduction à React\",\n        typeContenu: \"Video\",\n        duree: 30,\n        coursId: id,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 1\n      }, {\n        id: 3,\n        titre: \"Quiz - Bases de React\",\n        typeContenu: \"Quiz\",\n        seuilReussite: 70,\n        coursId: id,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 3\n      }],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false\n    };\n    this.courseForm.patchValue(mockCourse);\n    mockCourse.contenus.forEach(content => {\n      this.addContenu(content);\n    });\n    this.togglePriceField();\n    this.isLoading = false;\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(id).subscribe({\n      next: (data) => {\n        this.courseForm.patchValue(data);\n        data.contenus.forEach(content => {\n          this.addContenu(content);\n        });\n        this.togglePriceField();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  get contenus() {\n    return this.courseForm.get(\"contenus\");\n  }\n  addContenu(content) {\n    let contentGroup;\n    if (content) {\n      if (content.typeContenu === \"Video\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          duree: [content.duree, [Validators.min(1)]],\n          coursId: [content.coursId]\n        });\n      } else if (content.typeContenu === \"Quiz\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          seuilReussite: [content.seuilReussite, [Validators.min(0), Validators.max(100)]],\n          coursId: [content.coursId]\n        });\n      } else if (content.typeContenu === \"Resume\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          contenuTexte: [content.contenuTexte],\n          coursId: [content.coursId]\n        });\n      } else {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          coursId: [content.coursId]\n        });\n      }\n    } else {\n      contentGroup = this.fb.group({\n        id: [0],\n        titre: [\"\", Validators.required],\n        typeContenu: [\"Video\", Validators.required],\n        duree: [null, [Validators.min(1)]],\n        seuilReussite: [null, [Validators.min(0), Validators.max(100)]],\n        contenuTexte: [\"\"],\n        coursId: [this.courseId]\n      });\n    }\n    this.contenus.push(contentGroup);\n  }\n  removeContenu(index) {\n    this.contenus.removeAt(index);\n  }\n  onContentTypeChange(index) {\n    const contentGroup = this.contenus.at(index);\n    const type = contentGroup.get(\"typeContenu\")?.value;\n    // Reset and re-apply validators based on type\n    contentGroup.get(\"duree\")?.clearValidators();\n    contentGroup.get(\"duree\")?.updateValueAndValidity();\n    contentGroup.get(\"seuilReussite\")?.clearValidators();\n    contentGroup.get(\"seuilReussite\")?.updateValueAndValidity();\n    contentGroup.get(\"contenuTexte\")?.clearValidators();\n    contentGroup.get(\"contenuTexte\")?.updateValueAndValidity();\n    if (type === \"Video\") {\n      contentGroup.get(\"duree\")?.setValidators([Validators.min(1)]);\n    } else if (type === \"Quiz\") {\n      contentGroup.get(\"seuilReussite\")?.setValidators([Validators.min(0), Validators.max(100)]);\n    }\n    // No specific validators for Resume contentText, it's optional\n  }\n\n  togglePriceField() {\n    const estGratuitControl = this.courseForm.get(\"estGratuit\");\n    const prixControl = this.courseForm.get(\"prix\");\n    if (estGratuitControl?.value) {\n      prixControl?.disable();\n      prixControl?.setValue(0);\n    } else {\n      prixControl?.enable();\n      prixControl?.setValue(null); // Clear value when re-enabling\n    }\n\n    prixControl?.updateValueAndValidity();\n  }\n  onSubmit() {\n    if (this.courseForm.valid) {\n      this.isLoading = true;\n      const courseData = this.courseForm.value;\n      // Ensure prix is 0 if estGratuit is true, even if disabled field value is not picked up\n      if (courseData.estGratuit) {\n        courseData.prix = 0;\n      }\n      if (this.isEditMode && this.courseId) {\n        // Update existing course\n        this.courseService.modifierCours(this.courseId, courseData).subscribe({\n          next: res => {\n            this.snackBar.open(\"Cours mis à jour avec succès !\", \"Fermer\", {\n              duration: 3000\n            });\n            this.isLoading = false;\n            this.router.navigate([\"/courses\", this.courseId]);\n          },\n          error: err => {\n            this.snackBar.open(\"Erreur lors de la mise à jour du cours.\", \"Fermer\", {\n              duration: 3000\n            });\n            console.error(err);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        // Create new course\n        // Note: Your backend's FormateurController has AjouterCours, but it expects FormateurId in URL.\n        // You might need a dedicated POST /api/cours endpoint or adjust this.\n        // For now, simulating success.\n        this.snackBar.open(\"Cours créé avec succès (simulé) !\", \"Fermer\", {\n          duration: 3000\n        });\n        this.isLoading = false;\n        this.router.navigate([\"/courses\"]);\n        /*\n        this.courseService.createCourse(courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open('Cours créé avec succès !', 'Fermer', { duration: 3000 });\n            this.isLoading = false;\n            this.router.navigate(['/courses']);\n          },\n          error: (err) => {\n            this.snackBar.open('Erreur lors de la création du cours.', 'Fermer', { duration: 3000 });\n            console.error(err);\n            this.isLoading = false;\n          }\n        });\n        */\n      }\n    } else {\n      this.snackBar.open(\"Veuillez remplir tous les champs requis.\", \"Fermer\", {\n        duration: 3000\n      });\n      this.courseForm.markAllAsTouched(); // Show validation errors\n    }\n  }\n\n  onCancel() {\n    this.router.navigate([\"/courses\"]);\n  }\n};\nCourseCreateEditComponent = __decorate([Component({\n  selector: \"app-course-create-edit\",\n  template: `\n    <div class=\"course-form-container\">\n      <mat-card class=\"course-form-card\">\n        <mat-card-header>\n          <mat-card-title>{{ isEditMode ? 'Modifier le cours' : 'Créer un nouveau cours' }}</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <form [formGroup]=\"courseForm\" (ngSubmit)=\"onSubmit()\">\n            <!-- Course Details -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Titre du cours</mat-label>\n              <input matInput formControlName=\"titre\">\n              <mat-error *ngIf=\"courseForm.get('titre')?.hasError('required')\">Le titre est requis</mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Description</mat-label>\n              <textarea matInput formControlName=\"description\" rows=\"4\"></textarea>\n              <mat-error *ngIf=\"courseForm.get('description')?.hasError('required')\">La description est requise</mat-error>\n            </mat-form-field>\n\n            <div class=\"row-fields\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Durée (minutes)</mat-label>\n                <input matInput type=\"number\" formControlName=\"duree\">\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('required')\">La durée est requise</mat-error>\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('min')\">La durée doit être positive</mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Niveau</mat-label>\n                <mat-select formControlName=\"niveau\">\n                  <mat-option value=\"Débutant\">Débutant</mat-option>\n                  <mat-option value=\"Intermédiaire\">Intermédiaire</mat-option>\n                  <mat-option value=\"Avancé\">Avancé</mat-option>\n                </mat-select>\n                <mat-error *ngIf=\"courseForm.get('niveau')?.hasError('required')\">Le niveau est requis</mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-checkbox formControlName=\"estGratuit\" (change)=\"togglePriceField()\">Cours gratuit</mat-checkbox>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\" *ngIf=\"!courseForm.get('estGratuit')?.value\">\n              <mat-label>Prix (€)</mat-label>\n              <input matInput type=\"number\" formControlName=\"prix\">\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('required')\">Le prix est requis</mat-error>\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('min')\">Le prix doit être positif</mat-error>\n            </mat-form-field>\n\n            <!-- Contents Section -->\n            <mat-card class=\"contents-card\">\n              <mat-card-title>Contenus du cours</mat-card-title>\n              <mat-card-content>\n                <div formArrayName=\"contenus\" class=\"content-list\">\n                  <div *ngFor=\"let content of contenus.controls; let i = index\" [formGroupName]=\"i\" class=\"content-item\">\n                    <mat-form-field appearance=\"outline\" class=\"content-type-select\">\n                      <mat-label>Type de contenu</mat-label>\n                      <mat-select formControlName=\"typeContenu\" (selectionChange)=\"onContentTypeChange(i)\">\n                        <mat-option value=\"Video\">Vidéo</mat-option>\n                        <mat-option value=\"Quiz\">Quiz</mat-option>\n                        <mat-option value=\"Resume\">Résumé</mat-option>\n                      </mat-select>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"content-title-input\">\n                      <mat-label>Titre du contenu</mat-label>\n                      <input matInput formControlName=\"titre\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Video -->\n                    <mat-form-field appearance=\"outline\" class=\"content-duration-input\" *ngIf=\"content.get('typeContenu')?.value === 'Video'\">\n                      <mat-label>Durée vidéo (min)</mat-label>\n                      <input matInput type=\"number\" formControlName=\"duree\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Quiz -->\n                    <ng-container *ngIf=\"content.get('typeContenu')?.value === 'Quiz'\">\n                      <mat-form-field appearance=\"outline\" class=\"content-quiz-threshold\">\n                        <mat-label>Seuil de réussite (%)</mat-label>\n                        <input matInput type=\"number\" formControlName=\"seuilReussite\">\n                      </mat-form-field>\n                      <!-- Add quiz questions management here if needed -->\n                    </ng-container>\n\n                    <!-- Specific fields for Resume -->\n                    <mat-form-field appearance=\"outline\" class=\"content-resume-text\" *ngIf=\"content.get('typeContenu')?.value === 'Resume'\">\n                      <mat-label>Contenu texte</mat-label>\n                      <textarea matInput formControlName=\"contenuTexte\" rows=\"3\"></textarea>\n                    </mat-form-field>\n\n                    <button mat-icon-button color=\"warn\" (click)=\"removeContenu(i)\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n                </div>\n                <button mat-raised-button color=\"accent\" (click)=\"addContenu()\">\n                  <mat-icon>add</mat-icon> Ajouter un contenu\n                </button>\n              </mat-card-content>\n            </mat-card>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"courseForm.invalid || isLoading\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">{{ isEditMode ? 'Mettre à jour' : 'Créer le cours' }}</span>\n              </button>\n              <button mat-stroked-button color=\"warn\" type=\"button\" (click)=\"onCancel()\">Annuler</button>\n            </div>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .course-form-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n      display: flex;\n      justify-content: center;\n      align-items: flex-start;\n    }\n\n    .course-form-card {\n      max-width: 800px;\n      width: 100%;\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .course-form-card mat-card-title {\n      font-size: 1.8rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: center;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .row-fields {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n    }\n\n    mat-checkbox {\n      margin-bottom: 1.5rem;\n    }\n\n    .contents-card {\n      margin-top: 2rem;\n      padding: 1.5rem;\n      background-color: #f9f9f9;\n      border: 1px solid #eee;\n    }\n\n    .contents-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: left;\n    }\n\n    .content-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1.5rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .content-item {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);\n    }\n\n    .content-type-select {\n      flex: 1 1 180px;\n    }\n\n    .content-title-input {\n      flex: 2 1 250px;\n    }\n\n    .content-duration-input, .content-quiz-threshold {\n      flex: 1 1 150px;\n    }\n\n    .content-resume-text {\n      flex: 3 1 300px;\n    }\n\n    .content-item button {\n      flex-shrink: 0;\n    }\n\n    .form-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: flex-end;\n      margin-top: 2rem;\n    }\n\n    .form-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    mat-spinner {\n      margin-right: 0.5rem;\n    }\n\n    @media (max-width: 768px) {\n      .row-fields {\n        flex-direction: column;\n      }\n      .half-width {\n        width: 100%;\n      }\n      .content-item {\n        flex-direction: column;\n        align-items: stretch;\n      }\n      .content-item mat-form-field {\n        width: 100%;\n      }\n      .form-actions {\n        flex-direction: column;\n      }\n    }\n  `]\n})], CourseCreateEditComponent);", "map": {"version": 3, "names": ["Component", "Validators", "CourseCreateEditComponent", "constructor", "fb", "route", "router", "courseService", "snackBar", "isEditMode", "courseId", "isLoading", "ngOnInit", "initForm", "paramMap", "subscribe", "params", "id", "get", "Number", "loadCourse", "courseForm", "group", "titre", "required", "description", "duree", "min", "niveau", "estGratuit", "prix", "value", "disabled", "contenus", "array", "disable", "mockCourse", "formateurId", "formateur", "nom", "prenom", "typeContenu", "coursId", "estComplete", "estDebloque", "ordre", "<PERSON>uil<PERSON><PERSON><PERSON>", "nombreEtudiants", "note", "patchValue", "for<PERSON>ach", "content", "<PERSON><PERSON><PERSON><PERSON>", "togglePriceField", "contentGroup", "max", "contenuTexte", "push", "<PERSON><PERSON><PERSON><PERSON>", "index", "removeAt", "onContentTypeChange", "at", "type", "clearValidators", "updateValueAndValidity", "setValidators", "estGratuitControl", "prixControl", "setValue", "enable", "onSubmit", "valid", "courseData", "modifierCours", "next", "res", "open", "duration", "navigate", "error", "err", "console", "mark<PERSON>llAsTouched", "onCancel", "__decorate", "selector", "template", "styles"], "sources": ["C:\\e-learning\\src\\app\\features\\courses\\course-create-edit\\course-create-edit.component.ts"], "sourcesContent": ["import { Component, type OnInit } from \"@angular/core\"\nimport { type Form<PERSON>uilder, type FormGroup, Validators, type FormArray } from \"@angular/forms\"\nimport type { ActivatedRoute, Router } from \"@angular/router\"\nimport type { CourseService } from \"../../../core/services/course.service\"\nimport type { MatSnackBar } from \"@angular/material/snack-bar\"\nimport type { Course, Contenu, Quiz, Video, Resume } from \"../../../core/models/course.model\"\n\n@Component({\n  selector: \"app-course-create-edit\",\n  template: `\n    <div class=\"course-form-container\">\n      <mat-card class=\"course-form-card\">\n        <mat-card-header>\n          <mat-card-title>{{ isEditMode ? 'Modifier le cours' : 'Créer un nouveau cours' }}</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <form [formGroup]=\"courseForm\" (ngSubmit)=\"onSubmit()\">\n            <!-- Course Details -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Titre du cours</mat-label>\n              <input matInput formControlName=\"titre\">\n              <mat-error *ngIf=\"courseForm.get('titre')?.hasError('required')\">Le titre est requis</mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Description</mat-label>\n              <textarea matInput formControlName=\"description\" rows=\"4\"></textarea>\n              <mat-error *ngIf=\"courseForm.get('description')?.hasError('required')\">La description est requise</mat-error>\n            </mat-form-field>\n\n            <div class=\"row-fields\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Durée (minutes)</mat-label>\n                <input matInput type=\"number\" formControlName=\"duree\">\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('required')\">La durée est requise</mat-error>\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('min')\">La durée doit être positive</mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Niveau</mat-label>\n                <mat-select formControlName=\"niveau\">\n                  <mat-option value=\"Débutant\">Débutant</mat-option>\n                  <mat-option value=\"Intermédiaire\">Intermédiaire</mat-option>\n                  <mat-option value=\"Avancé\">Avancé</mat-option>\n                </mat-select>\n                <mat-error *ngIf=\"courseForm.get('niveau')?.hasError('required')\">Le niveau est requis</mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-checkbox formControlName=\"estGratuit\" (change)=\"togglePriceField()\">Cours gratuit</mat-checkbox>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\" *ngIf=\"!courseForm.get('estGratuit')?.value\">\n              <mat-label>Prix (€)</mat-label>\n              <input matInput type=\"number\" formControlName=\"prix\">\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('required')\">Le prix est requis</mat-error>\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('min')\">Le prix doit être positif</mat-error>\n            </mat-form-field>\n\n            <!-- Contents Section -->\n            <mat-card class=\"contents-card\">\n              <mat-card-title>Contenus du cours</mat-card-title>\n              <mat-card-content>\n                <div formArrayName=\"contenus\" class=\"content-list\">\n                  <div *ngFor=\"let content of contenus.controls; let i = index\" [formGroupName]=\"i\" class=\"content-item\">\n                    <mat-form-field appearance=\"outline\" class=\"content-type-select\">\n                      <mat-label>Type de contenu</mat-label>\n                      <mat-select formControlName=\"typeContenu\" (selectionChange)=\"onContentTypeChange(i)\">\n                        <mat-option value=\"Video\">Vidéo</mat-option>\n                        <mat-option value=\"Quiz\">Quiz</mat-option>\n                        <mat-option value=\"Resume\">Résumé</mat-option>\n                      </mat-select>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"content-title-input\">\n                      <mat-label>Titre du contenu</mat-label>\n                      <input matInput formControlName=\"titre\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Video -->\n                    <mat-form-field appearance=\"outline\" class=\"content-duration-input\" *ngIf=\"content.get('typeContenu')?.value === 'Video'\">\n                      <mat-label>Durée vidéo (min)</mat-label>\n                      <input matInput type=\"number\" formControlName=\"duree\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Quiz -->\n                    <ng-container *ngIf=\"content.get('typeContenu')?.value === 'Quiz'\">\n                      <mat-form-field appearance=\"outline\" class=\"content-quiz-threshold\">\n                        <mat-label>Seuil de réussite (%)</mat-label>\n                        <input matInput type=\"number\" formControlName=\"seuilReussite\">\n                      </mat-form-field>\n                      <!-- Add quiz questions management here if needed -->\n                    </ng-container>\n\n                    <!-- Specific fields for Resume -->\n                    <mat-form-field appearance=\"outline\" class=\"content-resume-text\" *ngIf=\"content.get('typeContenu')?.value === 'Resume'\">\n                      <mat-label>Contenu texte</mat-label>\n                      <textarea matInput formControlName=\"contenuTexte\" rows=\"3\"></textarea>\n                    </mat-form-field>\n\n                    <button mat-icon-button color=\"warn\" (click)=\"removeContenu(i)\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n                </div>\n                <button mat-raised-button color=\"accent\" (click)=\"addContenu()\">\n                  <mat-icon>add</mat-icon> Ajouter un contenu\n                </button>\n              </mat-card-content>\n            </mat-card>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"courseForm.invalid || isLoading\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">{{ isEditMode ? 'Mettre à jour' : 'Créer le cours' }}</span>\n              </button>\n              <button mat-stroked-button color=\"warn\" type=\"button\" (click)=\"onCancel()\">Annuler</button>\n            </div>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [\n    `\n    .course-form-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n      display: flex;\n      justify-content: center;\n      align-items: flex-start;\n    }\n\n    .course-form-card {\n      max-width: 800px;\n      width: 100%;\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .course-form-card mat-card-title {\n      font-size: 1.8rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: center;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .row-fields {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n    }\n\n    mat-checkbox {\n      margin-bottom: 1.5rem;\n    }\n\n    .contents-card {\n      margin-top: 2rem;\n      padding: 1.5rem;\n      background-color: #f9f9f9;\n      border: 1px solid #eee;\n    }\n\n    .contents-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: left;\n    }\n\n    .content-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1.5rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .content-item {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);\n    }\n\n    .content-type-select {\n      flex: 1 1 180px;\n    }\n\n    .content-title-input {\n      flex: 2 1 250px;\n    }\n\n    .content-duration-input, .content-quiz-threshold {\n      flex: 1 1 150px;\n    }\n\n    .content-resume-text {\n      flex: 3 1 300px;\n    }\n\n    .content-item button {\n      flex-shrink: 0;\n    }\n\n    .form-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: flex-end;\n      margin-top: 2rem;\n    }\n\n    .form-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    mat-spinner {\n      margin-right: 0.5rem;\n    }\n\n    @media (max-width: 768px) {\n      .row-fields {\n        flex-direction: column;\n      }\n      .half-width {\n        width: 100%;\n      }\n      .content-item {\n        flex-direction: column;\n        align-items: stretch;\n      }\n      .content-item mat-form-field {\n        width: 100%;\n      }\n      .form-actions {\n        flex-direction: column;\n      }\n    }\n  `,\n  ],\n})\nexport class CourseCreateEditComponent implements OnInit {\n  courseForm!: FormGroup\n  isEditMode = false\n  courseId: number | null = null\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private courseService: CourseService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.initForm()\n    this.route.paramMap.subscribe((params) => {\n      const id = params.get(\"id\")\n      if (id) {\n        this.courseId = Number(id)\n        this.isEditMode = true\n        this.loadCourse(this.courseId)\n      }\n    })\n  }\n\n  initForm(): void {\n    this.courseForm = this.fb.group({\n      titre: [\"\", Validators.required],\n      description: [\"\", Validators.required],\n      duree: [null, [Validators.required, Validators.min(1)]],\n      niveau: [\"\", Validators.required],\n      estGratuit: [false],\n      prix: [{ value: null, disabled: false }, [Validators.required, Validators.min(0)]],\n      contenus: this.fb.array([]),\n    })\n\n    // Disable price if estGratuit is true initially\n    if (this.courseForm.get(\"estGratuit\")?.value) {\n      this.courseForm.get(\"prix\")?.disable()\n    }\n  }\n\n  loadCourse(id: number): void {\n    this.isLoading = true\n    // Mock data for demonstration\n    const mockCourse: Course = {\n      id: id,\n      titre: \"React Fundamentals (Edit)\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\" },\n      contenus: [\n        {\n          id: 1,\n          titre: \"Introduction à React\",\n          typeContenu: \"Video\",\n          duree: 30,\n          coursId: id,\n          estComplete: false,\n          estDebloque: true,\n          ordre: 1,\n        },\n        {\n          id: 3,\n          titre: \"Quiz - Bases de React\",\n          typeContenu: \"Quiz\",\n          seuilReussite: 70,\n          coursId: id,\n          estComplete: false,\n          estDebloque: true,\n          ordre: 3,\n        },\n      ],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n    }\n    this.courseForm.patchValue(mockCourse)\n    mockCourse.contenus.forEach((content) => {\n      this.addContenu(content)\n    })\n    this.togglePriceField()\n    this.isLoading = false\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(id).subscribe({\n      next: (data) => {\n        this.courseForm.patchValue(data);\n        data.contenus.forEach(content => {\n          this.addContenu(content);\n        });\n        this.togglePriceField();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  get contenus(): FormArray {\n    return this.courseForm.get(\"contenus\") as FormArray\n  }\n\n  addContenu(content?: Contenu): void {\n    let contentGroup: FormGroup\n    if (content) {\n      if (content.typeContenu === \"Video\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          duree: [(content as Video).duree, [Validators.min(1)]],\n          coursId: [content.coursId],\n        })\n      } else if (content.typeContenu === \"Quiz\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          seuilReussite: [(content as Quiz).seuilReussite, [Validators.min(0), Validators.max(100)]],\n          coursId: [content.coursId],\n        })\n      } else if (content.typeContenu === \"Resume\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          contenuTexte: [(content as Resume).contenuTexte],\n          coursId: [content.coursId],\n        })\n      } else {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          coursId: [content.coursId],\n        })\n      }\n    } else {\n      contentGroup = this.fb.group({\n        id: [0], // Temp ID for new content\n        titre: [\"\", Validators.required],\n        typeContenu: [\"Video\", Validators.required], // Default to Video\n        duree: [null, [Validators.min(1)]], // For video\n        seuilReussite: [null, [Validators.min(0), Validators.max(100)]], // For quiz\n        contenuTexte: [\"\"], // For resume\n        coursId: [this.courseId],\n      })\n    }\n    this.contenus.push(contentGroup)\n  }\n\n  removeContenu(index: number): void {\n    this.contenus.removeAt(index)\n  }\n\n  onContentTypeChange(index: number): void {\n    const contentGroup = this.contenus.at(index) as FormGroup\n    const type = contentGroup.get(\"typeContenu\")?.value\n\n    // Reset and re-apply validators based on type\n    contentGroup.get(\"duree\")?.clearValidators()\n    contentGroup.get(\"duree\")?.updateValueAndValidity()\n    contentGroup.get(\"seuilReussite\")?.clearValidators()\n    contentGroup.get(\"seuilReussite\")?.updateValueAndValidity()\n    contentGroup.get(\"contenuTexte\")?.clearValidators()\n    contentGroup.get(\"contenuTexte\")?.updateValueAndValidity()\n\n    if (type === \"Video\") {\n      contentGroup.get(\"duree\")?.setValidators([Validators.min(1)])\n    } else if (type === \"Quiz\") {\n      contentGroup.get(\"seuilReussite\")?.setValidators([Validators.min(0), Validators.max(100)])\n    }\n    // No specific validators for Resume contentText, it's optional\n  }\n\n  togglePriceField(): void {\n    const estGratuitControl = this.courseForm.get(\"estGratuit\")\n    const prixControl = this.courseForm.get(\"prix\")\n\n    if (estGratuitControl?.value) {\n      prixControl?.disable()\n      prixControl?.setValue(0)\n    } else {\n      prixControl?.enable()\n      prixControl?.setValue(null) // Clear value when re-enabling\n    }\n    prixControl?.updateValueAndValidity()\n  }\n\n  onSubmit(): void {\n    if (this.courseForm.valid) {\n      this.isLoading = true\n      const courseData = this.courseForm.value\n      // Ensure prix is 0 if estGratuit is true, even if disabled field value is not picked up\n      if (courseData.estGratuit) {\n        courseData.prix = 0\n      }\n\n      if (this.isEditMode && this.courseId) {\n        // Update existing course\n        this.courseService.modifierCours(this.courseId, courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open(\"Cours mis à jour avec succès !\", \"Fermer\", { duration: 3000 })\n            this.isLoading = false\n            this.router.navigate([\"/courses\", this.courseId])\n          },\n          error: (err) => {\n            this.snackBar.open(\"Erreur lors de la mise à jour du cours.\", \"Fermer\", { duration: 3000 })\n            console.error(err)\n            this.isLoading = false\n          },\n        })\n      } else {\n        // Create new course\n        // Note: Your backend's FormateurController has AjouterCours, but it expects FormateurId in URL.\n        // You might need a dedicated POST /api/cours endpoint or adjust this.\n        // For now, simulating success.\n        this.snackBar.open(\"Cours créé avec succès (simulé) !\", \"Fermer\", { duration: 3000 })\n        this.isLoading = false\n        this.router.navigate([\"/courses\"])\n        /*\n        this.courseService.createCourse(courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open('Cours créé avec succès !', 'Fermer', { duration: 3000 });\n            this.isLoading = false;\n            this.router.navigate(['/courses']);\n          },\n          error: (err) => {\n            this.snackBar.open('Erreur lors de la création du cours.', 'Fermer', { duration: 3000 });\n            console.error(err);\n            this.isLoading = false;\n          }\n        });\n        */\n      }\n    } else {\n      this.snackBar.open(\"Veuillez remplir tous les champs requis.\", \"Fermer\", { duration: 3000 })\n      this.courseForm.markAllAsTouched() // Show validation errors\n    }\n  }\n\n  onCancel(): void {\n    this.router.navigate([\"/courses\"])\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAqB,eAAe;AACtD,SAA2CC,UAAU,QAAwB,gBAAgB;AA+PtF,WAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAMpCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAG,KAAK;EAQd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,MAAMC,EAAE,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAID,EAAE,EAAE;QACN,IAAI,CAACP,QAAQ,GAAGS,MAAM,CAACF,EAAE,CAAC;QAC1B,IAAI,CAACR,UAAU,GAAG,IAAI;QACtB,IAAI,CAACW,UAAU,CAAC,IAAI,CAACV,QAAQ,CAAC;;IAElC,CAAC,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACuB,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,EAAExB,UAAU,CAACuB,QAAQ,CAAC;MACtCE,KAAK,EAAE,CAAC,IAAI,EAAE,CAACzB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvDC,MAAM,EAAE,CAAC,EAAE,EAAE3B,UAAU,CAACuB,QAAQ,CAAC;MACjCK,UAAU,EAAE,CAAC,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAK,CAAE,EAAE,CAAC/B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClFM,QAAQ,EAAE,IAAI,CAAC7B,EAAE,CAAC8B,KAAK,CAAC,EAAE;KAC3B,CAAC;IAEF;IACA,IAAI,IAAI,CAACb,UAAU,CAACH,GAAG,CAAC,YAAY,CAAC,EAAEa,KAAK,EAAE;MAC5C,IAAI,CAACV,UAAU,CAACH,GAAG,CAAC,MAAM,CAAC,EAAEiB,OAAO,EAAE;;EAE1C;EAEAf,UAAUA,CAACH,EAAU;IACnB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB;IACA,MAAMyB,UAAU,GAAW;MACzBnB,EAAE,EAAEA,EAAE;MACNM,KAAK,EAAE,2BAA2B;MAClCE,WAAW,EAAE,kFAAkF;MAC/FK,IAAI,EAAE,KAAK;MACXJ,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,UAAU;MAClBS,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAErB,EAAE,EAAE,CAAC;QAAEsB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAM,CAAE;MACnDP,QAAQ,EAAE,CACR;QACEhB,EAAE,EAAE,CAAC;QACLM,KAAK,EAAE,sBAAsB;QAC7BkB,WAAW,EAAE,OAAO;QACpBf,KAAK,EAAE,EAAE;QACTgB,OAAO,EAAEzB,EAAE;QACX0B,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACE5B,EAAE,EAAE,CAAC;QACLM,KAAK,EAAE,uBAAuB;QAC9BkB,WAAW,EAAE,MAAM;QACnBK,aAAa,EAAE,EAAE;QACjBJ,OAAO,EAAEzB,EAAE;QACX0B,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDE,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTnB,UAAU,EAAE;KACb;IACD,IAAI,CAACR,UAAU,CAAC4B,UAAU,CAACb,UAAU,CAAC;IACtCA,UAAU,CAACH,QAAQ,CAACiB,OAAO,CAAEC,OAAO,IAAI;MACtC,IAAI,CAACC,UAAU,CAACD,OAAO,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAACE,gBAAgB,EAAE;IACvB,IAAI,CAAC1C,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;;;EAkBF;;EAEA,IAAIsB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACZ,UAAU,CAACH,GAAG,CAAC,UAAU,CAAc;EACrD;EAEAkC,UAAUA,CAACD,OAAiB;IAC1B,IAAIG,YAAuB;IAC3B,IAAIH,OAAO,EAAE;MACX,IAAIA,OAAO,CAACV,WAAW,KAAK,OAAO,EAAE;QACnCa,YAAY,GAAG,IAAI,CAAClD,EAAE,CAACkB,KAAK,CAAC;UAC3BL,EAAE,EAAE,CAACkC,OAAO,CAAClC,EAAE,CAAC;UAChBM,KAAK,EAAE,CAAC4B,OAAO,CAAC5B,KAAK,EAAEtB,UAAU,CAACuB,QAAQ,CAAC;UAC3CiB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAExC,UAAU,CAACuB,QAAQ,CAAC;UACvDE,KAAK,EAAE,CAAEyB,OAAiB,CAACzB,KAAK,EAAE,CAACzB,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACtDe,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM,IAAIS,OAAO,CAACV,WAAW,KAAK,MAAM,EAAE;QACzCa,YAAY,GAAG,IAAI,CAAClD,EAAE,CAACkB,KAAK,CAAC;UAC3BL,EAAE,EAAE,CAACkC,OAAO,CAAClC,EAAE,CAAC;UAChBM,KAAK,EAAE,CAAC4B,OAAO,CAAC5B,KAAK,EAAEtB,UAAU,CAACuB,QAAQ,CAAC;UAC3CiB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAExC,UAAU,CAACuB,QAAQ,CAAC;UACvDsB,aAAa,EAAE,CAAEK,OAAgB,CAACL,aAAa,EAAE,CAAC7C,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,EAAE1B,UAAU,CAACsD,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UAC1Fb,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM,IAAIS,OAAO,CAACV,WAAW,KAAK,QAAQ,EAAE;QAC3Ca,YAAY,GAAG,IAAI,CAAClD,EAAE,CAACkB,KAAK,CAAC;UAC3BL,EAAE,EAAE,CAACkC,OAAO,CAAClC,EAAE,CAAC;UAChBM,KAAK,EAAE,CAAC4B,OAAO,CAAC5B,KAAK,EAAEtB,UAAU,CAACuB,QAAQ,CAAC;UAC3CiB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAExC,UAAU,CAACuB,QAAQ,CAAC;UACvDgC,YAAY,EAAE,CAAEL,OAAkB,CAACK,YAAY,CAAC;UAChDd,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM;QACLY,YAAY,GAAG,IAAI,CAAClD,EAAE,CAACkB,KAAK,CAAC;UAC3BL,EAAE,EAAE,CAACkC,OAAO,CAAClC,EAAE,CAAC;UAChBM,KAAK,EAAE,CAAC4B,OAAO,CAAC5B,KAAK,EAAEtB,UAAU,CAACuB,QAAQ,CAAC;UAC3CiB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAExC,UAAU,CAACuB,QAAQ,CAAC;UACvDkB,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;;KAEL,MAAM;MACLY,YAAY,GAAG,IAAI,CAAClD,EAAE,CAACkB,KAAK,CAAC;QAC3BL,EAAE,EAAE,CAAC,CAAC,CAAC;QACPM,KAAK,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACuB,QAAQ,CAAC;QAChCiB,WAAW,EAAE,CAAC,OAAO,EAAExC,UAAU,CAACuB,QAAQ,CAAC;QAC3CE,KAAK,EAAE,CAAC,IAAI,EAAE,CAACzB,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClCmB,aAAa,EAAE,CAAC,IAAI,EAAE,CAAC7C,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,EAAE1B,UAAU,CAACsD,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/DC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBd,OAAO,EAAE,CAAC,IAAI,CAAChC,QAAQ;OACxB,CAAC;;IAEJ,IAAI,CAACuB,QAAQ,CAACwB,IAAI,CAACH,YAAY,CAAC;EAClC;EAEAI,aAAaA,CAACC,KAAa;IACzB,IAAI,CAAC1B,QAAQ,CAAC2B,QAAQ,CAACD,KAAK,CAAC;EAC/B;EAEAE,mBAAmBA,CAACF,KAAa;IAC/B,MAAML,YAAY,GAAG,IAAI,CAACrB,QAAQ,CAAC6B,EAAE,CAACH,KAAK,CAAc;IACzD,MAAMI,IAAI,GAAGT,YAAY,CAACpC,GAAG,CAAC,aAAa,CAAC,EAAEa,KAAK;IAEnD;IACAuB,YAAY,CAACpC,GAAG,CAAC,OAAO,CAAC,EAAE8C,eAAe,EAAE;IAC5CV,YAAY,CAACpC,GAAG,CAAC,OAAO,CAAC,EAAE+C,sBAAsB,EAAE;IACnDX,YAAY,CAACpC,GAAG,CAAC,eAAe,CAAC,EAAE8C,eAAe,EAAE;IACpDV,YAAY,CAACpC,GAAG,CAAC,eAAe,CAAC,EAAE+C,sBAAsB,EAAE;IAC3DX,YAAY,CAACpC,GAAG,CAAC,cAAc,CAAC,EAAE8C,eAAe,EAAE;IACnDV,YAAY,CAACpC,GAAG,CAAC,cAAc,CAAC,EAAE+C,sBAAsB,EAAE;IAE1D,IAAIF,IAAI,KAAK,OAAO,EAAE;MACpBT,YAAY,CAACpC,GAAG,CAAC,OAAO,CAAC,EAAEgD,aAAa,CAAC,CAACjE,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9D,MAAM,IAAIoC,IAAI,KAAK,MAAM,EAAE;MAC1BT,YAAY,CAACpC,GAAG,CAAC,eAAe,CAAC,EAAEgD,aAAa,CAAC,CAACjE,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,EAAE1B,UAAU,CAACsD,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;IAE5F;EACF;;EAEAF,gBAAgBA,CAAA;IACd,MAAMc,iBAAiB,GAAG,IAAI,CAAC9C,UAAU,CAACH,GAAG,CAAC,YAAY,CAAC;IAC3D,MAAMkD,WAAW,GAAG,IAAI,CAAC/C,UAAU,CAACH,GAAG,CAAC,MAAM,CAAC;IAE/C,IAAIiD,iBAAiB,EAAEpC,KAAK,EAAE;MAC5BqC,WAAW,EAAEjC,OAAO,EAAE;MACtBiC,WAAW,EAAEC,QAAQ,CAAC,CAAC,CAAC;KACzB,MAAM;MACLD,WAAW,EAAEE,MAAM,EAAE;MACrBF,WAAW,EAAEC,QAAQ,CAAC,IAAI,CAAC,EAAC;;;IAE9BD,WAAW,EAAEH,sBAAsB,EAAE;EACvC;EAEAM,QAAQA,CAAA;IACN,IAAI,IAAI,CAAClD,UAAU,CAACmD,KAAK,EAAE;MACzB,IAAI,CAAC7D,SAAS,GAAG,IAAI;MACrB,MAAM8D,UAAU,GAAG,IAAI,CAACpD,UAAU,CAACU,KAAK;MACxC;MACA,IAAI0C,UAAU,CAAC5C,UAAU,EAAE;QACzB4C,UAAU,CAAC3C,IAAI,GAAG,CAAC;;MAGrB,IAAI,IAAI,CAACrB,UAAU,IAAI,IAAI,CAACC,QAAQ,EAAE;QACpC;QACA,IAAI,CAACH,aAAa,CAACmE,aAAa,CAAC,IAAI,CAAChE,QAAQ,EAAE+D,UAAU,CAAC,CAAC1D,SAAS,CAAC;UACpE4D,IAAI,EAAGC,GAAG,IAAI;YACZ,IAAI,CAACpE,QAAQ,CAACqE,IAAI,CAAC,gCAAgC,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAClF,IAAI,CAACnE,SAAS,GAAG,KAAK;YACtB,IAAI,CAACL,MAAM,CAACyE,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACrE,QAAQ,CAAC,CAAC;UACnD,CAAC;UACDsE,KAAK,EAAGC,GAAG,IAAI;YACb,IAAI,CAACzE,QAAQ,CAACqE,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAC3FI,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;YAClB,IAAI,CAACtE,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;OACH,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACH,QAAQ,CAACqE,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACrF,IAAI,CAACnE,SAAS,GAAG,KAAK;QACtB,IAAI,CAACL,MAAM,CAACyE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;;;;;;;;;;;;;;;KAeH,MAAM;MACL,IAAI,CAACvE,QAAQ,CAACqE,IAAI,CAAC,0CAA0C,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC5F,IAAI,CAACzD,UAAU,CAAC8D,gBAAgB,EAAE,EAAC;;EAEvC;;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC9E,MAAM,CAACyE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;CACD;AA/PY7E,yBAAyB,GAAAmF,UAAA,EAzPrCrF,SAAS,CAAC;EACTsF,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgHT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkID;CAEF,CAAC,C,EACWtF,yBAAyB,CA+PrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}