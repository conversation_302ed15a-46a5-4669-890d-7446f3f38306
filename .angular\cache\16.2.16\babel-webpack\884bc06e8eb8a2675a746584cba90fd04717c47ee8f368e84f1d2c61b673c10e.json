{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { MatChipsModule } from \"@angular/material/chips\";\nimport { CertificatesComponent } from \"./certificates.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class CertificatesModule {\n  static {\n    this.ɵfac = function CertificatesModule_Factory(t) {\n      return new (t || CertificatesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CertificatesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, RouterModule.forChild([{\n        path: \"\",\n        component: CertificatesComponent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CertificatesModule, {\n    declarations: [CertificatesComponent],\n    imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "CertificatesComponent", "CertificatesModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\e-learning\\src\\app\\features\\certificates\\certificates.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\"\nimport { CommonModule } from \"@angular/common\"\nimport { RouterModule } from \"@angular/router\"\n\n// Angular Material\nimport { MatCardModule } from \"@angular/material/card\"\nimport { MatButtonModule } from \"@angular/material/button\"\nimport { MatIconModule } from \"@angular/material/icon\"\nimport { MatChipsModule } from \"@angular/material/chips\"\n\nimport { CertificatesComponent } from \"./certificates.component\"\n\n@NgModule({\n  declarations: [CertificatesComponent],\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    RouterModule.forChild([{ path: \"\", component: CertificatesComponent }]),\n  ],\n})\nexport class CertificatesModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,qBAAqB,QAAQ,0BAA0B;;;AAahE,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAR3BP,YAAY,EACZE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdJ,YAAY,CAACO,QAAQ,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAqB,CAAE,CAAC,CAAC;IAAA;EAAA;;;2EAG9DC,kBAAkB;IAAAI,YAAA,GAVdL,qBAAqB;IAAAM,OAAA,GAElCZ,YAAY,EACZE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EAAAQ,EAAA,CAAAZ,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}