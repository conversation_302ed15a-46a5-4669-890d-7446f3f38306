{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../core/services/course.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nfunction CourseCreateEditComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le titre est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"La description est requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"La dur\\u00E9e est requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"La dur\\u00E9e doit \\u00EAtre positive\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le niveau est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_mat_form_field_37_mat_error_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le prix est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_mat_form_field_37_mat_error_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le prix doit \\u00EAtre positif\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_mat_form_field_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 3)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Prix (\\u20AC)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 24);\n    i0.ɵɵtemplate(4, CourseCreateEditComponent_mat_form_field_37_mat_error_4_Template, 2, 0, \"mat-error\", 5);\n    i0.ɵɵtemplate(5, CourseCreateEditComponent_mat_form_field_37_mat_error_5_Template, 2, 0, \"mat-error\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.courseForm.get(\"prix\")) == null ? null : tmp_0_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r5.courseForm.get(\"prix\")) == null ? null : tmp_1_0.hasError(\"min\"));\n  }\n}\nfunction CourseCreateEditComponent_div_43_mat_form_field_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 35)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Dur\\u00E9e vid\\u00E9o (min)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_div_43_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-form-field\", 36)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Seuil de r\\u00E9ussite (%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CourseCreateEditComponent_div_43_mat_form_field_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 38)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Contenu texte\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"textarea\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CourseCreateEditComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"mat-form-field\", 26)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Type de contenu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 27);\n    i0.ɵɵlistener(\"selectionChange\", function CourseCreateEditComponent_div_43_Template_mat_select_selectionChange_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const i_r12 = restoredCtx.index;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onContentTypeChange(i_r12));\n    });\n    i0.ɵɵelementStart(5, \"mat-option\", 28);\n    i0.ɵɵtext(6, \"Vid\\u00E9o\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 29);\n    i0.ɵɵtext(8, \"Quiz\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-option\", 30);\n    i0.ɵɵtext(10, \"R\\u00E9sum\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 31)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Titre du contenu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, CourseCreateEditComponent_div_43_mat_form_field_15_Template, 4, 0, \"mat-form-field\", 32);\n    i0.ɵɵtemplate(16, CourseCreateEditComponent_div_43_ng_container_16_Template, 5, 0, \"ng-container\", 5);\n    i0.ɵɵtemplate(17, CourseCreateEditComponent_div_43_mat_form_field_17_Template, 4, 0, \"mat-form-field\", 33);\n    i0.ɵɵelementStart(18, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function CourseCreateEditComponent_div_43_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const i_r12 = restoredCtx.index;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.removeContenu(i_r12));\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const content_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r12);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = content_r11.get(\"typeContenu\")) == null ? null : tmp_1_0.value) === \"Video\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = content_r11.get(\"typeContenu\")) == null ? null : tmp_2_0.value) === \"Quiz\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = content_r11.get(\"typeContenu\")) == null ? null : tmp_3_0.value) === \"Resume\");\n  }\n}\nfunction CourseCreateEditComponent_mat_spinner_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 40);\n  }\n}\nfunction CourseCreateEditComponent_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er le cours\");\n  }\n}\nexport class CourseCreateEditComponent {\n  constructor(fb, route, router, courseService, snackBar) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.courseService = courseService;\n    this.snackBar = snackBar;\n    this.isEditMode = false;\n    this.courseId = null;\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.initForm();\n    this.route.paramMap.subscribe(params => {\n      const id = params.get(\"id\");\n      if (id) {\n        this.courseId = Number(id);\n        this.isEditMode = true;\n        this.loadCourse(this.courseId);\n      }\n    });\n  }\n  initForm() {\n    this.courseForm = this.fb.group({\n      titre: [\"\", Validators.required],\n      description: [\"\", Validators.required],\n      duree: [null, [Validators.required, Validators.min(1)]],\n      niveau: [\"\", Validators.required],\n      estGratuit: [false],\n      prix: [{\n        value: null,\n        disabled: false\n      }, [Validators.required, Validators.min(0)]],\n      contenus: this.fb.array([])\n    });\n    // Disable price if estGratuit is true initially\n    if (this.courseForm.get(\"estGratuit\")?.value) {\n      this.courseForm.get(\"prix\")?.disable();\n    }\n  }\n  loadCourse(id) {\n    this.isLoading = true;\n    // Mock data for demonstration\n    const mockCourse = {\n      id: id,\n      titre: \"React Fundamentals (Edit)\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: {\n        id: 1,\n        nom: \"Dupont\",\n        prenom: \"Jean\"\n      },\n      contenus: [{\n        id: 1,\n        titre: \"Introduction à React\",\n        typeContenu: \"Video\",\n        duree: 30,\n        coursId: id,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 1\n      }, {\n        id: 3,\n        titre: \"Quiz - Bases de React\",\n        typeContenu: \"Quiz\",\n        seuilReussite: 70,\n        coursId: id,\n        estComplete: false,\n        estDebloque: true,\n        ordre: 3\n      }],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false\n    };\n    this.courseForm.patchValue(mockCourse);\n    mockCourse.contenus.forEach(content => {\n      this.addContenu(content);\n    });\n    this.togglePriceField();\n    this.isLoading = false;\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(id).subscribe({\n      next: (data) => {\n        this.courseForm.patchValue(data);\n        data.contenus.forEach(content => {\n          this.addContenu(content);\n        });\n        this.togglePriceField();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  get contenus() {\n    return this.courseForm.get(\"contenus\");\n  }\n  addContenu(content) {\n    let contentGroup;\n    if (content) {\n      if (content.typeContenu === \"Video\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          duree: [content.duree, [Validators.min(1)]],\n          coursId: [content.coursId]\n        });\n      } else if (content.typeContenu === \"Quiz\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          seuilReussite: [content.seuilReussite, [Validators.min(0), Validators.max(100)]],\n          coursId: [content.coursId]\n        });\n      } else if (content.typeContenu === \"Resume\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          contenuTexte: [content.contenuTexte],\n          coursId: [content.coursId]\n        });\n      } else {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          coursId: [content.coursId]\n        });\n      }\n    } else {\n      contentGroup = this.fb.group({\n        id: [0],\n        titre: [\"\", Validators.required],\n        typeContenu: [\"Video\", Validators.required],\n        duree: [null, [Validators.min(1)]],\n        seuilReussite: [null, [Validators.min(0), Validators.max(100)]],\n        contenuTexte: [\"\"],\n        coursId: [this.courseId]\n      });\n    }\n    this.contenus.push(contentGroup);\n  }\n  removeContenu(index) {\n    this.contenus.removeAt(index);\n  }\n  onContentTypeChange(index) {\n    const contentGroup = this.contenus.at(index);\n    const type = contentGroup.get(\"typeContenu\")?.value;\n    // Reset and re-apply validators based on type\n    contentGroup.get(\"duree\")?.clearValidators();\n    contentGroup.get(\"duree\")?.updateValueAndValidity();\n    contentGroup.get(\"seuilReussite\")?.clearValidators();\n    contentGroup.get(\"seuilReussite\")?.updateValueAndValidity();\n    contentGroup.get(\"contenuTexte\")?.clearValidators();\n    contentGroup.get(\"contenuTexte\")?.updateValueAndValidity();\n    if (type === \"Video\") {\n      contentGroup.get(\"duree\")?.setValidators([Validators.min(1)]);\n    } else if (type === \"Quiz\") {\n      contentGroup.get(\"seuilReussite\")?.setValidators([Validators.min(0), Validators.max(100)]);\n    }\n    // No specific validators for Resume contentText, it's optional\n  }\n\n  togglePriceField() {\n    const estGratuitControl = this.courseForm.get(\"estGratuit\");\n    const prixControl = this.courseForm.get(\"prix\");\n    if (estGratuitControl?.value) {\n      prixControl?.disable();\n      prixControl?.setValue(0);\n    } else {\n      prixControl?.enable();\n      prixControl?.setValue(null); // Clear value when re-enabling\n    }\n\n    prixControl?.updateValueAndValidity();\n  }\n  onSubmit() {\n    if (this.courseForm.valid) {\n      this.isLoading = true;\n      const courseData = this.courseForm.value;\n      // Ensure prix is 0 if estGratuit is true, even if disabled field value is not picked up\n      if (courseData.estGratuit) {\n        courseData.prix = 0;\n      }\n      if (this.isEditMode && this.courseId) {\n        // Update existing course\n        this.courseService.modifierCours(this.courseId, courseData).subscribe({\n          next: res => {\n            this.snackBar.open(\"Cours mis à jour avec succès !\", \"Fermer\", {\n              duration: 3000\n            });\n            this.isLoading = false;\n            this.router.navigate([\"/courses\", this.courseId]);\n          },\n          error: err => {\n            this.snackBar.open(\"Erreur lors de la mise à jour du cours.\", \"Fermer\", {\n              duration: 3000\n            });\n            console.error(err);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        // Create new course\n        // Note: Your backend's FormateurController has AjouterCours, but it expects FormateurId in URL.\n        // You might need a dedicated POST /api/cours endpoint or adjust this.\n        // For now, simulating success.\n        this.snackBar.open(\"Cours créé avec succès (simulé) !\", \"Fermer\", {\n          duration: 3000\n        });\n        this.isLoading = false;\n        this.router.navigate([\"/courses\"]);\n        /*\n        this.courseService.createCourse(courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open('Cours créé avec succès !', 'Fermer', { duration: 3000 });\n            this.isLoading = false;\n            this.router.navigate(['/courses']);\n          },\n          error: (err) => {\n            this.snackBar.open('Erreur lors de la création du cours.', 'Fermer', { duration: 3000 });\n            console.error(err);\n            this.isLoading = false;\n          }\n        });\n        */\n      }\n    } else {\n      this.snackBar.open(\"Veuillez remplir tous les champs requis.\", \"Fermer\", {\n        duration: 3000\n      });\n      this.courseForm.markAllAsTouched(); // Show validation errors\n    }\n  }\n\n  onCancel() {\n    this.router.navigate([\"/courses\"]);\n  }\n  static {\n    this.ɵfac = function CourseCreateEditComponent_Factory(t) {\n      return new (t || CourseCreateEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.CourseService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CourseCreateEditComponent,\n      selectors: [[\"app-course-create-edit\"]],\n      decls: 54,\n      vars: 12,\n      consts: [[1, \"course-form-container\"], [1, \"course-form-card\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"titre\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"4\"], [1, \"row-fields\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"duree\"], [\"formControlName\", \"niveau\"], [\"value\", \"D\\u00E9butant\"], [\"value\", \"Interm\\u00E9diaire\"], [\"value\", \"Avanc\\u00E9\"], [\"formControlName\", \"estGratuit\", 3, \"change\"], [\"appearance\", \"outline\", \"class\", \"full-width\", 4, \"ngIf\"], [1, \"contents-card\"], [\"formArrayName\", \"contenus\", 1, \"content-list\"], [\"class\", \"content-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"prix\"], [1, \"content-item\", 3, \"formGroupName\"], [\"appearance\", \"outline\", 1, \"content-type-select\"], [\"formControlName\", \"typeContenu\", 3, \"selectionChange\"], [\"value\", \"Video\"], [\"value\", \"Quiz\"], [\"value\", \"Resume\"], [\"appearance\", \"outline\", 1, \"content-title-input\"], [\"appearance\", \"outline\", \"class\", \"content-duration-input\", 4, \"ngIf\"], [\"appearance\", \"outline\", \"class\", \"content-resume-text\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 3, \"click\"], [\"appearance\", \"outline\", 1, \"content-duration-input\"], [\"appearance\", \"outline\", 1, \"content-quiz-threshold\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"seuilReussite\"], [\"appearance\", \"outline\", 1, \"content-resume-text\"], [\"matInput\", \"\", \"formControlName\", \"contenuTexte\", \"rows\", \"3\"], [\"diameter\", \"20\"]],\n      template: function CourseCreateEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function CourseCreateEditComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(7, \"mat-form-field\", 3)(8, \"mat-label\");\n          i0.ɵɵtext(9, \"Titre du cours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 4);\n          i0.ɵɵtemplate(11, CourseCreateEditComponent_mat_error_11_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-form-field\", 3)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"textarea\", 6);\n          i0.ɵɵtemplate(16, CourseCreateEditComponent_mat_error_16_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"mat-form-field\", 8)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Dur\\u00E9e (minutes)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 9);\n          i0.ɵɵtemplate(22, CourseCreateEditComponent_mat_error_22_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵtemplate(23, CourseCreateEditComponent_mat_error_23_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-form-field\", 8)(25, \"mat-label\");\n          i0.ɵɵtext(26, \"Niveau\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-select\", 10)(28, \"mat-option\", 11);\n          i0.ɵɵtext(29, \"D\\u00E9butant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-option\", 12);\n          i0.ɵɵtext(31, \"Interm\\u00E9diaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-option\", 13);\n          i0.ɵɵtext(33, \"Avanc\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(34, CourseCreateEditComponent_mat_error_34_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"mat-checkbox\", 14);\n          i0.ɵɵlistener(\"change\", function CourseCreateEditComponent_Template_mat_checkbox_change_35_listener() {\n            return ctx.togglePriceField();\n          });\n          i0.ɵɵtext(36, \"Cours gratuit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, CourseCreateEditComponent_mat_form_field_37_Template, 6, 2, \"mat-form-field\", 15);\n          i0.ɵɵelementStart(38, \"mat-card\", 16)(39, \"mat-card-title\");\n          i0.ɵɵtext(40, \"Contenus du cours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"mat-card-content\")(42, \"div\", 17);\n          i0.ɵɵtemplate(43, CourseCreateEditComponent_div_43_Template, 21, 4, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function CourseCreateEditComponent_Template_button_click_44_listener() {\n            return ctx.addContenu();\n          });\n          i0.ɵɵelementStart(45, \"mat-icon\");\n          i0.ɵɵtext(46, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \" Ajouter un contenu \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 20)(49, \"button\", 21);\n          i0.ɵɵtemplate(50, CourseCreateEditComponent_mat_spinner_50_Template, 1, 0, \"mat-spinner\", 22);\n          i0.ɵɵtemplate(51, CourseCreateEditComponent_span_51_Template, 2, 1, \"span\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function CourseCreateEditComponent_Template_button_click_52_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(53, \"Annuler\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Modifier le cours\" : \"Cr\\u00E9er un nouveau cours\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.courseForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.courseForm.get(\"titre\")) == null ? null : tmp_2_0.hasError(\"required\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.courseForm.get(\"description\")) == null ? null : tmp_3_0.hasError(\"required\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.courseForm.get(\"duree\")) == null ? null : tmp_4_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.courseForm.get(\"duree\")) == null ? null : tmp_5_0.hasError(\"min\"));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.courseForm.get(\"niveau\")) == null ? null : tmp_6_0.hasError(\"required\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_7_0 = ctx.courseForm.get(\"estGratuit\")) == null ? null : tmp_7_0.value));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.contenus.controls);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.courseForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatInput, i10.MatFormField, i10.MatLabel, i10.MatError, i11.MatSelect, i12.MatOption],\n      styles: [\".course-form-container[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n}\\n\\n.course-form-card[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  width: 100%;\\n  padding: 1.5rem;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.course-form-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin-bottom: 1.5rem;\\n  text-align: center;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n\\n.row-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\nmat-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.contents-card[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1.5rem;\\n  background-color: #f9f9f9;\\n  border: 1px solid #eee;\\n}\\n\\n.contents-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  margin-bottom: 1.5rem;\\n  text-align: left;\\n}\\n\\n.content-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.content-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n  align-items: center;\\n  padding: 1rem;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);\\n}\\n\\n.content-type-select[_ngcontent-%COMP%] {\\n  flex: 1 1 180px;\\n}\\n\\n.content-title-input[_ngcontent-%COMP%] {\\n  flex: 2 1 250px;\\n}\\n\\n.content-duration-input[_ngcontent-%COMP%], .content-quiz-threshold[_ngcontent-%COMP%] {\\n  flex: 1 1 150px;\\n}\\n\\n.content-resume-text[_ngcontent-%COMP%] {\\n  flex: 3 1 300px;\\n}\\n\\n.content-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: flex-end;\\n  margin-top: 2rem;\\n}\\n\\n.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.8rem 1.5rem;\\n  font-size: 1rem;\\n}\\n\\nmat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .row-fields[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .content-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .content-item[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "CourseCreateEditComponent_mat_form_field_37_mat_error_4_Template", "CourseCreateEditComponent_mat_form_field_37_mat_error_5_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r5", "courseForm", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_1_0", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵlistener", "CourseCreateEditComponent_div_43_Template_mat_select_selectionChange_4_listener", "restoredCtx", "ɵɵrestoreView", "_r17", "i_r12", "index", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "onContentTypeChange", "CourseCreateEditComponent_div_43_mat_form_field_15_Template", "CourseCreateEditComponent_div_43_ng_container_16_Template", "CourseCreateEditComponent_div_43_mat_form_field_17_Template", "CourseCreateEditComponent_div_43_Template_button_click_18_listener", "ctx_r18", "<PERSON><PERSON><PERSON><PERSON>", "content_r11", "value", "tmp_2_0", "tmp_3_0", "ɵɵtextInterpolate", "ctx_r8", "isEditMode", "CourseCreateEditComponent", "constructor", "fb", "route", "router", "courseService", "snackBar", "courseId", "isLoading", "ngOnInit", "initForm", "paramMap", "subscribe", "params", "id", "Number", "loadCourse", "group", "titre", "required", "description", "duree", "min", "niveau", "estGratuit", "prix", "disabled", "contenus", "array", "disable", "mockCourse", "formateurId", "formateur", "nom", "prenom", "typeContenu", "coursId", "estComplete", "estDebloque", "ordre", "<PERSON>uil<PERSON><PERSON><PERSON>", "nombreEtudiants", "note", "patchValue", "for<PERSON>ach", "content", "<PERSON><PERSON><PERSON><PERSON>", "togglePriceField", "contentGroup", "max", "contenuTexte", "push", "removeAt", "at", "type", "clearValidators", "updateValueAndValidity", "setValidators", "estGratuitControl", "prixControl", "setValue", "enable", "onSubmit", "valid", "courseData", "modifierCours", "next", "res", "open", "duration", "navigate", "error", "err", "console", "mark<PERSON>llAsTouched", "onCancel", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "CourseService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "CourseCreateEditComponent_Template", "rf", "ctx", "CourseCreateEditComponent_Template_form_ngSubmit_6_listener", "CourseCreateEditComponent_mat_error_11_Template", "CourseCreateEditComponent_mat_error_16_Template", "CourseCreateEditComponent_mat_error_22_Template", "CourseCreateEditComponent_mat_error_23_Template", "CourseCreateEditComponent_mat_error_34_Template", "CourseCreateEditComponent_Template_mat_checkbox_change_35_listener", "CourseCreateEditComponent_mat_form_field_37_Template", "CourseCreateEditComponent_div_43_Template", "CourseCreateEditComponent_Template_button_click_44_listener", "CourseCreateEditComponent_mat_spinner_50_Template", "CourseCreateEditComponent_span_51_Template", "CourseCreateEditComponent_Template_button_click_52_listener", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "controls", "invalid"], "sources": ["C:\\e-learning\\src\\app\\features\\courses\\course-create-edit\\course-create-edit.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { FormBuilder, FormGroup, Validators, FormArray } from \"@angular/forms\"\nimport { ActivatedRoute, Router } from \"@angular/router\"\nimport { CourseService } from \"../../../core/services/course.service\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { Course, Contenu, Quiz, Video, Resume } from \"../../../core/models/course.model\"\n\n@Component({\n  selector: \"app-course-create-edit\",\n  template: `\n    <div class=\"course-form-container\">\n      <mat-card class=\"course-form-card\">\n        <mat-card-header>\n          <mat-card-title>{{ isEditMode ? 'Modifier le cours' : 'Créer un nouveau cours' }}</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <form [formGroup]=\"courseForm\" (ngSubmit)=\"onSubmit()\">\n            <!-- Course Details -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Titre du cours</mat-label>\n              <input matInput formControlName=\"titre\">\n              <mat-error *ngIf=\"courseForm.get('titre')?.hasError('required')\">Le titre est requis</mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Description</mat-label>\n              <textarea matInput formControlName=\"description\" rows=\"4\"></textarea>\n              <mat-error *ngIf=\"courseForm.get('description')?.hasError('required')\">La description est requise</mat-error>\n            </mat-form-field>\n\n            <div class=\"row-fields\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Durée (minutes)</mat-label>\n                <input matInput type=\"number\" formControlName=\"duree\">\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('required')\">La durée est requise</mat-error>\n                <mat-error *ngIf=\"courseForm.get('duree')?.hasError('min')\">La durée doit être positive</mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Niveau</mat-label>\n                <mat-select formControlName=\"niveau\">\n                  <mat-option value=\"Débutant\">Débutant</mat-option>\n                  <mat-option value=\"Intermédiaire\">Intermédiaire</mat-option>\n                  <mat-option value=\"Avancé\">Avancé</mat-option>\n                </mat-select>\n                <mat-error *ngIf=\"courseForm.get('niveau')?.hasError('required')\">Le niveau est requis</mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-checkbox formControlName=\"estGratuit\" (change)=\"togglePriceField()\">Cours gratuit</mat-checkbox>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\" *ngIf=\"!courseForm.get('estGratuit')?.value\">\n              <mat-label>Prix (€)</mat-label>\n              <input matInput type=\"number\" formControlName=\"prix\">\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('required')\">Le prix est requis</mat-error>\n              <mat-error *ngIf=\"courseForm.get('prix')?.hasError('min')\">Le prix doit être positif</mat-error>\n            </mat-form-field>\n\n            <!-- Contents Section -->\n            <mat-card class=\"contents-card\">\n              <mat-card-title>Contenus du cours</mat-card-title>\n              <mat-card-content>\n                <div formArrayName=\"contenus\" class=\"content-list\">\n                  <div *ngFor=\"let content of contenus.controls; let i = index\" [formGroupName]=\"i\" class=\"content-item\">\n                    <mat-form-field appearance=\"outline\" class=\"content-type-select\">\n                      <mat-label>Type de contenu</mat-label>\n                      <mat-select formControlName=\"typeContenu\" (selectionChange)=\"onContentTypeChange(i)\">\n                        <mat-option value=\"Video\">Vidéo</mat-option>\n                        <mat-option value=\"Quiz\">Quiz</mat-option>\n                        <mat-option value=\"Resume\">Résumé</mat-option>\n                      </mat-select>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"content-title-input\">\n                      <mat-label>Titre du contenu</mat-label>\n                      <input matInput formControlName=\"titre\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Video -->\n                    <mat-form-field appearance=\"outline\" class=\"content-duration-input\" *ngIf=\"content.get('typeContenu')?.value === 'Video'\">\n                      <mat-label>Durée vidéo (min)</mat-label>\n                      <input matInput type=\"number\" formControlName=\"duree\">\n                    </mat-form-field>\n\n                    <!-- Specific fields for Quiz -->\n                    <ng-container *ngIf=\"content.get('typeContenu')?.value === 'Quiz'\">\n                      <mat-form-field appearance=\"outline\" class=\"content-quiz-threshold\">\n                        <mat-label>Seuil de réussite (%)</mat-label>\n                        <input matInput type=\"number\" formControlName=\"seuilReussite\">\n                      </mat-form-field>\n                      <!-- Add quiz questions management here if needed -->\n                    </ng-container>\n\n                    <!-- Specific fields for Resume -->\n                    <mat-form-field appearance=\"outline\" class=\"content-resume-text\" *ngIf=\"content.get('typeContenu')?.value === 'Resume'\">\n                      <mat-label>Contenu texte</mat-label>\n                      <textarea matInput formControlName=\"contenuTexte\" rows=\"3\"></textarea>\n                    </mat-form-field>\n\n                    <button mat-icon-button color=\"warn\" (click)=\"removeContenu(i)\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n                </div>\n                <button mat-raised-button color=\"accent\" (click)=\"addContenu()\">\n                  <mat-icon>add</mat-icon> Ajouter un contenu\n                </button>\n              </mat-card-content>\n            </mat-card>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"courseForm.invalid || isLoading\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">{{ isEditMode ? 'Mettre à jour' : 'Créer le cours' }}</span>\n              </button>\n              <button mat-stroked-button color=\"warn\" type=\"button\" (click)=\"onCancel()\">Annuler</button>\n            </div>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [\n    `\n    .course-form-container {\n      padding: 2rem;\n      background-color: #f5f5f5;\n      min-height: 100vh;\n      display: flex;\n      justify-content: center;\n      align-items: flex-start;\n    }\n\n    .course-form-card {\n      max-width: 800px;\n      width: 100%;\n      padding: 1.5rem;\n      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n    }\n\n    .course-form-card mat-card-title {\n      font-size: 1.8rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: center;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .row-fields {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n    }\n\n    mat-checkbox {\n      margin-bottom: 1.5rem;\n    }\n\n    .contents-card {\n      margin-top: 2rem;\n      padding: 1.5rem;\n      background-color: #f9f9f9;\n      border: 1px solid #eee;\n    }\n\n    .contents-card mat-card-title {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 1.5rem;\n      text-align: left;\n    }\n\n    .content-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1.5rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .content-item {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 1rem;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background-color: #fff;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);\n    }\n\n    .content-type-select {\n      flex: 1 1 180px;\n    }\n\n    .content-title-input {\n      flex: 2 1 250px;\n    }\n\n    .content-duration-input, .content-quiz-threshold {\n      flex: 1 1 150px;\n    }\n\n    .content-resume-text {\n      flex: 3 1 300px;\n    }\n\n    .content-item button {\n      flex-shrink: 0;\n    }\n\n    .form-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: flex-end;\n      margin-top: 2rem;\n    }\n\n    .form-actions button {\n      padding: 0.8rem 1.5rem;\n      font-size: 1rem;\n    }\n\n    mat-spinner {\n      margin-right: 0.5rem;\n    }\n\n    @media (max-width: 768px) {\n      .row-fields {\n        flex-direction: column;\n      }\n      .half-width {\n        width: 100%;\n      }\n      .content-item {\n        flex-direction: column;\n        align-items: stretch;\n      }\n      .content-item mat-form-field {\n        width: 100%;\n      }\n      .form-actions {\n        flex-direction: column;\n      }\n    }\n  `,\n  ],\n})\nexport class CourseCreateEditComponent implements OnInit {\n  courseForm!: FormGroup\n  isEditMode = false\n  courseId: number | null = null\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private courseService: CourseService,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.initForm()\n    this.route.paramMap.subscribe((params) => {\n      const id = params.get(\"id\")\n      if (id) {\n        this.courseId = Number(id)\n        this.isEditMode = true\n        this.loadCourse(this.courseId)\n      }\n    })\n  }\n\n  initForm(): void {\n    this.courseForm = this.fb.group({\n      titre: [\"\", Validators.required],\n      description: [\"\", Validators.required],\n      duree: [null, [Validators.required, Validators.min(1)]],\n      niveau: [\"\", Validators.required],\n      estGratuit: [false],\n      prix: [{ value: null, disabled: false }, [Validators.required, Validators.min(0)]],\n      contenus: this.fb.array([]),\n    })\n\n    // Disable price if estGratuit is true initially\n    if (this.courseForm.get(\"estGratuit\")?.value) {\n      this.courseForm.get(\"prix\")?.disable()\n    }\n  }\n\n  loadCourse(id: number): void {\n    this.isLoading = true\n    // Mock data for demonstration\n    const mockCourse: Course = {\n      id: id,\n      titre: \"React Fundamentals (Edit)\",\n      description: \"Apprenez les bases de React avec des exemples pratiques et des projets concrets.\",\n      prix: 99.99,\n      duree: 120,\n      niveau: \"Débutant\",\n      formateurId: 1,\n      formateur: { id: 1, nom: \"Dupont\", prenom: \"Jean\" },\n      contenus: [\n        {\n          id: 1,\n          titre: \"Introduction à React\",\n          typeContenu: \"Video\",\n          duree: 30,\n          coursId: id,\n          estComplete: false,\n          estDebloque: true,\n          ordre: 1,\n        },\n        {\n          id: 3,\n          titre: \"Quiz - Bases de React\",\n          typeContenu: \"Quiz\",\n          seuilReussite: 70,\n          coursId: id,\n          estComplete: false,\n          estDebloque: true,\n          ordre: 3,\n        },\n      ],\n      nombreEtudiants: 245,\n      note: 4.8,\n      estGratuit: false,\n    }\n    this.courseForm.patchValue(mockCourse)\n    mockCourse.contenus.forEach((content) => {\n      this.addContenu(content)\n    })\n    this.togglePriceField()\n    this.isLoading = false\n\n    // Uncomment to fetch from API\n    /*\n    this.courseService.getCours(id).subscribe({\n      next: (data) => {\n        this.courseForm.patchValue(data);\n        data.contenus.forEach(content => {\n          this.addContenu(content);\n        });\n        this.togglePriceField();\n        this.isLoading = false;\n      },\n      error: (err) => {\n        this.snackBar.open('Erreur lors du chargement du cours.', 'Fermer', { duration: 3000 });\n        console.error(err);\n        this.isLoading = false;\n        this.router.navigate(['/courses']);\n      }\n    });\n    */\n  }\n\n  get contenus(): FormArray {\n    return this.courseForm.get(\"contenus\") as FormArray\n  }\n\n  addContenu(content?: Contenu): void {\n    let contentGroup: FormGroup\n    if (content) {\n      if (content.typeContenu === \"Video\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          duree: [(content as Video).duree, [Validators.min(1)]],\n          coursId: [content.coursId],\n        })\n      } else if (content.typeContenu === \"Quiz\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          seuilReussite: [(content as Quiz).seuilReussite, [Validators.min(0), Validators.max(100)]],\n          coursId: [content.coursId],\n        })\n      } else if (content.typeContenu === \"Resume\") {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          contenuTexte: [(content as Resume).contenuTexte],\n          coursId: [content.coursId],\n        })\n      } else {\n        contentGroup = this.fb.group({\n          id: [content.id],\n          titre: [content.titre, Validators.required],\n          typeContenu: [content.typeContenu, Validators.required],\n          coursId: [content.coursId],\n        })\n      }\n    } else {\n      contentGroup = this.fb.group({\n        id: [0], // Temp ID for new content\n        titre: [\"\", Validators.required],\n        typeContenu: [\"Video\", Validators.required], // Default to Video\n        duree: [null, [Validators.min(1)]], // For video\n        seuilReussite: [null, [Validators.min(0), Validators.max(100)]], // For quiz\n        contenuTexte: [\"\"], // For resume\n        coursId: [this.courseId],\n      })\n    }\n    this.contenus.push(contentGroup)\n  }\n\n  removeContenu(index: number): void {\n    this.contenus.removeAt(index)\n  }\n\n  onContentTypeChange(index: number): void {\n    const contentGroup = this.contenus.at(index) as FormGroup\n    const type = contentGroup.get(\"typeContenu\")?.value\n\n    // Reset and re-apply validators based on type\n    contentGroup.get(\"duree\")?.clearValidators()\n    contentGroup.get(\"duree\")?.updateValueAndValidity()\n    contentGroup.get(\"seuilReussite\")?.clearValidators()\n    contentGroup.get(\"seuilReussite\")?.updateValueAndValidity()\n    contentGroup.get(\"contenuTexte\")?.clearValidators()\n    contentGroup.get(\"contenuTexte\")?.updateValueAndValidity()\n\n    if (type === \"Video\") {\n      contentGroup.get(\"duree\")?.setValidators([Validators.min(1)])\n    } else if (type === \"Quiz\") {\n      contentGroup.get(\"seuilReussite\")?.setValidators([Validators.min(0), Validators.max(100)])\n    }\n    // No specific validators for Resume contentText, it's optional\n  }\n\n  togglePriceField(): void {\n    const estGratuitControl = this.courseForm.get(\"estGratuit\")\n    const prixControl = this.courseForm.get(\"prix\")\n\n    if (estGratuitControl?.value) {\n      prixControl?.disable()\n      prixControl?.setValue(0)\n    } else {\n      prixControl?.enable()\n      prixControl?.setValue(null) // Clear value when re-enabling\n    }\n    prixControl?.updateValueAndValidity()\n  }\n\n  onSubmit(): void {\n    if (this.courseForm.valid) {\n      this.isLoading = true\n      const courseData = this.courseForm.value\n      // Ensure prix is 0 if estGratuit is true, even if disabled field value is not picked up\n      if (courseData.estGratuit) {\n        courseData.prix = 0\n      }\n\n      if (this.isEditMode && this.courseId) {\n        // Update existing course\n        this.courseService.modifierCours(this.courseId, courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open(\"Cours mis à jour avec succès !\", \"Fermer\", { duration: 3000 })\n            this.isLoading = false\n            this.router.navigate([\"/courses\", this.courseId])\n          },\n          error: (err) => {\n            this.snackBar.open(\"Erreur lors de la mise à jour du cours.\", \"Fermer\", { duration: 3000 })\n            console.error(err)\n            this.isLoading = false\n          },\n        })\n      } else {\n        // Create new course\n        // Note: Your backend's FormateurController has AjouterCours, but it expects FormateurId in URL.\n        // You might need a dedicated POST /api/cours endpoint or adjust this.\n        // For now, simulating success.\n        this.snackBar.open(\"Cours créé avec succès (simulé) !\", \"Fermer\", { duration: 3000 })\n        this.isLoading = false\n        this.router.navigate([\"/courses\"])\n        /*\n        this.courseService.createCourse(courseData).subscribe({\n          next: (res) => {\n            this.snackBar.open('Cours créé avec succès !', 'Fermer', { duration: 3000 });\n            this.isLoading = false;\n            this.router.navigate(['/courses']);\n          },\n          error: (err) => {\n            this.snackBar.open('Erreur lors de la création du cours.', 'Fermer', { duration: 3000 });\n            console.error(err);\n            this.isLoading = false;\n          }\n        });\n        */\n      }\n    } else {\n      this.snackBar.open(\"Veuillez remplir tous les champs requis.\", \"Fermer\", { duration: 3000 })\n      this.courseForm.markAllAsTouched() // Show validation errors\n    }\n  }\n\n  onCancel(): void {\n    this.router.navigate([\"/courses\"])\n  }\n}\n"], "mappings": "AACA,SAAiCA,UAAU,QAAmB,gBAAgB;;;;;;;;;;;;;;;;IAoBhEC,EAAA,CAAAC,cAAA,gBAAiE;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMhGH,EAAA,CAAAC,cAAA,gBAAuE;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO3GH,EAAA,CAAAC,cAAA,gBAAiE;IAAAD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACjGH,EAAA,CAAAC,cAAA,gBAA4D;IAAAD,EAAA,CAAAE,MAAA,4CAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUnGH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASpGH,EAAA,CAAAC,cAAA,gBAAgE;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC9FH,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAE,MAAA,qCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAJlGH,EAAA,CAAAC,cAAA,wBAAqG;IACxFD,EAAA,CAAAE,MAAA,oBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/BH,EAAA,CAAAI,SAAA,gBAAqD;IACrDJ,EAAA,CAAAK,UAAA,IAAAC,gEAAA,uBAA8F;IAC9FN,EAAA,CAAAK,UAAA,IAAAE,gEAAA,uBAAgG;IAClGP,EAAA,CAAAG,YAAA,EAAiB;;;;;;IAFHH,EAAA,CAAAQ,SAAA,GAAkD;IAAlDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,QAAA,aAAkD;IAClDd,EAAA,CAAAQ,SAAA,GAA6C;IAA7CR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAD,QAAA,QAA6C;;;;;IAwBnDd,EAAA,CAAAC,cAAA,yBAA0H;IAC7GD,EAAA,CAAAE,MAAA,kCAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAI,SAAA,eAAsD;IACxDJ,EAAA,CAAAG,YAAA,EAAiB;;;;;IAGjBH,EAAA,CAAAgB,uBAAA,GAAmE;IACjEhB,EAAA,CAAAC,cAAA,yBAAoE;IACvDD,EAAA,CAAAE,MAAA,iCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5CH,EAAA,CAAAI,SAAA,gBAA8D;IAChEJ,EAAA,CAAAG,YAAA,EAAiB;IAEnBH,EAAA,CAAAiB,qBAAA,EAAe;;;;;IAGfjB,EAAA,CAAAC,cAAA,yBAAwH;IAC3GD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAI,SAAA,mBAAsE;IACxEJ,EAAA,CAAAG,YAAA,EAAiB;;;;;;IAlCnBH,EAAA,CAAAC,cAAA,cAAuG;IAExFD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAC,cAAA,qBAAqF;IAA3CD,EAAA,CAAAkB,UAAA,6BAAAC,gFAAA;MAAA,MAAAC,WAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,KAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAmB1B,EAAA,CAAA2B,WAAA,CAAAF,OAAA,CAAAG,mBAAA,CAAAL,KAAA,CAAsB;IAAA,EAAC;IAClFvB,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC5CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAIlDH,EAAA,CAAAC,cAAA,0BAAiE;IACpDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAI,SAAA,gBAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAiB;IAGjBH,EAAA,CAAAK,UAAA,KAAAwB,2DAAA,6BAGiB;IAGjB7B,EAAA,CAAAK,UAAA,KAAAyB,yDAAA,0BAMe;IAGf9B,EAAA,CAAAK,UAAA,KAAA0B,2DAAA,6BAGiB;IAEjB/B,EAAA,CAAAC,cAAA,kBAAgE;IAA3BD,EAAA,CAAAkB,UAAA,mBAAAc,mEAAA;MAAA,MAAAZ,WAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,KAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAS,OAAA,GAAAjC,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAM,OAAA,CAAAC,aAAA,CAAAX,KAAA,CAAgB;IAAA,EAAC;IAC7DvB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;IArC+BH,EAAA,CAAAS,UAAA,kBAAAc,KAAA,CAAmB;IAgBVvB,EAAA,CAAAQ,SAAA,IAAmD;IAAnDR,EAAA,CAAAS,UAAA,WAAAM,OAAA,GAAAoB,WAAA,CAAAtB,GAAA,kCAAAE,OAAA,CAAAqB,KAAA,cAAmD;IAMzGpC,EAAA,CAAAQ,SAAA,GAAkD;IAAlDR,EAAA,CAAAS,UAAA,WAAA4B,OAAA,GAAAF,WAAA,CAAAtB,GAAA,kCAAAwB,OAAA,CAAAD,KAAA,aAAkD;IASCpC,EAAA,CAAAQ,SAAA,GAAoD;IAApDR,EAAA,CAAAS,UAAA,WAAA6B,OAAA,GAAAH,WAAA,CAAAtB,GAAA,kCAAAyB,OAAA,CAAAF,KAAA,eAAoD;;;;;IAkB1HpC,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5DH,EAAA,CAAAQ,SAAA,GAAqD;IAArDR,EAAA,CAAAuC,iBAAA,CAAAC,MAAA,CAAAC,UAAA,gDAAqD;;;AA+I9F,OAAM,MAAOC,yBAAyB;EAMpCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAP,UAAU,GAAG,KAAK;IAClB,KAAAQ,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAG,KAAK;EAQd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,MAAMC,EAAE,GAAGD,MAAM,CAAC1C,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAI2C,EAAE,EAAE;QACN,IAAI,CAACP,QAAQ,GAAGQ,MAAM,CAACD,EAAE,CAAC;QAC1B,IAAI,CAACf,UAAU,GAAG,IAAI;QACtB,IAAI,CAACiB,UAAU,CAAC,IAAI,CAACT,QAAQ,CAAC;;IAElC,CAAC,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACxC,UAAU,GAAG,IAAI,CAACgC,EAAE,CAACe,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,EAAE/D,UAAU,CAAC8D,QAAQ,CAAC;MACtCE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAChE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvDC,MAAM,EAAE,CAAC,EAAE,EAAElE,UAAU,CAAC8D,QAAQ,CAAC;MACjCK,UAAU,EAAE,CAAC,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC;QAAE/B,KAAK,EAAE,IAAI;QAAEgC,QAAQ,EAAE;MAAK,CAAE,EAAE,CAACrE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClFK,QAAQ,EAAE,IAAI,CAACzB,EAAE,CAAC0B,KAAK,CAAC,EAAE;KAC3B,CAAC;IAEF;IACA,IAAI,IAAI,CAAC1D,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEuB,KAAK,EAAE;MAC5C,IAAI,CAACxB,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE0D,OAAO,EAAE;;EAE1C;EAEAb,UAAUA,CAACF,EAAU;IACnB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB;IACA,MAAMsB,UAAU,GAAW;MACzBhB,EAAE,EAAEA,EAAE;MACNI,KAAK,EAAE,2BAA2B;MAClCE,WAAW,EAAE,kFAAkF;MAC/FK,IAAI,EAAE,KAAK;MACXJ,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,UAAU;MAClBQ,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAElB,EAAE,EAAE,CAAC;QAAEmB,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAM,CAAE;MACnDP,QAAQ,EAAE,CACR;QACEb,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,sBAAsB;QAC7BiB,WAAW,EAAE,OAAO;QACpBd,KAAK,EAAE,EAAE;QACTe,OAAO,EAAEtB,EAAE;QACXuB,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,EACD;QACEzB,EAAE,EAAE,CAAC;QACLI,KAAK,EAAE,uBAAuB;QAC9BiB,WAAW,EAAE,MAAM;QACnBK,aAAa,EAAE,EAAE;QACjBJ,OAAO,EAAEtB,EAAE;QACXuB,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;OACR,CACF;MACDE,eAAe,EAAE,GAAG;MACpBC,IAAI,EAAE,GAAG;MACTlB,UAAU,EAAE;KACb;IACD,IAAI,CAACtD,UAAU,CAACyE,UAAU,CAACb,UAAU,CAAC;IACtCA,UAAU,CAACH,QAAQ,CAACiB,OAAO,CAAEC,OAAO,IAAI;MACtC,IAAI,CAACC,UAAU,CAACD,OAAO,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAACE,gBAAgB,EAAE;IACvB,IAAI,CAACvC,SAAS,GAAG,KAAK;IAEtB;IACA;;;;;;;;;;;;;;;;;;EAkBF;;EAEA,IAAImB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACzD,UAAU,CAACC,GAAG,CAAC,UAAU,CAAc;EACrD;EAEA2E,UAAUA,CAACD,OAAiB;IAC1B,IAAIG,YAAuB;IAC3B,IAAIH,OAAO,EAAE;MACX,IAAIA,OAAO,CAACV,WAAW,KAAK,OAAO,EAAE;QACnCa,YAAY,GAAG,IAAI,CAAC9C,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAAC+B,OAAO,CAAC/B,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC2B,OAAO,CAAC3B,KAAK,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;UAC3CgB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAE9E,UAAU,CAAC8D,QAAQ,CAAC;UACvDE,KAAK,EAAE,CAAEwB,OAAiB,CAACxB,KAAK,EAAE,CAAChE,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACtDc,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM,IAAIS,OAAO,CAACV,WAAW,KAAK,MAAM,EAAE;QACzCa,YAAY,GAAG,IAAI,CAAC9C,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAAC+B,OAAO,CAAC/B,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC2B,OAAO,CAAC3B,KAAK,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;UAC3CgB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAE9E,UAAU,CAAC8D,QAAQ,CAAC;UACvDqB,aAAa,EAAE,CAAEK,OAAgB,CAACL,aAAa,EAAE,CAACnF,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,EAAEjE,UAAU,CAAC4F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UAC1Fb,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM,IAAIS,OAAO,CAACV,WAAW,KAAK,QAAQ,EAAE;QAC3Ca,YAAY,GAAG,IAAI,CAAC9C,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAAC+B,OAAO,CAAC/B,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC2B,OAAO,CAAC3B,KAAK,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;UAC3CgB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAE9E,UAAU,CAAC8D,QAAQ,CAAC;UACvD+B,YAAY,EAAE,CAAEL,OAAkB,CAACK,YAAY,CAAC;UAChDd,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;OACH,MAAM;QACLY,YAAY,GAAG,IAAI,CAAC9C,EAAE,CAACe,KAAK,CAAC;UAC3BH,EAAE,EAAE,CAAC+B,OAAO,CAAC/B,EAAE,CAAC;UAChBI,KAAK,EAAE,CAAC2B,OAAO,CAAC3B,KAAK,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;UAC3CgB,WAAW,EAAE,CAACU,OAAO,CAACV,WAAW,EAAE9E,UAAU,CAAC8D,QAAQ,CAAC;UACvDiB,OAAO,EAAE,CAACS,OAAO,CAACT,OAAO;SAC1B,CAAC;;KAEL,MAAM;MACLY,YAAY,GAAG,IAAI,CAAC9C,EAAE,CAACe,KAAK,CAAC;QAC3BH,EAAE,EAAE,CAAC,CAAC,CAAC;QACPI,KAAK,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;QAChCgB,WAAW,EAAE,CAAC,OAAO,EAAE9E,UAAU,CAAC8D,QAAQ,CAAC;QAC3CE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAChE,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClCkB,aAAa,EAAE,CAAC,IAAI,EAAE,CAACnF,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,EAAEjE,UAAU,CAAC4F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/DC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBd,OAAO,EAAE,CAAC,IAAI,CAAC7B,QAAQ;OACxB,CAAC;;IAEJ,IAAI,CAACoB,QAAQ,CAACwB,IAAI,CAACH,YAAY,CAAC;EAClC;EAEAxD,aAAaA,CAACV,KAAa;IACzB,IAAI,CAAC6C,QAAQ,CAACyB,QAAQ,CAACtE,KAAK,CAAC;EAC/B;EAEAI,mBAAmBA,CAACJ,KAAa;IAC/B,MAAMkE,YAAY,GAAG,IAAI,CAACrB,QAAQ,CAAC0B,EAAE,CAACvE,KAAK,CAAc;IACzD,MAAMwE,IAAI,GAAGN,YAAY,CAAC7E,GAAG,CAAC,aAAa,CAAC,EAAEuB,KAAK;IAEnD;IACAsD,YAAY,CAAC7E,GAAG,CAAC,OAAO,CAAC,EAAEoF,eAAe,EAAE;IAC5CP,YAAY,CAAC7E,GAAG,CAAC,OAAO,CAAC,EAAEqF,sBAAsB,EAAE;IACnDR,YAAY,CAAC7E,GAAG,CAAC,eAAe,CAAC,EAAEoF,eAAe,EAAE;IACpDP,YAAY,CAAC7E,GAAG,CAAC,eAAe,CAAC,EAAEqF,sBAAsB,EAAE;IAC3DR,YAAY,CAAC7E,GAAG,CAAC,cAAc,CAAC,EAAEoF,eAAe,EAAE;IACnDP,YAAY,CAAC7E,GAAG,CAAC,cAAc,CAAC,EAAEqF,sBAAsB,EAAE;IAE1D,IAAIF,IAAI,KAAK,OAAO,EAAE;MACpBN,YAAY,CAAC7E,GAAG,CAAC,OAAO,CAAC,EAAEsF,aAAa,CAAC,CAACpG,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9D,MAAM,IAAIgC,IAAI,KAAK,MAAM,EAAE;MAC1BN,YAAY,CAAC7E,GAAG,CAAC,eAAe,CAAC,EAAEsF,aAAa,CAAC,CAACpG,UAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,EAAEjE,UAAU,CAAC4F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;IAE5F;EACF;;EAEAF,gBAAgBA,CAAA;IACd,MAAMW,iBAAiB,GAAG,IAAI,CAACxF,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;IAC3D,MAAMwF,WAAW,GAAG,IAAI,CAACzF,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC;IAE/C,IAAIuF,iBAAiB,EAAEhE,KAAK,EAAE;MAC5BiE,WAAW,EAAE9B,OAAO,EAAE;MACtB8B,WAAW,EAAEC,QAAQ,CAAC,CAAC,CAAC;KACzB,MAAM;MACLD,WAAW,EAAEE,MAAM,EAAE;MACrBF,WAAW,EAAEC,QAAQ,CAAC,IAAI,CAAC,EAAC;;;IAE9BD,WAAW,EAAEH,sBAAsB,EAAE;EACvC;EAEAM,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC5F,UAAU,CAAC6F,KAAK,EAAE;MACzB,IAAI,CAACvD,SAAS,GAAG,IAAI;MACrB,MAAMwD,UAAU,GAAG,IAAI,CAAC9F,UAAU,CAACwB,KAAK;MACxC;MACA,IAAIsE,UAAU,CAACxC,UAAU,EAAE;QACzBwC,UAAU,CAACvC,IAAI,GAAG,CAAC;;MAGrB,IAAI,IAAI,CAAC1B,UAAU,IAAI,IAAI,CAACQ,QAAQ,EAAE;QACpC;QACA,IAAI,CAACF,aAAa,CAAC4D,aAAa,CAAC,IAAI,CAAC1D,QAAQ,EAAEyD,UAAU,CAAC,CAACpD,SAAS,CAAC;UACpEsD,IAAI,EAAGC,GAAG,IAAI;YACZ,IAAI,CAAC7D,QAAQ,CAAC8D,IAAI,CAAC,gCAAgC,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAClF,IAAI,CAAC7D,SAAS,GAAG,KAAK;YACtB,IAAI,CAACJ,MAAM,CAACkE,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC/D,QAAQ,CAAC,CAAC;UACnD,CAAC;UACDgE,KAAK,EAAGC,GAAG,IAAI;YACb,IAAI,CAAClE,QAAQ,CAAC8D,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAC3FI,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;YAClB,IAAI,CAAChE,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;OACH,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACF,QAAQ,CAAC8D,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACrF,IAAI,CAAC7D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACkE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;;;;;;;;;;;;;;;KAeH,MAAM;MACL,IAAI,CAAChE,QAAQ,CAAC8D,IAAI,CAAC,0CAA0C,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC5F,IAAI,CAACnG,UAAU,CAACwG,gBAAgB,EAAE,EAAC;;EAEvC;;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACvE,MAAM,CAACkE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;;;uBA9PWtE,yBAAyB,EAAA1C,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxH,EAAA,CAAAsH,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1H,EAAA,CAAAsH,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA3H,EAAA,CAAAsH,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA7H,EAAA,CAAAsH,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAzBrF,yBAAyB;MAAAsF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtPlCtI,EAAA,CAAAC,cAAA,aAAmC;UAGbD,EAAA,CAAAE,MAAA,GAAiE;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAEpGH,EAAA,CAAAC,cAAA,uBAAkB;UACeD,EAAA,CAAAkB,UAAA,sBAAAsH,4DAAA;YAAA,OAAYD,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UAEpDxG,EAAA,CAAAC,cAAA,wBAAwD;UAC3CD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAI,SAAA,gBAAwC;UACxCJ,EAAA,CAAAK,UAAA,KAAAoI,+CAAA,uBAAgG;UAClGzI,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,mBAAqE;UACrEJ,EAAA,CAAAK,UAAA,KAAAqI,+CAAA,uBAA6G;UAC/G1I,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,cAAwB;UAETD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAI,SAAA,gBAAsD;UACtDJ,EAAA,CAAAK,UAAA,KAAAsI,+CAAA,uBAAiG;UACjG3I,EAAA,CAAAK,UAAA,KAAAuI,+CAAA,uBAAmG;UACrG5I,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAC,cAAA,sBAAqC;UACND,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAClDH,EAAA,CAAAC,cAAA,sBAAkC;UAAAD,EAAA,CAAAE,MAAA,0BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC5DH,EAAA,CAAAC,cAAA,sBAA2B;UAAAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAEhDH,EAAA,CAAAK,UAAA,KAAAwI,+CAAA,uBAAkG;UACpG7I,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,wBAAyE;UAA9BD,EAAA,CAAAkB,UAAA,oBAAA4H,mEAAA;YAAA,OAAUP,GAAA,CAAA9C,gBAAA,EAAkB;UAAA,EAAC;UAACzF,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAErGH,EAAA,CAAAK,UAAA,KAAA0I,oDAAA,6BAKiB;UAGjB/I,EAAA,CAAAC,cAAA,oBAAgC;UACdD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAClDH,EAAA,CAAAC,cAAA,wBAAkB;UAEdD,EAAA,CAAAK,UAAA,KAAA2I,yCAAA,mBAuCM;UACRhJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,kBAAgE;UAAvBD,EAAA,CAAAkB,UAAA,mBAAA+H,4DAAA;YAAA,OAASV,GAAA,CAAA/C,UAAA,EAAY;UAAA,EAAC;UAC7DxF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAE,MAAA,4BAC3B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAK,UAAA,KAAA6I,iDAAA,0BAA2D;UAC3DlJ,EAAA,CAAAK,UAAA,KAAA8I,0CAAA,kBAAqF;UACvFnJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA2E;UAArBD,EAAA,CAAAkB,UAAA,mBAAAkI,4DAAA;YAAA,OAASb,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAC;UAACrH,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;UAtG/EH,EAAA,CAAAQ,SAAA,GAAiE;UAAjER,EAAA,CAAAuC,iBAAA,CAAAgG,GAAA,CAAA9F,UAAA,uDAAiE;UAG3EzC,EAAA,CAAAQ,SAAA,GAAwB;UAAxBR,EAAA,CAAAS,UAAA,cAAA8H,GAAA,CAAA3H,UAAA,CAAwB;UAKdZ,EAAA,CAAAQ,SAAA,GAAmD;UAAnDR,EAAA,CAAAS,UAAA,UAAA4B,OAAA,GAAAkG,GAAA,CAAA3H,UAAA,CAAAC,GAAA,4BAAAwB,OAAA,CAAAvB,QAAA,aAAmD;UAMnDd,EAAA,CAAAQ,SAAA,GAAyD;UAAzDR,EAAA,CAAAS,UAAA,UAAA6B,OAAA,GAAAiG,GAAA,CAAA3H,UAAA,CAAAC,GAAA,kCAAAyB,OAAA,CAAAxB,QAAA,aAAyD;UAOvDd,EAAA,CAAAQ,SAAA,GAAmD;UAAnDR,EAAA,CAAAS,UAAA,UAAA4I,OAAA,GAAAd,GAAA,CAAA3H,UAAA,CAAAC,GAAA,4BAAAwI,OAAA,CAAAvI,QAAA,aAAmD;UACnDd,EAAA,CAAAQ,SAAA,GAA8C;UAA9CR,EAAA,CAAAS,UAAA,UAAA6I,OAAA,GAAAf,GAAA,CAAA3H,UAAA,CAAAC,GAAA,4BAAAyI,OAAA,CAAAxI,QAAA,QAA8C;UAU9Cd,EAAA,CAAAQ,SAAA,IAAoD;UAApDR,EAAA,CAAAS,UAAA,UAAA8I,OAAA,GAAAhB,GAAA,CAAA3H,UAAA,CAAAC,GAAA,6BAAA0I,OAAA,CAAAzI,QAAA,aAAoD;UAMXd,EAAA,CAAAQ,SAAA,GAA0C;UAA1CR,EAAA,CAAAS,UAAA,YAAA+I,OAAA,GAAAjB,GAAA,CAAA3H,UAAA,CAAAC,GAAA,iCAAA2I,OAAA,CAAApH,KAAA,EAA0C;UAYpEpC,EAAA,CAAAQ,SAAA,GAAsB;UAAtBR,EAAA,CAAAS,UAAA,YAAA8H,GAAA,CAAAlE,QAAA,CAAAoF,QAAA,CAAsB;UAgDKzJ,EAAA,CAAAQ,SAAA,GAA4C;UAA5CR,EAAA,CAAAS,UAAA,aAAA8H,GAAA,CAAA3H,UAAA,CAAA8I,OAAA,IAAAnB,GAAA,CAAArF,SAAA,CAA4C;UACtElD,EAAA,CAAAQ,SAAA,GAAe;UAAfR,EAAA,CAAAS,UAAA,SAAA8H,GAAA,CAAArF,SAAA,CAAe;UACpClD,EAAA,CAAAQ,SAAA,GAAgB;UAAhBR,EAAA,CAAAS,UAAA,UAAA8H,GAAA,CAAArF,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}