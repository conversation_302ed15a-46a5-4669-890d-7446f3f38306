{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"../../../core/services/client.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/icon\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le pr\\u00E9nom est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le nom est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" L'email est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Format d'email invalide \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le mot de passe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le mot de passe doit contenir au moins 6 caract\\u00E8res \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La confirmation est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Les mots de passe ne correspondent pas \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" S\\u00E9lectionnez un type de compte \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Vous devez accepter les conditions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_spinner_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 25);\n  }\n}\nfunction RegisterComponent_span_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"S'inscrire\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(fb, authService, clientService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.clientService = clientService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.registerForm = this.fb.group({\n      firstName: [\"\", Validators.required],\n      lastName: [\"\", Validators.required],\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", [Validators.required, Validators.minLength(6)]],\n      confirmPassword: [\"\", Validators.required],\n      accountType: [\"\", Validators.required],\n      acceptTerms: [false, Validators.requiredTrue]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get(\"password\");\n    const confirmPassword = form.get(\"confirmPassword\");\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      return {\n        passwordMismatch: true\n      };\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      const formData = this.registerForm.value;\n      this.authService.register(formData).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open(\"Inscription réussie !\", \"Fermer\", {\n            duration: 3000\n          });\n          this.router.navigate([\"/dashboard\"]);\n        },\n        error: error => {\n          this.isLoading = false;\n          console.error(\"Erreur d'inscription complète:\", error);\n          console.error(\"Status:\", error.status);\n          console.error(\"StatusText:\", error.statusText);\n          console.error(\"Error object:\", error.error);\n          console.error(\"Error message:\", error.message);\n          console.error(\"URL:\", error.url);\n          let errorMessage = \"Erreur lors de l'inscription\";\n          if (error.error && typeof error.error === 'string') {\n            errorMessage = error.error;\n          } else if (error.error && error.error.message) {\n            errorMessage = error.error.message;\n          }\n          this.snackBar.open(errorMessage, \"Fermer\", {\n            duration: 5000\n          });\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ClientService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 68,\n      vars: 18,\n      consts: [[1, \"auth-container\"], [1, \"auth-wrapper\"], [1, \"auth-card\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"placeholder\", \"Votre pr\\u00E9nom\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"placeholder\", \"Votre nom\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matInput\", \"\", \"formControlName\", \"password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", 3, \"type\"], [\"formControlName\", \"accountType\"], [\"value\", \"client\"], [\"value\", \"formateur\"], [\"value\", \"admin\"], [1, \"checkbox-container\"], [\"formControlName\", \"acceptTerms\"], [\"href\", \"/terms\", \"target\", \"_blank\", 1, \"link\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"submit-btn\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"auth-links\"], [\"routerLink\", \"/auth/login\", 1, \"link\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2)(3, \"mat-card-header\")(4, \"mat-card-title\");\n          i0.ɵɵtext(5, \"Inscription\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"form\", 3);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(8, \"div\", 4)(9, \"mat-form-field\", 5)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 6);\n          i0.ɵɵtemplate(13, RegisterComponent_mat_error_13_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"mat-form-field\", 5)(15, \"mat-label\");\n          i0.ɵɵtext(16, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 8);\n          i0.ɵɵtemplate(18, RegisterComponent_mat_error_18_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 9)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"E-mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 10);\n          i0.ɵɵtemplate(23, RegisterComponent_mat_error_23_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵtemplate(24, RegisterComponent_mat_error_24_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-form-field\", 9)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 11);\n          i0.ɵɵelementStart(29, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_29_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(30, \"mat-icon\");\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(32, RegisterComponent_mat_error_32_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵtemplate(33, RegisterComponent_mat_error_33_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-form-field\", 9)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Confirmer le mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 13);\n          i0.ɵɵelementStart(38, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_38_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(39, \"mat-icon\");\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(41, RegisterComponent_mat_error_41_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵtemplate(42, RegisterComponent_mat_error_42_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"mat-form-field\", 9)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"Type de compte\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-select\", 14)(47, \"mat-option\", 15);\n          i0.ɵɵtext(48, \"Client (Apprenant)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"mat-option\", 16);\n          i0.ɵɵtext(50, \"Formateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-option\", 17);\n          i0.ɵɵtext(52, \"Administrateur\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(53, RegisterComponent_mat_error_53_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 18)(55, \"mat-checkbox\", 19);\n          i0.ɵɵtext(56, \" J'accepte les \");\n          i0.ɵɵelementStart(57, \"a\", 20);\n          i0.ɵɵtext(58, \"conditions d'utilisation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(59, RegisterComponent_mat_error_59_Template, 2, 0, \"mat-error\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"button\", 21);\n          i0.ɵɵtemplate(61, RegisterComponent_mat_spinner_61_Template, 1, 0, \"mat-spinner\", 22);\n          i0.ɵɵtemplate(62, RegisterComponent_span_62_Template, 2, 0, \"span\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 23)(64, \"p\");\n          i0.ɵɵtext(65, \" D\\u00E9j\\u00E0 un compte ? \");\n          i0.ɵɵelementStart(66, \"a\", 24);\n          i0.ɵɵtext(67, \"Se connecter\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_11_0;\n          let tmp_13_0;\n          let tmp_14_0;\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.hasError(\"required\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_4_0.hasError(\"email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_7_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_11_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.registerForm.hasError(\"passwordMismatch\"));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", (tmp_13_0 = ctx.registerForm.get(\"accountType\")) == null ? null : tmp_13_0.hasError(\"required\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", (tmp_14_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_14_0.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatFormField, i8.MatLabel, i8.MatError, i8.MatSuffix, i9.MatInput, i10.MatButton, i10.MatIconButton, i11.MatCheckbox, i12.MatSelect, i13.MatOption, i14.MatIcon, i15.MatProgressSpinner, i4.RouterLink],\n      styles: [\".auth-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1rem;\\n}\\n\\n.auth-wrapper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 500px;\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n}\\n\\n.name-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 1rem;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1.1rem;\\n}\\n\\n.auth-links[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1.5rem;\\n}\\n\\n.link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "RegisterComponent", "constructor", "fb", "authService", "clientService", "router", "snackBar", "hidePassword", "hideConfirmPassword", "isLoading", "ngOnInit", "registerForm", "group", "firstName", "required", "lastName", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "accountType", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "form", "get", "value", "passwordMismatch", "onSubmit", "valid", "formData", "register", "subscribe", "next", "response", "open", "duration", "navigate", "error", "console", "status", "statusText", "message", "url", "errorMessage", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "ClientService", "i4", "Router", "i5", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_7_listener", "ɵɵtemplate", "RegisterComponent_mat_error_13_Template", "RegisterComponent_mat_error_18_Template", "RegisterComponent_mat_error_23_Template", "RegisterComponent_mat_error_24_Template", "RegisterComponent_Template_button_click_29_listener", "RegisterComponent_mat_error_32_Template", "RegisterComponent_mat_error_33_Template", "RegisterComponent_Template_button_click_38_listener", "RegisterComponent_mat_error_41_Template", "RegisterComponent_mat_error_42_Template", "RegisterComponent_mat_error_53_Template", "RegisterComponent_mat_error_59_Template", "RegisterComponent_mat_spinner_61_Template", "RegisterComponent_span_62_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_2_0", "tmp_3_0", "tmp_4_0", "ɵɵtextInterpolate", "tmp_7_0", "tmp_8_0", "tmp_11_0", "tmp_13_0", "tmp_14_0", "invalid"], "sources": ["C:\\e-learning\\src\\app\\features\\auth\\register\\register.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\"\nimport { Form<PERSON>uilder, FormGroup, Validators } from \"@angular/forms\"\nimport { Router } from \"@angular/router\"\nimport { MatSnackBar } from \"@angular/material/snack-bar\"\nimport { AuthService } from \"../../../core/services/auth.service\"\nimport { ClientService } from \"../../../core/services/client.service\"\n\n@Component({\n  selector: \"app-register\",\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-wrapper\">\n        <mat-card class=\"auth-card\">\n          <mat-card-header>\n            <mat-card-title>Inscription</mat-card-title>\n          </mat-card-header>\n\n          <mat-card-content>\n            <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n              <div class=\"name-row\">\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Prénom</mat-label>\n                  <input matInput formControlName=\"firstName\" placeholder=\"Votre prénom\">\n                  <mat-error *ngIf=\"registerForm.get('firstName')?.hasError('required')\">\n                    Le prénom est requis\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Nom</mat-label>\n                  <input matInput formControlName=\"lastName\" placeholder=\"Votre nom\">\n                  <mat-error *ngIf=\"registerForm.get('lastName')?.hasError('required')\">\n                    Le nom est requis\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>E-mail</mat-label>\n                <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n                <mat-error *ngIf=\"registerForm.get('email')?.hasError('required')\">\n                  L'email est requis\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.get('email')?.hasError('email')\">\n                  Format d'email invalide\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Mot de passe</mat-label>\n                <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\">\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"registerForm.get('password')?.hasError('required')\">\n                  Le mot de passe est requis\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.get('password')?.hasError('minlength')\">\n                  Le mot de passe doit contenir au moins 6 caractères\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Confirmer le mot de passe</mat-label>\n                <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\" formControlName=\"confirmPassword\">\n                <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n                  <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n                <mat-error *ngIf=\"registerForm.get('confirmPassword')?.hasError('required')\">\n                  La confirmation est requise\n                </mat-error>\n                <mat-error *ngIf=\"registerForm.hasError('passwordMismatch')\">\n                  Les mots de passe ne correspondent pas\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Type de compte</mat-label>\n                <mat-select formControlName=\"accountType\">\n                  <mat-option value=\"client\">Client (Apprenant)</mat-option>\n                  <mat-option value=\"formateur\">Formateur</mat-option>\n                  <mat-option value=\"admin\">Administrateur</mat-option>\n                </mat-select>\n                <mat-error *ngIf=\"registerForm.get('accountType')?.hasError('required')\">\n                  Sélectionnez un type de compte\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"acceptTerms\">\n                  J'accepte les <a href=\"/terms\" target=\"_blank\" class=\"link\">conditions d'utilisation</a>\n                </mat-checkbox>\n                <mat-error *ngIf=\"registerForm.get('acceptTerms')?.hasError('required')\">\n                  Vous devez accepter les conditions\n                </mat-error>\n              </div>\n\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"registerForm.invalid || isLoading\" class=\"full-width submit-btn\">\n                <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">S'inscrire</span>\n              </button>\n            </form>\n\n            <div class=\"auth-links\">\n              <p>\n                Déjà un compte ? \n                <a routerLink=\"/auth/login\" class=\"link\">Se connecter</a>\n              </p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [\n    `\n    .auth-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 1rem;\n    }\n\n    .auth-wrapper {\n      width: 100%;\n      max-width: 500px;\n    }\n\n    .auth-card {\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n    }\n\n    .name-row {\n      display: flex;\n      gap: 1rem;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 1rem;\n    }\n\n    .half-width {\n      flex: 1;\n      margin-bottom: 1rem;\n    }\n\n    .checkbox-container {\n      margin-bottom: 1.5rem;\n    }\n\n    .submit-btn {\n      height: 48px;\n      font-size: 1.1rem;\n    }\n\n    .auth-links {\n      text-align: center;\n      margin-top: 1.5rem;\n    }\n\n    .link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .link:hover {\n      text-decoration: underline;\n    }\n  `,\n  ],\n})\nexport class RegisterComponent implements OnInit {\n  registerForm!: FormGroup\n  hidePassword = true\n  hideConfirmPassword = true\n  isLoading = false\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private clientService: ClientService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n  ) {}\n\n  ngOnInit(): void {\n    this.registerForm = this.fb.group(\n      {\n        firstName: [\"\", Validators.required],\n        lastName: [\"\", Validators.required],\n        email: [\"\", [Validators.required, Validators.email]],\n        password: [\"\", [Validators.required, Validators.minLength(6)]],\n        confirmPassword: [\"\", Validators.required],\n        accountType: [\"\", Validators.required],\n        acceptTerms: [false, Validators.requiredTrue],\n      },\n      { validators: this.passwordMatchValidator },\n    )\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get(\"password\")\n    const confirmPassword = form.get(\"confirmPassword\")\n\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      return { passwordMismatch: true }\n    }\n    return null\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true\n      const formData = this.registerForm.value\n\n      this.authService.register(formData).subscribe({\n        next: (response) => {\n          this.isLoading = false\n          this.snackBar.open(\"Inscription réussie !\", \"Fermer\", { duration: 3000 })\n          this.router.navigate([\"/dashboard\"])\n        },\n        error: (error) => {\n          this.isLoading = false\n          console.error(\"Erreur d'inscription complète:\", error)\n          console.error(\"Status:\", error.status)\n          console.error(\"StatusText:\", error.statusText)\n          console.error(\"Error object:\", error.error)\n          console.error(\"Error message:\", error.message)\n          console.error(\"URL:\", error.url)\n\n          let errorMessage = \"Erreur lors de l'inscription\"\n          if (error.error && typeof error.error === 'string') {\n            errorMessage = error.error\n          } else if (error.error && error.error.message) {\n            errorMessage = error.error.message\n          }\n\n          this.snackBar.open(errorMessage, \"Fermer\", { duration: 5000 })\n        },\n      })\n    }\n  }\n}\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;IAsBjDC,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOdH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,iEACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASZH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOZH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAKZH,EAAA,CAAAI,SAAA,sBAA2D;;;;;IAC3DJ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AA4E1D,OAAM,MAAOE,iBAAiB;EAM5BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,aAA4B,EAC5BC,MAAc,EACdC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,SAAS,GAAG,KAAK;EAQd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAC/B;MACEC,SAAS,EAAE,CAAC,EAAE,EAAEnB,UAAU,CAACoB,QAAQ,CAAC;MACpCC,QAAQ,EAAE,CAAC,EAAE,EAAErB,UAAU,CAACoB,QAAQ,CAAC;MACnCE,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACsB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,eAAe,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACoB,QAAQ,CAAC;MAC1CM,WAAW,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAACoB,QAAQ,CAAC;MACtCO,WAAW,EAAE,CAAC,KAAK,EAAE3B,UAAU,CAAC4B,YAAY;KAC7C,EACD;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC5C;EACH;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAMR,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMP,eAAe,GAAGM,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAIT,QAAQ,IAAIE,eAAe,IAAIF,QAAQ,CAACU,KAAK,KAAKR,eAAe,CAACQ,KAAK,EAAE;MAC3E,OAAO;QAAEC,gBAAgB,EAAE;MAAI,CAAE;;IAEnC,OAAO,IAAI;EACb;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAClB,YAAY,CAACmB,KAAK,EAAE;MAC3B,IAAI,CAACrB,SAAS,GAAG,IAAI;MACrB,MAAMsB,QAAQ,GAAG,IAAI,CAACpB,YAAY,CAACgB,KAAK;MAExC,IAAI,CAACxB,WAAW,CAAC6B,QAAQ,CAACD,QAAQ,CAAC,CAACE,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC1B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACH,QAAQ,CAAC8B,IAAI,CAAC,uBAAuB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACzE,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC9B,SAAS,GAAG,KAAK;UACtB+B,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtDC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACE,MAAM,CAAC;UACtCD,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACG,UAAU,CAAC;UAC9CF,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;UAC3CC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACI,OAAO,CAAC;UAC9CH,OAAO,CAACD,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACK,GAAG,CAAC;UAEhC,IAAIC,YAAY,GAAG,8BAA8B;UACjD,IAAIN,KAAK,CAACA,KAAK,IAAI,OAAOA,KAAK,CAACA,KAAK,KAAK,QAAQ,EAAE;YAClDM,YAAY,GAAGN,KAAK,CAACA,KAAK;WAC3B,MAAM,IAAIA,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACI,OAAO,EAAE;YAC7CE,YAAY,GAAGN,KAAK,CAACA,KAAK,CAACI,OAAO;;UAGpC,IAAI,CAACrC,QAAQ,CAAC8B,IAAI,CAACS,YAAY,EAAE,QAAQ,EAAE;YAAER,QAAQ,EAAE;UAAI,CAAE,CAAC;QAChE;OACD,CAAC;;EAEN;;;uBAtEWrC,iBAAiB,EAAAL,EAAA,CAAAmD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArD,EAAA,CAAAmD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAvD,EAAA,CAAAmD,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAzD,EAAA,CAAAmD,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAA3D,EAAA,CAAAmD,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBxD,iBAAiB;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtK1BpE,EAAA,CAAAC,cAAA,aAA4B;UAIJD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAG9CH,EAAA,CAAAC,cAAA,uBAAkB;UACiBD,EAAA,CAAAsE,UAAA,sBAAAC,oDAAA;YAAA,OAAYF,GAAA,CAAAnC,QAAA,EAAU;UAAA,EAAC;UACtDlC,EAAA,CAAAC,cAAA,aAAsB;UAEPD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAI,SAAA,gBAAuE;UACvEJ,EAAA,CAAAwE,UAAA,KAAAC,uCAAA,uBAEY;UACdzE,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC1BH,EAAA,CAAAI,SAAA,gBAAmE;UACnEJ,EAAA,CAAAwE,UAAA,KAAAE,uCAAA,uBAEY;UACd1E,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAI,SAAA,iBAAmF;UACnFJ,EAAA,CAAAwE,UAAA,KAAAG,uCAAA,uBAEY;UACZ3E,EAAA,CAAAwE,UAAA,KAAAI,uCAAA,uBAEY;UACd5E,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAI,SAAA,iBAAuF;UACvFJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAsE,UAAA,mBAAAO,oDAAA;YAAA,OAAAR,GAAA,CAAAzD,YAAA,IAAAyD,GAAA,CAAAzD,YAAA;UAAA,EAAsC;UACtEZ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEzEH,EAAA,CAAAwE,UAAA,KAAAM,uCAAA,uBAEY;UACZ9E,EAAA,CAAAwE,UAAA,KAAAO,uCAAA,uBAEY;UACd/E,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChDH,EAAA,CAAAI,SAAA,iBAAqG;UACrGJ,EAAA,CAAAC,cAAA,kBAAqG;UAAnED,EAAA,CAAAsE,UAAA,mBAAAU,oDAAA;YAAA,OAAAX,GAAA,CAAAxD,mBAAA,IAAAwD,GAAA,CAAAxD,mBAAA;UAAA,EAAoD;UACpFb,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEhFH,EAAA,CAAAwE,UAAA,KAAAS,uCAAA,uBAEY;UACZjF,EAAA,CAAAwE,UAAA,KAAAU,uCAAA,uBAEY;UACdlF,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,sBAA0C;UACbD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC1DH,EAAA,CAAAC,cAAA,sBAA8B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACpDH,EAAA,CAAAC,cAAA,sBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAEvDH,EAAA,CAAAwE,UAAA,KAAAW,uCAAA,uBAEY;UACdnF,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,eAAgC;UAE5BD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAC,cAAA,aAA8C;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE1FH,EAAA,CAAAwE,UAAA,KAAAY,uCAAA,uBAEY;UACdpF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,kBACqF;UACnFD,EAAA,CAAAwE,UAAA,KAAAa,yCAAA,0BAA2D;UAC3DrF,EAAA,CAAAwE,UAAA,KAAAc,kCAAA,kBAA0C;UAC5CtF,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,eAAwB;UAEpBD,EAAA,CAAAE,MAAA,oCACA;UAAAF,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;;UAzFvDH,EAAA,CAAAuF,SAAA,GAA0B;UAA1BvF,EAAA,CAAAwF,UAAA,cAAAnB,GAAA,CAAArD,YAAA,CAA0B;UAKdhB,EAAA,CAAAuF,SAAA,GAAyD;UAAzDvF,EAAA,CAAAwF,UAAA,UAAAC,OAAA,GAAApB,GAAA,CAAArD,YAAA,CAAAe,GAAA,gCAAA0D,OAAA,CAAAC,QAAA,aAAyD;UAQzD1F,EAAA,CAAAuF,SAAA,GAAwD;UAAxDvF,EAAA,CAAAwF,UAAA,UAAAG,OAAA,GAAAtB,GAAA,CAAArD,YAAA,CAAAe,GAAA,+BAAA4D,OAAA,CAAAD,QAAA,aAAwD;UAS1D1F,EAAA,CAAAuF,SAAA,GAAqD;UAArDvF,EAAA,CAAAwF,UAAA,UAAAI,OAAA,GAAAvB,GAAA,CAAArD,YAAA,CAAAe,GAAA,4BAAA6D,OAAA,CAAAF,QAAA,aAAqD;UAGrD1F,EAAA,CAAAuF,SAAA,GAAkD;UAAlDvF,EAAA,CAAAwF,UAAA,UAAAK,OAAA,GAAAxB,GAAA,CAAArD,YAAA,CAAAe,GAAA,4BAAA8D,OAAA,CAAAH,QAAA,UAAkD;UAO9C1F,EAAA,CAAAuF,SAAA,GAA2C;UAA3CvF,EAAA,CAAAwF,UAAA,SAAAnB,GAAA,CAAAzD,YAAA,uBAA2C;UAE/CZ,EAAA,CAAAuF,SAAA,GAAkD;UAAlDvF,EAAA,CAAA8F,iBAAA,CAAAzB,GAAA,CAAAzD,YAAA,mCAAkD;UAElDZ,EAAA,CAAAuF,SAAA,GAAwD;UAAxDvF,EAAA,CAAAwF,UAAA,UAAAO,OAAA,GAAA1B,GAAA,CAAArD,YAAA,CAAAe,GAAA,+BAAAgE,OAAA,CAAAL,QAAA,aAAwD;UAGxD1F,EAAA,CAAAuF,SAAA,GAAyD;UAAzDvF,EAAA,CAAAwF,UAAA,UAAAQ,OAAA,GAAA3B,GAAA,CAAArD,YAAA,CAAAe,GAAA,+BAAAiE,OAAA,CAAAN,QAAA,cAAyD;UAOrD1F,EAAA,CAAAuF,SAAA,GAAkD;UAAlDvF,EAAA,CAAAwF,UAAA,SAAAnB,GAAA,CAAAxD,mBAAA,uBAAkD;UAEtDb,EAAA,CAAAuF,SAAA,GAAyD;UAAzDvF,EAAA,CAAA8F,iBAAA,CAAAzB,GAAA,CAAAxD,mBAAA,mCAAyD;UAEzDb,EAAA,CAAAuF,SAAA,GAA+D;UAA/DvF,EAAA,CAAAwF,UAAA,UAAAS,QAAA,GAAA5B,GAAA,CAAArD,YAAA,CAAAe,GAAA,sCAAAkE,QAAA,CAAAP,QAAA,aAA+D;UAG/D1F,EAAA,CAAAuF,SAAA,GAA+C;UAA/CvF,EAAA,CAAAwF,UAAA,SAAAnB,GAAA,CAAArD,YAAA,CAAA0E,QAAA,qBAA+C;UAY/C1F,EAAA,CAAAuF,SAAA,IAA2D;UAA3DvF,EAAA,CAAAwF,UAAA,UAAAU,QAAA,GAAA7B,GAAA,CAAArD,YAAA,CAAAe,GAAA,kCAAAmE,QAAA,CAAAR,QAAA,aAA2D;UAS3D1F,EAAA,CAAAuF,SAAA,GAA2D;UAA3DvF,EAAA,CAAAwF,UAAA,UAAAW,QAAA,GAAA9B,GAAA,CAAArD,YAAA,CAAAe,GAAA,kCAAAoE,QAAA,CAAAT,QAAA,aAA2D;UAMjE1F,EAAA,CAAAuF,SAAA,GAA8C;UAA9CvF,EAAA,CAAAwF,UAAA,aAAAnB,GAAA,CAAArD,YAAA,CAAAoF,OAAA,IAAA/B,GAAA,CAAAvD,SAAA,CAA8C;UACxBd,EAAA,CAAAuF,SAAA,GAAe;UAAfvF,EAAA,CAAAwF,UAAA,SAAAnB,GAAA,CAAAvD,SAAA,CAAe;UACpCd,EAAA,CAAAuF,SAAA,GAAgB;UAAhBvF,EAAA,CAAAwF,UAAA,UAAAnB,GAAA,CAAAvD,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}