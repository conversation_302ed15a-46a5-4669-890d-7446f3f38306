import { NgModule } from "@angular/core"
import { CommonModule } from "@angular/common"
import { RouterModule } from "@angular/router"
import { FormsModule } from "@angular/forms" // For ngModel in radio buttons

// Angular Material
import { MatCardModule } from "@angular/material/card"
import { MatButtonModule } from "@angular/material/button"
import { MatIconModule } from "@angular/material/icon"
import { MatProgressBarModule } from "@angular/material/progress-bar"
import { MatRadioModule } from "@angular/material/radio"
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner"

import { QuizComponent } from "./quiz.component"

@NgModule({
  declarations: [QuizComponent],
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatRadioModule,
    MatProgressSpinnerModule,
    RouterModule.forChild([{ path: ":id", component: QuizComponent }]),
  ],
})
export class QuizModule {}
