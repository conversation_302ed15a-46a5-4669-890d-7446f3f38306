{"ast": null, "code": "import * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, Optional, Inject, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, Injectable, ANIMATION_MODULE_TYPE as ANIMATION_MODULE_TYPE$1, SkipSelf, Directive, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i4 from '@angular/cdk/portal';\nimport { PortalModule } from '@angular/cdk/portal';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { MatCommonModule } from '@angular/material/core';\nimport { trigger, state, style, transition, group, animate, query, animateChild } from '@angular/animations';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nfunction MatDialogContainer_ng_template_2_Template(rf, ctx) {}\nclass MatDialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Custom class for the overlay pane. */\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n    /** Custom class for the backdrop. */\n    this.backdropClass = '';\n    /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n    this.disableClose = false;\n    /** Width of the dialog. */\n    this.width = '';\n    /** Height of the dialog. */\n    this.height = '';\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n    this.maxWidth = '80vw';\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    this.ariaLabelledBy = null;\n    /** Aria label to assign to the dialog element. */\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the\n     * previously-focused element, after it's closed.\n     */\n    this.restoreFocus = true;\n    /** Whether to wait for the opening animation to finish before trapping focus. */\n    this.delayFocusTrap = true;\n    /**\n     * Whether the dialog should close when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    this.closeOnNavigation = true;\n    // TODO(jelbourn): add configuration for lifecycle hooks, ARIA labelling.\n  }\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\n/**\n * Base class for the `MatDialogContainer`. The base class does not implement\n * animations as these are left to implementers of the dialog container.\n */\n// tslint:disable-next-line:validate-decorators\nclass _MatDialogContainerBase extends CdkDialogContainer {\n  constructor(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, focusMonitor) {\n    super(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, focusMonitor);\n    /** Emits when an animation state changes. */\n    this._animationStateChanged = new EventEmitter();\n  }\n  _captureInitialFocus() {\n    if (!this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Callback for when the open dialog animation has finished. Intended to\n   * be called by sub-classes that use different animation implementations.\n   */\n  _openAnimationDone(totalTime) {\n    if (this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n    this._animationStateChanged.next({\n      state: 'opened',\n      totalTime\n    });\n  }\n  static {\n    this.ɵfac = function _MatDialogContainerBase_Factory(t) {\n      return new (t || _MatDialogContainerBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MatDialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _MatDialogContainerBase,\n      selectors: [[\"ng-component\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 0,\n      vars: 0,\n      template: function _MatDialogContainerBase_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatDialogContainerBase, [{\n    type: Component,\n    args: [{\n      template: ''\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: MatDialogConfig\n    }, {\n      type: i1.InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.OverlayRef\n    }, {\n      type: i1.FocusMonitor\n    }];\n  }, null);\n})();\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\n/**\n * Internal component that wraps user-provided dialog content in a MDC dialog.\n * @docs-private\n */\nclass MatDialogContainer extends _MatDialogContainerBase {\n  constructor(elementRef, focusTrapFactory, document, dialogConfig, checker, ngZone, overlayRef, _animationMode, focusMonitor) {\n    super(elementRef, focusTrapFactory, document, dialogConfig, checker, ngZone, overlayRef, focusMonitor);\n    this._animationMode = _animationMode;\n    /** Whether animations are enabled. */\n    this._animationsEnabled = this._animationMode !== 'NoopAnimations';\n    /** Host element of the dialog container component. */\n    this._hostElement = this._elementRef.nativeElement;\n    /** Duration of the dialog open animation. */\n    this._enterAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION : 0;\n    /** Duration of the dialog close animation. */\n    this._exitAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION : 0;\n    /** Current timer for dialog animations. */\n    this._animationTimer = null;\n    /**\n     * Completes the dialog open by clearing potential animation classes, trapping\n     * focus and emitting an opened event.\n     */\n    this._finishDialogOpen = () => {\n      this._clearAnimationClasses();\n      this._openAnimationDone(this._enterAnimationDuration);\n    };\n    /**\n     * Completes the dialog close by clearing potential animation classes, restoring\n     * focus and emitting a closed event.\n     */\n    this._finishDialogClose = () => {\n      this._clearAnimationClasses();\n      this._animationStateChanged.emit({\n        state: 'closed',\n        totalTime: this._exitAnimationDuration\n      });\n    };\n  }\n  _contentAttached() {\n    // Delegate to the original dialog-container initialization (i.e. saving the\n    // previous element, setting up the focus trap and moving focus to the container).\n    super._contentAttached();\n    // Note: Usually we would be able to use the MDC dialog foundation here to handle\n    // the dialog animation for us, but there are a few reasons why we just leverage\n    // their styles and not use the runtime foundation code:\n    //   1. Foundation does not allow us to disable animations.\n    //   2. Foundation contains unnecessary features we don't need and aren't\n    //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n    //   3. Foundation uses unnecessary timers for animations to work around limitations\n    //      in React's `setState` mechanism.\n    //      https://github.com/material-components/material-components-web/pull/3682.\n    this._startOpenAnimation();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n  }\n  /** Starts the dialog open animation if enabled. */\n  _startOpenAnimation() {\n    this._animationStateChanged.emit({\n      state: 'opening',\n      totalTime: this._enterAnimationDuration\n    });\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      // One would expect that the open class is added once the animation finished, but MDC\n      // uses the open class in combination with the opening class to start the animation.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n      this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n    } else {\n      this._hostElement.classList.add(OPEN_CLASS);\n      // Note: We could immediately finish the dialog opening here with noop animations,\n      // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n      // Executing this immediately would mean that `afterOpened` emits synchronously\n      // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n      Promise.resolve().then(() => this._finishDialogOpen());\n    }\n  }\n  /**\n   * Starts the exit animation of the dialog if enabled. This method is\n   * called by the dialog ref.\n   */\n  _startExitAnimation() {\n    this._animationStateChanged.emit({\n      state: 'closing',\n      totalTime: this._exitAnimationDuration\n    });\n    this._hostElement.classList.remove(OPEN_CLASS);\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n      this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n    } else {\n      // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n      // set up before any user can subscribe to the backdrop click. The subscription triggers\n      // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n      // animation state event if animations are disabled, the overlay would be disposed\n      // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n      // skipped. We work around this by waiting with the dialog close until the next tick when\n      // all subscriptions have been fired as expected. This is not an ideal solution, but\n      // there doesn't seem to be any other good way. Alternatives that have been considered:\n      //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n      //      Also this issue is specific to the MDC implementation where the dialog could\n      //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n      //      and closing always takes at least a tick.\n      //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n      //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n      //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n      // Based on the fact that this is specific to the MDC-based implementation of the dialog\n      // animations, the defer is applied here.\n      Promise.resolve().then(() => this._finishDialogClose());\n    }\n  }\n  /** Clears all dialog animation classes. */\n  _clearAnimationClasses() {\n    this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n  }\n  _waitForAnimationToComplete(duration, callback) {\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n    // Note that we want this timer to run inside the NgZone, because we want\n    // the related events like `afterClosed` to be inside the zone as well.\n    this._animationTimer = setTimeout(callback, duration);\n  }\n  /** Runs a callback in `requestAnimationFrame`, if available. */\n  _requestAnimationFrame(callback) {\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(callback);\n      } else {\n        callback();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function MatDialogContainer_Factory(t) {\n      return new (t || MatDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MatDialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDialogContainer,\n      selectors: [[\"mat-dialog-container\"]],\n      hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-mdc-dialog-container\", \"mdc-dialog\"],\n      hostVars: 8,\n      hostBindings: function MatDialogContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx._config.id);\n          i0.ɵɵattribute(\"aria-modal\", ctx._config.ariaModal)(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledBy)(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", !ctx._animationsEnabled);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"mdc-dialog__container\"], [1, \"mat-mdc-dialog-surface\", \"mdc-dialog__surface\"], [\"cdkPortalOutlet\", \"\"]],\n      template: function MatDialogContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, MatDialogContainer_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i4.CdkPortalOutlet],\n      styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;transform:scale(0.8);opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{transform:none;opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{display:block;width:100%;height:100%}.mat-mdc-dialog-container{--mdc-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mdc-dialog-container-shadow-color:#000;--mdc-dialog-container-shape:4px;--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition-duration:var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container{transition:none}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-actions{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-dialog-container mdc-dialog',\n        'tabindex': '-1',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[id]': '_config.id',\n        '[attr.role]': '_config.role',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n        '[class._mat-animation-noopable]': '!_animationsEnabled'\n      },\n      template: \"<div class=\\\"mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet></ng-template>\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;transform:scale(0.8);opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{transform:none;opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{display:block;width:100%;height:100%}.mat-mdc-dialog-container{--mdc-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mdc-dialog-container-shadow-color:#000;--mdc-dialog-container-shape:4px;--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition-duration:var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container{transition:none}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-actions{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: MatDialogConfig\n    }, {\n      type: i1.InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.OverlayRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: i1.FocusMonitor\n    }];\n  }, null);\n})();\n\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n  constructor(_ref, config, _containerInstance) {\n    this._ref = _ref;\n    this._containerInstance = _containerInstance;\n    /** Subject for notifying the user that the dialog has finished opening. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the dialog has started closing. */\n    this._beforeClosed = new Subject();\n    /** Current state of the dialog. */\n    this._state = 0 /* MatDialogState.OPEN */;\n    this.disableClose = config.disableClose;\n    this.id = _ref.id;\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._finishDialogClose();\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult) {\n    this._result = dialogResult;\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n      this._beforeClosed.complete();\n      this._ref.overlayRef.detachBackdrop();\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n    this._state = 1 /* MatDialogState.CLOSING */;\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed() {\n    return this._ref.closed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position) {\n    let strategy = this._ref.config.positionStrategy;\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n    this._ref.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  _finishDialogClose() {\n    this._state = 2 /* MatDialogState.CLOSED */;\n    this._ref.close(this._result, {\n      focusOrigin: this._closeInteractionType\n    });\n    this.componentInstance = null;\n  }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n  ref._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy');\n/** @docs-private */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/** @docs-private */\nfunction MAT_DIALOG_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n// Counter for unique dialog ids.\nlet uniqueId = 0;\n/**\n * Base class for dialog services. The base dialog service allows\n * for arbitrary dialog refs and dialog container components.\n */\nclass _MatDialogBase {\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  constructor(_overlay, injector, _defaultOptions, _parentDialog,\n  /**\n   * @deprecated No longer used. To be removed.\n   * @breaking-change 15.0.0\n   */\n  _overlayContainer, scrollStrategy, _dialogRefConstructor, _dialogContainerType, _dialogDataToken,\n  /**\n   * @deprecated No longer used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  _animationMode) {\n    this._overlay = _overlay;\n    this._defaultOptions = _defaultOptions;\n    this._parentDialog = _parentDialog;\n    this._dialogRefConstructor = _dialogRefConstructor;\n    this._dialogContainerType = _dialogContainerType;\n    this._dialogDataToken = _dialogDataToken;\n    this._openDialogsAtThisLevel = [];\n    this._afterAllClosedAtThisLevel = new Subject();\n    this._afterOpenedAtThisLevel = new Subject();\n    this._idPrefix = 'mat-dialog-';\n    this.dialogConfigClass = MatDialogConfig;\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    this._scrollStrategy = scrollStrategy;\n    this._dialog = injector.get(Dialog);\n  }\n  open(componentOrTemplateRef, config) {\n    let dialogRef;\n    config = {\n      ...(this._defaultOptions || new MatDialogConfig()),\n      ...config\n    };\n    config.id = config.id || `${this._idPrefix}${uniqueId++}`;\n    config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n    const cdkRef = this._dialog.open(componentOrTemplateRef, {\n      ...config,\n      positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Disable closing on destroy, because this service cleans up its open dialogs as well.\n      // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n      // the dialogs immediately whereas we want it to wait for the animations to finish.\n      closeOnDestroy: false,\n      // Disable closing on detachments so that we can sync up the animation.\n      // The Material dialog ref handles this manually.\n      closeOnOverlayDetachments: false,\n      container: {\n        type: this._dialogContainerType,\n        providers: () => [\n        // Provide our config as the CDK config as well since it has the same interface as the\n        // CDK one, but it contains the actual values passed in by the user for things like\n        // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n        {\n          provide: this.dialogConfigClass,\n          useValue: config\n        }, {\n          provide: DialogConfig,\n          useValue: config\n        }]\n      },\n      templateContext: () => ({\n        dialogRef\n      }),\n      providers: (ref, cdkConfig, dialogContainer) => {\n        dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n        dialogRef.updatePosition(config?.position);\n        return [{\n          provide: this._dialogContainerType,\n          useValue: dialogContainer\n        }, {\n          provide: this._dialogDataToken,\n          useValue: cdkConfig.data\n        }, {\n          provide: this._dialogRefConstructor,\n          useValue: dialogRef\n        }];\n      }\n    });\n    // This can't be assigned in the `providers` callback, because\n    // the instance hasn't been assigned to the CDK ref yet.\n    dialogRef.componentRef = cdkRef.componentRef;\n    dialogRef.componentInstance = cdkRef.componentInstance;\n    this.openDialogs.push(dialogRef);\n    this.afterOpened.next(dialogRef);\n    dialogRef.afterClosed().subscribe(() => {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        if (!this.openDialogs.length) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    });\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    this._closeDialogs(this.openDialogs);\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Only close the dialogs at this level on destroy\n    // since the parent service may still be active.\n    this._closeDialogs(this._openDialogsAtThisLevel);\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n  }\n  _closeDialogs(dialogs) {\n    let i = dialogs.length;\n    while (i--) {\n      dialogs[i].close();\n    }\n  }\n  static {\n    this.ɵfac = function _MatDialogBase_Factory(t) {\n      i0.ɵɵinvalidFactory();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: _MatDialogBase,\n      factory: _MatDialogBase.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatDialogBase, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.Injector\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }, {\n      type: i1$1.OverlayContainer\n    }, {\n      type: undefined\n    }, {\n      type: i0.Type\n    }, {\n      type: i0.Type\n    }, {\n      type: i0.InjectionToken\n    }, {\n      type: undefined\n    }];\n  }, null);\n})();\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog extends _MatDialogBase {\n  constructor(overlay, injector,\n  /**\n   * @deprecated `_location` parameter to be removed.\n   * @breaking-change 10.0.0\n   */\n  location, defaultOptions, scrollStrategy, parentDialog,\n  /**\n   * @deprecated No longer used. To be removed.\n   * @breaking-change 15.0.0\n   */\n  overlayContainer,\n  /**\n   * @deprecated No longer used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  animationMode) {\n    super(overlay, injector, defaultOptions, parentDialog, overlayContainer, scrollStrategy, MatDialogRef, MatDialogContainer, MAT_DIALOG_DATA, animationMode);\n    this._idPrefix = 'mat-mdc-dialog-';\n  }\n  static {\n    this.ɵfac = function MatDialog_Factory(t) {\n      return new (t || MatDialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.Location, 8), i0.ɵɵinject(MAT_DIALOG_DEFAULT_OPTIONS, 8), i0.ɵɵinject(MAT_DIALOG_SCROLL_STRATEGY), i0.ɵɵinject(MatDialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(ANIMATION_MODULE_TYPE$1, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatDialog,\n      factory: MatDialog.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialog, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.Injector\n    }, {\n      type: i2.Location,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: MatDialogConfig,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_DIALOG_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_DIALOG_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: MatDialog,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: i1$1.OverlayContainer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE$1]\n      }]\n    }];\n  }, null);\n})();\n\n/** Counter used to generate unique IDs for dialog elements. */\nlet dialogElementUid = 0;\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n  constructor(\n  // The dialog title directive is always used in combination with a `MatDialogRef`.\n  // tslint:disable-next-line: lightweight-tokens\n  dialogRef, _elementRef, _dialog) {\n    this.dialogRef = dialogRef;\n    this._elementRef = _elementRef;\n    this._dialog = _dialog;\n    /** Default to \"button\" to prevents accidental form submits. */\n    this.type = 'button';\n  }\n  ngOnInit() {\n    if (!this.dialogRef) {\n      // When this directive is included in a dialog via TemplateRef (rather than being\n      // in a Component), the DialogRef isn't available via injection because embedded\n      // views cannot be given a custom injector. Instead, we look up the DialogRef by\n      // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n      // be resolved at constructor time.\n      this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n  }\n  ngOnChanges(changes) {\n    const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n    if (proxiedChange) {\n      this.dialogResult = proxiedChange.currentValue;\n    }\n  }\n  _onButtonClick(event) {\n    // Determinate the focus origin using the click event, because using the FocusMonitor will\n    // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n    // dialog, and therefore clicking the button won't result in a focus change. This means that\n    // the FocusMonitor won't detect any origin change, and will always output `program`.\n    _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n  }\n  static {\n    this.ɵfac = function MatDialogClose_Factory(t) {\n      return new (t || MatDialogClose)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogClose,\n      selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n      hostVars: 2,\n      hostBindings: function MatDialogClose_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatDialogClose_click_HostBindingHandler($event) {\n            return ctx._onButtonClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        type: \"type\",\n        dialogResult: [\"mat-dialog-close\", \"dialogResult\"],\n        _matDialogClose: [\"matDialogClose\", \"_matDialogClose\"]\n      },\n      exportAs: [\"matDialogClose\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogClose, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-close], [matDialogClose]',\n      exportAs: 'matDialogClose',\n      host: {\n        '(click)': '_onButtonClick($event)',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.type]': 'type'\n      }\n    }]\n  }], function () {\n    return [{\n      type: MatDialogRef,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: MatDialog\n    }];\n  }, {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    type: [{\n      type: Input\n    }],\n    dialogResult: [{\n      type: Input,\n      args: ['mat-dialog-close']\n    }],\n    _matDialogClose: [{\n      type: Input,\n      args: ['matDialogClose']\n    }]\n  });\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle {\n  constructor(\n  // The dialog title directive is always used in combination with a `MatDialogRef`.\n  // tslint:disable-next-line: lightweight-tokens\n  _dialogRef, _elementRef, _dialog) {\n    this._dialogRef = _dialogRef;\n    this._elementRef = _elementRef;\n    this._dialog = _dialog;\n    this.id = `mat-mdc-dialog-title-${dialogElementUid++}`;\n  }\n  ngOnInit() {\n    if (!this._dialogRef) {\n      this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n    if (this._dialogRef) {\n      Promise.resolve().then(() => {\n        const container = this._dialogRef._containerInstance;\n        if (container && !container._ariaLabelledBy) {\n          container._ariaLabelledBy = this.id;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MatDialogTitle_Factory(t) {\n      return new (t || MatDialogTitle)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogTitle,\n      selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-dialog-title\", \"mdc-dialog__title\"],\n      hostVars: 1,\n      hostBindings: function MatDialogTitle_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n        }\n      },\n      inputs: {\n        id: \"id\"\n      },\n      exportAs: [\"matDialogTitle\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogTitle, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-title], [matDialogTitle]',\n      exportAs: 'matDialogTitle',\n      host: {\n        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n        '[id]': 'id'\n      }\n    }]\n  }], function () {\n    return [{\n      type: MatDialogRef,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: MatDialog\n    }];\n  }, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n  static {\n    this.ɵfac = function MatDialogContent_Factory(t) {\n      return new (t || MatDialogContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogContent,\n      selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-dialog-content\", \"mdc-dialog__content\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContent, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n      host: {\n        'class': 'mat-mdc-dialog-content mdc-dialog__content'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions {\n  constructor() {\n    /**\n     * Horizontal alignment of action buttons.\n     */\n    this.align = 'start';\n  }\n  static {\n    this.ɵfac = function MatDialogActions_Factory(t) {\n      return new (t || MatDialogActions)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogActions,\n      selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-dialog-actions\", \"mdc-dialog__actions\"],\n      hostVars: 4,\n      hostBindings: function MatDialogActions_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-dialog-actions-align-center\", ctx.align === \"center\")(\"mat-mdc-dialog-actions-align-end\", ctx.align === \"end\");\n        }\n      },\n      inputs: {\n        align: \"align\"\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogActions, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n      host: {\n        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\nclass MatDialogModule {\n  static {\n    this.ɵfac = function MatDialogModule_Factory(t) {\n      return new (t || MatDialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatDialogModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MatDialog, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER],\n      imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule],\n      exports: [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogContent, MatDialogActions, MatCommonModule],\n      declarations: [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent],\n      providers: [MatDialog, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n */\nconst _defaultParams = {\n  params: {\n    enterAnimationDuration: '150ms',\n    exitAnimationDuration: '75ms'\n  }\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n */\nconst matDialogAnimations = {\n  /** Animation that is applied on the dialog container by default. */\n  dialogContainer: trigger('dialogContainer', [\n  // Note: The `enter` animation transitions to `transform: none`, because for some reason\n  // specifying the transform explicitly, causes IE both to blur the dialog content and\n  // decimate the animation performance. Leaving it as `none` solves both issues.\n  state('void, exit', style({\n    opacity: 0,\n    transform: 'scale(0.7)'\n  })), state('enter', style({\n    transform: 'none'\n  })), transition('* => enter', group([animate('{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)', style({\n    transform: 'none',\n    opacity: 1\n  })), query('@*', animateChild(), {\n    optional: true\n  })]), _defaultParams), transition('* => void, * => exit', group([animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({\n    opacity: 0\n  })), query('@*', animateChild(), {\n    optional: true\n  })]), _defaultParams)])\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DIALOG_DATA, MAT_DIALOG_DEFAULT_OPTIONS, MAT_DIALOG_SCROLL_STRATEGY, MAT_DIALOG_SCROLL_STRATEGY_FACTORY, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, MatDialog, MatDialogActions, MatDialogClose, MatDialogConfig, MatDialogContainer, MatDialogContent, MatDialogModule, MatDialogRef, MatDialogTitle, _MatDialogBase, _MatDialogContainerBase, _closeDialogVia, _defaultParams, matDialogAnimations };", "map": {"version": 3, "names": ["i1$1", "Overlay", "OverlayModule", "i2", "DOCUMENT", "i0", "EventEmitter", "Component", "Optional", "Inject", "ViewEncapsulation", "ChangeDetectionStrategy", "InjectionToken", "Injectable", "ANIMATION_MODULE_TYPE", "ANIMATION_MODULE_TYPE$1", "SkipSelf", "Directive", "Input", "NgModule", "i1", "CdkDialogContainer", "Dialog", "DialogConfig", "DialogModule", "coerceNumberProperty", "i4", "PortalModule", "Subject", "merge", "defer", "filter", "take", "startWith", "ESCAPE", "hasModifierKey", "MatCommonModule", "trigger", "state", "style", "transition", "group", "animate", "query", "animate<PERSON><PERSON><PERSON>", "MatDialogContainer_ng_template_2_Template", "rf", "ctx", "MatDialogConfig", "constructor", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "width", "height", "max<PERSON><PERSON><PERSON>", "data", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "delayFocusTrap", "closeOnNavigation", "OPEN_CLASS", "OPENING_CLASS", "CLOSING_CLASS", "OPEN_ANIMATION_DURATION", "CLOSE_ANIMATION_DURATION", "_MatDialogContainerBase", "elementRef", "focusTrapFactory", "_document", "dialogConfig", "interactivityChecker", "ngZone", "overlayRef", "focusMonitor", "_animationStateChanged", "_captureInitialFocus", "_config", "_trapFocus", "_openAnimationDone", "totalTime", "next", "ɵfac", "_MatDialogContainerBase_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "FocusTrapFactory", "InteractivityChecker", "NgZone", "OverlayRef", "FocusMonitor", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "template", "_MatDialogContainerBase_Template", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "undefined", "decorators", "TRANSITION_DURATION_PROPERTY", "parseCssTime", "time", "endsWith", "substring", "length", "MatDialogContainer", "document", "checker", "_animationMode", "_animationsEnabled", "_hostElement", "_elementRef", "nativeElement", "_enterAnimationDuration", "enterAnimationDuration", "_exitAnimationDuration", "exitAnimationDuration", "_animationTimer", "_finishDialogOpen", "_clearAnimationClasses", "_finishDialogClose", "emit", "_contentAttached", "_startOpenAnimation", "ngOnDestroy", "clearTimeout", "setProperty", "_requestAnimationFrame", "classList", "add", "_waitForAnimationToComplete", "Promise", "resolve", "then", "_startExitAnimation", "remove", "duration", "callback", "setTimeout", "_ngZone", "runOutsideAngular", "requestAnimationFrame", "MatDialogContainer_Factory", "hostAttrs", "hostVars", "hostBindings", "MatDialogContainer_HostBindings", "ɵɵhostProperty", "id", "ɵɵattribute", "_ariaLabelledBy", "ɵɵclassProp", "consts", "MatDialogContainer_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "dependencies", "CdkPortalOutlet", "styles", "selector", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "host", "MatDialogRef", "_ref", "config", "_containerInstance", "_afterOpened", "_beforeClosed", "_state", "pipe", "event", "subscribe", "complete", "_closeFallbackTimeout", "detachments", "_result", "backdropClick", "keydownEvents", "keyCode", "preventDefault", "_closeDialogVia", "close", "dialogResult", "detachBackdrop", "afterOpened", "afterClosed", "closed", "beforeClosed", "updatePosition", "position", "strategy", "positionStrategy", "left", "right", "centerHorizontally", "top", "bottom", "centerVertically", "updateSize", "addPanelClass", "classes", "removePanelClass", "getState", "<PERSON><PERSON><PERSON><PERSON>", "_closeInteractionType", "componentInstance", "ref", "interactionType", "result", "MAT_DIALOG_DATA", "MAT_DIALOG_DEFAULT_OPTIONS", "MAT_DIALOG_SCROLL_STRATEGY", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "block", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MAT_DIALOG_SCROLL_STRATEGY_FACTORY", "uniqueId", "_MatDialogBase", "openDialogs", "_parentDialog", "_openDialogsAtThisLevel", "_afterOpenedAtThisLevel", "_getAfterAllClosed", "parent", "_afterAllClosedAtThisLevel", "_overlay", "injector", "_defaultOptions", "_overlayContainer", "scrollStrategy", "_dialogRefConstructor", "_dialogContainerType", "_dialogDataToken", "_idPrefix", "dialogConfigClass", "afterAllClosed", "_scrollStrategy", "_dialog", "get", "open", "componentOrTemplateRef", "dialogRef", "cdkRef", "global", "closeOnDestroy", "closeOnOverlayDetachments", "container", "providers", "useValue", "templateContext", "cdkConfig", "dialogContainer", "componentRef", "push", "index", "indexOf", "splice", "closeAll", "_closeDialogs", "getDialogById", "find", "dialog", "dialogs", "i", "_MatDialogBase_Factory", "ɵɵinvalidFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "Injector", "OverlayContainer", "Type", "MatDialog", "location", "defaultOptions", "parentDialog", "overlayContainer", "animationMode", "MatDialog_Factory", "ɵɵinject", "Location", "dialogElementUid", "MatDialogClose", "ngOnInit", "getClosestDialog", "ngOnChanges", "changes", "proxied<PERSON><PERSON>e", "currentValue", "_onButtonClick", "screenX", "screenY", "MatDialogClose_Factory", "ɵdir", "ɵɵdefineDirective", "MatDialogClose_HostBindings", "ɵɵlistener", "MatDialogClose_click_HostBindingHandler", "$event", "inputs", "_matDialogClose", "exportAs", "ɵɵNgOnChangesFeature", "MatDialogTitle", "_dialogRef", "MatDialogTitle_Factory", "MatDialogTitle_HostBindings", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogContent_Factory", "MatDialogActions", "align", "MatDialogActions_Factory", "MatDialogActions_HostBindings", "element", "parentElement", "contains", "MatDialogModule", "MatDialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations", "_defaultParams", "params", "matDialogAnimations", "opacity", "transform", "optional"], "sources": ["C:/e-learning/node_modules/@angular/material/fesm2022/dialog.mjs"], "sourcesContent": ["import * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, Optional, Inject, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, Injectable, ANIMATION_MODULE_TYPE as ANIMATION_MODULE_TYPE$1, SkipSelf, Directive, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i4 from '@angular/cdk/portal';\nimport { PortalModule } from '@angular/cdk/portal';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { MatCommonModule } from '@angular/material/core';\nimport { trigger, state, style, transition, group, animate, query, animateChild } from '@angular/animations';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nclass MatDialogConfig {\n    constructor() {\n        /** The ARIA role of the dialog element. */\n        this.role = 'dialog';\n        /** Custom class for the overlay pane. */\n        this.panelClass = '';\n        /** Whether the dialog has a backdrop. */\n        this.hasBackdrop = true;\n        /** Custom class for the backdrop. */\n        this.backdropClass = '';\n        /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n        this.disableClose = false;\n        /** Width of the dialog. */\n        this.width = '';\n        /** Height of the dialog. */\n        this.height = '';\n        /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n        this.maxWidth = '80vw';\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** ID of the element that describes the dialog. */\n        this.ariaDescribedBy = null;\n        /** ID of the element that labels the dialog. */\n        this.ariaLabelledBy = null;\n        /** Aria label to assign to the dialog element. */\n        this.ariaLabel = null;\n        /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n        this.ariaModal = true;\n        /**\n         * Where the dialog should focus on open.\n         * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n         * AutoFocusTarget instead.\n         */\n        this.autoFocus = 'first-tabbable';\n        /**\n         * Whether the dialog should restore focus to the\n         * previously-focused element, after it's closed.\n         */\n        this.restoreFocus = true;\n        /** Whether to wait for the opening animation to finish before trapping focus. */\n        this.delayFocusTrap = true;\n        /**\n         * Whether the dialog should close when the user goes backwards/forwards in history.\n         * Note that this usually doesn't include clicking on links (unless the user is using\n         * the `HashLocationStrategy`).\n         */\n        this.closeOnNavigation = true;\n        // TODO(jelbourn): add configuration for lifecycle hooks, ARIA labelling.\n    }\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\n/**\n * Base class for the `MatDialogContainer`. The base class does not implement\n * animations as these are left to implementers of the dialog container.\n */\n// tslint:disable-next-line:validate-decorators\nclass _MatDialogContainerBase extends CdkDialogContainer {\n    constructor(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, focusMonitor) {\n        super(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, focusMonitor);\n        /** Emits when an animation state changes. */\n        this._animationStateChanged = new EventEmitter();\n    }\n    _captureInitialFocus() {\n        if (!this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Callback for when the open dialog animation has finished. Intended to\n     * be called by sub-classes that use different animation implementations.\n     */\n    _openAnimationDone(totalTime) {\n        if (this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n        this._animationStateChanged.next({ state: 'opened', totalTime });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatDialogContainerBase, deps: [{ token: i0.ElementRef }, { token: i1.FocusTrapFactory }, { token: DOCUMENT, optional: true }, { token: MatDialogConfig }, { token: i1.InteractivityChecker }, { token: i0.NgZone }, { token: i1$1.OverlayRef }, { token: i1.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatDialogContainerBase, selector: \"ng-component\", usesInheritance: true, ngImport: i0, template: '', isInline: true }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatDialogContainerBase, decorators: [{\n            type: Component,\n            args: [{ template: '' }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: MatDialogConfig }, { type: i1.InteractivityChecker }, { type: i0.NgZone }, { type: i1$1.OverlayRef }, { type: i1.FocusMonitor }]; } });\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n    if (time == null) {\n        return null;\n    }\n    if (typeof time === 'number') {\n        return time;\n    }\n    if (time.endsWith('ms')) {\n        return coerceNumberProperty(time.substring(0, time.length - 2));\n    }\n    if (time.endsWith('s')) {\n        return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n    }\n    if (time === '0') {\n        return 0;\n    }\n    return null; // anything else is invalid.\n}\n/**\n * Internal component that wraps user-provided dialog content in a MDC dialog.\n * @docs-private\n */\nclass MatDialogContainer extends _MatDialogContainerBase {\n    constructor(elementRef, focusTrapFactory, document, dialogConfig, checker, ngZone, overlayRef, _animationMode, focusMonitor) {\n        super(elementRef, focusTrapFactory, document, dialogConfig, checker, ngZone, overlayRef, focusMonitor);\n        this._animationMode = _animationMode;\n        /** Whether animations are enabled. */\n        this._animationsEnabled = this._animationMode !== 'NoopAnimations';\n        /** Host element of the dialog container component. */\n        this._hostElement = this._elementRef.nativeElement;\n        /** Duration of the dialog open animation. */\n        this._enterAnimationDuration = this._animationsEnabled\n            ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION\n            : 0;\n        /** Duration of the dialog close animation. */\n        this._exitAnimationDuration = this._animationsEnabled\n            ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION\n            : 0;\n        /** Current timer for dialog animations. */\n        this._animationTimer = null;\n        /**\n         * Completes the dialog open by clearing potential animation classes, trapping\n         * focus and emitting an opened event.\n         */\n        this._finishDialogOpen = () => {\n            this._clearAnimationClasses();\n            this._openAnimationDone(this._enterAnimationDuration);\n        };\n        /**\n         * Completes the dialog close by clearing potential animation classes, restoring\n         * focus and emitting a closed event.\n         */\n        this._finishDialogClose = () => {\n            this._clearAnimationClasses();\n            this._animationStateChanged.emit({ state: 'closed', totalTime: this._exitAnimationDuration });\n        };\n    }\n    _contentAttached() {\n        // Delegate to the original dialog-container initialization (i.e. saving the\n        // previous element, setting up the focus trap and moving focus to the container).\n        super._contentAttached();\n        // Note: Usually we would be able to use the MDC dialog foundation here to handle\n        // the dialog animation for us, but there are a few reasons why we just leverage\n        // their styles and not use the runtime foundation code:\n        //   1. Foundation does not allow us to disable animations.\n        //   2. Foundation contains unnecessary features we don't need and aren't\n        //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n        //   3. Foundation uses unnecessary timers for animations to work around limitations\n        //      in React's `setState` mechanism.\n        //      https://github.com/material-components/material-components-web/pull/3682.\n        this._startOpenAnimation();\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n    }\n    /** Starts the dialog open animation if enabled. */\n    _startOpenAnimation() {\n        this._animationStateChanged.emit({ state: 'opening', totalTime: this._enterAnimationDuration });\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            // One would expect that the open class is added once the animation finished, but MDC\n            // uses the open class in combination with the opening class to start the animation.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n            this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n        }\n        else {\n            this._hostElement.classList.add(OPEN_CLASS);\n            // Note: We could immediately finish the dialog opening here with noop animations,\n            // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n            // Executing this immediately would mean that `afterOpened` emits synchronously\n            // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n            Promise.resolve().then(() => this._finishDialogOpen());\n        }\n    }\n    /**\n     * Starts the exit animation of the dialog if enabled. This method is\n     * called by the dialog ref.\n     */\n    _startExitAnimation() {\n        this._animationStateChanged.emit({ state: 'closing', totalTime: this._exitAnimationDuration });\n        this._hostElement.classList.remove(OPEN_CLASS);\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n            this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n        }\n        else {\n            // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n            // set up before any user can subscribe to the backdrop click. The subscription triggers\n            // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n            // animation state event if animations are disabled, the overlay would be disposed\n            // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n            // skipped. We work around this by waiting with the dialog close until the next tick when\n            // all subscriptions have been fired as expected. This is not an ideal solution, but\n            // there doesn't seem to be any other good way. Alternatives that have been considered:\n            //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n            //      Also this issue is specific to the MDC implementation where the dialog could\n            //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n            //      and closing always takes at least a tick.\n            //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n            //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n            //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n            // Based on the fact that this is specific to the MDC-based implementation of the dialog\n            // animations, the defer is applied here.\n            Promise.resolve().then(() => this._finishDialogClose());\n        }\n    }\n    /** Clears all dialog animation classes. */\n    _clearAnimationClasses() {\n        this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n    }\n    _waitForAnimationToComplete(duration, callback) {\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n        // Note that we want this timer to run inside the NgZone, because we want\n        // the related events like `afterClosed` to be inside the zone as well.\n        this._animationTimer = setTimeout(callback, duration);\n    }\n    /** Runs a callback in `requestAnimationFrame`, if available. */\n    _requestAnimationFrame(callback) {\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame === 'function') {\n                requestAnimationFrame(callback);\n            }\n            else {\n                callback();\n            }\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogContainer, deps: [{ token: i0.ElementRef }, { token: i1.FocusTrapFactory }, { token: DOCUMENT, optional: true }, { token: MatDialogConfig }, { token: i1.InteractivityChecker }, { token: i0.NgZone }, { token: i1$1.OverlayRef }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: i1.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDialogContainer, selector: \"mat-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.aria-modal\": \"_config.ariaModal\", \"id\": \"_config.id\", \"attr.role\": \"_config.role\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledBy\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\", \"class._mat-animation-noopable\": \"!_animationsEnabled\" }, classAttribute: \"mat-mdc-dialog-container mdc-dialog\" }, usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet></ng-template>\\n  </div>\\n</div>\\n\", styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;transform:scale(0.8);opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{transform:none;opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{display:block;width:100%;height:100%}.mat-mdc-dialog-container{--mdc-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mdc-dialog-container-shadow-color:#000;--mdc-dialog-container-shape:4px;--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition-duration:var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container{transition:none}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-actions{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"], dependencies: [{ kind: \"directive\", type: i4.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-dialog-container mdc-dialog',\n                        'tabindex': '-1',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[id]': '_config.id',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                        '[class._mat-animation-noopable]': '!_animationsEnabled',\n                    }, template: \"<div class=\\\"mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet></ng-template>\\n  </div>\\n</div>\\n\", styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;transform:scale(0.8);opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{transform:none;opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{display:block;width:100%;height:100%}.mat-mdc-dialog-container{--mdc-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mdc-dialog-container-shadow-color:#000;--mdc-dialog-container-shape:4px;--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition-duration:var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container{transition:none}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-actions{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: MatDialogConfig }, { type: i1.InteractivityChecker }, { type: i0.NgZone }, { type: i1$1.OverlayRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: i1.FocusMonitor }]; } });\n\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n    constructor(_ref, config, _containerInstance) {\n        this._ref = _ref;\n        this._containerInstance = _containerInstance;\n        /** Subject for notifying the user that the dialog has finished opening. */\n        this._afterOpened = new Subject();\n        /** Subject for notifying the user that the dialog has started closing. */\n        this._beforeClosed = new Subject();\n        /** Current state of the dialog. */\n        this._state = 0 /* MatDialogState.OPEN */;\n        this.disableClose = config.disableClose;\n        this.id = _ref.id;\n        // Emit when opening animation completes\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'opened'), take(1))\n            .subscribe(() => {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        });\n        // Dispose overlay when closing animation is complete\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closed'), take(1))\n            .subscribe(() => {\n            clearTimeout(this._closeFallbackTimeout);\n            this._finishDialogClose();\n        });\n        _ref.overlayRef.detachments().subscribe(() => {\n            this._beforeClosed.next(this._result);\n            this._beforeClosed.complete();\n            this._finishDialogClose();\n        });\n        merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n            if (!this.disableClose) {\n                event.preventDefault();\n                _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param dialogResult Optional result to return to the dialog opener.\n     */\n    close(dialogResult) {\n        this._result = dialogResult;\n        // Transition the backdrop in parallel to the dialog.\n        this._containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closing'), take(1))\n            .subscribe(event => {\n            this._beforeClosed.next(dialogResult);\n            this._beforeClosed.complete();\n            this._ref.overlayRef.detachBackdrop();\n            // The logic that disposes of the overlay depends on the exit animation completing, however\n            // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n            // timeout which will clean everything up if the animation hasn't fired within the specified\n            // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n            // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n            this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n        });\n        this._state = 1 /* MatDialogState.CLOSING */;\n        this._containerInstance._startExitAnimation();\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished opening.\n     */\n    afterOpened() {\n        return this._afterOpened;\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished closing.\n     */\n    afterClosed() {\n        return this._ref.closed;\n    }\n    /**\n     * Gets an observable that is notified when the dialog has started closing.\n     */\n    beforeClosed() {\n        return this._beforeClosed;\n    }\n    /**\n     * Gets an observable that emits when the overlay's backdrop has been clicked.\n     */\n    backdropClick() {\n        return this._ref.backdropClick;\n    }\n    /**\n     * Gets an observable that emits when keydown events are targeted on the overlay.\n     */\n    keydownEvents() {\n        return this._ref.keydownEvents;\n    }\n    /**\n     * Updates the dialog's position.\n     * @param position New dialog position.\n     */\n    updatePosition(position) {\n        let strategy = this._ref.config.positionStrategy;\n        if (position && (position.left || position.right)) {\n            position.left ? strategy.left(position.left) : strategy.right(position.right);\n        }\n        else {\n            strategy.centerHorizontally();\n        }\n        if (position && (position.top || position.bottom)) {\n            position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n        }\n        else {\n            strategy.centerVertically();\n        }\n        this._ref.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this._ref.updateSize(width, height);\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this._ref.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this._ref.removePanelClass(classes);\n        return this;\n    }\n    /** Gets the current state of the dialog's lifecycle. */\n    getState() {\n        return this._state;\n    }\n    /**\n     * Finishes the dialog close by updating the state of the dialog\n     * and disposing the overlay.\n     */\n    _finishDialogClose() {\n        this._state = 2 /* MatDialogState.CLOSED */;\n        this._ref.close(this._result, { focusOrigin: this._closeInteractionType });\n        this.componentInstance = null;\n    }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n    ref._closeInteractionType = interactionType;\n    return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy');\n/** @docs-private */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/** @docs-private */\nfunction MAT_DIALOG_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n// Counter for unique dialog ids.\nlet uniqueId = 0;\n/**\n * Base class for dialog services. The base dialog service allows\n * for arbitrary dialog refs and dialog container components.\n */\nclass _MatDialogBase {\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    constructor(_overlay, injector, _defaultOptions, _parentDialog, \n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 15.0.0\n     */\n    _overlayContainer, scrollStrategy, _dialogRefConstructor, _dialogContainerType, _dialogDataToken, \n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    _animationMode) {\n        this._overlay = _overlay;\n        this._defaultOptions = _defaultOptions;\n        this._parentDialog = _parentDialog;\n        this._dialogRefConstructor = _dialogRefConstructor;\n        this._dialogContainerType = _dialogContainerType;\n        this._dialogDataToken = _dialogDataToken;\n        this._openDialogsAtThisLevel = [];\n        this._afterAllClosedAtThisLevel = new Subject();\n        this._afterOpenedAtThisLevel = new Subject();\n        this._idPrefix = 'mat-dialog-';\n        this.dialogConfigClass = MatDialogConfig;\n        /**\n         * Stream that emits when all open dialog have finished closing.\n         * Will emit on subscribe if there are no open dialogs to begin with.\n         */\n        this.afterAllClosed = defer(() => this.openDialogs.length\n            ? this._getAfterAllClosed()\n            : this._getAfterAllClosed().pipe(startWith(undefined)));\n        this._scrollStrategy = scrollStrategy;\n        this._dialog = injector.get(Dialog);\n    }\n    open(componentOrTemplateRef, config) {\n        let dialogRef;\n        config = { ...(this._defaultOptions || new MatDialogConfig()), ...config };\n        config.id = config.id || `${this._idPrefix}${uniqueId++}`;\n        config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n        const cdkRef = this._dialog.open(componentOrTemplateRef, {\n            ...config,\n            positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n            // Disable closing since we need to sync it up to the animation ourselves.\n            disableClose: true,\n            // Disable closing on destroy, because this service cleans up its open dialogs as well.\n            // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n            // the dialogs immediately whereas we want it to wait for the animations to finish.\n            closeOnDestroy: false,\n            // Disable closing on detachments so that we can sync up the animation.\n            // The Material dialog ref handles this manually.\n            closeOnOverlayDetachments: false,\n            container: {\n                type: this._dialogContainerType,\n                providers: () => [\n                    // Provide our config as the CDK config as well since it has the same interface as the\n                    // CDK one, but it contains the actual values passed in by the user for things like\n                    // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n                    { provide: this.dialogConfigClass, useValue: config },\n                    { provide: DialogConfig, useValue: config },\n                ],\n            },\n            templateContext: () => ({ dialogRef }),\n            providers: (ref, cdkConfig, dialogContainer) => {\n                dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n                dialogRef.updatePosition(config?.position);\n                return [\n                    { provide: this._dialogContainerType, useValue: dialogContainer },\n                    { provide: this._dialogDataToken, useValue: cdkConfig.data },\n                    { provide: this._dialogRefConstructor, useValue: dialogRef },\n                ];\n            },\n        });\n        // This can't be assigned in the `providers` callback, because\n        // the instance hasn't been assigned to the CDK ref yet.\n        dialogRef.componentRef = cdkRef.componentRef;\n        dialogRef.componentInstance = cdkRef.componentInstance;\n        this.openDialogs.push(dialogRef);\n        this.afterOpened.next(dialogRef);\n        dialogRef.afterClosed().subscribe(() => {\n            const index = this.openDialogs.indexOf(dialogRef);\n            if (index > -1) {\n                this.openDialogs.splice(index, 1);\n                if (!this.openDialogs.length) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        });\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        this._closeDialogs(this.openDialogs);\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Only close the dialogs at this level on destroy\n        // since the parent service may still be active.\n        this._closeDialogs(this._openDialogsAtThisLevel);\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n    }\n    _closeDialogs(dialogs) {\n        let i = dialogs.length;\n        while (i--) {\n            dialogs[i].close();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatDialogBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatDialogBase }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatDialogBase, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.Injector }, { type: undefined }, { type: undefined }, { type: i1$1.OverlayContainer }, { type: undefined }, { type: i0.Type }, { type: i0.Type }, { type: i0.InjectionToken }, { type: undefined }]; } });\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog extends _MatDialogBase {\n    constructor(overlay, injector, \n    /**\n     * @deprecated `_location` parameter to be removed.\n     * @breaking-change 10.0.0\n     */\n    location, defaultOptions, scrollStrategy, parentDialog, \n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 15.0.0\n     */\n    overlayContainer, \n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    animationMode) {\n        super(overlay, injector, defaultOptions, parentDialog, overlayContainer, scrollStrategy, MatDialogRef, MatDialogContainer, MAT_DIALOG_DATA, animationMode);\n        this._idPrefix = 'mat-mdc-dialog-';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialog, deps: [{ token: i1$1.Overlay }, { token: i0.Injector }, { token: i2.Location, optional: true }, { token: MAT_DIALOG_DEFAULT_OPTIONS, optional: true }, { token: MAT_DIALOG_SCROLL_STRATEGY }, { token: MatDialog, optional: true, skipSelf: true }, { token: i1$1.OverlayContainer }, { token: ANIMATION_MODULE_TYPE$1, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialog }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialog, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.Injector }, { type: i2.Location, decorators: [{\n                    type: Optional\n                }] }, { type: MatDialogConfig, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DIALOG_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_DIALOG_SCROLL_STRATEGY]\n                }] }, { type: MatDialog, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: i1$1.OverlayContainer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE$1]\n                }] }]; } });\n\n/** Counter used to generate unique IDs for dialog elements. */\nlet dialogElementUid = 0;\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n    constructor(\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    dialogRef, _elementRef, _dialog) {\n        this.dialogRef = dialogRef;\n        this._elementRef = _elementRef;\n        this._dialog = _dialog;\n        /** Default to \"button\" to prevents accidental form submits. */\n        this.type = 'button';\n    }\n    ngOnInit() {\n        if (!this.dialogRef) {\n            // When this directive is included in a dialog via TemplateRef (rather than being\n            // in a Component), the DialogRef isn't available via injection because embedded\n            // views cannot be given a custom injector. Instead, we look up the DialogRef by\n            // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n            // be resolved at constructor time.\n            this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n    }\n    ngOnChanges(changes) {\n        const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n        if (proxiedChange) {\n            this.dialogResult = proxiedChange.currentValue;\n        }\n    }\n    _onButtonClick(event) {\n        // Determinate the focus origin using the click event, because using the FocusMonitor will\n        // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n        // dialog, and therefore clicking the button won't result in a focus change. This means that\n        // the FocusMonitor won't detect any origin change, and will always output `program`.\n        _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogClose, deps: [{ token: MatDialogRef, optional: true }, { token: i0.ElementRef }, { token: MatDialog }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDialogClose, selector: \"[mat-dialog-close], [matDialogClose]\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], type: \"type\", dialogResult: [\"mat-dialog-close\", \"dialogResult\"], _matDialogClose: [\"matDialogClose\", \"_matDialogClose\"] }, host: { listeners: { \"click\": \"_onButtonClick($event)\" }, properties: { \"attr.aria-label\": \"ariaLabel || null\", \"attr.type\": \"type\" } }, exportAs: [\"matDialogClose\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogClose, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-close], [matDialogClose]',\n                    exportAs: 'matDialogClose',\n                    host: {\n                        '(click)': '_onButtonClick($event)',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.type]': 'type',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: MatDialogRef, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }, { type: MatDialog }]; }, propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], type: [{\n                type: Input\n            }], dialogResult: [{\n                type: Input,\n                args: ['mat-dialog-close']\n            }], _matDialogClose: [{\n                type: Input,\n                args: ['matDialogClose']\n            }] } });\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle {\n    constructor(\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    _dialogRef, _elementRef, _dialog) {\n        this._dialogRef = _dialogRef;\n        this._elementRef = _elementRef;\n        this._dialog = _dialog;\n        this.id = `mat-mdc-dialog-title-${dialogElementUid++}`;\n    }\n    ngOnInit() {\n        if (!this._dialogRef) {\n            this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n        if (this._dialogRef) {\n            Promise.resolve().then(() => {\n                const container = this._dialogRef._containerInstance;\n                if (container && !container._ariaLabelledBy) {\n                    container._ariaLabelledBy = this.id;\n                }\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogTitle, deps: [{ token: MatDialogRef, optional: true }, { token: i0.ElementRef }, { token: MatDialog }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDialogTitle, selector: \"[mat-dialog-title], [matDialogTitle]\", inputs: { id: \"id\" }, host: { properties: { \"id\": \"id\" }, classAttribute: \"mat-mdc-dialog-title mdc-dialog__title\" }, exportAs: [\"matDialogTitle\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-title], [matDialogTitle]',\n                    exportAs: 'matDialogTitle',\n                    host: {\n                        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n                        '[id]': 'id',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: MatDialogRef, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }, { type: MatDialog }]; }, propDecorators: { id: [{\n                type: Input\n            }] } });\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDialogContent, selector: \"[mat-dialog-content], mat-dialog-content, [matDialogContent]\", host: { classAttribute: \"mat-mdc-dialog-content mdc-dialog__content\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n                    host: { 'class': 'mat-mdc-dialog-content mdc-dialog__content' },\n                }]\n        }] });\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions {\n    constructor() {\n        /**\n         * Horizontal alignment of action buttons.\n         */\n        this.align = 'start';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogActions, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatDialogActions, selector: \"[mat-dialog-actions], mat-dialog-actions, [matDialogActions]\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-dialog-actions-align-center\": \"align === \\\"center\\\"\", \"class.mat-mdc-dialog-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-dialog-actions mdc-dialog__actions\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n                    host: {\n                        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n                        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n                        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n    let parent = element.nativeElement.parentElement;\n    while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n        parent = parent.parentElement;\n    }\n    return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\n\nclass MatDialogModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogModule, declarations: [MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogActions,\n            MatDialogContent], imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule], exports: [MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogContent,\n            MatDialogActions,\n            MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogModule, providers: [MatDialog, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER], imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule],\n                    exports: [\n                        MatDialogContainer,\n                        MatDialogClose,\n                        MatDialogTitle,\n                        MatDialogContent,\n                        MatDialogActions,\n                        MatCommonModule,\n                    ],\n                    declarations: [\n                        MatDialogContainer,\n                        MatDialogClose,\n                        MatDialogTitle,\n                        MatDialogActions,\n                        MatDialogContent,\n                    ],\n                    providers: [MatDialog, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n */\nconst _defaultParams = {\n    params: { enterAnimationDuration: '150ms', exitAnimationDuration: '75ms' },\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n */\nconst matDialogAnimations = {\n    /** Animation that is applied on the dialog container by default. */\n    dialogContainer: trigger('dialogContainer', [\n        // Note: The `enter` animation transitions to `transform: none`, because for some reason\n        // specifying the transform explicitly, causes IE both to blur the dialog content and\n        // decimate the animation performance. Leaving it as `none` solves both issues.\n        state('void, exit', style({ opacity: 0, transform: 'scale(0.7)' })),\n        state('enter', style({ transform: 'none' })),\n        transition('* => enter', group([\n            animate('{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)', style({ transform: 'none', opacity: 1 })),\n            query('@*', animateChild(), { optional: true }),\n        ]), _defaultParams),\n        transition('* => void, * => exit', group([\n            animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({ opacity: 0 })),\n            query('@*', animateChild(), { optional: true }),\n        ]), _defaultParams),\n    ]),\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DIALOG_DATA, MAT_DIALOG_DEFAULT_OPTIONS, MAT_DIALOG_SCROLL_STRATEGY, MAT_DIALOG_SCROLL_STRATEGY_FACTORY, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, MatDialog, MatDialogActions, MatDialogClose, MatDialogConfig, MatDialogContainer, MatDialogContent, MatDialogModule, MatDialogRef, MatDialogTitle, _MatDialogBase, _MatDialogContainerBase, _closeDialogVia, _defaultParams, matDialogAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,QAAQ,sBAAsB;AAC7D,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACzO,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASN,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASO,kBAAkB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,YAAY,QAAQ,qBAAqB;AAC5F,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,YAAY,QAAQ,qBAAqB;;AAE5G;AACA;AACA;AAFA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AAGA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;IACA,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB;IACA,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,gBAAgB;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;EACJ;AACJ;;AAEA;AACA,MAAMC,UAAU,GAAG,kBAAkB;AACrC;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,uBAAuB,GAAG,GAAG;AACnC;AACA,MAAMC,wBAAwB,GAAG,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,SAASnD,kBAAkB,CAAC;EACrD4B,WAAWA,CAACwB,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAE;IACvH,KAAK,CAACP,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,CAAC;IACpH;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAI3E,YAAY,CAAC,CAAC;EACpD;EACA4E,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACC,OAAO,CAAClB,cAAc,EAAE;MAC9B,IAAI,CAACmB,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,IAAI,IAAI,CAACH,OAAO,CAAClB,cAAc,EAAE;MAC7B,IAAI,CAACmB,UAAU,CAAC,CAAC;IACrB;IACA,IAAI,CAACH,sBAAsB,CAACM,IAAI,CAAC;MAAEjD,KAAK,EAAE,QAAQ;MAAEgD;IAAU,CAAC,CAAC;EACpE;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,gCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFlB,uBAAuB,EAAjCnE,EAAE,CAAAsF,iBAAA,CAAiDtF,EAAE,CAACuF,UAAU,GAAhEvF,EAAE,CAAAsF,iBAAA,CAA2EvE,EAAE,CAACyE,gBAAgB,GAAhGxF,EAAE,CAAAsF,iBAAA,CAA2GvF,QAAQ,MAArHC,EAAE,CAAAsF,iBAAA,CAAgJ3C,eAAe,GAAjK3C,EAAE,CAAAsF,iBAAA,CAA4KvE,EAAE,CAAC0E,oBAAoB,GAArMzF,EAAE,CAAAsF,iBAAA,CAAgNtF,EAAE,CAAC0F,MAAM,GAA3N1F,EAAE,CAAAsF,iBAAA,CAAsO3F,IAAI,CAACgG,UAAU,GAAvP3F,EAAE,CAAAsF,iBAAA,CAAkQvE,EAAE,CAAC6E,YAAY;IAAA,CAA4C;EAAE;EACja;IAAS,IAAI,CAACC,IAAI,kBAD8E7F,EAAE,CAAA8F,iBAAA;MAAAC,IAAA,EACJ5B,uBAAuB;MAAA6B,SAAA;MAAAC,QAAA,GADrBjG,EAAE,CAAAkG,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iCAAA7D,EAAA,EAAAC,GAAA;MAAA6D,aAAA;IAAA,EACmH;EAAE;AAC3N;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGxG,EAAE,CAAAyG,iBAAA,CAGXtC,uBAAuB,EAAc,CAAC;IACrH4B,IAAI,EAAE7F,SAAS;IACfwG,IAAI,EAAE,CAAC;MAAEL,QAAQ,EAAE;IAAG,CAAC;EAC3B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEN,IAAI,EAAE/F,EAAE,CAACuF;IAAW,CAAC,EAAE;MAAEQ,IAAI,EAAEhF,EAAE,CAACyE;IAAiB,CAAC,EAAE;MAAEO,IAAI,EAAEY,SAAS;MAAEC,UAAU,EAAE,CAAC;QACtHb,IAAI,EAAE5F;MACV,CAAC,EAAE;QACC4F,IAAI,EAAE3F,MAAM;QACZsG,IAAI,EAAE,CAAC3G,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAEpD;IAAgB,CAAC,EAAE;MAAEoD,IAAI,EAAEhF,EAAE,CAAC0E;IAAqB,CAAC,EAAE;MAAEM,IAAI,EAAE/F,EAAE,CAAC0F;IAAO,CAAC,EAAE;MAAEK,IAAI,EAAEpG,IAAI,CAACgG;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEhF,EAAE,CAAC6E;IAAa,CAAC,CAAC;EAAE,CAAC;AAAA;AACjK,MAAMiB,4BAA4B,GAAG,kCAAkC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,IAAI,EAAE;EACxB,IAAIA,IAAI,IAAI,IAAI,EAAE;IACd,OAAO,IAAI;EACf;EACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf;EACA,IAAIA,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrB,OAAO5F,oBAAoB,CAAC2F,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;EACnE;EACA,IAAIH,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACpB,OAAO5F,oBAAoB,CAAC2F,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAC1E;EACA,IAAIH,IAAI,KAAK,GAAG,EAAE;IACd,OAAO,CAAC;EACZ;EACA,OAAO,IAAI,CAAC,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA,MAAMI,kBAAkB,SAAShD,uBAAuB,CAAC;EACrDvB,WAAWA,CAACwB,UAAU,EAAEC,gBAAgB,EAAE+C,QAAQ,EAAE7C,YAAY,EAAE8C,OAAO,EAAE5C,MAAM,EAAEC,UAAU,EAAE4C,cAAc,EAAE3C,YAAY,EAAE;IACzH,KAAK,CAACP,UAAU,EAAEC,gBAAgB,EAAE+C,QAAQ,EAAE7C,YAAY,EAAE8C,OAAO,EAAE5C,MAAM,EAAEC,UAAU,EAAEC,YAAY,CAAC;IACtG,IAAI,CAAC2C,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACD,cAAc,KAAK,gBAAgB;IAClE;IACA,IAAI,CAACE,YAAY,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa;IAClD;IACA,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACJ,kBAAkB,GAChDT,YAAY,CAAC,IAAI,CAAChC,OAAO,CAAC8C,sBAAsB,CAAC,IAAI3D,uBAAuB,GAC5E,CAAC;IACP;IACA,IAAI,CAAC4D,sBAAsB,GAAG,IAAI,CAACN,kBAAkB,GAC/CT,YAAY,CAAC,IAAI,CAAChC,OAAO,CAACgD,qBAAqB,CAAC,IAAI5D,wBAAwB,GAC5E,CAAC;IACP;IACA,IAAI,CAAC6D,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,MAAM;MAC3B,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACjD,kBAAkB,CAAC,IAAI,CAAC2C,uBAAuB,CAAC;IACzD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACO,kBAAkB,GAAG,MAAM;MAC5B,IAAI,CAACD,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACrD,sBAAsB,CAACuD,IAAI,CAAC;QAAElG,KAAK,EAAE,QAAQ;QAAEgD,SAAS,EAAE,IAAI,CAAC4C;MAAuB,CAAC,CAAC;IACjG,CAAC;EACL;EACAO,gBAAgBA,CAAA,EAAG;IACf;IACA;IACA,KAAK,CAACA,gBAAgB,CAAC,CAAC;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACAC,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,IAAI,CAACP,eAAe,KAAK,IAAI,EAAE;MAC/BQ,YAAY,CAAC,IAAI,CAACR,eAAe,CAAC;IACtC;EACJ;EACA;EACAM,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACzD,sBAAsB,CAACuD,IAAI,CAAC;MAAElG,KAAK,EAAE,SAAS;MAAEgD,SAAS,EAAE,IAAI,CAAC0C;IAAwB,CAAC,CAAC;IAC/F,IAAI,IAAI,CAACJ,kBAAkB,EAAE;MACzB,IAAI,CAACC,YAAY,CAACtF,KAAK,CAACsG,WAAW,CAAC3B,4BAA4B,EAAG,GAAE,IAAI,CAACc,uBAAwB,IAAG,CAAC;MACtG;MACA;MACA;MACA,IAAI,CAACc,sBAAsB,CAAC,MAAM,IAAI,CAACjB,YAAY,CAACkB,SAAS,CAACC,GAAG,CAAC5E,aAAa,EAAED,UAAU,CAAC,CAAC;MAC7F,IAAI,CAAC8E,2BAA2B,CAAC,IAAI,CAACjB,uBAAuB,EAAE,IAAI,CAACK,iBAAiB,CAAC;IAC1F,CAAC,MACI;MACD,IAAI,CAACR,YAAY,CAACkB,SAAS,CAACC,GAAG,CAAC7E,UAAU,CAAC;MAC3C;MACA;MACA;MACA;MACA+E,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACf,iBAAiB,CAAC,CAAC,CAAC;IAC1D;EACJ;EACA;AACJ;AACA;AACA;EACIgB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACpE,sBAAsB,CAACuD,IAAI,CAAC;MAAElG,KAAK,EAAE,SAAS;MAAEgD,SAAS,EAAE,IAAI,CAAC4C;IAAuB,CAAC,CAAC;IAC9F,IAAI,CAACL,YAAY,CAACkB,SAAS,CAACO,MAAM,CAACnF,UAAU,CAAC;IAC9C,IAAI,IAAI,CAACyD,kBAAkB,EAAE;MACzB,IAAI,CAACC,YAAY,CAACtF,KAAK,CAACsG,WAAW,CAAC3B,4BAA4B,EAAG,GAAE,IAAI,CAACgB,sBAAuB,IAAG,CAAC;MACrG;MACA,IAAI,CAACY,sBAAsB,CAAC,MAAM,IAAI,CAACjB,YAAY,CAACkB,SAAS,CAACC,GAAG,CAAC3E,aAAa,CAAC,CAAC;MACjF,IAAI,CAAC4E,2BAA2B,CAAC,IAAI,CAACf,sBAAsB,EAAE,IAAI,CAACK,kBAAkB,CAAC;IAC1F,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAW,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACb,kBAAkB,CAAC,CAAC,CAAC;IAC3D;EACJ;EACA;EACAD,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACT,YAAY,CAACkB,SAAS,CAACO,MAAM,CAAClF,aAAa,EAAEC,aAAa,CAAC;EACpE;EACA4E,2BAA2BA,CAACM,QAAQ,EAAEC,QAAQ,EAAE;IAC5C,IAAI,IAAI,CAACpB,eAAe,KAAK,IAAI,EAAE;MAC/BQ,YAAY,CAAC,IAAI,CAACR,eAAe,CAAC;IACtC;IACA;IACA;IACA,IAAI,CAACA,eAAe,GAAGqB,UAAU,CAACD,QAAQ,EAAED,QAAQ,CAAC;EACzD;EACA;EACAT,sBAAsBA,CAACU,QAAQ,EAAE;IAC7B,IAAI,CAACE,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAOC,qBAAqB,KAAK,UAAU,EAAE;QAC7CA,qBAAqB,CAACJ,QAAQ,CAAC;MACnC,CAAC,MACI;QACDA,QAAQ,CAAC,CAAC;MACd;IACJ,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAChE,IAAI,YAAAqE,2BAAAnE,CAAA;MAAA,YAAAA,CAAA,IAAwF8B,kBAAkB,EA9K5BnH,EAAE,CAAAsF,iBAAA,CA8K4CtF,EAAE,CAACuF,UAAU,GA9K3DvF,EAAE,CAAAsF,iBAAA,CA8KsEvE,EAAE,CAACyE,gBAAgB,GA9K3FxF,EAAE,CAAAsF,iBAAA,CA8KsGvF,QAAQ,MA9KhHC,EAAE,CAAAsF,iBAAA,CA8K2I3C,eAAe,GA9K5J3C,EAAE,CAAAsF,iBAAA,CA8KuKvE,EAAE,CAAC0E,oBAAoB,GA9KhMzF,EAAE,CAAAsF,iBAAA,CA8K2MtF,EAAE,CAAC0F,MAAM,GA9KtN1F,EAAE,CAAAsF,iBAAA,CA8KiO3F,IAAI,CAACgG,UAAU,GA9KlP3F,EAAE,CAAAsF,iBAAA,CA8K6P7E,qBAAqB,MA9KpRT,EAAE,CAAAsF,iBAAA,CA8K+SvE,EAAE,CAAC6E,YAAY;IAAA,CAA4C;EAAE;EAC9c;IAAS,IAAI,CAACC,IAAI,kBA/K8E7F,EAAE,CAAA8F,iBAAA;MAAAC,IAAA,EA+KJoB,kBAAkB;MAAAnB,SAAA;MAAAyD,SAAA,eAAsE,IAAI;MAAAC,QAAA;MAAAC,YAAA,WAAAC,gCAAAnH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/K1FzC,EAAE,CAAA6J,cAAA,OAAAnH,GAAA,CAAAoC,OAAA,CAAAgF,EAAA;UAAF9J,EAAE,CAAA+J,WAAA,eAAArH,GAAA,CAAAoC,OAAA,CAAArB,SAAA,UAAAf,GAAA,CAAAoC,OAAA,CAAAjC,IAAA,qBAAAH,GAAA,CAAAoC,OAAA,CAAAtB,SAAA,UAAAd,GAAA,CAAAsH,eAAA,gBAAAtH,GAAA,CAAAoC,OAAA,CAAAtB,SAAA,sBAAAd,GAAA,CAAAoC,OAAA,CAAAxB,eAAA;UAAFtD,EAAE,CAAAiK,WAAA,6BAAAvH,GAAA,CAAA6E,kBAAA;QAAA;MAAA;MAAAtB,QAAA,GAAFjG,EAAE,CAAAkG,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAA8D,MAAA;MAAA7D,QAAA,WAAA8D,4BAAA1H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAoK,cAAA,YA+KojB,CAAC,YAAD,CAAC;UA/KvjBpK,EAAE,CAAAqK,UAAA,IAAA7H,yCAAA,wBA+KmqB,CAAC;UA/KtqBxC,EAAE,CAAAsK,YAAA,CA+K6qB,CAAC,CAAD,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAuoVlJ,EAAE,CAACmJ,eAAe;MAAAC,MAAA;MAAAlE,aAAA;IAAA,EAAuN;EAAE;AACtoX;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjLoGxG,EAAE,CAAAyG,iBAAA,CAiLXU,kBAAkB,EAAc,CAAC;IAChHpB,IAAI,EAAE7F,SAAS;IACfwG,IAAI,EAAE,CAAC;MAAEgE,QAAQ,EAAE,sBAAsB;MAAEnE,aAAa,EAAElG,iBAAiB,CAACsK,IAAI;MAAEC,eAAe,EAAEtK,uBAAuB,CAACuK,OAAO;MAAEC,IAAI,EAAE;QAC9H,OAAO,EAAE,qCAAqC;QAC9C,UAAU,EAAE,IAAI;QAChB,mBAAmB,EAAE,mBAAmB;QACxC,MAAM,EAAE,YAAY;QACpB,aAAa,EAAE,cAAc;QAC7B,wBAAwB,EAAE,4CAA4C;QACtE,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE,iCAAiC;QAC5D,iCAAiC,EAAE;MACvC,CAAC;MAAEzE,QAAQ,EAAE,0KAA0K;MAAEoE,MAAM,EAAE,CAAC,okVAAokV;IAAE,CAAC;EACrxV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE1E,IAAI,EAAE/F,EAAE,CAACuF;IAAW,CAAC,EAAE;MAAEQ,IAAI,EAAEhF,EAAE,CAACyE;IAAiB,CAAC,EAAE;MAAEO,IAAI,EAAEY,SAAS;MAAEC,UAAU,EAAE,CAAC;QACtHb,IAAI,EAAE5F;MACV,CAAC,EAAE;QACC4F,IAAI,EAAE3F,MAAM;QACZsG,IAAI,EAAE,CAAC3G,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAEpD;IAAgB,CAAC,EAAE;MAAEoD,IAAI,EAAEhF,EAAE,CAAC0E;IAAqB,CAAC,EAAE;MAAEM,IAAI,EAAE/F,EAAE,CAAC0F;IAAO,CAAC,EAAE;MAAEK,IAAI,EAAEpG,IAAI,CAACgG;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEY,SAAS;MAAEC,UAAU,EAAE,CAAC;QAChJb,IAAI,EAAE5F;MACV,CAAC,EAAE;QACC4F,IAAI,EAAE3F,MAAM;QACZsG,IAAI,EAAE,CAACjG,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAEsF,IAAI,EAAEhF,EAAE,CAAC6E;IAAa,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEnD;AACA;AACA;AACA,MAAMmF,YAAY,CAAC;EACfnI,WAAWA,CAACoI,IAAI,EAAEC,MAAM,EAAEC,kBAAkB,EAAE;IAC1C,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACC,YAAY,GAAG,IAAI5J,OAAO,CAAC,CAAC;IACjC;IACA,IAAI,CAAC6J,aAAa,GAAG,IAAI7J,OAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAAC8J,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACpI,YAAY,GAAGgI,MAAM,CAAChI,YAAY;IACvC,IAAI,CAAC6G,EAAE,GAAGkB,IAAI,CAAClB,EAAE;IACjB;IACAoB,kBAAkB,CAACtG,sBAAsB,CACpC0G,IAAI,CAAC5J,MAAM,CAAC6J,KAAK,IAAIA,KAAK,CAACtJ,KAAK,KAAK,QAAQ,CAAC,EAAEN,IAAI,CAAC,CAAC,CAAC,CAAC,CACxD6J,SAAS,CAAC,MAAM;MACjB,IAAI,CAACL,YAAY,CAACjG,IAAI,CAAC,CAAC;MACxB,IAAI,CAACiG,YAAY,CAACM,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC;IACF;IACAP,kBAAkB,CAACtG,sBAAsB,CACpC0G,IAAI,CAAC5J,MAAM,CAAC6J,KAAK,IAAIA,KAAK,CAACtJ,KAAK,KAAK,QAAQ,CAAC,EAAEN,IAAI,CAAC,CAAC,CAAC,CAAC,CACxD6J,SAAS,CAAC,MAAM;MACjBjD,YAAY,CAAC,IAAI,CAACmD,qBAAqB,CAAC;MACxC,IAAI,CAACxD,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF8C,IAAI,CAACtG,UAAU,CAACiH,WAAW,CAAC,CAAC,CAACH,SAAS,CAAC,MAAM;MAC1C,IAAI,CAACJ,aAAa,CAAClG,IAAI,CAAC,IAAI,CAAC0G,OAAO,CAAC;MACrC,IAAI,CAACR,aAAa,CAACK,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACvD,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF1G,KAAK,CAAC,IAAI,CAACqK,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAACR,IAAI,CAAC5J,MAAM,CAAC6J,KAAK,IAAIA,KAAK,CAACQ,OAAO,KAAKlK,MAAM,IAAI,CAAC,IAAI,CAACoB,YAAY,IAAI,CAACnB,cAAc,CAACyJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAACD,KAAK,IAAI;MACjK,IAAI,CAAC,IAAI,CAACtI,YAAY,EAAE;QACpBsI,KAAK,CAACS,cAAc,CAAC,CAAC;QACtBC,eAAe,CAAC,IAAI,EAAEV,KAAK,CAACxF,IAAI,KAAK,SAAS,GAAG,UAAU,GAAG,OAAO,CAAC;MAC1E;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACImG,KAAKA,CAACC,YAAY,EAAE;IAChB,IAAI,CAACP,OAAO,GAAGO,YAAY;IAC3B;IACA,IAAI,CAACjB,kBAAkB,CAACtG,sBAAsB,CACzC0G,IAAI,CAAC5J,MAAM,CAAC6J,KAAK,IAAIA,KAAK,CAACtJ,KAAK,KAAK,SAAS,CAAC,EAAEN,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD6J,SAAS,CAACD,KAAK,IAAI;MACpB,IAAI,CAACH,aAAa,CAAClG,IAAI,CAACiH,YAAY,CAAC;MACrC,IAAI,CAACf,aAAa,CAACK,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACT,IAAI,CAACtG,UAAU,CAAC0H,cAAc,CAAC,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACV,qBAAqB,GAAGtC,UAAU,CAAC,MAAM,IAAI,CAAClB,kBAAkB,CAAC,CAAC,EAAEqD,KAAK,CAACtG,SAAS,GAAG,GAAG,CAAC;IACnG,CAAC,CAAC;IACF,IAAI,CAACoG,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACH,kBAAkB,CAAClC,mBAAmB,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACIqD,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClB,YAAY;EAC5B;EACA;AACJ;AACA;EACImB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACtB,IAAI,CAACuB,MAAM;EAC3B;EACA;AACJ;AACA;EACIC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpB,aAAa;EAC7B;EACA;AACJ;AACA;EACIS,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACb,IAAI,CAACa,aAAa;EAClC;EACA;AACJ;AACA;EACIC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACd,IAAI,CAACc,aAAa;EAClC;EACA;AACJ;AACA;AACA;EACIW,cAAcA,CAACC,QAAQ,EAAE;IACrB,IAAIC,QAAQ,GAAG,IAAI,CAAC3B,IAAI,CAACC,MAAM,CAAC2B,gBAAgB;IAChD,IAAIF,QAAQ,KAAKA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,CAAC,EAAE;MAC/CJ,QAAQ,CAACG,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACH,QAAQ,CAACG,IAAI,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAACJ,QAAQ,CAACI,KAAK,CAAC;IACjF,CAAC,MACI;MACDH,QAAQ,CAACI,kBAAkB,CAAC,CAAC;IACjC;IACA,IAAIL,QAAQ,KAAKA,QAAQ,CAACM,GAAG,IAAIN,QAAQ,CAACO,MAAM,CAAC,EAAE;MAC/CP,QAAQ,CAACM,GAAG,GAAGL,QAAQ,CAACK,GAAG,CAACN,QAAQ,CAACM,GAAG,CAAC,GAAGL,QAAQ,CAACM,MAAM,CAACP,QAAQ,CAACO,MAAM,CAAC;IAChF,CAAC,MACI;MACDN,QAAQ,CAACO,gBAAgB,CAAC,CAAC;IAC/B;IACA,IAAI,CAAClC,IAAI,CAACyB,cAAc,CAAC,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIU,UAAUA,CAACjK,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAAC6H,IAAI,CAACmC,UAAU,CAACjK,KAAK,EAAEC,MAAM,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACAiK,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACrC,IAAI,CAACoC,aAAa,CAACC,OAAO,CAAC;IAChC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAACrC,IAAI,CAACsC,gBAAgB,CAACD,OAAO,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClC,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACInD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACmD,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACL,IAAI,CAACkB,KAAK,CAAC,IAAI,CAACN,OAAO,EAAE;MAAE4B,WAAW,EAAE,IAAI,CAACC;IAAsB,CAAC,CAAC;IAC1E,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzB,eAAeA,CAAC0B,GAAG,EAAEC,eAAe,EAAEC,MAAM,EAAE;EACnDF,GAAG,CAACF,qBAAqB,GAAGG,eAAe;EAC3C,OAAOD,GAAG,CAACzB,KAAK,CAAC2B,MAAM,CAAC;AAC5B;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIvN,cAAc,CAAC,kBAAkB,CAAC;AAC9D;AACA,MAAMwN,0BAA0B,GAAG,IAAIxN,cAAc,CAAC,gCAAgC,CAAC;AACvF;AACA,MAAMyN,0BAA0B,GAAG,IAAIzN,cAAc,CAAC,gCAAgC,CAAC;AACvF;AACA,SAAS0N,2CAA2CA,CAACC,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA,MAAMC,mCAAmC,GAAG;EACxCC,OAAO,EAAEN,0BAA0B;EACnCO,IAAI,EAAE,CAAC3O,OAAO,CAAC;EACf4O,UAAU,EAAEP;AAChB,CAAC;AACD;AACA,SAASQ,kCAAkCA,CAACP,OAAO,EAAE;EACjD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA,IAAIM,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjB;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,WAAW,GAAG,IAAI,CAACE,uBAAuB;EAC7F;EACA;EACA,IAAIzC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACwC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACxC,WAAW,GAAG,IAAI,CAAC0C,uBAAuB;EAC7F;EACAC,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,MAAM,GAAG,IAAI,CAACJ,aAAa;IACjC,OAAOI,MAAM,GAAGA,MAAM,CAACD,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACE,0BAA0B;EACjF;EACAtM,WAAWA,CAACuM,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAER,aAAa;EAC9D;AACJ;AACA;AACA;EACIS,iBAAiB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,oBAAoB,EAAEC,gBAAgB;EAChG;AACJ;AACA;AACA;EACIpI,cAAc,EAAE;IACZ,IAAI,CAAC6H,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACW,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACZ,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACI,0BAA0B,GAAG,IAAI3N,OAAO,CAAC,CAAC;IAC/C,IAAI,CAACwN,uBAAuB,GAAG,IAAIxN,OAAO,CAAC,CAAC;IAC5C,IAAI,CAACoO,SAAS,GAAG,aAAa;IAC9B,IAAI,CAACC,iBAAiB,GAAGjN,eAAe;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAACkN,cAAc,GAAGpO,KAAK,CAAC,MAAM,IAAI,CAACmN,WAAW,CAAC1H,MAAM,GACnD,IAAI,CAAC8H,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC1D,IAAI,CAAC1J,SAAS,CAAC+E,SAAS,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACmJ,eAAe,GAAGP,cAAc;IACrC,IAAI,CAACQ,OAAO,GAAGX,QAAQ,CAACY,GAAG,CAAC/O,MAAM,CAAC;EACvC;EACAgP,IAAIA,CAACC,sBAAsB,EAAEjF,MAAM,EAAE;IACjC,IAAIkF,SAAS;IACblF,MAAM,GAAG;MAAE,IAAI,IAAI,CAACoE,eAAe,IAAI,IAAI1M,eAAe,CAAC,CAAC,CAAC;MAAE,GAAGsI;IAAO,CAAC;IAC1EA,MAAM,CAACnB,EAAE,GAAGmB,MAAM,CAACnB,EAAE,IAAK,GAAE,IAAI,CAAC6F,SAAU,GAAEjB,QAAQ,EAAG,EAAC;IACzDzD,MAAM,CAACsE,cAAc,GAAGtE,MAAM,CAACsE,cAAc,IAAI,IAAI,CAACO,eAAe,CAAC,CAAC;IACvE,MAAMM,MAAM,GAAG,IAAI,CAACL,OAAO,CAACE,IAAI,CAACC,sBAAsB,EAAE;MACrD,GAAGjF,MAAM;MACT2B,gBAAgB,EAAE,IAAI,CAACuC,QAAQ,CAACzC,QAAQ,CAAC,CAAC,CAAC2D,MAAM,CAAC,CAAC,CAACtD,kBAAkB,CAAC,CAAC,CAACG,gBAAgB,CAAC,CAAC;MAC3F;MACAjK,YAAY,EAAE,IAAI;MAClB;MACA;MACA;MACAqN,cAAc,EAAE,KAAK;MACrB;MACA;MACAC,yBAAyB,EAAE,KAAK;MAChCC,SAAS,EAAE;QACPzK,IAAI,EAAE,IAAI,CAAC0J,oBAAoB;QAC/BgB,SAAS,EAAEA,CAAA,KAAM;QACb;QACA;QACA;QACA;UAAEnC,OAAO,EAAE,IAAI,CAACsB,iBAAiB;UAAEc,QAAQ,EAAEzF;QAAO,CAAC,EACrD;UAAEqD,OAAO,EAAEpN,YAAY;UAAEwP,QAAQ,EAAEzF;QAAO,CAAC;MAEnD,CAAC;MACD0F,eAAe,EAAEA,CAAA,MAAO;QAAER;MAAU,CAAC,CAAC;MACtCM,SAAS,EAAEA,CAAC9C,GAAG,EAAEiD,SAAS,EAAEC,eAAe,KAAK;QAC5CV,SAAS,GAAG,IAAI,IAAI,CAACX,qBAAqB,CAAC7B,GAAG,EAAE1C,MAAM,EAAE4F,eAAe,CAAC;QACxEV,SAAS,CAAC1D,cAAc,CAACxB,MAAM,EAAEyB,QAAQ,CAAC;QAC1C,OAAO,CACH;UAAE4B,OAAO,EAAE,IAAI,CAACmB,oBAAoB;UAAEiB,QAAQ,EAAEG;QAAgB,CAAC,EACjE;UAAEvC,OAAO,EAAE,IAAI,CAACoB,gBAAgB;UAAEgB,QAAQ,EAAEE,SAAS,CAACvN;QAAK,CAAC,EAC5D;UAAEiL,OAAO,EAAE,IAAI,CAACkB,qBAAqB;UAAEkB,QAAQ,EAAEP;QAAU,CAAC,CAC/D;MACL;IACJ,CAAC,CAAC;IACF;IACA;IACAA,SAAS,CAACW,YAAY,GAAGV,MAAM,CAACU,YAAY;IAC5CX,SAAS,CAACzC,iBAAiB,GAAG0C,MAAM,CAAC1C,iBAAiB;IACtD,IAAI,CAACkB,WAAW,CAACmC,IAAI,CAACZ,SAAS,CAAC;IAChC,IAAI,CAAC9D,WAAW,CAACnH,IAAI,CAACiL,SAAS,CAAC;IAChCA,SAAS,CAAC7D,WAAW,CAAC,CAAC,CAACd,SAAS,CAAC,MAAM;MACpC,MAAMwF,KAAK,GAAG,IAAI,CAACpC,WAAW,CAACqC,OAAO,CAACd,SAAS,CAAC;MACjD,IAAIa,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ,IAAI,CAACpC,WAAW,CAACsC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,CAACpC,WAAW,CAAC1H,MAAM,EAAE;UAC1B,IAAI,CAAC8H,kBAAkB,CAAC,CAAC,CAAC9J,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;IACF,OAAOiL,SAAS;EACpB;EACA;AACJ;AACA;EACIgB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,aAAa,CAAC,IAAI,CAACxC,WAAW,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIyC,aAAaA,CAACvH,EAAE,EAAE;IACd,OAAO,IAAI,CAAC8E,WAAW,CAAC0C,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACzH,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACAxB,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAAC8I,aAAa,CAAC,IAAI,CAACtC,uBAAuB,CAAC;IAChD,IAAI,CAACI,0BAA0B,CAACzD,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACsD,uBAAuB,CAACtD,QAAQ,CAAC,CAAC;EAC3C;EACA2F,aAAaA,CAACI,OAAO,EAAE;IACnB,IAAIC,CAAC,GAAGD,OAAO,CAACtK,MAAM;IACtB,OAAOuK,CAAC,EAAE,EAAE;MACRD,OAAO,CAACC,CAAC,CAAC,CAACvF,KAAK,CAAC,CAAC;IACtB;EACJ;EACA;IAAS,IAAI,CAAC/G,IAAI,YAAAuM,uBAAArM,CAAA;MAjgB8ErF,EAAE,CAAA2R,gBAAA;IAAA,CAigBiF;EAAE;EACrL;IAAS,IAAI,CAACC,KAAK,kBAlgB6E5R,EAAE,CAAA6R,kBAAA;MAAAC,KAAA,EAkgBYnD,cAAc;MAAAoD,OAAA,EAAdpD,cAAc,CAAAxJ;IAAA,EAAG;EAAE;AACrI;AACA;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KApgBoGxG,EAAE,CAAAyG,iBAAA,CAogBXkI,cAAc,EAAc,CAAC;IAC5G5I,IAAI,EAAEvF;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAEpG,IAAI,CAACC;IAAQ,CAAC,EAAE;MAAEmG,IAAI,EAAE/F,EAAE,CAACgS;IAAS,CAAC,EAAE;MAAEjM,IAAI,EAAEY;IAAU,CAAC,EAAE;MAAEZ,IAAI,EAAEY;IAAU,CAAC,EAAE;MAAEZ,IAAI,EAAEpG,IAAI,CAACsS;IAAiB,CAAC,EAAE;MAAElM,IAAI,EAAEY;IAAU,CAAC,EAAE;MAAEZ,IAAI,EAAE/F,EAAE,CAACkS;IAAK,CAAC,EAAE;MAAEnM,IAAI,EAAE/F,EAAE,CAACkS;IAAK,CAAC,EAAE;MAAEnM,IAAI,EAAE/F,EAAE,CAACO;IAAe,CAAC,EAAE;MAAEwF,IAAI,EAAEY;IAAU,CAAC,CAAC;EAAE,CAAC;AAAA;AAC3R;AACA;AACA;AACA,MAAMwL,SAAS,SAASxD,cAAc,CAAC;EACnC/L,WAAWA,CAACsL,OAAO,EAAEkB,QAAQ;EAC7B;AACJ;AACA;AACA;EACIgD,QAAQ,EAAEC,cAAc,EAAE9C,cAAc,EAAE+C,YAAY;EACtD;AACJ;AACA;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,aAAa,EAAE;IACX,KAAK,CAACtE,OAAO,EAAEkB,QAAQ,EAAEiD,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEhD,cAAc,EAAExE,YAAY,EAAE5D,kBAAkB,EAAE2G,eAAe,EAAE0E,aAAa,CAAC;IAC1J,IAAI,CAAC7C,SAAS,GAAG,iBAAiB;EACtC;EACA;IAAS,IAAI,CAACxK,IAAI,YAAAsN,kBAAApN,CAAA;MAAA,YAAAA,CAAA,IAAwF8M,SAAS,EA9hBnBnS,EAAE,CAAA0S,QAAA,CA8hBmC/S,IAAI,CAACC,OAAO,GA9hBjDI,EAAE,CAAA0S,QAAA,CA8hB4D1S,EAAE,CAACgS,QAAQ,GA9hBzEhS,EAAE,CAAA0S,QAAA,CA8hBoF5S,EAAE,CAAC6S,QAAQ,MA9hBjG3S,EAAE,CAAA0S,QAAA,CA8hB4H3E,0BAA0B,MA9hBxJ/N,EAAE,CAAA0S,QAAA,CA8hBmL1E,0BAA0B,GA9hB/MhO,EAAE,CAAA0S,QAAA,CA8hB0NP,SAAS,OA9hBrOnS,EAAE,CAAA0S,QAAA,CA8hBgR/S,IAAI,CAACsS,gBAAgB,GA9hBvSjS,EAAE,CAAA0S,QAAA,CA8hBkThS,uBAAuB;IAAA,CAA6D;EAAE;EAC1e;IAAS,IAAI,CAACkR,KAAK,kBA/hB6E5R,EAAE,CAAA6R,kBAAA;MAAAC,KAAA,EA+hBYK,SAAS;MAAAJ,OAAA,EAATI,SAAS,CAAAhN;IAAA,EAAG;EAAE;AAChI;AACA;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KAjiBoGxG,EAAE,CAAAyG,iBAAA,CAiiBX0L,SAAS,EAAc,CAAC;IACvGpM,IAAI,EAAEvF;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAEpG,IAAI,CAACC;IAAQ,CAAC,EAAE;MAAEmG,IAAI,EAAE/F,EAAE,CAACgS;IAAS,CAAC,EAAE;MAAEjM,IAAI,EAAEjG,EAAE,CAAC6S,QAAQ;MAAE/L,UAAU,EAAE,CAAC;QAC/Gb,IAAI,EAAE5F;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4F,IAAI,EAAEpD,eAAe;MAAEiE,UAAU,EAAE,CAAC;QACxCb,IAAI,EAAE5F;MACV,CAAC,EAAE;QACC4F,IAAI,EAAE3F,MAAM;QACZsG,IAAI,EAAE,CAACqH,0BAA0B;MACrC,CAAC;IAAE,CAAC,EAAE;MAAEhI,IAAI,EAAEY,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCb,IAAI,EAAE3F,MAAM;QACZsG,IAAI,EAAE,CAACsH,0BAA0B;MACrC,CAAC;IAAE,CAAC,EAAE;MAAEjI,IAAI,EAAEoM,SAAS;MAAEvL,UAAU,EAAE,CAAC;QAClCb,IAAI,EAAE5F;MACV,CAAC,EAAE;QACC4F,IAAI,EAAEpF;MACV,CAAC;IAAE,CAAC,EAAE;MAAEoF,IAAI,EAAEpG,IAAI,CAACsS;IAAiB,CAAC,EAAE;MAAElM,IAAI,EAAEY,SAAS;MAAEC,UAAU,EAAE,CAAC;QACnEb,IAAI,EAAE5F;MACV,CAAC,EAAE;QACC4F,IAAI,EAAE3F,MAAM;QACZsG,IAAI,EAAE,CAAChG,uBAAuB;MAClC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,IAAIkS,gBAAgB,GAAG,CAAC;AACxB;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBjQ,WAAWA;EACX;EACA;EACAuN,SAAS,EAAE1I,WAAW,EAAEsI,OAAO,EAAE;IAC7B,IAAI,CAACI,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC1I,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACsI,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAAChK,IAAI,GAAG,QAAQ;EACxB;EACA+M,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC3C,SAAS,EAAE;MACjB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACA,SAAS,GAAG4C,gBAAgB,CAAC,IAAI,CAACtL,WAAW,EAAE,IAAI,CAACsI,OAAO,CAACnB,WAAW,CAAC;IACjF;EACJ;EACAoE,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC,IAAIA,OAAO,CAAC,uBAAuB,CAAC;IACpF,IAAIC,aAAa,EAAE;MACf,IAAI,CAAC/G,YAAY,GAAG+G,aAAa,CAACC,YAAY;IAClD;EACJ;EACAC,cAAcA,CAAC7H,KAAK,EAAE;IAClB;IACA;IACA;IACA;IACAU,eAAe,CAAC,IAAI,CAACkE,SAAS,EAAE5E,KAAK,CAAC8H,OAAO,KAAK,CAAC,IAAI9H,KAAK,CAAC+H,OAAO,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,IAAI,CAACnH,YAAY,CAAC;EACzH;EACA;IAAS,IAAI,CAAChH,IAAI,YAAAoO,uBAAAlO,CAAA;MAAA,YAAAA,CAAA,IAAwFwN,cAAc,EA/lBxB7S,EAAE,CAAAsF,iBAAA,CA+lBwCyF,YAAY,MA/lBtD/K,EAAE,CAAAsF,iBAAA,CA+lBiFtF,EAAE,CAACuF,UAAU,GA/lBhGvF,EAAE,CAAAsF,iBAAA,CA+lB2G6M,SAAS;IAAA,CAA4C;EAAE;EACpQ;IAAS,IAAI,CAACqB,IAAI,kBAhmB8ExT,EAAE,CAAAyT,iBAAA;MAAA1N,IAAA,EAgmBJ8M,cAAc;MAAA7M,SAAA;MAAA0D,QAAA;MAAAC,YAAA,WAAA+J,4BAAAjR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhmBZzC,EAAE,CAAA2T,UAAA,mBAAAC,wCAAAC,MAAA;YAAA,OAgmBJnR,GAAA,CAAA0Q,cAAA,CAAAS,MAAqB,CAAC;UAAA;QAAA;QAAA,IAAApR,EAAA;UAhmBpBzC,EAAE,CAAA+J,WAAA,eAAArH,GAAA,CAAAc,SAAA,kBAAAd,GAAA,CAAAqD,IAAA;QAAA;MAAA;MAAA+N,MAAA;QAAAtQ,SAAA;QAAAuC,IAAA;QAAAoG,YAAA;QAAA4H,eAAA;MAAA;MAAAC,QAAA;MAAA/N,QAAA,GAAFjG,EAAE,CAAAiU,oBAAA;IAAA,EAgmBub;EAAE;AAC/hB;AACA;EAAA,QAAAzN,SAAA,oBAAAA,SAAA,KAlmBoGxG,EAAE,CAAAyG,iBAAA,CAkmBXoM,cAAc,EAAc,CAAC;IAC5G9M,IAAI,EAAEnF,SAAS;IACf8F,IAAI,EAAE,CAAC;MACCgE,QAAQ,EAAE,sCAAsC;MAChDsJ,QAAQ,EAAE,gBAAgB;MAC1BlJ,IAAI,EAAE;QACF,SAAS,EAAE,wBAAwB;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,aAAa,EAAE;MACnB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/E,IAAI,EAAEgF,YAAY;MAAEnE,UAAU,EAAE,CAAC;QACjEb,IAAI,EAAE5F;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4F,IAAI,EAAE/F,EAAE,CAACuF;IAAW,CAAC,EAAE;MAAEQ,IAAI,EAAEoM;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE3O,SAAS,EAAE,CAAC;MACtFuC,IAAI,EAAElF,KAAK;MACX6F,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEX,IAAI,EAAE,CAAC;MACPA,IAAI,EAAElF;IACV,CAAC,CAAC;IAAEsL,YAAY,EAAE,CAAC;MACfpG,IAAI,EAAElF,KAAK;MACX6F,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEqN,eAAe,EAAE,CAAC;MAClBhO,IAAI,EAAElF,KAAK;MACX6F,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMwN,cAAc,CAAC;EACjBtR,WAAWA;EACX;EACA;EACAuR,UAAU,EAAE1M,WAAW,EAAEsI,OAAO,EAAE;IAC9B,IAAI,CAACoE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC1M,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACsI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjG,EAAE,GAAI,wBAAuB8I,gBAAgB,EAAG,EAAC;EAC1D;EACAE,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACqB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAGpB,gBAAgB,CAAC,IAAI,CAACtL,WAAW,EAAE,IAAI,CAACsI,OAAO,CAACnB,WAAW,CAAC;IAClF;IACA,IAAI,IAAI,CAACuF,UAAU,EAAE;MACjBtL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,MAAMyH,SAAS,GAAG,IAAI,CAAC2D,UAAU,CAACjJ,kBAAkB;QACpD,IAAIsF,SAAS,IAAI,CAACA,SAAS,CAACxG,eAAe,EAAE;UACzCwG,SAAS,CAACxG,eAAe,GAAG,IAAI,CAACF,EAAE;QACvC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;IAAS,IAAI,CAAC3E,IAAI,YAAAiP,uBAAA/O,CAAA;MAAA,YAAAA,CAAA,IAAwF6O,cAAc,EArpBxBlU,EAAE,CAAAsF,iBAAA,CAqpBwCyF,YAAY,MArpBtD/K,EAAE,CAAAsF,iBAAA,CAqpBiFtF,EAAE,CAACuF,UAAU,GArpBhGvF,EAAE,CAAAsF,iBAAA,CAqpB2G6M,SAAS;IAAA,CAA4C;EAAE;EACpQ;IAAS,IAAI,CAACqB,IAAI,kBAtpB8ExT,EAAE,CAAAyT,iBAAA;MAAA1N,IAAA,EAspBJmO,cAAc;MAAAlO,SAAA;MAAAyD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA0K,4BAAA5R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtpBZzC,EAAE,CAAA6J,cAAA,OAAAnH,GAAA,CAAAoH,EAAA;QAAA;MAAA;MAAAgK,MAAA;QAAAhK,EAAA;MAAA;MAAAkK,QAAA;IAAA,EAspBiO;EAAE;AACzU;AACA;EAAA,QAAAxN,SAAA,oBAAAA,SAAA,KAxpBoGxG,EAAE,CAAAyG,iBAAA,CAwpBXyN,cAAc,EAAc,CAAC;IAC5GnO,IAAI,EAAEnF,SAAS;IACf8F,IAAI,EAAE,CAAC;MACCgE,QAAQ,EAAE,sCAAsC;MAChDsJ,QAAQ,EAAE,gBAAgB;MAC1BlJ,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/E,IAAI,EAAEgF,YAAY;MAAEnE,UAAU,EAAE,CAAC;QACjEb,IAAI,EAAE5F;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4F,IAAI,EAAE/F,EAAE,CAACuF;IAAW,CAAC,EAAE;MAAEQ,IAAI,EAAEoM;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAErI,EAAE,EAAE,CAAC;MAC/E/D,IAAI,EAAElF;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMyT,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACnP,IAAI,YAAAoP,yBAAAlP,CAAA;MAAA,YAAAA,CAAA,IAAwFiP,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACd,IAAI,kBA5qB8ExT,EAAE,CAAAyT,iBAAA;MAAA1N,IAAA,EA4qBJuO,gBAAgB;MAAAtO,SAAA;MAAAyD,SAAA;IAAA,EAAmK;EAAE;AACvR;AACA;EAAA,QAAAjD,SAAA,oBAAAA,SAAA,KA9qBoGxG,EAAE,CAAAyG,iBAAA,CA8qBX6N,gBAAgB,EAAc,CAAC;IAC9GvO,IAAI,EAAEnF,SAAS;IACf8F,IAAI,EAAE,CAAC;MACCgE,QAAQ,EAAG,8DAA6D;MACxEI,IAAI,EAAE;QAAE,OAAO,EAAE;MAA6C;IAClE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM0J,gBAAgB,CAAC;EACnB5R,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAAC6R,KAAK,GAAG,OAAO;EACxB;EACA;IAAS,IAAI,CAACtP,IAAI,YAAAuP,yBAAArP,CAAA;MAAA,YAAAA,CAAA,IAAwFmP,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAChB,IAAI,kBAjsB8ExT,EAAE,CAAAyT,iBAAA;MAAA1N,IAAA,EAisBJyO,gBAAgB;MAAAxO,SAAA;MAAAyD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAgL,8BAAAlS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjsBdzC,EAAE,CAAAiK,WAAA,wCAAAvH,GAAA,CAAA+R,KAAA,mDAAA/R,GAAA,CAAA+R,KAAA;QAAA;MAAA;MAAAX,MAAA;QAAAW,KAAA;MAAA;IAAA,EAisB+V;EAAE;AACvc;AACA;EAAA,QAAAjO,SAAA,oBAAAA,SAAA,KAnsBoGxG,EAAE,CAAAyG,iBAAA,CAmsBX+N,gBAAgB,EAAc,CAAC;IAC9GzO,IAAI,EAAEnF,SAAS;IACf8F,IAAI,EAAE,CAAC;MACCgE,QAAQ,EAAG,8DAA6D;MACxEI,IAAI,EAAE;QACF,OAAO,EAAE,4CAA4C;QACrD,6CAA6C,EAAE,oBAAoB;QACnE,0CAA0C,EAAE;MAChD;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE2J,KAAK,EAAE,CAAC;MACtB1O,IAAI,EAAElF;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,SAASkS,gBAAgBA,CAAC6B,OAAO,EAAEhG,WAAW,EAAE;EAC5C,IAAIK,MAAM,GAAG2F,OAAO,CAAClN,aAAa,CAACmN,aAAa;EAChD,OAAO5F,MAAM,IAAI,CAACA,MAAM,CAACvG,SAAS,CAACoM,QAAQ,CAAC,0BAA0B,CAAC,EAAE;IACrE7F,MAAM,GAAGA,MAAM,CAAC4F,aAAa;EACjC;EACA,OAAO5F,MAAM,GAAGL,WAAW,CAAC0C,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACzH,EAAE,KAAKmF,MAAM,CAACnF,EAAE,CAAC,GAAG,IAAI;AAC9E;AAEA,MAAMiL,eAAe,CAAC;EAClB;IAAS,IAAI,CAAC5P,IAAI,YAAA6P,wBAAA3P,CAAA;MAAA,YAAAA,CAAA,IAAwF0P,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBA/tB8EjV,EAAE,CAAAkV,gBAAA;MAAAnP,IAAA,EA+tBSgP;IAAe,EAS/F;EAAE;EAC7B;IAAS,IAAI,CAACI,IAAI,kBAzuB8EnV,EAAE,CAAAoV,gBAAA;MAAA3E,SAAA,EAyuBqC,CAAC0B,SAAS,EAAE9D,mCAAmC,CAAC;MAAAgH,OAAA,GAAYlU,YAAY,EAAEtB,aAAa,EAAEyB,YAAY,EAAES,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AACxR;AACA;EAAA,QAAAyE,SAAA,oBAAAA,SAAA,KA3uBoGxG,EAAE,CAAAyG,iBAAA,CA2uBXsO,eAAe,EAAc,CAAC;IAC7GhP,IAAI,EAAEjF,QAAQ;IACd4F,IAAI,EAAE,CAAC;MACC2O,OAAO,EAAE,CAAClU,YAAY,EAAEtB,aAAa,EAAEyB,YAAY,EAAES,eAAe,CAAC;MACrEuT,OAAO,EAAE,CACLnO,kBAAkB,EAClB0L,cAAc,EACdqB,cAAc,EACdI,gBAAgB,EAChBE,gBAAgB,EAChBzS,eAAe,CAClB;MACDwT,YAAY,EAAE,CACVpO,kBAAkB,EAClB0L,cAAc,EACdqB,cAAc,EACdM,gBAAgB,EAChBF,gBAAgB,CACnB;MACD7D,SAAS,EAAE,CAAC0B,SAAS,EAAE9D,mCAAmC;IAC9D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMmH,cAAc,GAAG;EACnBC,MAAM,EAAE;IAAE7N,sBAAsB,EAAE,OAAO;IAAEE,qBAAqB,EAAE;EAAO;AAC7E,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM4N,mBAAmB,GAAG;EACxB;EACA7E,eAAe,EAAE7O,OAAO,CAAC,iBAAiB,EAAE;EACxC;EACA;EACA;EACAC,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC;IAAEyT,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CAAC,CAAC,EACnE3T,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;IAAE0T,SAAS,EAAE;EAAO,CAAC,CAAC,CAAC,EAC5CzT,UAAU,CAAC,YAAY,EAAEC,KAAK,CAAC,CAC3BC,OAAO,CAAC,uDAAuD,EAAEH,KAAK,CAAC;IAAE0T,SAAS,EAAE,MAAM;IAAED,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EAC1GrT,KAAK,CAAC,IAAI,EAAEC,YAAY,CAAC,CAAC,EAAE;IAAEsT,QAAQ,EAAE;EAAK,CAAC,CAAC,CAClD,CAAC,EAAEL,cAAc,CAAC,EACnBrT,UAAU,CAAC,sBAAsB,EAAEC,KAAK,CAAC,CACrCC,OAAO,CAAC,0DAA0D,EAAEH,KAAK,CAAC;IAAEyT,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EAC1FrT,KAAK,CAAC,IAAI,EAAEC,YAAY,CAAC,CAAC,EAAE;IAAEsT,QAAQ,EAAE;EAAK,CAAC,CAAC,CAClD,CAAC,EAAEL,cAAc,CAAC,CACtB;AACL,CAAC;;AAED;AACA;AACA;;AAEA,SAAS1H,eAAe,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAES,kCAAkC,EAAEJ,mCAAmC,EAAEJ,2CAA2C,EAAEkE,SAAS,EAAEqC,gBAAgB,EAAE3B,cAAc,EAAElQ,eAAe,EAAEwE,kBAAkB,EAAEmN,gBAAgB,EAAES,eAAe,EAAEhK,YAAY,EAAEmJ,cAAc,EAAEvF,cAAc,EAAExK,uBAAuB,EAAE8H,eAAe,EAAEuJ,cAAc,EAAEE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}